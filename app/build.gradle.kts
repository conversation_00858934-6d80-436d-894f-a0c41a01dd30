plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-kapt")
    id("dagger.hilt.android.plugin")
    id("kotlin-parcelize")
}


android {
    namespace = "com.metathought.food_order.casheir"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.metathought.food_order.casheir"
        minSdk = 24
        targetSdk = 34
        versionCode = 85
        versionName = "2.17.10"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }


    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding = true
        dataBinding = true
        buildConfig = true
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    bundle {
        language {
            enableSplit = false
        }
    }
    flavorDimensions += "version"

    signingConfigs {
        register("release") {
            enableV1Signing = true
            enableV2Signing = true
            enableV3Signing = true
            enableV4Signing = true
            keyAlias = "cashier_alias"
            keyPassword = "132468"
            storeFile = file("src/main/jks/cashier_app.jks")
            storePassword = "132468"
        }
    }
    buildTypes {
        debug {
            signingConfig = signingConfigs.getByName("release")
        }
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }
    productFlavors {
        create("dev") {
            applicationIdSuffix = ".dev"
            dimension = "version"
            manifestPlaceholders.put("PACKAGE_NAME", "${namespace}.dev")
//            //俊贤
//            buildConfigField("String", "BASE_URL", "\"http://192.168.6.117:8089/api/\"")
//            buildConfigField("String", "BASE_WS", "\"ws://192.168.6.117:9988/ws\"")
            //建鹏
            buildConfigField("String", "BASE_URL", "\"http://192.168.6.136:8089/api/\"")
            buildConfigField("String", "BASE_WS", "\"ws://192.168.6.136:9988/ws\"")

            //buildConfigField("String", "BASE_URL", "\"https://test-mstaff.metathought.co/api/\"")
//            buildConfigField("String", "BASE_URL", "\"https://dev-mstaff.metathought.co/api/\"")
//            buildConfigField("String", "BASE_WS", "\"wss://dev-mstaff.metathought.co/ws\"")

//            buildConfigField("String", "BASE_URL", "\"http://192.168.6.104:8080/api/\"")
//            buildConfigField("String", "BASE_WS", "\"ws://192.168.6.104:8081/ws\"")

            buildConfigField(
                "String",
                "token",
                "\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiI2OjE1ODQ4MzE4MzE3ODA3NDkzMTIiLCJyblN0ciI6Imo3bTFyT0NLYlliOThSOU1oaEJ2ZVNNTUQzenBFb0dQIiwidXNlclR5cGUiOiI2IiwidXNlcklkIjoxNTg0ODMxODMxNzgwNzQ5MzEyfQ.knuJYlgcxEficW_3HYf1-hi6OeuWE9sLdpWELN5WPhY'\""
            )
            resValue("string", "app_name", "Dev Cashier")
        }
        create("itest") {
            applicationIdSuffix = ".test"
            dimension = "version"
            manifestPlaceholders.put("PACKAGE_NAME", "${namespace}.test")
            buildConfigField("String", "BASE_URL", "\"https://test-mstaff.metathought.co/api/\"")
            buildConfigField("String", "BASE_WS", "\"wss://test-mstaff.metathought.co/ws\"")
            buildConfigField(
                "String",
                "token",
                "\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiI2OjE1ODQ4MzE4MzE3ODA3NDkzMTIiLCJyblN0ciI6Imo3bTFyT0NLYlliOThSOU1oaEJ2ZVNNTUQzenBFb0dQIiwidXNlclR5cGUiOiI2IiwidXNlcklkIjoxNTg0ODMxODMxNzgwNzQ5MzEyfQ.knuJYlgcxEficW_3HYf1-hi6OeuWE9sLdpWELN5WPhY'\""
            )
            resValue("string", "app_name", "Test Cashier")
        }
        create("uat") {
            applicationIdSuffix = ".uat"
            dimension = "version"
            manifestPlaceholders.put("PACKAGE_NAME", "${namespace}.uat")
            buildConfigField("String", "BASE_URL", "\"https://uat-mstaff.metathought.co/api/\"")
            buildConfigField("String", "BASE_WS", "\"wss://uat-mstaff.metathought.co/ws\"")
            buildConfigField(
                "String",
                "token",
                "\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiI2OjE1ODQ4MzE4MzE3ODA3NDkzMTIiLCJyblN0ciI6Imo3bTFyT0NLYlliOThSOU1oaEJ2ZVNNTUQzenBFb0dQIiwidXNlclR5cGUiOiI2IiwidXNlcklkIjoxNTg0ODMxODMxNzgwNzQ5MzEyfQ.knuJYlgcxEficW_3HYf1-hi6OeuWE9sLdpWELN5WPhY'\""
            )
            resValue("string", "app_name", "UAT Cashier")
        }
        create("prod") {
            dimension = "version"
            manifestPlaceholders.put("PACKAGE_NAME", "$namespace")

            buildConfigField("String", "BASE_URL", "\"https://api.m-pos.cc/api/\"")
            buildConfigField("String", "BASE_WS", "\"wss://api.m-pos.cc/ws\"")
            buildConfigField(
                "String",
                "token",
                "\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiI2OjE1ODQ4MzE4MzE3ODA3NDkzMTIiLCJyblN0ciI6Imo3bTFyT0NLYlliOThSOU1oaEJ2ZVNNTUQzenBFb0dQIiwidXNlclR5cGUiOiI2IiwidXNlcklkIjoxNTg0ODMxODMxNzgwNzQ5MzEyfQ.knuJYlgcxEficW_3HYf1-hi6OeuWE9sLdpWELN5WPhY'\""
            )
            resValue("string", "app_name", "MPOS Cashier")
        }
    }
}


dependencies {
    implementation(project(":ccp"))
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.11.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.legacy:legacy-support-v4:1.0.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.6.1")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1")
    implementation("androidx.navigation:navigation-fragment-ktx:2.7.6")
    implementation("androidx.navigation:navigation-ui-ktx:2.7.6")
    implementation("androidx.annotation:annotation:1.7.1")
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")

    implementation("com.alibaba:fastjson:1.2.83")
    implementation("org.jetbrains.kotlin:kotlin-reflect:1.4.32")

    //gson
    implementation("com.google.code.gson:gson:2.10")
    implementation("com.jakewharton.timber:timber:5.0.1")

    // retrofit
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    //log Http
    implementation("com.squareup.okhttp3:logging-interceptor:4.9.0")


    //QR Image Generator
    implementation("com.github.alexzhirkevich:custom-qr-generator:1.6.2")
    
    // Enhanced DateTime Pickers
    implementation("com.wdullaer:materialdatetimepicker:4.2.3")
    implementation("com.github.Kunzisoft:Android-SwitchDateTimePicker:2.1")

    //Image loader
    implementation("com.github.bumptech.glide:glide:4.16.0")
    //dagger-hilt
    implementation("com.google.dagger:hilt-android:2.48")
    kapt("com.google.dagger:hilt-compiler:2.48")

    //sunmi printer
    implementation("com.sunmi:printerx:1.0.17")
    implementation("com.alibaba:fastjson:1.2.83")

    implementation("androidx.datastore:datastore-preferences:1.0.0")

    //chart
    implementation("com.github.PhilJay:MPAndroidChart:v3.1.0")

    //slideshow
    implementation("com.github.denzcoskun:ImageSlideshow:0.1.2")
    //Flow LayoutManager
//    implementation("com.beloo.widget:ChipsLayoutManager:0.3.7@aar")
    implementation("com.github.Dboy233:ChipsLayoutManagerX:1.0.0")

//    val room_version = "2.6.1"
    //Room
//    implementation("androidx.room:room-runtime:$room_version")
//    ksp("androidx.room:room-compiler:$room_version")
//    implementation("androidx.room:room-ktx:$room_version")
    implementation("org.litepal.guolindev:core:3.2.3")

    //QR Image Generator
    implementation("com.github.alexzhirkevich:custom-qr-generator:1.6.2")

    implementation("io.github.scwang90:refresh-layout-kernel:2.1.0")      //核心必须依赖]
    implementation("io.github.scwang90:refresh-header-material:2.1.0")
    implementation("io.github.scwang90:refresh-footer-classics:2.1.0")

    // Moshi
    implementation("com.squareup.moshi:moshi:1.15.0")
    implementation("com.squareup.moshi:moshi-kotlin:1.15.0")

    implementation("kh.org.nbc.bakong_khqr:sdk-java:*******")

    //WebSocket
    implementation("com.tinder.scarlet:scarlet:0.1.12")
    implementation("com.tinder.scarlet:websocket-okhttp:0.1.12")
    implementation("com.tinder.scarlet:lifecycle-android:0.1.12")
    implementation("com.tinder.scarlet:message-adapter-gson:0.1.12")
    implementation("com.tinder.scarlet:stream-adapter-coroutines:0.1.12")
    implementation("com.tinder.scarlet:stream-adapter-rxjava2:0.1.12")

    // rx
    implementation("io.reactivex.rxjava2:rxjava:2.2.21")
    implementation("io.reactivex.rxjava2:rxandroid:2.1.1")
    implementation("io.reactivex.rxjava2:rxkotlin:2.4.0")

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
    implementation(files("libs/printer-lib-3.2.0.aar"))
    implementation(files("libs/mtcashboxsdk-release.aar"))
//    implementation(files("libs/rongta_pos_lib_2.2.9.jar"))

    implementation("com.google.android.flexbox:flexbox:3.0.0")

    implementation("com.github.VictorAlbertos:RxActivityResult:0.5.0-2.x")

//    implementation("androidx.recyclerview:recyclerview:1.2.1")
//    implementation("implementation 'com.github.li-xiaojun:XPopup:2.1.0")

    implementation("com.facebook.stetho:stetho:1.6.0")
    implementation("com.facebook.stetho:stetho-okhttp3:1.6.0")

    implementation("cn.bingoogolapple:bga-badgeview-api:1.1.8")
    kapt("cn.bingoogolapple:bga-badgeview-compiler:1.1.8")

    implementation("org.greenrobot:eventbus:3.1.1")

//    implementation("com.github.MZCretin:ExpandableTextView:v1.6.1-x")
    implementation("com.github.maning0303:MNPasswordEditText:V1.0.4")

//    implementation("androidx.appcompat:appcompat:1.3.1")
//    implementation("com.google.android.material:material:1.4.0")
    implementation("androidx.recyclerview:recyclerview:1.2.1")
    implementation("com.github.li-xiaojun:XPopup:2.10.0")


//    implementation("com.google.android.libraries.places:places:3.5.0")
    implementation("io.reactivex.rxjava3:rxjava:3.1.5")

    implementation(files("libs/crashreport-4.1.9.3.aar"))
    implementation("com.github.lygttpod:SuperTextView:2.4.6")
    implementation("com.github.ChinaLike:TagTextView:0.2.7")

    implementation("net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3")

    implementation("com.jakewharton.threetenabp:threetenabp:1.4.2")

    implementation("io.coil-kt.coil3:coil:3.0.4")
    implementation("io.coil-kt.coil3:coil-network-okhttp:3.0.4")

    implementation(files("libs/AclasDriverSdk.jar"))

//    implementation("com.amazonaws:aws-android-sdk-s3:2.27.15")  // AWS S3 SDK
//    implementation("com.amazonaws:aws-android-sdk-core:2.27.15") // AWS 核心库
//    implementation("com.google.maps:google-maps-services:2.2.0")
}