<?xml version="1.0" encoding="utf-8"?>
<litepal>
    <!--
    	Define the database name of your application.
    	By default each database name should be end with .db.
    	If you didn't name your database end with .db,
    	LitePal would plus the suffix automatically for you.
    	For example:
    	<dbname value="demo" />
    -->
    <dbname value="default" />

    <!--
    	Define the version of your database. Each time you want
    	to upgrade your database, the version tag would helps.
    	Modify the models you defined in the mapping tag, and just
    	make the version value plus one, the upgrade of database
    	will be processed automatically without concern.
			For example:
    	<version value="1" />
    -->
    <version value="11" />

    <!--
    	Define your models in the list with mapping tag, LitePal will
    	create tables for each mapping class. The supported fields
    	defined in models will be mapped into columns.
    	For example:
    	<list>
    		<mapping class="com.test.model.Reader" />
    		<mapping class="com.test.model.Magazine" />
    	</list>
    -->
    <list>
        <mapping class="com.metathought.food_order.casheir.database.ShoppingRecord" />
        <mapping class="com.metathought.food_order.casheir.database.GoodsRecord" />
        <mapping class="com.metathought.food_order.casheir.database.HashRecord" />
        <mapping class="com.metathought.food_order.casheir.database.GoodsListRecord" />
        <mapping class="com.metathought.food_order.casheir.database.WsEventRecord" />
        <mapping
            class="com.metathought.food_order.casheir.database.TakeOutPlatformToDiningRecord" />
    </list>

    <!--
        Define where the .db file should be. "internal" means the .db file
        will be stored in the database folder of internal storage which no
        one can access. "external" means the .db file will be stored in the
        path to the directory on the primary external storage device where
        the application can place persistent files it owns which everyone
        can access. "internal" will act as default.
        For example:
        <storage value="external" />
    -->

</litepal>