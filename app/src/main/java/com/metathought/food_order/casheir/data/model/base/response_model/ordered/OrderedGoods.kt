package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.PricingMethodEnum
import com.metathought.food_order.casheir.constant.ServiceChargeCalculationTypeEnum
import com.metathought.food_order.casheir.constant.SingleDiscountType
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.response_model.order.ActivityLabel
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.SingleItemDiscount
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.toHexStr
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber
import java.math.BigDecimal
import java.util.Locale
import java.util.Objects

/**
 * 订单菜品model
 *
 * @property orderId
 * @property acceptOrderCount
 * @property acceptOrderId
 * @property acceptOrderTime
 * @property cartsId
 * @property feeds
 * @property finalSinglePrice
 * @property finalSingleVatPrice
 * @property finalVipPrice
 * @property finalVipVatPrice
 * @property finalDiscountPrice
 * @property finalDiscountVatPrice
 * @property finalSingleServiceCharge
 * @property finalDiscountServiceCharge
 * @property finalVipServiceCharge
 * @property finalReduceSingleServiceCharge
 * @property id
 * @property labels
 * @property name
 * @property nameEn
 * @property nameKm
 * @property num
 * @property number
 * @property picUrl
 * @property sellPrice
 * @property vatWhitelisting
 * @property serviceChargeWhitelisting
 * @property tagItems
 * @property tagItemsEn
 * @property tagItemsKm
 * @property vipPrice
 * @property refundsNum
 * @property alreadyRefundNum
 * @property restrictNum
 * @property hashKey
 * @property packingFee
 * @property pricingMethod
 * @property weight
 * @property kitchenMaking
 * @property seriesNo
 * @property storeKitchenId
 * @property discountPrice
 * @property finalReduceSinglePrice
 * @property finalReduceSingleVatPrice
 * @property finalReduceSinglePackingFee
 * @property activityLabels
 * @property printLabel
 * @property goodsType
 * @property singleItemDiscount
 * @property finalSingleGoodsReducePrice
 * @property finalSingleGoodsReduceServiceCharge
 * @property finalSingleGoodsReduceVatPrice
 * @property finalSingleGoodsReduceVipPrice
 * @property finalSingleGoodsReduceVipServiceCharge
 * @property finalSingleGoodsReduceVipVatPrice
 * @property totalSingleGoodsReducePrice
 * @property totalSingleGoodsReduceVipPrice
 * @property singleItemDiscountList
 * @property discountItemWhitelisting
 * @property orderMealSetGoodsDTOList
 * @property goodsPriceKey
 * @property goodHashKey
 * @property serviceChargePercentage
 * @property warehouseId
 * @property materialId
 * @property commissionPercentage
 * @property goodsPackingFee
 * @property note
 * @property spuPriceKey
 * @property discountRemarkIdKey
 * @property packingFeeKey
 * @constructor Create empty Ordered goods
 */
data class OrderedGoods(
    /**
     * 所属订单id
     */
    @SerializedName("orderId")
    val orderId: String?,

    /**
     * 第几次加购的  2.12.0 之前的版本是null
     */
    @SerializedName("acceptOrderCount")
    var acceptOrderCount: Int? = null,

    /**
     * 第几次加购订单id  2.12.0 之前的版本是null
     */
    @SerializedName("acceptOrderId")
    var acceptOrderId: String? = null,

    /**
     * 第几次加购时间  2.12.0 之前的版本是null
     */
    @SerializedName("acceptOrderTime")
    var acceptOrderTime: String? = null,

    @SerializedName("cartsId")
    val cartsId: String?,
    @SerializedName("feeds")
    val feeds: List<Feed>?,
    @SerializedName("finalSinglePrice")
    var finalSinglePrice: Long?,
    @SerializedName("finalSingleVatPrice")
    var finalSingleVatPrice: Long?,
    @SerializedName("finalVipPrice")
    var finalVipPrice: Long?,
    @SerializedName("finalVipVatPrice")
    var finalVipVatPrice: Long?,
    @SerializedName("finalDiscountPrice")
    var finalDiscountPrice: Long?,
    @SerializedName("finalDiscountVatPrice")
    var finalDiscountVatPrice: Long?,

    //堂食服务费
    @SerializedName("finalSingleServiceCharge")
    val finalSingleServiceCharge: Long?,
    //折扣价堂食服务费
    @SerializedName("finalDiscountServiceCharge")
    val finalDiscountServiceCharge: Long?,
    //vip堂食服务费
    @SerializedName("finalVipServiceCharge")
    val finalVipServiceCharge: Long?,
    //最终折扣减免价堂食服务费
    @SerializedName("finalReduceSingleServiceCharge")
    val finalReduceSingleServiceCharge: Long?,

    //总最终外卖单品佣金(单品佣金*数量)
    @SerializedName("totalSingleGoodsCommissionPrice")
    val totalSingleGoodsCommissionPrice: Long?,

    @SerializedName("id")
    val id: String?,
    @SerializedName("labels")
    val labels: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("nameEn")
    var nameEn: String?,
    @SerializedName("nameKm")
    var nameKm: String?,
    @SerializedName("num")
    var num: Int?,
    /**
     * 后台自己设置的编号
     */
    @SerializedName("number")
    var number: String?,
    @SerializedName("picUrl")
    val picUrl: String?,
    @SerializedName("sellPrice")
    val sellPrice: Long?,
    @SerializedName("vatWhitelisting")
    val vatWhitelisting: Boolean?,
    @SerializedName("serviceChargeWhitelisting")
    val serviceChargeWhitelisting: Boolean?,
    @SerializedName("tagItems")
    var tagItems: List<GoodsTagItem>?,
    @SerializedName("tagItemsEn")
    var tagItemsEn: List<GoodsTagItem>?,
    @SerializedName("tagItemsKm")
    var tagItemsKm: List<GoodsTagItem>?,
    @SerializedName("vipPrice")
    val vipPrice: Long?,
    @Transient
    var refundsNum: Int?,
    @Transient
    var alreadyRefundNum: Int?,

    @SerializedName("restrictNum")
    val restrictNum: Int? = null,

    @SerializedName("hashKey")
    val hashKey: String?,

    @SerializedName("packingFee")
    val packingFee: Int?,

    /**
     * 计价方式
     * WHOLE_UNIT(0, "整份计费", ""),
     * PER_KILOGRAM(1, "每公斤", "KG"),
     * PER_POUND(2, "每磅", "LB"),
     * PER_LITER(3, "每升", "L"),
     * PER_OUNCE(4, "每盎司", "OZ"),
     * PER_GALLON(5, "每加仑", "GAL"),
     * PER_GRAM(6, "每克", "G");
     */
    @SerializedName("pricingMethod")
    val pricingMethod: Int?,
    /**
     * 重量
     */
    @SerializedName("weight")
    var weight: Double?,
    //TODO 2.16.10 新增字段
    //商品唯一标识(称重商品使用)
    @SerializedName("uuid")
    val uuid: String?,

    /**
     * 商品是否处理完成，对于商品有确定价格的为true
     */
    @SerializedName("isProcessed")
    var isProcessed: Boolean?,

    /**
     * 是否称重完成true是false否
     */
    @SerializedName("weighingCompleted")
    var weighingCompleted: Boolean?,

    /**
     * 是否定价完成
     */
    @SerializedName("pricingCompleted")
    val pricingCompleted: Boolean?,

    /**
     * 是否需要厨房制作
     */
    @SerializedName("kitchenMaking")
    val kitchenMaking: Boolean?,

    /**
     * 后台厨打设置独立打印 编码
     */
    @SerializedName("seriesNo")
    var seriesNo: String?,

    /**
     * 厨房独立打印时，套餐商品合并打印 true是合并 false是分开
     */
    @SerializedName("kitchenMergePrint")
    var kitchenMergePrint: Boolean? = true,

    //多端厨房功能  用于区分菜品用哪个打印机打印
    @SerializedName("storeKitchenId")
    val storeKitchenId: String?,

    //单个商品折扣价 ，如果有这个价格就把原价展示为划线价
    @SerializedName("discountPrice")
    val discountPrice: Long?,

    //===========退款=======
    //减免折扣后的单价
    @SerializedName("finalReduceSinglePrice")
    val finalReduceSinglePrice: Long?,

    //减免折扣后的vat
    @SerializedName("finalReduceSingleVatPrice")
    val finalReduceSingleVatPrice: Long?,

    //减免折扣后的打包费
    @SerializedName("finalReduceSinglePackingFee")
    val finalReduceSinglePackingFee: Long?,

    /**
     * 商品参与优惠活动标签
     */
    @SerializedName("activityLabels")
    var activityLabels: List<ActivityLabel>? = null,

    /**
     * 是否打印标签贴纸
     */
    @SerializedName("printLabel")
    var printLabel: Boolean? = null,

    /**
     * 商品类型:0-普通商品， 1-临时商品"
     */
    @SerializedName("goodsType")
    var goodsType: Int? = null,

    @SerializedName("singleItemDiscount")
    var singleItemDiscount: SingleItemDiscount? = null,

    @SerializedName("finalSingleGoodsReducePrice")
    var finalSingleGoodsReducePrice: Long? = null,
    @SerializedName("finalSingleGoodsReduceServiceCharge")
    var finalSingleGoodsReduceServiceCharge: Long? = null,
    @SerializedName("finalSingleGoodsReduceVatPrice")
    var finalSingleGoodsReduceVatPrice: Long? = null,
    @SerializedName("finalSingleGoodsReduceVipPrice")
    var finalSingleGoodsReduceVipPrice: Long? = null,
    @SerializedName("finalSingleGoodsReduceVipServiceCharge")
    var finalSingleGoodsReduceVipServiceCharge: Long? = null,
    @SerializedName("finalSingleGoodsReduceVipVatPrice")
    var finalSingleGoodsReduceVipVatPrice: Long? = null,


    /**
     *总最终单品价(包含单品减免*数量)
     */
    @SerializedName("totalSingleGoodsReducePrice")
    var totalSingleGoodsReducePrice: Long? = null,
    /**
     *总vip-最终单品价(包含单品减免*数量)
     */
    @SerializedName("totalSingleGoodsReduceVipPrice")
    var totalSingleGoodsReduceVipPrice: Long? = null,

    @SerializedName("totalSingleGoodsReduceServiceCharge")
    var totalSingleGoodsReduceServiceCharge: Long? = null,

    @SerializedName("totalSingleGoodsReduceVipServiceCharge")
    var totalSingleGoodsReduceVipServiceCharge: Long? = null,


    /**
     *  打印合并菜品用，计算同一个菜品可能设置不同折扣
     */
    @Transient
    var singleItemDiscountList: List<SingleItemDiscount>? = null,

    /**
     * 是否不参与折扣商品白名单，true：不参与，false：参与，仅限于整单减免和优惠券减免
     */
    @SerializedName("discountItemWhitelisting")
    var discountItemWhitelisting: Boolean? = null,

    /**
     * 套餐内容
     */
    @SerializedName("orderMealSetGoodsDTOList")
    var orderMealSetGoodsDTOList: List<OrderMealSetGood>? = null,

    /**
     * goodsPriceKey
     */
    @SerializedName("goodsPriceKey")
    var goodsPriceKey: String? = null,

    /**
     * goodHashKey  翻译用
     */
    @SerializedName("goodHashKey")
    var goodHashKey: String? = null,

    @Transient
    var serviceChargePercentage: Int? = null,

    @SerializedName("warehouseId")
    var warehouseId: String? = null,

    @SerializedName("materialId")
    var materialId: String? = null,

    /**
     * 佣金比例
     */
    @SerializedName("commissionPercentage")
    val commissionPercentage: BigDecimal? = null,


    @SerializedName("goodsPackingFee")
    val goodsPackingFee: BigDecimal? = null,

    /**
     * 菜品备注
     */
    @SerializedName("note")
    val note: String? = null,

    @SerializedName("spuPriceKey")
    val spuPriceKey: String? = null,

    @SerializedName("discountRemarkIdKey")
    val discountRemarkIdKey: String? = null,

    @SerializedName("packingFeeKey")
    val packingFeeKey: String? = null,

    /**
     * 是否时价菜
     */
    @SerializedName("currentPrice")
    val currentPrice: Boolean? = null,

    /**
     * 打包费显示，0:独立显示，1:计入商品价格
     */
    @SerializedName("packingFeeDisplay")
    val packingFeeDisplay: Boolean? = null,

    @SerializedName("serviceChargeMode")
    var serviceChargeMode: Int? = null,

    /**
     * 退菜原因
     */
    @SerializedName("removeReason")
    var removeReason: String? = null,


    ) : BaseOrderGoods() {


    /**
     *套餐是否 合并厨打
     */
    fun isKitchenMergePrint(): Boolean {
        return kitchenMergePrint ?: true
    }

    /**
     * 是否套餐
     */
    fun isMealSet(): Boolean {
        return !orderMealSetGoodsDTOList.isNullOrEmpty()
    }

    fun getFinalNote(): String {
        return (note?.replace(";<br/>", "\n") ?: "").trimStart()
    }

    fun getFinalRemoveReason(): String {
        return (removeReason?.replace(";<br/>", "\n") ?: "").trimStart()
    }

    fun getNameByLocal(locale: Locale): String? {
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (nameKm.isNullOrEmpty()) {
                name
            } else {
                nameKm
            }
        } else if (locale == Locale.CHINESE) {
            name
        } else if (locale == Locale.ENGLISH) {
            if (nameEn.isNullOrEmpty()) {
                name
            } else {
                nameEn
            }
        } else {
            name
        }
    }

    fun getPackingFeeKeyStr(): String {
        //难道就是 packingFeeKey
        if ((packingFee ?: 0) == 0) {
            return ""
        }
        return "${packingFee}${isPackingFeeDisplay()}"
//        return "${packingFee}${goodsPackingFee}"
    }


    override fun hashCode(): Int {
        if (hashKey.isNullOrEmpty()) {
            return Objects.hash(id)
        }
        return Objects.hash(hashKey)
    }

    override fun equals(other: Any?): Boolean {
        if (other === this) return true
        if (other !is OrderedGoods) return false
        if (other.hashKey.isNullOrEmpty()) {
            return other.id == this.id
        }
        return other.hashKey == this.hashKey
    }

    fun getCanRefundNum(): Int {
        return (num ?: 0) - (alreadyRefundNum ?: 0)
    }

    fun clone(): OrderedGoods {
        val stringJson = MyApplication.globalGson.toJson(this, OrderedGoods::class.java)
        return MyApplication.globalGson.fromJson<OrderedGoods>(
            stringJson,
            OrderedGoods::class.java
        )
    }

    fun getHash(): String {
        if (hashKey != null) {
            return hashKey
        }
        //下面几乎没用到了
        val sbf = StringBuffer()
        sbf.append(id)
        if (!feeds.isNullOrEmpty()) {
            feeds.sortedBy { it.id }.forEach {
                sbf.append("${it.id}${it.alreadyNum}")
            }
        }
        if (!tagItems.isNullOrEmpty()) {
            tagItems?.sortedBy { it.id }?.forEach {
                sbf.append(it.id)
            }
        }
//        Log.e("OrderedJson:", MyApplication.globalGson.toJson(this))
//        Log.e("Hash:", sbf.toString())
        return sbf.toString().toHexStr()
    }

    fun getTagItemId(): String? {
        val sbf = StringBuffer()
        tagItems?.sortedBy { it?.id }?.forEachIndexed { index, goodsTagItem ->
            sbf.append(goodsTagItem.id)
            if (index != (tagItems?.size ?: 0) - 1) {
                sbf.append(",")
            }
        }
        if (sbf.isEmpty()) {
            return null
        }
        return sbf.toString()
    }

    fun getFeedBo(): List<FeedBo> {
        val feedBoList = ArrayList<FeedBo>()
        feeds?.forEach {
            feedBoList.add(
                FeedBo(
                    id = it.id,
                    num = it.alreadyNum
                )
            )
        }
        return feedBoList
    }

    fun getGoodsTagStr(): String {
        val sbf = StringBuffer()
        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            sbf.append(OrderHelper.getSelectMealSetGoodStr(orderMealSetGoodsDTOList))
        } else {
            tagItems?.let {
                if (it.isNotEmpty()) {
                    for (i in it.indices) {
                        sbf.append(it[i].name)
                        if (i != it.size - 1)
                            sbf.append(", ")
                    }
                }
            }

            feeds?.let {
                if (it.isNotEmpty()) {
                    if (tagItems?.isNotEmpty() == true) {
                        sbf.append(", ")
                    }
                    for (i in it.indices) {
                        sbf.append(it[i].name + " x" + it[i].alreadyNum)
                        if (i != it.size - 1)
                            sbf.append(", ")
                    }
                }
            }
        }
        return sbf.toString()
    }

    fun getGoodsTagStrNotMealSet(): String {
        val sbf = StringBuffer()
        tagItems?.let {
            if (it.isNotEmpty()) {
                for (i in it.indices) {
                    sbf.append(it[i].name)
                    if (i != it.size - 1)
                        sbf.append(", ")
                }
            }
        }

        feeds?.let {
            if (it.isNotEmpty()) {
                if (tagItems?.isNotEmpty() == true) {
                    sbf.append(", ")
                }
                for (i in it.indices) {
                    sbf.append(it[i].name + " x" + it[i].alreadyNum)
                    if (i != it.size - 1)
                        sbf.append(", ")
                }
            }
        }
        return sbf.toString()
    }


    fun getLocaleFeedTagStr(locale: Locale): String {
        val sbf = StringBuffer()
        feeds?.let {
            if (it.isNotEmpty()) {
//                if (tagItems?.isNotEmpty() == true) {
//                    sbf.append(", ")
//                }
                it.forEachIndexed { index, feed ->
                    val name = if (locale == MyApplication.LOCALE_KHMER) {
                        if (feed.nameKh.isNullOrEmpty()) {
                            feed.name
                        } else {
                            feed.nameKh
                        }
                    } else if (locale == Locale.CHINESE) {
                        feed.name
                    } else if (locale == Locale.ENGLISH) {
                        if (feed.nameEn.isNullOrEmpty()) {
                            feed.name
                        } else {
                            feed.nameEn
                        }
                    } else {
                        feed.name
                    }
                    sbf.append(name + " x" + it[index].alreadyNum)
                    if (index != it.size - 1)
                        sbf.append(", ")
                }
            }
        }
        return sbf.toString()
    }


    fun getLocaleGoodsTagStrByTicket(locale: Locale): String {
        val sbf = StringBuffer()
        val list = if (locale == MyApplication.LOCALE_KHMER) {
            if (tagItemsKm.isNullOrEmpty()) {
                tagItems
            } else {
                tagItemsKm
            }
        } else if (locale == Locale.CHINESE) {
            tagItems
        } else if (locale == Locale.ENGLISH) {
            if (tagItemsEn.isNullOrEmpty()) {
                tagItems
            } else {
                tagItemsEn
            }
        } else {
            tagItems
        }
        list?.let {
            if (it.isNotEmpty()) {
                for (i in it.indices) {
                    sbf.append(it[i].name)
                    if (i != it.size - 1)
                        sbf.append(", ")
                }
            }
        }
        return sbf.toString()
    }

    //根据语言获取单一GoodsTag 的name
    fun getLocaleSingleGoodsTagStr(locale: Locale, goodsTagItem: GoodsTagItem): String {
        val list = if (locale == MyApplication.LOCALE_KHMER) {
            if (tagItemsKm.isNullOrEmpty()) {
                tagItems
            } else {
                tagItemsKm
            }
        } else if (locale == Locale.CHINESE) {
            tagItems
        } else if (locale == Locale.ENGLISH) {
            if (tagItemsEn.isNullOrEmpty()) {
                tagItems
            } else {
                tagItemsEn
            }
        } else {
            tagItems
        }
        return list?.firstOrNull {
            it.id == goodsTagItem.id
        }?.name ?: ""
    }

    /**
     * 获取当前的单价
     *
     * @return
     */
    fun getTicketSinglePrice(): Long {
        if (singleItemDiscount != null && singleItemDiscount?.type == SingleDiscountType.MODIFY_PRICE.id) {
            if (singleItemDiscount?.adjustSalePrice != null) {
                return ((singleItemDiscount?.adjustSalePrice ?: 0.0) * 100.0).toLong()
            }
        }
        return getCalculateDiscountPrice() ?: 0L
//        if (isHasDiscountPrice()) {
//            return (discountPrice ?: 0)
//        }
//        return (sellPrice ?: 0)
    }

    fun getCurrentPriceWithOutSingleDiscount(): Long {
        return getCalculateDiscountPrice() ?: 0L
//        if (isHasDiscountPrice()) {
//            return (discountPrice ?: 0)
//        }
//        return (sellPrice ?: 0)
    }

    /**
     * 获取现价 如果有折扣价是折扣价， 没有就是原价
     *
     * @return
     */
    fun getCurrentFinalPrice(): Long {
        var price = finalSinglePrice ?: 0
        if (isHasDiscountPrice()) {
            price = finalDiscountPrice ?: 0
        }
        return price
    }

    /**
     * Get current final price
     *
     * @return
     */
    fun getCurrentFinalLabelPrintPrice(): Long {
        if (singleItemDiscount != null && singleItemDiscount?.type == SingleDiscountType.MODIFY_PRICE.id) {
            if (singleItemDiscount?.adjustSalePrice != null) {
                return ((singleItemDiscount?.adjustSalePrice ?: 0.0) * 100.0).toLong()
            }
        }
        var price = finalSinglePrice ?: 0
        if (isHasDiscountPrice()) {
            price = finalDiscountPrice ?: 0
        }
        return price
    }


    /**
     * 计算单品折扣优惠了多少钱
     *
     */
    fun calculateSingleDiscountPrice(): Long {
        val price = totalPrice()
        //单品优惠金额
        if (singleItemDiscount != null) {
            if (singleItemDiscount?.type == 3) {
                return 0L
            }
        }
        if (totalSingleGoodsReducePrice != null) {
            return price - (totalSingleGoodsReducePrice ?: 0)
        }
        return 0L
    }


    private fun getCurrentSinglePriceVat(): Long {
        var price = (finalSingleVatPrice ?: 0)
        if (isHasDiscountPrice()) {
            price = (finalDiscountVatPrice ?: 0)
        }
        if (finalSingleGoodsReduceVatPrice != null && finalSingleGoodsReduceVatPrice != finalDiscountVatPrice) {
            price = finalSingleGoodsReduceVatPrice!!
        }
        return price
    }


    private fun getCurrentSinglePriceService(): Long {
        var price = (finalSingleServiceCharge ?: 0)
        if (isHasDiscountPrice()) {
            price = (finalDiscountServiceCharge ?: 0)
        }
        if (finalSingleGoodsReduceServiceCharge != null && finalSingleGoodsReduceServiceCharge != finalDiscountServiceCharge) {
            price = finalSingleGoodsReduceServiceCharge!!
        }
        return price
    }

    //finalSinglePrice is price already calculate with num count
    //原销售价
    fun totalSalePrice(): Long {
        return (calculateSinglePrice() ?: 0) * (num ?: 0)
    }

    /**
     * 计算单个销售价(包含小料，规格)
     *
     * @return
     */
    private fun calculateSinglePrice(
    ): Long {
        val targetPrice = OrderHelper.calculateTagPrice(
            ArrayList(feeds ?: listOf()),
            ArrayList(tagItems ?: listOf())
        )
        val mealSetAddPrice = getMealSetAddPrice()

        var basePrice = getCalculateSellPrice() ?: 0
        if (isToBeWeighed() && !isMealSet()) {
            //重量计算 舍去小数位
            basePrice = basePrice.times((weight ?: 1.0)).toLong()
        }
        val price = basePrice + targetPrice + mealSetAddPrice
        return price
    }


    /**
     * 获取套餐加价
     */
    private fun getMealSetAddPrice(): Long {
        //加价金额
        var addPrice = 0L
        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            orderMealSetGoodsDTOList?.forEach { goods ->
                var priceMarkup = goods.priceMarkup ?: 0
                if (goods.isToBeWeighed() && goods.isHasCompleteWeight()) {
                    priceMarkup = (priceMarkup * (goods.weight ?: 0.0)).toLong()
                }
                var num = goods.num ?: 0
                var tagPrice = 0.0
                goods.mealSetTagItemList?.forEach { tag ->
                    if ((tag.price ?: 0.0) > 0.0) {
                        tagPrice += (tag.price ?: 0.0)
                    }
                }
                val price = (priceMarkup + tagPrice.times(num)).times(goods.number ?: 0)
                addPrice += (price.toLong())
                Timber.e("mealSetGood:${goods.mealSetGoodsName} 单个子商品的加价  price:${price}")
            }
        }
        return addPrice
    }


    //现总价，如果有折扣价 返回折扣价，没有返回销售价
    fun totalPrice(): Long {
        return getCurrentFinalPrice() * (num ?: 0)
    }

    /**
     * 单品减免折扣后的总价
     * @return
     */
    fun totalPriceWithSingleDiscount(): Long {
        var price = totalPrice()

        //已这个为主
        if (singleItemDiscount != null) {
            if (singleItemDiscount?.type == 1) {
                //百分比减免
                if (singleItemDiscount?.reduceRatio != null) {
                    price =
                        price - BigDecimal((price * singleItemDiscount!!.reduceRatio!!) / 100.0).halfUp(
                            0
                        ).toLong()
                }
            } else if (singleItemDiscount?.type == 2) {
                //固定金额减免
                price -= (singleItemDiscount?.saleReduce
                    ?: BigDecimal.ZERO).multiply(BigDecimal(100)).toLong()
            }
        }

        if (totalSingleGoodsReducePrice != null) {
            price = totalSingleGoodsReducePrice ?: 0L
        }

        return price
    }

    fun totalVatPrice(): Long {
        return getCurrentSinglePriceVat() * (num ?: 0)
    }

    fun totalServiceFee(): Long {
        var price = (finalDiscountServiceCharge ?: 0L) * (num ?: 0)
        //服务端是否受单品折扣 和 优惠活动影响
        val serviceEffectByDiscount = (serviceChargeMode
            ?: MainDashboardFragment.STORE_INFO?.serviceChargeMode) == ServiceChargeCalculationTypeEnum.AFTER_DISCOUNT.id

        if (serviceEffectByDiscount && totalSingleGoodsReduceServiceCharge != null) {
            price = totalSingleGoodsReduceServiceCharge ?: 0L
        }
        return price
    }

    fun totalVipServiceFee(): Long {
        var price = (finalVipServiceCharge ?: 0) * (num ?: 0)
        if (singleItemDiscount != null) {
//            price =
//                BigDecimal(
//                    totalVipPriceWithSingleDiscount() * (serviceChargePercentage ?: 0)
//                ).divide(
//                    BigDecimal(100)
//                ).halfUp(0).toLong()
            price = totalSingleGoodsReduceVipServiceCharge ?: 0L
        }

        return price
    }


    /**
     * 退菜总金额
     *
     * @return
     */
    fun totalCancelGoodsPrice(): Long {
        if (finalSingleGoodsReducePrice != null) {
            return (finalSingleGoodsReducePrice ?: 0) * (refundsNum ?: 0)
        }
        return getCurrentFinalPrice() * (refundsNum ?: 0)
    }


    fun totalRefundPrice(): Long {
        if (finalSingleGoodsReducePrice != null) {
            return (finalSingleGoodsReducePrice ?: 0) * (refundsNum ?: 0)
        }
        return getCurrentFinalPrice() * (refundsNum ?: 0)
    }

    fun totalRefundVatPrice(): Long {
        if (finalSingleGoodsReduceVatPrice != null) {
            return (finalSingleGoodsReduceVatPrice ?: 0) * (refundsNum ?: 0)
        }
        return getCurrentSinglePriceVat() * (refundsNum ?: 0)
    }

    fun totalRefundServiceFee(): Long {
        if (finalSingleGoodsReduceServiceCharge != null) {
            return (finalSingleGoodsReduceServiceCharge ?: 0) * (refundsNum ?: 0)
        }
        return getCurrentSinglePriceService() * (refundsNum ?: 0)
    }

    fun totalRefundVipPrice(): Long {
        if (finalSingleGoodsReduceVipPrice != null) {
            return (finalSingleGoodsReduceVipPrice ?: 0) * (refundsNum ?: 0)
        }
        return (finalVipPrice ?: 0L) * (refundsNum ?: 0)
    }

    fun totalRefundVipVatPrice(): Long {
        if (finalSingleGoodsReduceVipVatPrice != null) {
            return (finalSingleGoodsReduceVipVatPrice ?: 0) * (refundsNum ?: 0)
        }
        return (finalVipVatPrice ?: 0L) * (refundsNum ?: 0)
    }

    fun totalRefundVipServiceFee(): Long {
        if (finalSingleGoodsReduceVipServiceCharge != null) {
            return (finalSingleGoodsReduceVipServiceCharge ?: 0) * (refundsNum ?: 0)
        }
        return (finalVipServiceCharge ?: 0L) * (refundsNum ?: 0)
    }

    fun totalRefundReducePrice(hasRefund: Boolean? = false): Long {
        if (hasRefund == true) {
            //订单列表已退用这个
            return (finalReduceSinglePrice ?: 0) * (num ?: 0)
        }
        /**
         * 退款弹窗用这个
         */
        return (finalReduceSinglePrice ?: 0) * (refundsNum ?: 0)
    }

    fun totalRefundReduceVatPrice(): Long {
        return (finalReduceSingleVatPrice ?: 0) * (refundsNum ?: 0)
    }

    fun totalRefundReducePackingFee(): Long {
        return (finalReduceSinglePackingFee ?: 0) * (refundsNum ?: 0)
    }

    fun totalRefundReduceServiceFee(): Long {
        return (finalReduceSingleServiceCharge ?: 0) * (refundsNum ?: 0)
    }

    fun totalCancelPackingFee(): Int {
        return (packingFee ?: 0) * (refundsNum ?: 0)
    }

//
//
//    //全部退款
//    fun totalFullRefundPrice(): Long {
//        return getCurrentFinalPrice() * getCanRefundNum()
//    }
//
//    fun totalFullRefundVatPrice(): Long {
//        return getCurrentPriceVat() * getCanRefundNum()
//    }
//
//    fun totalFullRefundVipPrice(): Long {
//        return (finalVipPrice ?: 0L) * getCanRefundNum()
//    }
//
//    fun totalFullRefundVipVatPrice(): Long {
//        return (finalVipVatPrice ?: 0) * getCanRefundNum()
//    }


    fun totalPriceAfterFirstRefund(): Long {
        return getCurrentFinalPrice() * getCanRefundNum()
    }

    fun totalVipPriceAfterFirstRefund(): Long {
        return (finalVipPrice ?: 0L) * getCanRefundNum()
    }

    fun totalReducePriceAfterFirstRefund(): Long {
        return (finalReduceSinglePrice ?: 0L) * getCanRefundNum()
    }

//    fun totalReduceVatPriceAfterFirstRefund(): Long {
//        return (finalReduceSingleVatPrice ?: 0L) * getCanRefundNum()
//    }
//
//    fun totalReducePackingFeeAfterFirstRefund(): Long {
//        return (finalReduceSinglePackingFee ?: 0L) * getCanRefundNum()
//    }
//
//
//    fun totalFullRefundPackingFee(): Int {
//        return (packingFee ?: 0) * getCanRefundNum()
//    }


    //回去最终可退款金额
    fun totalFinalPriceAfterFirstRefund(
        isUseDiscount: Boolean? = null,
        isReduce: Boolean? = false
    ): Long {
        if (isReduce == false) {
            //退菜走这里
            if (isUseDiscount == true) {
                if (totalSingleGoodsReduceVipPrice != null) {
                    return totalSingleGoodsReduceVipPrice!!
                }
            }

            if (totalSingleGoodsReducePrice != null) {
                return totalSingleGoodsReducePrice!!
            }
        }

        if (finalReduceSinglePrice != null) {
            return totalReducePriceAfterFirstRefund()
        }
        if (isUseDiscount == true) {
            return totalVipPriceAfterFirstRefund()
        }
        return totalPriceAfterFirstRefund()
    }


    fun totalVipPrice(): Long {
        return (finalVipPrice ?: 0) * (num ?: 0)
    }


    fun totalVipPriceWithSingleDiscount(): Long {
        var price = totalVipPrice()

        if (singleItemDiscount != null) {
            if (singleItemDiscount?.type == 1) {
                //百分比减免
                if (singleItemDiscount?.reduceRatio != null) {
                    price =
                        price - BigDecimal((price * singleItemDiscount!!.reduceRatio!!) / 100.0).halfUp(
                            0
                        ).toLong()
//                    price = price - (price * singleItemDiscount!!.reduceRatio!!) / 100.0
                }
            } else if (singleItemDiscount?.type == 2) {
                //固定金额减免
                price -= (singleItemDiscount?.vipReduce
                    ?: BigDecimal.ZERO).multiply(BigDecimal(100)).toLong()
            }
        }

        if (totalSingleGoodsReduceVipPrice != null) {
            price = totalSingleGoodsReduceVipPrice ?: 0L
        }
        return price
    }


    /**
     * 计算单品折扣vip优惠了多少钱
     *
     */
    fun calculateSingleDiscountVipPrice(): Long {
        val price = totalVipPrice()
//        //单品优惠金额
        if (singleItemDiscount != null) {
            if (singleItemDiscount?.type == 3) {
                return 0L
            }
        }
        if (totalSingleGoodsReduceVipPrice != null) {
            return price - (totalSingleGoodsReduceVipPrice ?: 0)
        }
        return 0L
    }

    /**
     * 打包费是否计入 true 是计入
     */
    fun isPackingFeeDisplay(): Boolean {
        Timber.e("packingFeeDisplay: ${packingFeeDisplay}")
        if (packingFeeDisplay == null) {
            return false
        }
        return packingFeeDisplay == true
    }

    /**
     * 获取参与计算的SellPrice
     */
    fun getCalculateSellPrice(): Long? {
        if (isPackingFeeDisplay()) {
            return (sellPrice ?: 0L).plus(packingFee ?: 0)
        }
        return sellPrice
    }

    /**
     * 获取参与计算的VipPrice
     */
    fun getCalculateVipPrice(): Long? {
        if (vipPrice == null || isShowVipPrice() == false) {
            return null
        }
        if (isPackingFeeDisplay()) {
            return (vipPrice ?: 0L).plus(packingFee ?: 0)
        }
        return vipPrice
    }

    /**
     * 获取参与计算的discountPrice
     */
    fun getCalculateDiscountPrice(): Long? {
        if (!isHasDiscountPrice()) {
            return getCalculateSellPrice()
        }
        Timber.e("折扣价")
        if (isPackingFeeDisplay()) {
            return (discountPrice ?: 0L).plus(packingFee ?: 0)
        }
        return discountPrice
    }

    /**
     * 获取参与计算的打包费
     */
    fun getCalculatePackingFee(): Int {
        if (isPackingFeeDisplay()) {
            return 0
        }
        return packingFee ?: 0
    }

    fun getTicketSingleVipPrice(): Long {
        if (singleItemDiscount?.type == SingleDiscountType.MODIFY_PRICE.id && singleItemDiscount?.adjustVipPrice != null) {
            return ((singleItemDiscount?.adjustVipPrice ?: 0.0) * 100.0).toLong()
        }
        return getCalculateVipPrice() ?: 0L

//        if (isPackingFeeDisplay()) {
//            return (vipPrice ?: 0L).plus(packingFee ?: 0)
//        }
//        return vipPrice ?: 0L
    }

//    fun getVipPrice(): Long {
//        if (singleItemDiscount?.type == SingleDiscountType.MODIFY_PRICE.id && singleItemDiscount?.adjustVipPrice != null) {
//            return ((singleItemDiscount?.adjustVipPrice ?: 0.0) * 100.0).toLong()
//        }
//        return vipPrice ?: 0L
//    }


    fun totalPackingFee(): Int {
        return (packingFee ?: 0) * (num ?: 0)
    }

    fun isShowVipPrice(): Boolean {
        if (vipPrice == null) {
            return false
        }
        return (vipPrice ?: 0) >= 0 && (vipPrice ?: 0) != (sellPrice
            ?: 0L)   //&& withVipPrice == true
    }


    /**
     * 计算佣金
     *
     */
    fun getTotalCommissionPrice(): Long {
        return BigDecimal.valueOf(totalSingleGoodsCommissionPrice ?: 0).toLong()
    }

    //是否有折扣价
    fun isHasDiscountPrice(): Boolean {
        if (discountPrice == null) {
            return false
        }
        //时价菜有discountPrice。所以这里要判断不等于sellprice
        return discountPrice != sellPrice
    }


    //时价菜 是否已经完成了设置价格
    fun isHasCompletePricing(): Boolean {
        return pricingCompleted == true
    }

    //是否时价菜
    fun isTimePriceGood(): Boolean {
        if (currentPrice == null) {
            return false
        }
        return currentPrice == true
    }

    /**
     *是否完成了称重
     *
     * @return
     */
    fun isHasCompleteWeight(): Boolean {
        if (isProcessed == true) {
            return true
        }

        Timber.e("weighingCompleted==>  ${weighingCompleted}")
        return weighingCompleted == true
    }

    /**
     * 套餐是否完成称重
     */
    fun isMealSetHasCompleteWeight(): Boolean {
        val notWeight =
            orderMealSetGoodsDTOList?.firstOrNull { it.isToBeWeighed() && !it.isHasCompleteWeight() }
        return notWeight == null
    }

    //是否称重商品
    fun isToBeWeighed(): Boolean {
        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            //子商品有一个需要称重套餐就是需要称重的
            val isToBeWeighed = orderMealSetGoodsDTOList?.firstOrNull {
                it.isToBeWeighed()
            }
            return isToBeWeighed != null
        }
        return (pricingMethod ?: 0) > 0  //&& !isTimePriceGood()
    }

    /**
     * 是否已经定价  有确定的价格
     *
     * @return
     */
    fun isHasProcessed(): Boolean {
        Timber.e("isProcessed $isProcessed")
        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            //套餐中是否有需要称重但未称重的字商品
            val notWeight = orderMealSetGoodsDTOList?.firstOrNull {
                it.isToBeWeighed() && !it.isHasCompleteWeight()
            }
            return notWeight == null
        }
        return isProcessed == true
    }

    /**
     * 待定价商品定价完的描述
     *
     * @return
     */
    fun getWeightStr(): String {
        if (weight?.isInt() == true) {
            return "${weight?.toInt()}${getWeightUnit()}"
        }
        return "${weight ?: 0}${getWeightUnit()}"
    }

    fun getWeightUnit(): String {
        return PricingMethodEnum.entries.find {
            it.id == (pricingMethod ?: 0)
        }?.unit ?: ""
    }


    fun setServiceChargePercentage(serviceChargePercentage: Int) {
        this.serviceChargePercentage = serviceChargePercentage
    }

    /**
     * 不参与整单减免 优惠券 优惠活动
     *
     * @return
     */
    fun isDiscountItemWhitelisting(): Boolean {
        return discountItemWhitelisting ?: false
    }

    /**
     * 是否有设置过单品减免折扣
     *
     * @return
     */
    fun isSetSingleItemDiscount(): Boolean {
        if (singleItemDiscount == null) {
            return false
        } else {
            if (singleItemDiscount?.type == null) {
                return false
            } else if (singleItemDiscount?.type == 1) {
                return singleItemDiscount?.reduceRatio != null
            } else if (singleItemDiscount?.type == 2) {
                return singleItemDiscount?.saleReduce != null || singleItemDiscount?.vipReduce != null
            } else if (singleItemDiscount?.type == 3) {
                return singleItemDiscount?.adjustVipPrice != null || singleItemDiscount?.adjustSalePrice != null
            }
            return false
        }
    }

    //是否做了单品改价
    fun isSetModifyPrice(): Boolean {
        return singleItemDiscount?.type == 3
    }

    /**
     * 支付后 单品减免折扣是否有效
     *
     * @return
     */
    fun isSetSingleItemDiscountEffect(isVip: Boolean): Boolean {
        if (singleItemDiscount == null) {
            return false
        } else {
            if (singleItemDiscount?.type == null) {
                return false
            } else if (singleItemDiscount?.type == 1) {
                return singleItemDiscount?.reduceRatio != null
            } else if (singleItemDiscount?.type == 2) {
                if (isVip) {
                    return singleItemDiscount?.vipReduce != null
                } else {
                    return singleItemDiscount?.saleReduce != null
                }
                //return singleItemDiscount?.saleReduce != null || singleItemDiscount?.vipReduce != null
            } else if (singleItemDiscount?.type == 3) {
                if (isVip) {
                    return singleItemDiscount?.adjustVipPrice != null
                } else {
                    return singleItemDiscount?.adjustSalePrice != null
                }
//                return singleItemDiscount?.adjustVipPrice != null || singleItemDiscount?.adjustSalePrice != null
            }
            return false
        }
    }

    /**
     * 是否改过价格成功
     *
     * @return
     */
    fun isModifyPriceSuccess(): Boolean {
        return singleItemDiscount?.type == 3 && (singleItemDiscount?.adjustVipPrice != null || singleItemDiscount?.adjustSalePrice != null)
    }


    /**
     * 打印是否要显示小料规格金额
     *
     * @return
     */
    fun isPrintShowTagItemPrice(orderInfo: OrderedInfoResponse?): Boolean {
        //没设置减免 或者 设置的减免不是改价 要显示小料规格的金额
        if (singleItemDiscount == null || singleItemDiscount?.type != 3) {
            return true
        }
        //如果是支付成功
        if (orderInfo?.isOrderSuccess() == true) {
            //如果是已支付
            if (orderInfo.isPayByBalance()) {
                if (isShowVipPrice()) {
                    return singleItemDiscount?.adjustVipPrice == null
                } else {
                    return singleItemDiscount?.adjustSalePrice == null
                }
            } else {
                return singleItemDiscount?.adjustSalePrice == null
            }
        } else {
            return singleItemDiscount?.adjustSalePrice == null
        }
    }

    /**
     * 订单的菜品model 转成购物车菜品model
     *
     * @return
     */
    fun orderedGoodsConvertToGoods(): Goods {
        return Goods(
            id = id ?: "",
            sellPrice = sellPrice,
            name = name,
//            cartsId = cartsId,
            vipPrice = vipPrice,
            finalVipPrice = finalVipPrice,
            finalVipVatPrice = finalVipVatPrice,
            packingFee = packingFee,
            totalCount = num,
            pricingMethod = pricingMethod,
            feedStr = getGoodsTagStr(),
            weight = weight,
            uuid = uuid,
            restrictNum = restrictNum,
            vatWhitelisting = vatWhitelisting,
            serviceChargeWhitelisting = serviceChargeWhitelisting,
            discountPrice = discountPrice,
            totalServiceCharge = totalServiceFee(),
            totalVipServiceCharge = totalVipServiceFee(),
            totalDiscountServiceCharge = totalServiceFee(),
            activityLabels = activityLabels,
            goodsType = goodsType,
            discountItemWhitelisting = discountItemWhitelisting,
            orderMealSetGoodsDTOList = orderMealSetGoodsDTOList,
            type = if (orderMealSetGoodsDTOList.isNullOrEmpty()) 0 else 1,
            singleItemDiscount = singleItemDiscount.apply {

            },
            serviceChargePercentage = serviceChargePercentage,
            commissionPercentage = commissionPercentage,
            totalCommission = getTotalCommissionPrice(),
            note = note,
            isProcessed = isHasProcessed(),
            pricingCompleted = isHasCompletePricing(),
            weighingCompleted = isHasCompleteWeight(),
            packingFeeDisplay = packingFeeDisplay,
            currentPrice = currentPrice,
            serviceChargeMode = serviceChargeMode
        )
    }

}