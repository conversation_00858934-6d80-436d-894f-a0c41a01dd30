package com.metathought.food_order.casheir.database.dao

import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Group
import com.metathought.food_order.casheir.data.model.base.response_model.order.PromotionActivity
import com.metathought.food_order.casheir.database.GoodsRecord
//import kotlinx.coroutines.CoroutineScope
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.launch
//import org.litepal.LitePal
//import org.litepal.extension.findFirst
import java.util.concurrent.ConcurrentHashMap
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/3/1910:00
 * @description
 */
object GoodsHelper {

    /**
     * 菜品map
     */
    private var goodsMap = mutableMapOf<Int, MutableMap<String, BaseGoods>>()


    private var goodsListMap = mutableMapOf<Int, ArrayList<BaseGoods>>()
    private var groupListMap = mutableMapOf<Int, List<Group>>()
    private var ongoingGiftPromotionList = mutableMapOf<Int, ArrayList<PromotionActivity>>()

    // 添加内存存储Map (key: Pair(goodsId, localDiningStyle))
    private val goodsRecords = ConcurrentHashMap<Pair<String, Int>, GoodsRecord>()


    fun getOngoingGiftPromotionList(diningStyle: Int): ArrayList<PromotionActivity>? {
        if (ongoingGiftPromotionList.contains(diningStyle)) {
            return ongoingGiftPromotionList[diningStyle]
        }
        return null
    }

    fun setOngoingGiftPromotionList(diningStyle: Int, goodsList: ArrayList<PromotionActivity>) {
        this.ongoingGiftPromotionList[diningStyle] = goodsList
    }


    fun getGoodsMap(diningStyle: Int, goodsId: String): BaseGoods? {
        if (goodsMap.contains(diningStyle)) {
            if (goodsMap[diningStyle]!!.contains(goodsId)) {
                return goodsMap[diningStyle]!![goodsId]
            }
        }
        return null
    }

    fun setGoodsList(diningStyle: Int, goodsList: ArrayList<BaseGoods>) {
        this.goodsListMap[diningStyle] = goodsList
        if (!goodsMap.contains(diningStyle)) {
            goodsMap[diningStyle] = mutableMapOf()
        }
        goodsList.forEach {
            if (it.header == false) {
                goodsMap[diningStyle]!![(it as Goods).id] = it
            }
        }
    }

    fun getGoodsList(diningStyle: Int): ArrayList<BaseGoods> {
        return goodsListMap[diningStyle] ?: ArrayList()
    }

    fun setGroupsList(diningStyle: Int, groupList: List<Group>) {
        this.groupListMap[diningStyle] = groupList
    }

    fun getGroupsList(diningStyle: Int): List<Group> {
        return groupListMap[diningStyle] ?: listOf()
    }

    fun clean() {
        ongoingGiftPromotionList.clear()
        goodsListMap.clear()
        groupListMap.clear()
        goodsMap.clear()
        goodsRecords.clear() // 清空内存数据
    }

    fun clearGoodsRecords() {
        goodsRecords.clear()
    }


    fun get(id: String, localDiningStyle: Int): GoodsRecord? {
//        return LitePal.where(
//            "goodsId = ? and localDiningStyle = ?",
//            id,
//            localDiningStyle.toString()
//        ).findFirst<GoodsRecord>()
        return goodsRecords[Pair(id, localDiningStyle)]
    }

    fun del(localDiningStyle: Int) {
//        LitePal.deleteAll(
//            GoodsRecord::class.java,
//            "localDiningStyle = ?",
//            localDiningStyle.toString()
//        )
        goodsRecords.keys.removeAll { it.second == localDiningStyle }
    }


    /**
     * 更新商品数量 - 异步版本
     */
    fun update(goods: Goods, localStyle: Int, num: Int) {
        Timber.e("更新商品数量")
        // 使用IO调度器在后台线程执行数据库操作
//        CoroutineScope(Dispatchers.IO).launch {
        updateSync(goods, localStyle, num)
//        }
    }

    /**
     * 同步更新商品数量 - 内部使用
     */
    private fun updateSync(goods: Goods, localStyle: Int, num: Int) {
//        val model = get(goods.id, localStyle)
//        if (model == null) {
//            GoodsRecord(
//                storeId = goods.storeId ?: "",
//                diningStyle = goods.diningStyle ?: 0,
//                num = num,
//                goodsId = goods.id,
//                localDiningStyle = localStyle,
//            ).save()
//        } else {
//            if (num > 0) {
//                model.num = num
//                model.save()
//            } else {
//                model.delete()
//            }
//        }
        val key = Pair(goods.id, localStyle)
        if (num > 0) {
            val model = goodsRecords[key] ?: GoodsRecord(
                storeId = goods.storeId ?: "",
                diningStyle = goods.diningStyle ?: 0,
                num = 0,
                goodsId = goods.id,
                localDiningStyle = localStyle,
            )
            model.num = num
            goodsRecords[key] = model
        } else {
            goodsRecords.remove(key)
        }
    }

    /**
     * 批量更新商品数量 - 提高性能
     */
    fun batchUpdate(goodsUpdates: List<Triple<Goods, Int, Int>>) {
//        CoroutineScope(Dispatchers.IO).launch {
        goodsUpdates.forEach { (goods, localStyle, num) ->
            updateSync(goods, localStyle, num)
        }
//        }
    }

}