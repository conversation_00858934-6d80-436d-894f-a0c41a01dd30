package com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood

data class BatchAddCart(
    @SerializedName("feedInfoList")
    val feedInfoList: List<FeedBo?>?,
    @SerializedName("goodsId")
    val goodsId: String?,
    @SerializedName("num")
    val num: Int?,
    @SerializedName("tagItemIds")
    val tagItemIds: String?,
    @SerializedName("goodsType")
    val goodsType: Int?,

    /**
     * 套餐内容
     */
    var cartMealSetGoodsDTOList: List<MealSetGood>? = null,

    var note: String?,
    /**
     * pricingCompleted sellPrice vipPrice  时价菜才传
     */
    var pricingCompleted: Boolean? = null,
    var sellPrice: Long? = null,
    var vipPrice: Long? = null,
)