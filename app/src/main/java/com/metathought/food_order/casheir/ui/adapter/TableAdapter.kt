package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.os.Build
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.TableStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Customer
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.TableItemBinding
import com.metathought.food_order.casheir.extension.formatDateWithoutSecond
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible


class TableAdapter(
    val list: TableResponse,
    val context: Context,
    val onClickCallback: (TableResponseItem) -> Unit
) : RecyclerView.Adapter<TableAdapter.TableItemViewHolder>() {
    var viewHight: Int = 0

    inner class TableItemViewHolder(val binding: TableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: TableResponseItem?) {
            resource?.let { _ ->
                updateUIBaseOnStatus(resource, binding)
                binding.cardViewMain.setOnClickListener {
                    onClickCallback.invoke(resource)
                }

//                binding.tvTableID.text = resource.name
//                binding.tvTableID.requestLayout()
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableItemViewHolder {
        var itemView = TableItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
        viewHight = (parent.height / 3.8f).toInt()
        itemView.root.layoutParams.height = viewHight
        return TableItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: TableItemViewHolder, position: Int) {
//        holder.itemView.minimumHeight = viewHight
        holder.bind(list[position])
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun updateItems(newItems: List<TableResponseItem>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateUIBaseOnStatus(resource: TableResponseItem, binding: TableItemBinding) {
        binding.apply {
            tvChairAvailable.text =
                "0/${resource.maxPeopleCount}"

            tvPrice.isVisible = (resource.totalPrice ?: 0) > 0
            tvPrice.text = "${resource.totalPrice?.priceFormatTwoDigitZero2()}"
            tvTableID.text = resource.name

//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                tvTableID.setAutoSizeTextTypeUniformWithConfiguration(
//                    12, // autoSizeMinTextSize
//                    18, // autoSizeMaxTextSize
//                    1,  // autoSizeStepGranularity
//                    TypedValue.COMPLEX_UNIT_SP
//                )
//            }

            if (resource.waitWeighMark == true) {
                tvPrice.text = context.getString(R.string.to_be_confirmed)
            }

            when (resource.status) {
                TableStatusEnum.AVAILABLE.id -> {
                    cardViewMain.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            R.color.primaryColor
                        )
                    )
                    //type 2 is CommonTable cannot reserve
                    if (resource.type == 2) {
                        tvStatus.text = context.getString(R.string.common_table)
                    } else {
                        tvStatus.text = context.getString(R.string.available)
                    }

                    tvPrice.isVisible = false
                    tvInfo.setVisibleInvisible(false)
                    tvTime.setVisibleInvisible(false)
                    tvCustomerName.isVisible = false
                }

                TableStatusEnum.RESERVED.id -> {
                    val customer =
                        Gson().fromJson<Customer>(resource.customerJson, Customer::class.java)
                    cardViewMain.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            R.color.main_yellow
                        )
                    )
                    customer?.let {
                        tvCustomerName.text = customer.name
                        tvCustomerName.isVisible = true
                        if (customer.getMobilePhone().length > 4 ) {
                            val telephone = customer.getMobilePhone().subSequence(
                                customer.getMobilePhone().length - 4,
                                customer.getMobilePhone().length
                            )

                            tvInfo.text = "(***${telephone})"
                        } else {
                            tvInfo.text = customer.name
                        }
                        tvTime.text = customer.diningTime?.formatDateWithoutSecond()
                        tvChairAvailable.text =
                            "${customer.diningNumber}/${resource.maxPeopleCount}"
                    }

                    tvStatus.text = context.getString(R.string.reserved)
                    tvPrice.setVisibleInvisible(true)
                    tvInfo.setVisibleInvisible(true)
                    tvTime.setVisibleInvisible(true)
                }

                TableStatusEnum.OCCUPIED.id -> {
                    val customer =
                        Gson().fromJson<Customer>(resource.customerJson, Customer::class.java)
                    customer?.let {
                        tvChairAvailable.text =
                            "${customer.diningNumber}/${resource.maxPeopleCount}"
                    }
                    cardViewMain.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            R.color.main_red
                        )
                    )
                    val minutes = (resource.duration?.div(1000) ?: 0) / 60
//                    tvInfo.text = context.getString(R.string.dining_time_in_mins, resource.duration)
                    if (resource.duration != null) {
                        tvInfo.text = convertMinutesToDHM(resource.duration)
                    } else {
                        tvInfo.text = ""
                    }
                    tvStatus.text = context.getString(R.string.dining)
                    tvPrice.setVisibleInvisible(true)
                    tvInfo.setVisibleInvisible(true)
                    tvTime.setVisibleGone(false)
                    tvCustomerName.isVisible = false
                }
            }
            if (resource.hasPrintPreSettlement == true) {
                cardViewMain.setCardBackgroundColor(
                    ContextCompat.getColor(
                        context,
                        R.color.color_FF7700
                    )
                )
//                tvTableID.setTextColor(context.getColor(R.color.main_red))
//                tvPrice.setTextColor(context.getColor(R.color.main_red))
//                tvTime.setTextColor(context.getColor(R.color.main_red))
//                tvCustomerName.setTextColor(context.getColor(R.color.main_red))
//                tvInfo.setTextColor(context.getColor(R.color.main_red))
//                tvChairAvailable.setTextColor(context.getColor(R.color.main_red))
////                tvChairAvailable.setTextViewDrawableColor(R.color.main_red)
//                val startDrawable = ContextCompat.getDrawable(
//                    context,
//                    R.drawable.ic_chair_available_red
//                )
//                tvChairAvailable.setCompoundDrawablesWithIntrinsicBounds(
//                    startDrawable,
//                    null,
//                    null,
//                    null
//                )
//                tvStatus.setTextColor(context.getColor(R.color.main_red))
                tvStatus.text = context.getString(R.string.table_pre_order_status)
            } else {
                tvTableID.setTextColor(context.getColor(R.color.white))
                tvPrice.setTextColor(context.getColor(R.color.white))
                tvTime.setTextColor(context.getColor(R.color.white))
                tvCustomerName.setTextColor(context.getColor(R.color.white))
                tvInfo.setTextColor(context.getColor(R.color.white))
                tvChairAvailable.setTextColor(context.getColor(R.color.white))
//                tvChairAvailable.setTextViewDrawableColor(R.color.white)
                val startDrawable = ContextCompat.getDrawable(
                    context,
                    R.drawable.ic_chair_available
                )
                tvChairAvailable.setCompoundDrawablesWithIntrinsicBounds(
                    startDrawable,
                    null,
                    null,
                    null
                )
                tvStatus.setTextColor(context.getColor(R.color.white))
            }
        }
    }

    fun convertMinutesToDHM(minutes: Long): String {
        val days = minutes / (24 * 60)
        val remainingMinutes = minutes % (24 * 60)
        val hours = remainingMinutes / 60
        val finalMinutes = remainingMinutes % 60
        var str = ""


        if (days != 0L) {
            str = "${context.getString(R.string.dining_time_in_day, days)}"
        }
        if (hours != 0L) {
            str = "$str ${context.getString(R.string.dining_time_in_hour, hours)}"
        }
        if (finalMinutes != 0L) {
            str = "$str ${context.getString(R.string.dining_time_in_mins, finalMinutes)}"
        }
        return str
    }
}