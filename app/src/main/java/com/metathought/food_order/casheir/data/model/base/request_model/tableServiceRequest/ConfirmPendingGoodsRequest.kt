package com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest

import com.google.gson.annotations.SerializedName

/**
 * 修改称重/时价菜定价
 *
 */
data class ConfirmPendingGoodsRequest(
    @SerializedName("cartsId")
    val cartsId: String? = null,
    @SerializedName("orderNo")
    val orderNo: String,
    /**
     * 重量，称重商品必填
     */
    @SerializedName("weight")
    val weight: String? = null,
    /**
     * 会员价
     */
    @SerializedName("vipPrice")
    val vipPrice: String? = null,
    /**
     * 销售价
     */
    @SerializedName("sellPrice")
    val sellPrice: String? = null,
    /**
     * 商品hash值
     */
    @SerializedName("hashKey")
    var hashKey: String?,

    /**
     *  "1定价2称重"
     */
    @SerializedName("type")
    var type: Int?,

    /**
     * 套餐内称重商品标识：套餐子商品的hashKey
     */
    @SerializedName("mealSetWeighingGoodsKey")
    var mealSetWeighingGoodsKey: String ?
)
