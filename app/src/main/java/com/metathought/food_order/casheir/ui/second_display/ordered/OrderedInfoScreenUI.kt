package com.metathought.food_order.casheir.ui.second_display.ordered

import android.app.Presentation
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.Display
import android.view.Gravity
import android.view.WindowManager
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.SecondOrderedInfoLayoutBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getDiningStyleString
import com.metathought.food_order.casheir.extension.getSourcePlatform
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.ui.adapter.SecondOrderedInfoAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.second_display.SecondaryManager
import com.metathought.food_order.casheir.ui.second_display.ordered.refunds.OrderedCancelDishDialog
import com.metathought.food_order.casheir.ui.second_display.ordered.refunds.OrderedRefundDialog
import com.metathought.food_order.casheir.ui.widget.DividerItemDecoration

/**
 * 订单详情副屏
 * Order details second-screen
 * <AUTHOR>
 * @date 2024/5/1316:26
 * @description
 */
class OrderedInfoScreenUI(private val mContext: Context, display: Display) :
    Presentation(mContext, display) {

    private lateinit var binding: SecondOrderedInfoLayoutBinding
    private var orderedInfoAdapter: SecondOrderedInfoAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = SecondOrderedInfoLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)


        orderedInfoAdapter = SecondOrderedInfoAdapter(arrayListOf(), context = mContext)
        binding.run {
            orderedInfoRecyclerView.run {
                val dividerItemDecoration =
                    DividerItemDecoration(mContext, DividerItemDecoration.VERTICAL).apply {
                        setDrawable(mContext.getDrawable(R.drawable.divider_vertical_ddd))
                    }
                addItemDecoration(dividerItemDecoration)
                adapter = orderedInfoAdapter
            }

            Glide.with(mContext).load(MainDashboardFragment.CURRENT_USER?.url).circleCrop()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgLogo)
            tvStoreName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
        }
        initResource()
    }

    private fun initResource() {
        binding.run {
            items.text = mContext.getString(R.string.items)
            quantity.text = mContext.getString(R.string.quantity)
            amount.text = mContext.getString(R.string.amount)
            tvCustomerTitle.text = mContext.getString(R.string.customer_info)
            customerName.text = mContext.getString(R.string.customer_name)
            phoneNumber.text = mContext.getString(R.string.phone_number)
            orderInfo.text = mContext.getString(R.string.order_info)
            orderId.text = mContext.getString(R.string.order_id)
            orderedTime.text = mContext.getString(R.string.ordered_time)
            orderType.text = mContext.getString(R.string.order_type)
            people.text = mContext.getString(R.string.people)
            orderBy.text = mContext.getString(R.string.order_by)
            paymentMethod.text = mContext.getString(R.string.payment_method)
            diningTime.text = mContext.getString(R.string.ordered_dining_time)
            cancelTime.text = mContext.getString(R.string.cancel_time)
            paymentTime.text = mContext.getString(R.string.payment_time)
            refundTime.text = mContext.getString(R.string.refund_time)
            remark.text = mContext.getString(R.string.remark)
            packingPrice.text = mContext.getString(R.string.packing_price)
            subtotal.text = mContext.getString(R.string.subtotal)
            vat.text = mContext.getString(R.string.vat)
            total2.text = mContext.getString(R.string.total_price)
            partialRefundAmount.text = mContext.getString(R.string.partial_refund_amount)
            vatRefund.text = mContext.getString(R.string.vat_refund)
            totalPrice.text = mContext.getString(R.string.receivable)
            actualReceivedAmount.text = mContext.getString(R.string.actual_received_amount)
            refundAmount.text = mContext.getString(R.string.refund_amount)
        }
    }

    //退款
    private var refundDialog: OrderedRefundDialog? = null
    fun getRefundDialog(): OrderedRefundDialog? {
        if (refundDialog == null) {
            refundDialog = OrderedRefundDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //解决多语言失效问题
                initResource(mContext)
            }
        }
        return refundDialog
    }

    fun dismissDialog() {
        refundDialog?.dismiss()
        refundDialog = null
    }


    //退菜
    private var cancelDishDialog: OrderedCancelDishDialog? = null
    fun getCancelDishDialog(): OrderedCancelDishDialog? {
        if (cancelDishDialog == null) {
            cancelDishDialog = OrderedCancelDishDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //解决多语言失效问题
                initResource(mContext)
            }
        }
        return cancelDishDialog
    }

    fun dismissCancelDishDialog() {
        cancelDishDialog?.dismiss()
        cancelDishDialog = null
    }


    fun updateOrderedInfoRecyclerView(position: Int) {
        binding.run {
            orderedInfoRecyclerView.smoothScrollToPosition(position)
        }
    }


    fun setOrderInfo(ordered: OrderedInfoResponse?) {
        if (!isShowing && (!SecondaryManager.isPaymentQrScreen()) && !SecondaryManager.isDestroy) {
            show()
        }
        mContext.run {
            binding.run {
                ordered?.let {
                    orderedInfoAdapter?.replaceData(
                        OrderHelper.sortingGoodsWithMergeOrder(
                            mContext,
                            it.mergeOrderIds,
                            it.acceptOrderIds,
                            it.goods
                        ),
                        it.refundGoodsJson,
                        orderedInfoResponse = it
                    )
                    tvTableID.text = it.tableName

                    llCustomerName.isVisible = !it.getLastCustomerName().isNullOrEmpty()
                    tvCustomerName.text = it.getLastCustomerName()

                    llCustomerPhone.isVisible = it.getShowPhone().isNotEmpty()
                    tvCustomerPhone.text = it.getShowPhone()

                    llCustomerTitle.isVisible =
                        llCustomerName.isVisible || llCustomerPhone.isVisible

                    tvOrderNo.text = it.orderNo
                    tvOrderTime.text = it.createTime?.formatDate()
                    llDiningTime.isVisible = !it.getDingTime().isNullOrEmpty()
                    tvDiningTime.text = it.getDingTime().formatDate()
                    tvDiningStyle.text = it.diningStyle?.getDiningStyleString(mContext)
                    mContext.let { mContext ->
                        tvOrderType.text = it.getDiningStyleStr(mContext)
                        tvOrderBy.text = it.sourcePlatform?.getSourcePlatform(mContext)
                    }
                    llPeople.isVisible =
                        !it.customerInfoVo?.diningNumber?.toString()
                            .isNullOrEmpty() && it.customerInfoVo?.diningNumber != 0
                    tvCustomerNum.text = it.customerInfoVo?.diningNumber?.toString() ?: ""
                    tvOrderRemark.text =
                        if (it.note.isNullOrEmpty()) mContext.getString(R.string.none) else it.getFinalNote()
//                    tvOrderRemark.post {
//                        tvOrderRemark.gravity =
//                            if (tvOrderRemark.lineCount > 1) Gravity.START else Gravity.END
//                    }


                    val subTotalPrice = it.getSubTotal()
                    val vatPrice = it.getRealVatPrice()
                    val totalPrice = it.getRealPayPrice()
                    val totalVipPrice = it.totalVipPrice ?: 0L


                    tvPackingAmount.text = "${it.totalPackingFee?.priceFormatTwoDigitZero2()}"
                    llPackPrice.isVisible = (it.totalPackingFee ?: 0) > 0


                    //小计
                    tvSubtotal.text = subTotalPrice.priceFormatTwoDigitZero2()
                    //增值税
                    tvVat.text = vatPrice.priceFormatTwoDigitZero2()
                    llVat.isVisible = vatPrice > 0
                    //总计2
                    tvTotalPrice2.text = totalPrice.priceFormatTwoDigitZero2()

                    //总计1
                    tvTotalPrice.text = totalPrice.priceFormatTwoDigitZero2()


                    //会员价
                    tvVipPrice.text = "${totalVipPrice.priceFormatTwoDigitZero2()}"
                    tvVipPrice.isVisible = totalVipPrice > 0 && totalVipPrice != totalPrice


                    //normal
//                llPaymentLayout.isGone = true
//                btnOrderRefund.isGone = true
                    llActualReceiveAmount.isGone = true
                    llVatRefundAmount.isGone = true
                    llPartialRefundAmount.isGone = true
                    llTotalPrice2.isGone = true
                    llRefundTime.isGone = true
                    llPaymentTime.isGone = it.payTime.isNullOrEmpty()
                    tvPaymentTime.text = it.payTime?.formatDate()
                    llPaymentMethod.isGone = true
                    llTotalPrice.isVisible = true
                    llRefundAmount.isGone = true
                    llCancelTime.isGone = true
                    llPartialRefundPackFee.isVisible = false
//                imgPrint.isVisible =
//                    (it.payStatus == OrderedStatusEnum.PAID.id || it.payStatus == OrderedStatusEnum.PREORDER.id) // ticket bug-view-2101 || it.payStatus == OrderedStatusEnum.BE_CONFIRM.id)
                    tvPaymentMethod.text = it.getPaymentMethod(context)
                    when (it.payStatus) {
                        OrderedStatusEnum.UNPAID.id -> {
                            //un paid
//                            llPaymentLayout.isVisible = true
//                            btnOrderRefund.isVisible = false
//                            btnOrderMore.isVisible = false
//                            btnCancelDish.isVisible = false
//                            btnCancelOrder.isVisible = true
                        }

                        OrderedStatusEnum.PAID.id -> {
                            //paid
//                            llPaymentLayout.isVisible = false
//                            btnOrderMore.isVisible = false
//                            btnCancelOrder.isVisible = false
//                            btnCancelDish.isVisible = false
//                            btnOrderRefund.isVisible = true
                            llPaymentMethod.isVisible = true
                            tvVipPrice.isVisible = false
                        }

                        OrderedStatusEnum.PARTIAL_REFUND.id -> {
                            //partial refund
                            llTotalPrice.isGone = true
                            llTotalPrice2.isVisible = true
//                            btnOrderRefund.isVisible = true
//                            btnOrderMore.isVisible = false
//                            btnCancelDish.isVisible = false
//                            btnCancelOrder.isVisible = false
                            llPaymentMethod.isVisible = true
                            tvVipPrice.isVisible = false


                            llRefundTime.isVisible = true
                            tvRefundTime.text = it.refundDateTime?.formatDate()


                            llActualReceiveAmount.isVisible = true
                            tvActualReceiveAmount.text =
                                (totalPrice - (it.refundPrice ?: 0)).priceFormatTwoDigitZero2()

                            val vatRefundAmount = it.getVatRefundAmount()
                            llVatRefundAmount.isVisible = vatRefundAmount > 0
                            tvVatRefundAmount.text =
                                "-${vatRefundAmount.priceFormatTwoDigitZero2()}"

                            val packRefundAmount = it.getPackRefundAmount()
                            llPartialRefundPackFee.isVisible = packRefundAmount > 0
                            tvPartialRefundPackFee.text =
                                "-${packRefundAmount.priceFormatTwoDigitZero2()}"

                            llPartialRefundAmount.isVisible = true
                            tvPartialRefundAmount.text =
                                "-${it.getPartialRefundAmount().priceFormatTwoDigitZero2()}"

                        }

                        OrderedStatusEnum.FULL_REFUND.id -> {
//                            btnOrderMore.isVisible = false
//                            btnCancelDish.isVisible = false
//                            btnCancelOrder.isVisible = false
//                            btnOrderRefund.isVisible = false
//                            llPaymentLayout.isVisible = false
                            llTotalPrice2.isVisible = true
                            llTotalPrice.isGone = true
                            llPaymentMethod.isVisible = true
                            llRefundTime.isVisible = true
                            tvRefundTime.text = it.refundDateTime?.formatDate()
                            //refund
                            llRefundAmount.isVisible = true
                            tvRefundAmount.text = "-${it.refundPrice?.priceFormatTwoDigitZero2()}"
                            tvVipPrice.isVisible = false
                        }

                        OrderedStatusEnum.CANCEL_ORDER.id -> {
                            //canceled
//                            btnOrderMore.isVisible = false
//                            btnCancelDish.isVisible = false
//                            btnCancelOrder.isVisible = false
//                            btnOrderRefund.isVisible = false
//                            llPaymentLayout.isVisible = false
                            llCancelTime.isVisible = !it.cancelTime.isNullOrEmpty()
                            tvCancelTime.text = it.cancelTime?.formatDate()
                            tvVipPrice.isVisible = false
                        }

                        OrderedStatusEnum.BE_CONFIRM.id -> {
                            //confirm
//                            llPaymentLayout.isVisible = true
//                            btnOrderRefund.isVisible = false
//                            btnOrderMore.isVisible = true
//                            btnCancelDish.isVisible = true
//                            btnCancelOrder.isVisible = true
                        }

                        OrderedStatusEnum.PREORDER.id -> {
//                            btnOrderMore.isVisible = false
//                            btnCancelDish.isVisible = false
//                            btnCancelOrder.isVisible = false
//                            llPaymentLayout.isVisible = false
//                            btnOrderRefund.isVisible = true
                            llPaymentMethod.isVisible = true
                        }

                        OrderedStatusEnum.TO_BE_CONFIRM.id -> {
                            //待确认没支付功能
//                            llPaymentLayout.isVisible = false
//                            btnOrderRefund.isVisible = false
//                            btnOrderMore.isVisible = true
//                            btnCancelDish.isVisible = true
//                            btnCancelOrder.isVisible = true
                        }
                    }
                }
            }
        }
    }
}