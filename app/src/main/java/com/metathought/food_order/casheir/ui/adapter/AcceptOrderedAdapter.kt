package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.AcceptOrderedItemBinding
import com.metathought.food_order.casheir.databinding.OrderedItemBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.formatShortDate
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TimeUtils
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class AcceptOrderedAdapter(
    val act: Context,
    var list: ArrayList<OrderedRecord>,
    val onItemClickListener: (OrderedRecord) -> Unit
) : RecyclerView.Adapter<AcceptOrderedAdapter.OrderedViewHolder>() {

    private var selectedId: String? = null

    inner class OrderedViewHolder(val binding: AcceptOrderedItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        if (it != -1) {
                            if (selectedId != null) {
                                val index = list.indexOfFirst { it.id == selectedId }
                                if (index != -1) {
                                    list[index].select = false
                                    notifyItemChanged(index)
                                }
                            }

                            selectedId = list[it].id
                            list[it].select = true
                            notifyItemChanged(it)

                            onItemClickListener.invoke(list[it])
                        }
                    }

                }

            }
        }

        fun bind(resource: OrderedRecord, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        if(it.isGrab == true){
                            tvTableID.setVisibleGone(false)
                            ivCrab.setVisibleGone(true)
                        }else{
                            tvTableID.setVisibleGone(true)
                            ivCrab.setVisibleGone(false)
                        }
                        tvOrderedID.text = it.getDiningStyleStr(act)
                        tvOrderedID.setCompoundDrawablesWithIntrinsicBounds(
                            ContextCompat.getDrawable(
                                itemView.context,
                                it.getDiningStyleIcon()
                            ), null, null, null
                        )

                        ivNew.isVisible = !(it.isRead ?: false)

                        clContent.setBackgroundColor(
                            ContextCompat.getColor(
                                this@run,
                                if (resource.select) R.color.color_e7f5ee else android.R.color.transparent
                            )
                        )
                        tvTableID.text = it.tableName ?: ""

                        if (it.sourcePlatform == SourcePlatformEnum.Kiosk.id) {
                            tvTableID.text = getString(R.string.kiosk).uppercase()
                        }

//                        val oderIdValue = "${getString(R.string.order_id)}: ${it.orderNo}"

                        val itemsValue =
                            "${getString(R.string.items)}: ${it.goodsTotalNum}"
                        tvItems.text = itemsValue

                        val timeValue =
                            "${it.createTime?.formatShortDate()}"
                        tvTime.text = timeValue

                        tvPrice.text = it.getShowPrice(itemView.context)

                        updateTime(tvCountDownTime, resource)
                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedViewHolder {
        val itemView =
            AcceptOrderedItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: OrderedViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    override fun onBindViewHolder(
        holder: OrderedViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            holder.binding.apply {
                updateTime(tvCountDownTime, list[position])
            }
        }
    }

    /**
     * 更新倒计时
     *
     * @param textview
     * @param orderedRecord
     */
    fun updateTime(textview: TextView, orderedRecord: OrderedRecord) {
        textview.isVisible = orderedRecord.isCanReceiveOrder()
        textview.text = act.getString(
            R.string.count_down,
            TimeUtils.convertMillisecondsToTime(orderedRecord.expireCountDown ?: 0L)
        )
    }

    fun setSelectFirst(indexOf: Int): OrderedRecord? {
        if (this.list.isNotEmpty()) {
            this.list[indexOf].select = true
            selectedId = this.list[indexOf].id
            return this.list[indexOf]
        }
        return null
    }


    fun replaceData(list: ArrayList<OrderedRecord>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<OrderedRecord>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


    fun updateStatus(record: OrderedRecord) {
//        if (selectedOrderId != null && selectedOrderId == record.orderNo) {
        val index = list.indexOfFirst { it.id == record.id }
        Timber.e("updateStatus  $index")
        if (index != -1) {
            list[index] = record
            notifyItemChanged(index)
        }
//        }
    }

    fun updateRecord(record: OrderedRecord) {
        val indexOf = list.indexOf(record)
        Timber.e("updateRecord  $indexOf")
        if (indexOf != -1) {
            list[indexOf] = record
            notifyItemChanged(indexOf)
        }
    }

    fun hasModel(id: String): OrderedRecord? {
        val index = list.indexOfFirst { it.id == id }
        if (index != -1) {
            return list[index]
        }
        return null
    }

    fun hasModelByOrderNo(orderNo: String): OrderedRecord? {
        val index = list.indexOfFirst { it.orderNo == orderNo }
        if (index != -1) {
            return list[index]
        }
        return null
    }


    fun insetOrderToTop(orderedRecord: OrderedRecord) {
        list.add(0, orderedRecord)
        notifyItemInserted(0)
    }

}