package com.metathought.food_order.casheir.ui.ordered.note


import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.dboy.chips.ChipsLayoutManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.QuickRemarkModel
import com.metathought.food_order.casheir.databinding.DialogEditRemarkBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.QuickRemarkAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.view.text.addTextTag
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber


@AndroidEntryPoint
class EditRemarkDialog : BaseDialogFragment() {

    private var binding: DialogEditRemarkBinding? = null
    private var updateCartCallBackListener: ((String, GoodsRequest?) -> Unit)? = null
    private var updateOrderCallBackListener: ((OrderedInfoResponse?) -> Unit)? = null
    private val viewModel: EditRemarkViewModel by viewModels()
    private var orderInfo: OrderedInfoResponse? = null
    private var cartGood: GoodsRequest? = null
    private var orderedGood: OrderedGoods? = null
    private lateinit var adapter: QuickRemarkAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditRemarkBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)

        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initListener()
        initObserver()
        initView()

    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
        }
    }

    private fun initView() {
        binding?.apply {
            llGoodInfo.isVisible = false

            if (orderedGood != null) {
                llGoodInfo.isVisible = true
                orderedGood?.let {
                    tvName.text = it.name
                    if (!it.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                        tvName.addMealSetTag(requireContext())
//                        tvName.addTextTag {
//                            text = getString(R.string.set_menu)
//                            this.position = 0
//                            strokeWidth = 1
//                            strokeColor = requireContext().getColor(R.color.color_ff7f00)
//                            textSize = DisplayUtils.dpf2(requireContext(), 12f)
//                            backgroundColor = Color.TRANSPARENT
//                            textColor = requireContext().getColor(R.color.color_ff7f00)
//                            marginRight = 4
//                        }
                    }
                    val activityLabel = it?.activityLabels?.firstOrNull()
                    if (activityLabel != null && orderInfo?.isTakeOut() != true) {
                        tvDiscountActivity.setStrokeAndColor(
                            color = Color.parseColor(
                                activityLabel.color
                            )
                        )
                        tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                        tvDiscountActivity.text = activityLabel.name
                        tvDiscountActivity.isVisible = true
                    } else {
                        tvDiscountActivity.isVisible = false
                    }
                    tvSpecification.isVisible = it.getGoodsTagStr().isNotEmpty()
                    tvSpecification.text = it.getGoodsTagStr()
                    tvQTY.text = it.num.toString()
                    layoutPrice.tvVipPrice.text =
                        it.totalVipPriceWithSingleDiscount().priceFormatTwoDigitZero2()
                    val isUseDiscount = orderInfo?.isUseDiscount == true
                    val isShowVipPrice = it.isShowVipPrice()

                    layoutPrice.tvVipPrice.isVisible = isShowVipPrice
                    layoutPrice.tvWeight.isVisible = false
                    if (it.isToBeWeighed()) {
                        //如果是称重商品
                        if (it.isHasCompleteWeight()) {
                            layoutPrice.tvWeight.isVisible = true
                            layoutPrice.tvWeight.text = "(${it.getWeightStr()})"
                            if (it.isMealSet()) {
                                layoutPrice.tvWeight.isVisible = false
                            }
                        } else {
                            layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
                            layoutPrice.tvVipPrice.isVisible = false
                        }
                    }

                    if (it.isTimePriceGood()) {
                        //如果是时价菜
                        if (!it.isHasCompletePricing()) {
                            layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
                        } else {
                            layoutPrice.tvTimePriceSign.isVisible = true
                        }
                    }
//                    if (it.isTimePriceGood()) {
//                        //时价商品
//                        tvFoodPrice.text =
//                            getString(R.string.time_price)
//                    } else if (it.isToBeWeighed()) {
//                        //称重商品
//                        tvFoodPrice.text =
//                            getString(R.string.to_be_weighed)
//                        tvWeight.isVisible = true
//                        tvWeight.text = "(${it.getWeightStr()})"
//                    }
//                    if (it.isTimePriceGood()) {
//                        //时价商品
//                        layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
//                        if (it.isHasProcessed()) {
//                            layoutPrice.tvTimePriceSign.isVisible = true
//                        } else {
//                            if (!it.isHasCompletePricing()) {
//                                layoutPrice.tvTimePriceSign.isVisible = false
//                            } else if (!it.isHasCompleteWeight()) {
//                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
//                                layoutPrice.tvTimePriceSign.isVisible = false
//                            }
//                            layoutPrice.tvVipPrice.isVisible = false
//                        }
//                    } else if (it.isToBeWeighed()) {
//                        //称重商品
//                        if (it.isHasProcessed()) {
//                            layoutPrice.tvWeight.isVisible = true
//                            layoutPrice.tvWeight.text = "(${it.getWeightStr()})"
//                        } else {
//                            layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
//                            layoutPrice.tvVipPrice.isVisible = false
//                        }
//                    }


                    if (it.isHasProcessed()) {
                        if (isUseDiscount) {
                            if (it.isShowVipPrice()) {
                                layoutPrice.tvFoodPrice.text =
                                    it.totalVipPriceWithSingleDiscount()
                                        .priceFormatTwoDigitZero2()
                            } else {
                                layoutPrice.tvFoodPrice.text =
                                    it.totalPriceWithSingleDiscount()
                                        .priceFormatTwoDigitZero2()
                            }
                        } else {
                            layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                                orderInfo?.conversionRatio
                                    ?: FoundationHelper.conversionRatio,
                                it.totalPriceWithSingleDiscount(),
                                orderInfo?.isKhr() == true
                            )
                        }
                    } else {
                        layoutPrice.tvVipPrice.isVisible = false
                    }
                }
            } else if (cartGood != null) {
                llGoodInfo.isVisible = true
                tvName.text = cartGood?.goods?.name
                if (!cartGood?.orderMealSetGoodList.isNullOrEmpty()) {
                    tvName.addMealSetTag(requireContext())
                }
                val activityLabel = cartGood?.goods?.activityLabels?.firstOrNull()
                if (activityLabel != null) {
                    tvDiscountActivity.setStrokeAndColor(color = Color.parseColor(activityLabel.color))
                    tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                    tvDiscountActivity.text = activityLabel.name
                    tvDiscountActivity.isVisible = true
                } else {
                    tvDiscountActivity.isVisible = false
                }
                if (cartGood?.goods?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                    tvTmpSign.setStrokeAndColor(color = R.color.black60)
                    tvTmpSign.isVisible = true
                } else {
                    tvTmpSign.isVisible = false
                }
                tvQTY.text = "${cartGood?.num}"
                tvSpecification.isVisible = !cartGood?.getGoodsTagStr().isNullOrEmpty()
                tvSpecification.text = cartGood?.getGoodsTagStr()

                layoutPrice.tvWeight.isVisible = false


                if (cartGood?.isToBeWeighed() == true) {
                    //如果是称重商品
                    if (cartGood?.isHasCompleteWeight() == true) {
                        Timber.e("已称重")
                        layoutPrice.tvWeight.isVisible = true
                        layoutPrice.tvWeight.text = "(${cartGood?.goods?.getWeightStr()})"
                        if (cartGood?.goods?.isMealSet() == true) {
                            layoutPrice.tvWeight.isVisible = false
                        }
                    } else {
                        Timber.e("未称重")
                        layoutPrice.tvFoodPrice.text =
                            requireContext().getString(R.string.to_be_weighed)
                        layoutPrice.tvVipPrice.isVisible = false
                    }
                }

                if (cartGood?.goods?.isTimePriceGood() == true) {
                    //如果是时价菜
                    if (cartGood?.goods?.isHasCompletePricing() != true) {
                        layoutPrice.tvFoodPrice.text =
                            requireContext().getString(R.string.time_price)
                    } else {
                        layoutPrice.tvTimePriceSign.isVisible = true
                    }
                }


                if (cartGood?.isProcessed() == true) {
                    val isShowVipPrice = cartGood?.goods?.isShowVipPrice()
                    layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
                    layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                        FoundationHelper.useConversionRatio,
                        cartGood?.totalDiscountPrice(),
                        FoundationHelper.isKrh
                    )
                    if (isShowVipPrice == true) {
                        layoutPrice.tvVipPrice.text =
                            cartGood?.totalVipPrice()?.priceFormatTwoDigitZero2()
                    }
                    if (cartGood?.goods?.isTimePriceGood() == true) {
                        layoutPrice.tvTimePriceSign.isVisible = true
                    }
                }
//                else {
//                    layoutPrice.tvFoodPrice.text =
//                        "${cartGood?.getShowPrice(requireContext())}"
//
//                    if (cartGood?.goods?.isTimePriceGood() == true && cartGood?.goods?.isToBeWeighed() == true && cartGood?.goods?.isHasCompletePricing() == true && cartGood?.goods?.isHasCompleteWeight() == false) {
//                        //如果是称重时价菜 且定价未称重
//                        layoutPrice.tvTimePriceSign.isVisible = true
//                    }
//                    layoutPrice.tvVipPrice.isVisible = false
//
//                }
            } else {
                edtRemark.hint = getString(R.string.order_remark)
            }

            val remark = arguments?.getString(REMARK) ?: ""
            edtRemark.setText(remark)
            edtRemark.setSelection(edtRemark.length())


            val chipsLayoutManager = ChipsLayoutManager.newBuilder(context).build()
            rvList.layoutManager = chipsLayoutManager
            adapter = QuickRemarkAdapter(
                ArrayList(),
            ) {
                val str = edtRemark.text.toString()
                if (str.trim().isNullOrEmpty()) {
                    edtRemark.setText("$it")
                } else {
                    edtRemark.setText("$str $it")
                }

                edtRemark.setSelection(edtRemark.length())
            }
            rvList.adapter = adapter
            lifecycleScope.launch {
                val list = PreferenceHelper.getQuickRemarkList()
                updateList(list)
            }
            viewModel.getShortcutNoteList()
        }
    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            btnYes.setOnClickListener {
                binding?.apply {
                    if (orderInfo == null) {
                        updateCartCallBackListener?.invoke(
                            edtRemark.text.toString().trim(),
                            cartGood
                        )
                        dismissCurrentDialog()
                    } else {
                        viewModel.updateNote(
                            orderInfo?.orderNo,
                            orderedGood,
                            edtRemark.text.toString().trim(),
                        )
                    }
                }
            }
        }
    }

    private fun initObserver() {
        viewModel.uiModeState.observe(viewLifecycleOwner) { state ->
            if (state.result is ApiResponse.Success) {
                binding?.apply {
                    updateOrderCallBackListener?.invoke(state.result.data)
                    dismissAllowingStateLoss()
                }
            }
        }

        viewModel.uiListModeState.observe(viewLifecycleOwner) { state ->
            if (state.result is ApiResponse.Success) {
                lifecycleScope.launch {
                    PreferenceHelper.setQuickRemarkList(state.result.data)
                }
                updateList(state.result.data)
            }
        }
    }


    private fun updateList(list: ArrayList<QuickRemarkModel>) {
        binding?.apply {
            adapter.replaceData(list)
            rvList.isVisible = adapter.itemCount != 0
        }
    }

    companion object {
        private const val TAG = "EditRemarkDialog"
        private const val REMARK = "REMARK"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderInfo: OrderedInfoResponse? = null,
            cartGood: GoodsRequest? = null,
            orderedGood: OrderedGoods? = null,
            remark: String?,
            updateCartCallBackListener: ((String, GoodsRequest?) -> Unit)? = null,
            updateOrderCallBackListener: ((
                OrderedInfoResponse?,
            ) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                orderInfo = orderInfo,
                remark = remark,
                cartGood = cartGood,
                orderedGood = orderedGood,
                updateCartCallBackListener = updateCartCallBackListener,
                updateOrderCallBackListener = updateOrderCallBackListener
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? EditRemarkDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            orderInfo: OrderedInfoResponse?,
            remark: String?,
            cartGood: GoodsRequest? = null,
            orderedGood: OrderedGoods? = null,
            updateCartCallBackListener: ((String, GoodsRequest?) -> Unit)? = null,
            updateOrderCallBackListener: ((
                OrderedInfoResponse?,
            ) -> Unit)? = null
        ): EditRemarkDialog {
            val args = Bundle()

            val fragment = EditRemarkDialog()
//            args.putString(ORDER_NO, orderNo)
            args.putString(REMARK, remark)
            fragment.arguments = args
            fragment.updateCartCallBackListener = updateCartCallBackListener
            fragment.updateOrderCallBackListener = updateOrderCallBackListener
            fragment.orderInfo = orderInfo
            fragment.cartGood = cartGood
            fragment.orderedGood = orderedGood
            return fragment
        }
    }


}
