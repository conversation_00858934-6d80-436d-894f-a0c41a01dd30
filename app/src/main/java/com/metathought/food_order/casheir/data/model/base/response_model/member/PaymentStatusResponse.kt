package com.metathought.food_order.casheir.data.model.base.response_model.member


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse

data class PaymentStatusResponse(
    @SerializedName("status")
    val status: Int?,
    @SerializedName("repaymentResultDTO")
    val repaymentResultDTO: RepaymentResponse
) {
    fun isOrderSuccess(): Boolean {
        return status == 1
    }

    fun isOrderExpire(): <PERSON>olean {
        return status == 3
    }
}