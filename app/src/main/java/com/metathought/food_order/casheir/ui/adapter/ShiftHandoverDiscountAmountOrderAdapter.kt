package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.OfflinePayMethodData
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.OrderDiscountInfo
import com.metathought.food_order.casheir.databinding.ShiftHandoverDiscountAmountItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverDiscountAmountOrderItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverOfflineRecordsItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverRecordsItemBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2


data class ItemData(val name: String, val price: Double?, val isTitle: Boolean)

class ShiftHandoverDiscountAmountOrderAdapter(var list: MutableList<ItemData>) :
    RecyclerView.Adapter<ShiftHandoverDiscountAmountOrderAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            ShiftHandoverDiscountAmountOrderItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(position)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    inner class ViewHolder(val binding: ShiftHandoverDiscountAmountOrderItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(position: Int) {
            binding.apply {
                list[position].let { log ->
                    tvProductName.text = if (log.isTitle) log.name else "-${log.name}"
                    tvAmount.text = log.price?.priceFormatTwoDigitZero2("$")?:""
                }
            }
        }

    }
}