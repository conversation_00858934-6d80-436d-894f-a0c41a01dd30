package com.metathought.food_order.casheir.data.model.base.request_model.menu

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.AddToCartRequest

/**
 * <AUTHOR>
 * @date 2025/04/09 16:15
 * @description
 */
data class AsyncChangeRequest(
    //购物车内商品id
    @SerializedName("tableUuid")
    val tableUuid: String? = null,
    @SerializedName("goodsId")
    val goodsId: String? = null,
    @SerializedName("deliveryPlatformId")
    var deliveryPlatformId: String? = null,
    @SerializedName("diningStyle")
    val diningStyle: Int? = 1,

    @SerializedName("addGoodsToCartDTOList")
    val addGoodsToCartDTOList: List<AddToCartRequest>? = null,

    @SerializedName("dataVersion")
    val dataVersion: Long = 0,
)


