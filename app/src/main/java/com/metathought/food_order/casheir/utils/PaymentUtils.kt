package com.metathought.food_order.casheir.utils

import android.content.Context
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.PayInfo
import timber.log.Timber

object PaymentUtils {

    /**
     * 获取混合支付方式文本
     * @param context 上下文
     * @param payInfoList 混合支付信息列表
     * @return 拼接后的支付方式文本
     */
    fun getMixedPaymentText(context: Context, payInfoList: List<PayInfo>?): String {
        if (payInfoList.isNullOrEmpty()) {
            return ""
        }

        val paymentTexts = mutableListOf<String>()

        payInfoList.forEach { payInfo ->
            if (payInfo.payType == PayTypeEnum.USER_BALANCE.id) {
                paymentTexts.add(context.getString(R.string.pay_by_balance))
            } else if (payInfo.payType == PayTypeEnum.ONLINE_PAYMENT.id) {
                paymentTexts.add(context.getString(R.string.online_payment))
            } else if (payInfo.payType == PayTypeEnum.CASH_PAYMENT.id) {
                paymentTexts.add(
                    getOfflinePaymentText(
                        context,
                        (payInfo.offlinePayment?.offlinePayChannelsId ?: "0").toLong(),
                        payInfo.offlinePayment?.offlinePayChannelsName
                    )
                )
            }
        }

        return paymentTexts.joinToString("\n")
    }

    /**
     * 获取线下支付方式文本
     */
    private fun getOfflinePaymentText(
        context: Context,
        channelId: Long?,
        channelName: String?
    ): String {
        return when (channelId) {
            OfflinePaymentChannelEnum.CASH.id.toLong() -> {
                "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
//                context.getString(R.string.cash)
            }

            OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toLong() -> {
                "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
            }

            else -> {
                "${context.getString(R.string.offline_payments)} - ${
                    channelName ?: ""
                }"
            }
        }
    }

    /**
     * 判断混合支付是否含有余额支付
     */
    fun isMixHasBalance(payInfoList: List<PayInfo>?): Boolean {
        return payInfoList?.any { it.payType == PayTypeEnum.USER_BALANCE.id } == true
    }
}