package com.metathought.food_order.casheir.ui.second_display

import android.content.Context
import android.media.MediaRouter
import android.util.Log
import android.view.Display
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.ui.second_display.menu.MenuOrderScreenUI
import com.metathought.food_order.casheir.ui.second_display.ordered.OrderedInfoScreenUI
import com.metathought.food_order.casheir.ui.second_display.other.OtherScreenUI
import com.metathought.food_order.casheir.ui.second_display.payment.PaymentScreenUI

/**
 * <AUTHOR>
 * @date 2024/5/1314:02
 * @description
 */
@Deprecated(message = "旧版V1副屏方案")
object SecondaryManager {

    private var display: Display? = null
    var userInfo: UserLoginResponse? = null
    private var otherScreenUI: OtherScreenUI? = null
    private var menuOrderScreenUI: MenuOrderScreenUI? = null
    private var orderedInfoScreenUI: OrderedInfoScreenUI? = null
    private var paymentScreenUI: PaymentScreenUI? = null

    var isDestroy = false
    fun onCreate() {
        isDestroy = false
    }

    fun onDestroy() {
        dismissScreen()
        isDestroy = true
    }

    //0:other
    private var routeIndex = SecondaryRoute.DEFAULT
    private fun getPresentationDisplay(context: Context): Display? {
        if (display == null) {
//            val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager?
//            val displays =displayManager!!.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
            //            if(displays.isNotEmpty()){
//                display=displays[0]
//            }
            val mediaRouter =
                context.getSystemService(Context.MEDIA_ROUTER_SERVICE) as MediaRouter
            val route = mediaRouter.getSelectedRoute(MediaRouter.ROUTE_TYPE_LIVE_VIDEO)
            display = route.presentationDisplay
        }
        return display
    }

    fun showDefault(context: Context) {
        if (userInfo != null) {
            showOtherScreen(context, userInfo)
        }
    }

    fun isOrderedScreen() = routeIndex == SecondaryRoute.ORDERED_INFO
    fun isPaymentQrScreen() =
        routeIndex == SecondaryRoute.PAYMENT_QR || routeIndex == SecondaryRoute.PAYMENT_RESULT

    fun getOrderedScreen(context: Context): OrderedInfoScreenUI? {
        if (!isDestroy) {
            getPresentationDisplay(context)?.apply {
                if(orderedInfoScreenUI==null) {
                    orderedInfoScreenUI = OrderedInfoScreenUI(context, this)
                }
            }
            setRoute(SecondaryRoute.ORDERED_INFO)
        }
        return orderedInfoScreenUI
    }

    fun showOtherScreen(context: Context, user: UserLoginResponse? = null) {
        if (!isDestroy) {
            getPresentationDisplay(context)?.apply {
                if (user != null) {
                    <EMAIL> = user
                }
                setRoute(SecondaryRoute.DEFAULT)
                if (otherScreenUI != null) {
                    if (otherScreenUI?.isShowing == true) {
                        otherScreenUI?.updateUI(<EMAIL>)
                    } else {
                        otherScreenUI?.show()
                        otherScreenUI?.updateUI(<EMAIL>)
                    }
                } else {
                    otherScreenUI = OtherScreenUI(context, this)
                    otherScreenUI?.show()
                    otherScreenUI?.updateUI(<EMAIL>)
                }
            }
        }
    }

    fun dismissScreen() {
        otherScreenUI?.run {
            Log.e("Secondary", "otherScreenUI dismiss")
            dismiss()
            otherScreenUI = null
        }
        menuOrderScreenUI?.run {
            Log.e("Secondary", "menuOrderScreenUI dismiss")
            dismiss()
            menuOrderScreenUI = null
        }
        orderedInfoScreenUI?.run {
            Log.e("Secondary", "OrderedInfoScreenUI dismiss")
            dismiss()
            orderedInfoScreenUI = null
        }
        paymentScreenUI?.run {
            Log.e("Secondary", "PaymentScreenUI dismiss")
            dismiss()
            paymentScreenUI = null
        }
    }

    fun dismissScreenByRoute() {
        when (routeIndex) {
            SecondaryRoute.DEFAULT -> {
                otherScreenUI?.run {
                    Log.e("Secondary", "otherScreenUI dismiss")
                    dismiss()
                    otherScreenUI = null
                }
            }

            SecondaryRoute.MENU_ORDER -> {
                menuOrderScreenUI?.run {
                    Log.e("Secondary", "menuOrderScreenUI dismiss")
                    dismiss()
                    menuOrderScreenUI = null
                }
            }

            SecondaryRoute.ORDERED_INFO -> {
                orderedInfoScreenUI?.run {
                    Log.e("Secondary", "OrderedInfoScreenUI dismiss")
                    dismiss()
                    orderedInfoScreenUI = null
                }
            }

            SecondaryRoute.PAYMENT_QR,
            SecondaryRoute.PAYMENT_RESULT -> {
                paymentScreenUI?.run {
                    Log.e("Secondary", "PaymentScreenUI dismiss")
                    dismiss()
                    paymentScreenUI = null
                }
            }
        }
    }


    fun getMenuOrderScreen(context: Context): MenuOrderScreenUI? {
        if (!isDestroy) {
            getPresentationDisplay(context)?.apply {
                if (menuOrderScreenUI != null) {
                    if (menuOrderScreenUI?.isShowing != true) {
                        menuOrderScreenUI?.show()
                    }
                } else {
                    menuOrderScreenUI = MenuOrderScreenUI(context, this)
                        .apply {
                            show()
                        }
                }
            }
            setRoute(SecondaryRoute.MENU_ORDER)
        }
        return menuOrderScreenUI
    }

    fun showPaymentSecond(context: Context, response: PaymentResponse?): PaymentScreenUI? {
        if (!isDestroy) {
            getPresentationDisplay(context)?.apply {
                paymentScreenUI = PaymentScreenUI(context, this).apply {
                    paymentResponse = response
                    show()
                }
            }
            setRoute(SecondaryRoute.PAYMENT_QR)
        }
        return paymentScreenUI
    }

    fun showPaymentResult(context: Context, orderedInfo: OrderedInfoResponse?): PaymentScreenUI? {
        if (!isDestroy) {
            getPresentationDisplay(context)?.apply {
                paymentScreenUI = PaymentScreenUI(context, this).apply {
                    this.orderedInfo = orderedInfo
                    show()
                }
            }
            setRoute(SecondaryRoute.PAYMENT_RESULT)
        }
        return paymentScreenUI
    }

    fun setDefaultRoute() {
        setRoute(SecondaryRoute.DEFAULT)
    }

    private fun setRoute(@SecondaryRoute secondaryRoute: Int) {
        routeIndex = secondaryRoute
    }


}