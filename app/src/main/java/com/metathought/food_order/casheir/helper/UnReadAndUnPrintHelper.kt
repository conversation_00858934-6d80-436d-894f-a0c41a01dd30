package com.metathought.food_order.casheir.helper


/**
 *<AUTHOR>
 *@time  2024/7/22
 *@desc
 **/

object UnReadAndUnPrintHelper {
    /**
     * 未读订单数量
     */
    private var unReadNum = 0

    /**
     * 未打印数量
     */
    private var unPrintNum = 0


    private var grabUnReadNum = 0

    /**
     * 未打印数量
     */
    private var grabUnPrintNum = 0


    fun setGrabUnReadNum(unReadNum: Int) {
        this.grabUnReadNum = unReadNum
    }

    fun getGrabUnReadNum(): Int {
        return grabUnReadNum
    }

    fun setGrabUnPrintNum(unPrintNum: Int) {
        this.grabUnPrintNum = unPrintNum
    }

    fun getGrabUnPrintNum(): Int {
        return grabUnPrintNum
    }


    /**
     * 未读接单数量
     */
    private var unReadReceiveOrderNum = 0

    /**
     * 公告未读数
     */
    private var unReadNoticeNum = 0


    fun setUnReadNum(unReadNum: Int) {
        this.unReadNum = unReadNum
    }

    fun getUnReadNum(): Int {
        return unReadNum
    }

    fun getAllUnReadNum(): Int {
        return unReadNum+ grabUnReadNum
    }


    fun getUnReadNumStr(): String {

        return if (unReadNum > 99) "99+" else "$unReadNum"
    }


    fun getAllUnReadNumStr(): String {
        var allNum= unReadNum+ grabUnReadNum;
        return if (allNum > 99) "99+" else "$allNum"
    }


    fun getGrabUnReadNumStr(): String {

        return if (grabUnReadNum > 99) "99+" else "$grabUnReadNum"
    }


    fun setUnPrintNum(unPrintNum: Int) {
        this.unPrintNum = unPrintNum
    }

    fun getUnPrintNum(): Int {
        return unPrintNum
    }

    fun getUnPrintNumStr(): String {
        return if (unPrintNum > 99) "99+" else "$unPrintNum"
    }

    fun getGrabUnPrintNumStr(): String {



        return if (grabUnPrintNum > 99) "99+" else "$grabUnPrintNum"
    }



    fun setUnReceiveOrderNum(unReadReceiveOrderNum: Int) {
        this.unReadReceiveOrderNum = unReadReceiveOrderNum
    }

    fun getUnReceiveOrderNum(): Int {
        return unReadReceiveOrderNum
    }

    fun getUnReceiveOrderNumStr(): String {
        return if (unReadReceiveOrderNum > 99) "99+" else "$unReadReceiveOrderNum"
    }

    fun setNoticeUnReadNum(unReadNum: Int) {
        this.unReadNoticeNum = unReadNum
    }

    fun getNoticeUnReadNum(): Int {
        return unReadNoticeNum
    }

    fun getNoticeUnReadNumStr(): String {
        return if (unReadNoticeNum > 99) "99+" else "$unReadNoticeNum"
    }


    fun clear() {
        unReadNum = 0
        unPrintNum = 0
        unReadReceiveOrderNum = 0
        unReadNoticeNum = 0
    }
}