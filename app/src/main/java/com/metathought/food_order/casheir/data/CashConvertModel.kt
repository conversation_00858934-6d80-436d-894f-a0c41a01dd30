package com.metathought.food_order.casheir.data

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

/**
 * <AUTHOR>
 * @date 2024/5/913:25
 * @description
 */
data class CashConvertModel(
    @SerializedName("collectCash")
    val collectCash: Long? = null,
    //找零的金额
    @SerializedName("changeAmount")
    val changeAmount: Long? = null,
    //收款的美元  请求的时候 单位是美元  返回值是美分
    @SerializedName("collectCashDollar")
    val collectCashDollar: BigDecimal? = null,
    //找零的美元  请求的时候 单位是美元  返回值是美分
    @SerializedName("changeAmountDollar")
    val changeAmountDollar: BigDecimal? = null,

    @SerializedName("offlinePayChannelsId")
    var offlinePayChannelsId: String? = null,
    @SerializedName("offlinePayChannelsName")
    var offlinePayChannelsName: String? = null,
    @SerializedName("offlinePayChannelsNameEn")
    var offlinePayChannelsNameEn: String? = null,
    @SerializedName("offlinePayChannelsNameKm")
    var offlinePayChannelsNameKm: String? = null,

)