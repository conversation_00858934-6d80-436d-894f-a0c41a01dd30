package com.metathought.food_order.casheir.extension

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import androidx.core.content.ContextCompat


// 新增：String 扩展函数（截断超过指定长度的字符串）
fun String.truncateIfLong(maxLength: Int = 30): String {
    return if (length > maxLength) {
        substring(0, maxLength) + "..."
    } else {
        this
    }
}

/**
 * 扩展方法：将字符串中指定字符设置为指定颜色
 * @param targetChar 需要染色的目标字符（如'='）
 * @param colorRes 颜色资源ID（如R.color.black60）
 * @return 处理后的SpannableString
 */
fun String.colorChar(
    context: Context,
    targetChar: Char,
    colorRes: Int
): SpannableString {
    val spannable = SpannableString(this)
    // 遍历查找所有目标字符位置
    for (index in this.indices) {
        if (this[index] == targetChar) {
            spannable.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(context, colorRes)),
                index,
                index + 1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }
    return spannable
}
