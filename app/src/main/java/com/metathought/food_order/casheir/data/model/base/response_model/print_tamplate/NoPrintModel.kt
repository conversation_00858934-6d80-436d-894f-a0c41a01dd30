package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate

import ProfitReportDetail
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.ComprehensiveReportData
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReport
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportData
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.CreateTempTableCodeResponse
import com.metathought.food_order.casheir.ui.widget.printer.TicketPrinter
import com.metathought.food_order.casheir.ui.widget.printer.TicketPrinter.getKitchenFinalList
import java.util.Date
import java.util.UUID


/**
 *<AUTHOR>
 *@time  2024/11/8
 *@desc  未打印的model
 **/
/**
 * No print model
 *
 * @property printTemplateResponseItem
 * @property currentOrderedInfo
 * @property isPrinterAgain
 * @property paymentQrCode
 * @property printerConfigInfo
 * @property isOrderMore
 * @property timeStamp
 * @constructor Create empty No print model
 */
data class NoPrintModel(
    /**
     * 模板
     */
    @SerializedName("printTemplateResponseItem")
    val printTemplateResponseItem: PrintTamplateResponseItem? = null,

    /**
     * 订单数据
     */
    @SerializedName("currentOrderedInfo")
    val currentOrderedInfo: OrderedInfoResponse? = null,

    val isPrinterAgain: Boolean? = false,
    val paymentQrCode: String? = null,
    val printerConfigInfo: PrinterConfigInfo? = null,
    val isOrderMore: Boolean? = false,
    val isMargePrint: Boolean? = false,

    /**
     * 临时码数据
     */
    val createTempTableResponse: CreateTempTableCodeResponse? = null,
    /**
     * 商品报表数据
     */
    val productReport: ProductReportData? = null,
    /**
     * 门店报表数据
     */
    val paymentMethodReport: PaymentMethodReport? = null,

    /**
     * 闭店报表数据
     */
    var shiftReport: ShiftReportPrint? = null,

    /**
     * 综合报表数据
     */
    var comprehensiveReport: ComprehensiveReportData? = null,

    /**
     * 收支报表数据
     */
    val profitReportDetail: ProfitReportDetail? = null,


    val timeStamp: Long = Date().time,

    /**
     * 挂账还款信息
     */
    val repaymentResponse: RepaymentResponse? = null,
    val printerOrderStatus: OrderedStatusEnum? = null,

    var printerResult: SimpleOrderedInfo? = null,

    var uuid: UUID = UUID.randomUUID()
) {
    //获取打印内容
    fun getPrintContentTypeAndSimplifiedData(): Pair<String, Any?> {
        return when {
            currentOrderedInfo != null -> {
                if (printerResult != null) {
                    "ORDER" to printerResult
                } else {
                    "ORDER" to SimpleOrderedInfo(
                        id = currentOrderedInfo.id,
                        orderNumber = currentOrderedInfo.orderNo,
                        goods = currentOrderedInfo.goods?.map {
                            SimpleOrderedGoods(
                                name = it.name,
                                quantity = it.num,
                                seriesNo = it.seriesNo
                            )
                        }, isOrderMore, isMargePrint
                    )
                }
            }

            createTempTableResponse != null -> "TEMP_CODE" to createTempTableResponse.qrCode
            repaymentResponse != null -> "REPAYMENT" to repaymentResponse.consumerId
            else -> "UNKNOWN" to ""
        }
    }

    // 新增：将Pair转换为指定key的JSON字符串
    fun getPrintContentAsJson(): String {
        val (type, data) = getPrintContentTypeAndSimplifiedData()
        val singleDesc = when (printTemplateResponseItem?.type) {
            PrintTemplateTypeEnum.KITCHEN.id -> {
                if (!printTemplateResponseItem?.storeKitchenName.isNullOrEmpty()) {
                    "厨房小票-${printTemplateResponseItem?.storeKitchenName}"
                } else {
                    "主厨房"
                }
            }

            PrintTemplateTypeEnum.DINE_IN.id -> "堂食小票"
            PrintTemplateTypeEnum.TAKE_AWAY.id -> "外带小票"
            PrintTemplateTypeEnum.PRE_ORDER.id -> "预定小票"
            PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id -> "结账小票"
            PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id -> "预结小票"
            PrintTemplateTypeEnum.PRODUCT_REPORT.id -> "商品小票"
            PrintTemplateTypeEnum.PAYMENT_METHOD_REPORT.id -> "支付渠道报表"
            PrintTemplateTypeEnum.LABEL.id -> "标签贴纸"
            PrintTemplateTypeEnum.SHIFT_HANDOVER.id -> "交班报表小票"
            PrintTemplateTypeEnum.TABLE_REPORT.id -> "桌台报表小票"
            PrintTemplateTypeEnum.STORE_PROFIT_REPORT.id -> "门店盈利报表小票"
            else -> {
                ""
            }
        }

        // 使用Gson将type和data封装为带指定key的对象
        val jsonObject = mapOf(
            "type" to type,
            "uuid" to uuid.toString(),
            "data" to data,
            "printTemplate" to "$singleDesc",
        )
        return Gson().toJson(jsonObject)
    }
}

data class SimpleOrderedInfo(
    val id: String?, // 订单ID（关键）
    val orderNumber: String?, // 订单号（关键）
    val goods: List<SimpleOrderedGoods>?,// 精简商品列表（仅保留名称、数量等）
    val isOrderMore: Boolean?,
    val isMargePrint: Boolean?
)

data class SimpleOrderedGoods(
    val name: String?, // 商品名称
    val quantity: Int?, // 数量
    val seriesNo: String? = null, // 商品系列号
)