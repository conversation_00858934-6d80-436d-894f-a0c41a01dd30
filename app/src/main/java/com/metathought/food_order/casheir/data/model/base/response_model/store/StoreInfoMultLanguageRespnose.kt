package com.metathought.food_order.casheir.data.model.base.response_model.store


/**
 *<AUTHOR>
 *@time  2024/10/29
 *@desc
 **/

class StoreInfoMultLanguageListRespnose(
    val name: List<StoreInfoMultLanguageRespnose>? = null,
    val description: List<StoreInfoMultLanguageRespnose>? = null
) {

    fun getNameByLanguage(language: String?): StoreInfoMultLanguageRespnose? {
        return name?.firstOrNull { it.language == language }
    }

    fun getDescByLanguage(language: String?): StoreInfoMultLanguageRespnose? {
        return description?.firstOrNull { it.language == language }
    }

}

class StoreInfoMultLanguageRespnose(
    val id: Long? = null,
    val columnId: String? = null,
    val tableName: String? = null,
    /**
     * name = 门店名称 description = 描述
     */
    var columnName: String? = null,
    var language: String? = null,
    var value: String? = null
)