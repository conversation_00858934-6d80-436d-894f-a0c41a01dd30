package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood

/**
 * <AUTHOR>
 * @date 2025/04/09 16:15
 * @description
 */
data class CartGoodEditRequest(
    //购物车内商品id
    @SerializedName("cartId")
    val cartId: String? = null,
    //购物车类型
    @SerializedName("diningStyle")
    val diningStyle: Int? = null,
    //商品id
    @SerializedName("goodsId")
    val goodsId: String? = null,
    //备注
    @SerializedName("note")
    val note: String? = null,
    //数量
    @SerializedName("num")
    val num: Int? = null,
    //桌子id
    @SerializedName("tableUuid")
    val tableUuid: String? = null,
    @SerializedName("feedInfoList")
    val feedInfoList: List<FeedBo?>?,
    @SerializedName("tagItemIds")
    val tagItemIds: String?,
    @SerializedName("goodsType")
    val goodsType: Int?,

    @SerializedName("groupId")
    val groupId: String?,


    /**
     * 套餐内容
     */
    var mealSetGoodsInfoList: List<MealSetGood>? = null,
)


