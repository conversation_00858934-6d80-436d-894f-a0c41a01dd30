package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Group
import com.metathought.food_order.casheir.databinding.SecondCategoryItemBinding


class SecondFoodCategoryAdapter(
    var categories: ArrayList<Group>,
    val context: Context
) : RecyclerView.Adapter<SecondFoodCategoryAdapter.FoodCategoryViewHolder>() {

    inner class FoodCategoryViewHolder(val binding: SecondCategoryItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(showIndex: Group, position: Int) {
            binding.apply {
                binding.tvValue.text = showIndex.name
                binding.tvValue.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (showIndex.checked == true) R.color.primaryColor else R.color.black
                    )
                )
            }
        }
    }


    fun setCheckedIndex(position: Int) {
//        for (i in 0..<categories.size) {
//            if (categories[i].checked == true) {
//                categories[i].checked = false
//                notifyItemChanged(i)
//            }
//        }
//        categories[position].checked = true
//        notifyItemChanged(position)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FoodCategoryViewHolder {
        return FoodCategoryViewHolder(
            SecondCategoryItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return categories.size
    }

    override fun onBindViewHolder(holder: FoodCategoryViewHolder, position: Int) {
        holder.bind(categories[position], position)
    }

    fun updateItems(newItems: ArrayList<Group>) {
        categories.clear()
        categories.addAll(newItems)
        notifyDataSetChanged()
    }
}