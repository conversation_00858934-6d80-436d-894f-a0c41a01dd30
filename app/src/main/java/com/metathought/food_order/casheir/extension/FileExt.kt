package com.metathought.food_order.casheir.extension

import android.net.Uri
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream

//判断文件是否完整
//FileUtils 里的 isCollectFileNeedDownLoad 方法 里面有一套java的
//如果需要改成hasCode 那么 那边也要改
//方法内部含有，删除文件方法，存在 且不是 fullSize 删除
fun File.fileAllSize(size:String):Boolean{

    if(this.exists() && this.length().toString() != size){
        this.delete()
        return false
    }
    return this.exists() && this.length().toString() == size
}

fun File.dir(): File {
    if(!exists()){ mkdirs() }
    return this
}

//拷贝SD to Q
// outFilePath 为沙盒地址
//fun Uri.copySDToQ(outFilePath: String): File {
//    val pfd = AppContextWrapper.getApplicationContext().contentResolver.openFileDescriptor(this, "r")//r代表读操作
//    FileInputStream(pfd?.fileDescriptor).use { fis ->
//        FileOutputStream(File(outFilePath)).use { fos ->
//            fis.copyTo(fos)
//        }
//    }
//    return File(outFilePath)
//}