package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods


/**
 *<AUTHOR>
 *@time  2024/5/27
 *@desc
 **/

data class WsModifyWeight(
    @SerializedName("orderId")
    val orderId: String? = null,
    @SerializedName("finalGoods")
    val finalGoods: List<OrderedGoods>? = null
)