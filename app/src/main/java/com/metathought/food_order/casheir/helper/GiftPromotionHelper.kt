package com.metathought.food_order.casheir.helper

import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.PromotionActivity
import com.metathought.food_order.casheir.data.model.base.response_model.order.PromotionActivityThreshold
import com.metathought.food_order.casheir.data.model.base.response_model.order.UsageType
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2025/5/19
 *@desc
 **/

object GiftPromotionHelper {
    /**
     * 获取赠品活动是否有效
     *
     * @param goodRequest
     * @param giftPromotionGood
     * @return
     */
    fun handlePromotionEffect(
        goodRequests: List<GoodsRequest>,
        giftPromotionGood: PromotionActivity?,
        diningStyle: Int
    ): Boolean {
        giftPromotionGood?.apply {
            when (giftPromotionGood.threshold) {
                PromotionActivityThreshold.NOTHRESHOLD.name -> {
                    //无门槛 购物车有商品就满足
                    return goodRequests.isNotEmpty()
                }

                PromotionActivityThreshold.THRESHOLD_ORDER_AMOUNT.name -> {
                    //订单金额为门槛  只判断现价满足
                    var price = BigDecimal.ZERO
                    goodRequests.forEach {
                        if (giftPromotionGood.usageType == UsageType.PARTIAL_GOODS) {
                            val contains =
                                (giftPromotionGood.designatedGoodIds ?: "").contains(
                                    it.goods?.id ?: ""
                                )
                            if (contains) {
                                price = price.plus(
                                    BigDecimal.valueOf(
                                        it.getTotalDiscountThresholdPrice(
                                            diningStyle
                                        )
                                    )
                                )
                            }
                        } else {
                            price = price.plus(
                                BigDecimal.valueOf(
                                    it.getTotalDiscountThresholdPrice(
                                        diningStyle
                                    )
                                )
                            )
                        }

                    }
                    return price >= (giftPromotionGood.base ?: BigDecimal.ZERO).times(
                        BigDecimal.valueOf(100)
                    )
                }

                PromotionActivityThreshold.THRESHOLD_ITEM_NUM.name -> {
                    var num = BigDecimal.ZERO
                    goodRequests.forEach {
                        if (giftPromotionGood.usageType == UsageType.PARTIAL_GOODS) {
                            val contains =
                                (giftPromotionGood.designatedGoodIds ?: "").contains(
                                    it.goods?.id ?: ""
                                )
                            if (contains) {
                                num = num.plus(
                                    BigDecimal.valueOf((it.num ?: 0).toLong())
                                )
                            }

                        } else {
                            num = num.plus(
                                BigDecimal.valueOf((it.num ?: 0).toLong())
                            )
                        }

                    }
                    return num >= (giftPromotionGood.base ?: BigDecimal.ZERO)
                }

                PromotionActivityThreshold.THRESHOLD_DESIGNATED_GOOD.name -> {
                    var isHasGood = false
                    goodRequests.forEach {
                        val contains =
                            (giftPromotionGood.designatedGoodIds ?: "").contains(it.goods?.id ?: "")
                        if (contains) {
                            isHasGood = true
                        }
                    }
                    return isHasGood
                }
            }
        }

        return false
    }
}