package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.ItemReceiveOrderInfoTypeBinding
import com.metathought.food_order.casheir.extension.formatDate


class CancelReceiveOrderListAdapter(
    val list: List<OrderedInfoResponse>,
    val context: Context,
) : RecyclerView.Adapter<CancelReceiveOrderListAdapter.ReceivingInfoViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReceivingInfoViewHolder {
        val itemView =
            ItemReceiveOrderInfoTypeBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return ReceivingInfoViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ReceivingInfoViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    inner class ReceivingInfoViewHolder(val binding: ItemReceiveOrderInfoTypeBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.apply {

            }
        }

        fun bind(resource: OrderedInfoResponse, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvTitle.text =
                            "${context.getString(R.string.add_goods_order)}${position + 1}"

                        val adapter =
                            OrderedInfoAdapter((it.goods ?: arrayListOf()) as ArrayList<BaseOrderGoods>)
                        receiverInfoRecyclerView.adapter = adapter


                        tvCancelTime.text = it.cancelTime?.formatDate()
                        tvCancelReason.text = it.cancelReason
                        llCancelReason.isVisible = !it.cancelReason.isNullOrEmpty()
                    }
                }
            }
        }

    }
}
