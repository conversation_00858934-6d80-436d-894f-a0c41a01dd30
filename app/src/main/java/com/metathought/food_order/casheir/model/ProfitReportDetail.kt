import com.google.gson.annotations.SerializedName

data class ProfitReportDetail(
    //日期
    @SerializedName("date") val date: String,
    //门店收入
    @SerializedName("revenueAmount") val revenueAmount: Double,
    //支出金额
    @SerializedName("amountPaid") val amountPaid: Double,
    //营业利润
    @SerializedName("operatingIncome") val operatingIncome: Double,
    //支付方式明细Vo
    @SerializedName("salesPayMethodReportDetails") val salesPayMethodReportDetails: List<SalesPayMethodReportDetail>,
    //总计
    @SerializedName("totalOrderAmount") val totalOrderAmount: Double,
    //备用金详情Vo
    @SerializedName("openingCashEditLogVos") val openingCashEditLogVos: List<OpeningCashEditLogVo>,
    //备用金总计
    @SerializedName("totalOpeningCash") val totalOpeningCash: Double,
    //备用金总计USD
    @SerializedName("totalOpeningCashUSD") val totalOpeningCashUSD: Double,
    //备用金总计KHR
    @SerializedName("totalOpeningCashKHR") val totalOpeningCashKHR: Long,
    //备用金详情Vo
    @SerializedName("openingCashPaidLogVos") val openingCashPaidLogVos: List<OpeningCashEditLogVo>,
    //备用金支出总计USD
    @SerializedName("totalOpeningPaidCashUSD") val totalOpeningPaidCashUSD: Double,
    //备用金支出总计KHR
    @SerializedName("totalOpeningPaidCashKHR") val totalOpeningPaidCashKHR: Long,
    //备用金支出总计
    @SerializedName("totalOpeningPaidCash") val totalOpeningPaidCash: Double,
    //订单信息Vo
    @SerializedName("orderVos") val orderVos: List<OrderVo>,
    //信用客户详情vo
    @SerializedName("creditCustomerVos") val creditCustomerVos: List<CreditCustomerVo>,
    //信用客户金额总计
    @SerializedName("totalCreditCustomerAmount") val totalCreditCustomerAmount: Double
)

data class SalesPayMethodReportDetail(
    //支付方式-枚举，因为payMethod被占用了，所以用payChannel,可用值:1,2,3,4,5,6
    @SerializedName("payChannel") val payChannel: String,
    //支付方式
    @SerializedName("payMethod") val payMethod: String,
    //订单金额
    @SerializedName("amount") val amount: Double,
    //订单数量
    @SerializedName("orderNum") val orderNum: Int,
    //金额占比
    @SerializedName("amountRatio") val amountRatio: String,
    //支付渠道id，交接班报表用到了这个字段，利用这个字段做多语言打印的处理
    @SerializedName("channelId") val channelId: Int
)

data class OpeningCashEditLogVo(
    //时间
    @SerializedName("time") val time: String,
    //备用金(USD)
    @SerializedName("openingCashUsd") val openingCashUsd: Double,
    //备用金(KHR)
    @SerializedName("openingCashKHR") val openingCashKHR: Long,
    //备注
    @SerializedName("note") val note: String
)

//data class OpeningCashPaidLogVo(
//    //时间
//    @SerializedName("time") val time: String,
//    //备用金(USD)
//    @SerializedName("openingCashUsd") val openingCashUsd: Double,
//    //备用金(KHR)
//    @SerializedName("openingCashKHR") val openingCashKHR: Long,
//    //备注
//    @SerializedName("note") val note: String
//)

data class OrderVo(
    //时间
    @SerializedName("time") val time: String,
    //支付渠道
    @SerializedName("payMethod") val payMethod: String,
    //桌台名
    @SerializedName("tableName") val tableName: String,
    //订单金额
    @SerializedName("amount") val amount: Double
)

data class CreditCustomerVo(
    //时间
    @SerializedName("time") val time: String,
    //客户账号
    @SerializedName("telephone") val telephone: String,
    //金额
    @SerializedName("amount") val amount: Double
)