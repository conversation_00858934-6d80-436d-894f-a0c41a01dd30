package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.SalesItemOrdersDetailVo
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.databinding.ItemProductReportBinding



/**

 */
class SalesItemOrdersDetailAdapter(
    val list: ArrayList<SalesItemOrdersDetailVo>
) :
    RecyclerView.Adapter<SalesItemOrdersDetailAdapter.SalesItemOrdersDetailViewHolder>() {

    inner class SalesItemOrdersDetailViewHolder(val binding: ItemProductReportBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: SalesItemOrdersDetailVo, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {

                        tvItem.text = resource.itemName
                        tvQty.text = "${resource.itemNum}"
                        tvPrice.text = "${resource.itemTotalPrice}"

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        SalesItemOrdersDetailViewHolder(
            ItemProductReportBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: SalesItemOrdersDetailViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: List<SalesItemOrdersDetailVo>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}