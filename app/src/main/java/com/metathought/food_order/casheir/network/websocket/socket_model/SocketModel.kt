package com.metathought.food_order.casheir.network.websocket.socket_model


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.cart.CartInfoResponse

data class SocketRequest(
    @SerializedName("action")
    val action: String?,
    @SerializedName("data")
    val data: Any?
)

data class Ack(
    @SerializedName("id")
    val id: String
)

data class SocketModel(
    @SerializedName("cmd")
    val cmd: Int?,
    @SerializedName("code")
    val code: Int?,
    @SerializedName("msg")
    val msg: String?,
    @SerializedName("data")
    val data: Any?
)

data class SocketBody(
    @SerializedName("data")
    val data: Any?,
    @SerializedName("orderId")
    val orderId: String?,
    @SerializedName("id")
    val id: String?,
)

data class SocketGoods(
    @SerializedName("tableUuid")
    val tableUuid: String?,
    @SerializedName("cartInfoVo")
    val cartInfoVo: CartInfoResponse?,
    @SerializedName("diningStyle")
    val diningStyle: Int?
)