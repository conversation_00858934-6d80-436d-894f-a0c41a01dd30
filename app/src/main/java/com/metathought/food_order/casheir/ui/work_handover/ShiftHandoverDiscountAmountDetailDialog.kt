package com.metathought.food_order.casheir.ui.work_handover

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.databinding.DialogShiftHandoverDiscountAmountDetailBinding
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.ShiftHandoverDiscountAmountAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.AndroidEntryPoint

/**
 * 交班记录折扣金额详情
 */
@AndroidEntryPoint
class ShiftHandoverDiscountAmountDetailDialog : BaseDialogFragment() {
    private lateinit var type: String
    private var binding: DialogShiftHandoverDiscountAmountDetailBinding? = null
    private var id: Long? = null

    private val viewModel: ShiftHandoverViewModel by viewModels()
    private val adapter = ShiftHandoverDiscountAmountAdapter()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        binding = DialogShiftHandoverDiscountAmountDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }


    private fun initData() {
        id = arguments?.getLong(LOG_INFO, 0) ?: 0

        binding?.apply {
            layoutEmpty.root.isVisible = adapter.list.isEmpty()
            rvList.isVisible = adapter.list.isNotEmpty()
            llLisTitle.isVisible = adapter.list.isNotEmpty()
            rvList.adapter = adapter
            if (type == TYPE_SHIFT_ORDER) {
                topBar.setTitle(getString(R.string.print_report_discount_amount))
                tvTableTitle.text = getString(R.string.print_report_discount_amount)
            } else if (type == TYPE_DAILY_REPORT) {
                topBar.setTitle(getString(R.string.discount_details2))
                tvTableTitle.text = getString(R.string.discount_amount2)
            }

        }

        if (type == TYPE_SHIFT_ORDER) {
            viewModel.getShiftOrderDiscountInfo(id)
        } else if (type == TYPE_DAILY_REPORT) {
            viewModel.getDailyReportDiscountDetails(id)
        }


    }


    private fun initObserver() {
        viewModel.shiftOrderDiscountInfo.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {

                }

                is ApiResponse.Error -> {
                    binding?.apply {

                    }
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        adapter.setList(it.data.data)
                        layoutEmpty.root.isVisible = adapter.list.isEmpty()
                        rvList.isVisible = adapter.list.isNotEmpty()
                        llLisTitle.isVisible = adapter.list.isNotEmpty()
                    }
                }
            }
        }
    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val TAG = "ShiftHandoverDiscountAmountDetailDialog"
        private const val LOG_INFO = "log_info"
        public const val TYPE_SHIFT_ORDER = "ShiftOrder"
        public const val TYPE_DAILY_REPORT = "DailyReport"

        fun showDialog(
            fragmentManager: FragmentManager,
            id: Long?,
            type: String,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(id, type)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? ShiftHandoverDiscountAmountDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            id: Long?,
            type: String,
        ): ShiftHandoverDiscountAmountDetailDialog {
            val args = Bundle()
            args.putLong(LOG_INFO, id ?: 0)
            val fragment = ShiftHandoverDiscountAmountDetailDialog()
            fragment.arguments = args
            fragment.type = type
            return fragment
        }
    }

}
