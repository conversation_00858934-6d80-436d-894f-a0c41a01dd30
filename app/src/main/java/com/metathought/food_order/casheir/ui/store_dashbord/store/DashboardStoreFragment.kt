package com.metathought.food_order.casheir.ui.store_dashbord.store

import android.content.res.Resources
import android.os.Bundle
import android.text.TextPaint
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.CurStoreMoneyOrder
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreStatisticResponse
import com.metathought.food_order.casheir.databinding.FragmentDashboardStoreBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.ui.adapter.DashboardListAdapter
import com.metathought.food_order.casheir.ui.adapter.TopSalesAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.store_dashbord.StoreDashboardViewModel
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class DashboardStoreFragment : BaseFragment() {

    companion object {
        fun newInstance() = DashboardStoreFragment()
    }

    private var _binding: FragmentDashboardStoreBinding? = null
    private val binding get() = _binding
    private val viewModel: StoreDashboardViewModel by viewModels()
    private var startDateString: String = ""
    private var endDateString: String = ""
    private var type: String = "1"
    private var topSaleAdapter: TopSalesAdapter? = null
    private var dashboardListAdapter: DashboardListAdapter? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentDashboardStoreBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.uiStoreStatisticState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        showProgress()
                }
                if (it.showEnd) {
                    dismissProgress()
                    if (it.isRefresh != false) {
                        dashboardListAdapter?.replaceData(arrayListOf())
                        it.showSuccess?.let {
                            setData(it)
                        }

                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    dismissProgress()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    dismissProgress()

                    if (it.isRefresh != false) {
                        ArrayList(
                            response.storeMoneyOrderDetailList?.storeMoneyOrderDetailVos ?: listOf()
                        )?.let { it1 ->
                            dashboardListAdapter?.replaceData(
                                it1
                            )
                        }
                        setData(response)
                        refreshLayout.finishRefresh()

                    } else {
                        ArrayList(
                            response.storeMoneyOrderDetailList?.storeMoneyOrderDetailVos ?: listOf()
                        ).let { it1 ->
                            dashboardListAdapter?.addData(
                                it1
                            )
                        }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }
    }


    private fun initView() {
        binding?.apply {

            arguments?.let {
                startDateString = it.getString("startDateString") ?: ""
                endDateString = it.getString("endDateString") ?: ""
                type = it.getString("type") ?: "1"
            }


            pieChartView.setNoDataText(getString(R.string.no_chart_data_available))
            setTitle()
            topSaleAdapter =
                context?.let { TopSalesAdapter(ArrayList(viewModel.getDefaultTopSaleList()), it) }
            recyclerViewTopSale.adapter = topSaleAdapter
            dashboardListAdapter = DashboardListAdapter(arrayListOf()) {}
            recyclerviewDashboardRevenue.adapter = dashboardListAdapter
        }
    }


    override fun onLoad() {
        super.onLoad()
        context?.let {
            getStoreData()
        }
    }


    private fun getStoreData(isRefresh: Boolean? = null) {
        viewModel.getStoreStatistic(isRefresh, type, startDateString, endDateString)
    }

    private fun setData(storeData: StoreStatisticResponse) {
        binding?.apply {
            layoutRevenueAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.turnover?.decimalFormatTwoDigitZero()}"
            layoutUnPaidAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.waitPayMoney?.decimalFormatTwoDigitZero()}"
            layoutActualRevenue.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.payMoney?.decimalFormatTwoDigitZero()}"
            layoutUnsettledAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.waitSettledMoney?.decimalFormatTwoDigitZero()}"
            layoutSettledAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.settledMoney?.decimalFormatTwoDigitZero()}"
            layoutOrderNumbers.tvValue.text = "${storeData.curStoreMoneyOrder?.orderNum}"
            layoutUnPaidOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.waitPayOrderNum}"
            layoutPaidOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.payOrderNum}"
            layoutRefundsOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.refundOrderNum}"
            layoutRefundAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.refundMoney?.decimalFormatTwoDigitZero()}"
            // yesterday

            layoutRevenueAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.turnover?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutUnPaidAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.waitPayMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutActualRevenue.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.payMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutUnsettledAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.waitSettledMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutSettledAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.settledMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutOrderNumbers.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.orderNum ?: 0}"
            layoutUnPaidOrders.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.waitPayOrderNum ?: 0}"
            layoutPaidOrders.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.payOrderNum ?: 0}"
            layoutRefundsOrders.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.refundOrderNum ?: 0}"
            layoutRefundAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.refundMoney?.decimalFormatTwoDigitZero() ?: 0.00}"

            layoutRevenueAmount.tvValueYesterday.isVisible = type == "1"
            layoutUnPaidAmount.tvValueYesterday.isVisible = type == "1"
            layoutActualRevenue.tvValueYesterday.isVisible = type == "1"
            layoutUnsettledAmount.tvValueYesterday.isVisible = type == "1"
            layoutSettledAmount.tvValueYesterday.isVisible = type == "1"
            layoutOrderNumbers.tvValueYesterday.isVisible = type == "1"
            layoutUnPaidOrders.tvValueYesterday.isVisible = type == "1"
            layoutPaidOrders.tvValueYesterday.isVisible = type == "1"
            layoutRefundsOrders.tvValueYesterday.isVisible = type == "1"
            layoutRefundAmount.tvValueYesterday.isVisible = type == "1"
            storeData.salesRankingList?.let {
                if (it.isEmpty())
                    topSaleAdapter?.updateItems(ArrayList(viewModel.getDefaultTopSaleList()))
                else
                    topSaleAdapter?.updateItems(ArrayList(it))
            }
            storeData.curStoreMoneyOrder?.let { showPieChart(it) }
        }
    }

    private fun showPieChart(storeData: CurStoreMoneyOrder) {
        binding?.pieChartView?.apply {
            setTouchEnabled(false)
            holeRadius = 75f
            rotationAngle = 90f
            description?.isEnabled = false
            centerText = getString(R.string.payment_nmethod)
            setCenterTextColor(ContextCompat.getColor(context, R.color.black))
            setCenterTextSize(10f)
            legend?.isEnabled = false
            setDrawRoundedSlices(true)
        }
        val pieEntries = ArrayList<PieEntry>()
        val label = "type"

        val online = storeData.onlinePay?.toFloat() ?: 0.0f
        val balance = storeData.balance?.toFloat() ?: 0.0f
        val cash = storeData.cash?.toFloat() ?: 0.0f
        val credit = storeData.credit?.toFloat() ?: 0.0f
        //initializing colors for the entries
        val colors = ArrayList<Int>()
        if (online != 0.0f || balance != 0.0f || cash != 0.0f || credit != 0.0f) {
            //OnlinePayment
            colors.add("#0F9D58".toColorInt())
            //Balance
            colors.add("#FF7F00".toColorInt())
            //Cash
            colors.add("#2C99FF".toColorInt())
            //Credit
            colors.add("#A968FD".toColorInt())

            pieEntries.add(PieEntry(online, ""))
            pieEntries.add(PieEntry(balance, ""))
            pieEntries.add(PieEntry(cash, ""))
            pieEntries.add(PieEntry(credit, ""))

        } else {
            colors.add("#E7F5EE".toColorInt())
            pieEntries.add(PieEntry(1.0f, ""))
        }
        //border: 18px solid #E7F5EE
        //collecting the entries with label name
        val pieDataSet = PieDataSet(pieEntries, label)
        pieDataSet.valueTextSize = 12f
        pieDataSet.colors = colors
        pieDataSet.setDrawValues(false)
        val pieData = PieData(pieDataSet)

        binding?.apply {
            pieChartView.setData(pieData)
            pieChartView.animateY(1000)
            pieChartView.invalidate()
            tvCashValue.text = "$${storeData.cash?.decimalFormatTwoDigitZero()}"
            tvBalanceValue.text = "$${storeData.balance?.decimalFormatTwoDigitZero()}"
            tvOnlinePaymentValue.text = "$${storeData.onlinePay?.decimalFormatTwoDigitZero()}"
            tvCreditValue.text = "$${storeData.credit?.decimalFormatTwoDigitZero()}"
        }
    }


    private fun setTitle() {
        binding?.apply {
            layoutRevenueAmount.tvTitle.text = getText(R.string.revenue_amount)
            layoutUnPaidAmount.tvTitle.text = getString(R.string.unpaid_amount)
            layoutActualRevenue.tvTitle.text = getString(R.string.actual_revenue)
            layoutUnsettledAmount.tvTitle.text = getString(R.string.unsettled_amount)
            layoutSettledAmount.tvTitle.text = getString(R.string.settled_amount)
            layoutOrderNumbers.tvTitle.text = getString(R.string.order_numbers)
            layoutUnPaidOrders.tvTitle.text = getString(R.string.unpaid_orders)
            layoutPaidOrders.tvTitle.text = getString(R.string.paid_orders)
            layoutRefundsOrders.tvTitle.text = getString(R.string.refund_orders)
            layoutRefundAmount.tvTitle.text = getString(R.string.refund_amount)
        }
    }


    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getStoreData(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getStoreData(false)
                }

            })

            layoutRevenueAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutRevenueAmount.tvTitleCue,
                    getString(R.string.revenue_amount_tip)
                )
            }

            layoutUnPaidAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutUnPaidAmount.tvTitleCue,
                    getString(R.string.unpaid_amount_tip)
                )
            }

            layoutActualRevenue.root.setOnClickListener {
                showSmartTipPopup(
                    layoutActualRevenue.tvTitleCue,
                    getString(R.string.actual_revenue_tip)
                )
            }

            layoutUnsettledAmount.tvTitleCue.isVisible = false
//            layoutUnsettledAmount.root.setOnClickListener {
////                showSmartTipPopup(
////                    layoutUnsettledAmount.tvTitleCue,
////                    getString(R.string.unsettled_amount_tip)
////                )
//            }
            layoutSettledAmount.tvTitleCue.isVisible = false
//            layoutSettledAmount.root.setOnClickListener {
//                showSmartTipPopup(
//                    layoutSettledAmount.tvTitleCue,
//                    getString(R.string.settled_amount_tip)
//                )
//            }

            layoutOrderNumbers.root.setOnClickListener {
                showSmartTipPopup(
                    layoutOrderNumbers.tvTitleCue,
                    getString(R.string.order_numbers_tip)
                )
            }

            layoutUnPaidOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutUnPaidOrders.tvTitleCue,
                    getString(R.string.unpaid_orders_tip)
                )
            }

            layoutPaidOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutPaidOrders.tvTitleCue,
                    getString(R.string.paid_orders_tip)
                )
            }

            layoutRefundsOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutRefundsOrders.tvTitleCue,
                    getString(R.string.refund_orders_tip)
                )
            }

            layoutRefundAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutRefundAmount.tvTitleCue,
                    getString(R.string.refund_amount_tip)
                )
            }

            llChart.setOnClickListener {
                showSmartTipPopup(
                    tvPaymentMethodTitleCue,
                    getString(R.string.payment_method_tip)
                )
            }

            pieChartView.setOnClickListener {
                showSmartTipPopup(
                    tvPaymentMethodTitleCue,
                    getString(R.string.payment_method_tip)
                )
            }
        }
    }

    /**
     * 显示智能提示弹窗，根据可用空间自动选择显示位置
     * - 左右空间都足够时，显示在中间
     * - 左侧空间不足时，显示在右侧
     * - 右侧空间不足时，显示在左侧
     *
     * @param anchorView 锚点视图，弹窗将显示在此视图附近
     * @param message 要显示的提示信息
     */
    private fun showSmartTipPopup(anchorView: View, message: String) {
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val anchorX = location[0]
        val anchorY = location[1]
        val anchorWidth = anchorView.width

        // 获取屏幕宽度
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenWidth = displayMetrics.widthPixels

// 估算消息宽度 (使用TextPaint计算实际文本宽度)
        val textPaint = TextPaint()
        textPaint.textSize =
            resources.getDimension(R.dimen._12ssp) // 使用与CustomBubbleAttachPopup相同的文本大小
        val estimatedMessageWidth = textPaint.measureText(message).toInt() +
                resources.getDimensionPixelSize(R.dimen.fragment_horizontal_margin) * 2 // 添加左右内边距
        val halfMessageWidth = estimatedMessageWidth / 2

        // 计算锚点中心位置
        val anchorCenterX = anchorX + (anchorWidth / 2)

        // 判断左右空间是否足够
        val hasEnoughSpaceOnLeft = anchorCenterX > halfMessageWidth
        val hasEnoughSpaceOnRight = (screenWidth - anchorCenterX) > halfMessageWidth

        // 创建XPopup构建器
        val popupBuilder = XPopup.Builder(requireContext())
            .hasShadowBg(false)
            .isTouchThrough(true)
            .isDestroyOnDismiss(true)

        // 根据空间决定显示位置
        if (hasEnoughSpaceOnLeft && hasEnoughSpaceOnRight) {
            // 左右空间都足够，显示在中间
            popupBuilder.popupPosition(PopupPosition.Top) // 使用Top位置，但实际上是居中的
            // 设置居中显示
            popupBuilder.isCenterHorizontal(true)
        } else if (hasEnoughSpaceOnRight) {
            // 右侧空间足够，显示在右侧
            popupBuilder.popupPosition(PopupPosition.Right)
        } else {
            // 默认显示在左侧
            popupBuilder.popupPosition(PopupPosition.Left)
        }

        // 显示弹窗
        popupBuilder
            .atView(anchorView)
//            .atPoint(PointF(anchorCenterX.toFloat(), anchorY.toFloat()))
            .asCustom(CustomBubbleAttachPopup(requireContext(), message, 5000))
            .show()
    }

}