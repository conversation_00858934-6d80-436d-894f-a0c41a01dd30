package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeTier
import com.metathought.food_order.casheir.databinding.RechargeLevelItemBinding
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber


class RechargeLevelAdapter : RecyclerView.Adapter<RechargeLevelAdapter.ViewHolder>() {

    private val list = mutableListOf<ConsumerRechargeTier>()
    private var isCustomMembershipAmount: Boolean = false    //	是否允许自定义会员充值金额
    private var customConsumerRechargeTier: ConsumerRechargeTier? = null
    private var selectIndex = -1    //	当前选择的充值等级索引

    var itemClick: ((ConsumerRechargeTier?, Int) -> Unit)? = null
    var itemCouponClick: ((ConsumerRechargeTier?, Int) -> Unit)? = null
    var editCustomCouponClick: ((ConsumerRechargeTier?, Int) -> Unit)? = null

    fun setCustomMembershipAmount(customConsumerRechargeTier: ConsumerRechargeTier) {
        this.customConsumerRechargeTier = customConsumerRechargeTier
        selectIndex = list.size
        notifyItemChanged(selectIndex)
    }

    fun clearSelectIndex() {
        val lastSelectIndex = selectIndex
        selectIndex = -1
        notifyItemChanged(lastSelectIndex)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            RechargeLevelItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        if (isCustomMembershipAmount) {
            return list.size + 1
        }
        return list.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (isCustomMembershipAmount && position == list.size) {
            holder.bind(customConsumerRechargeTier, position)
        } else {
            holder.bind(list[position], position)
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(isCustomMembershipAmount: Boolean, list: List<ConsumerRechargeTier>?) {
        this.isCustomMembershipAmount = isCustomMembershipAmount
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            val selectIndex = list.indexOfFirst { it.isDefault ?: false }
            selectLevel(selectIndex)
            notifyDataSetChanged()
        }
    }

    fun getSelectLevel(): ConsumerRechargeTier? {
        return if (selectIndex == -1) null
        else if (selectIndex < list.size) {
            list[selectIndex]
        } else {
            customConsumerRechargeTier
        }
    }

    private fun selectLevel(position: Int) {
        if (position != -1 && selectIndex != position) {
            val lastSelectIndex = selectIndex
            selectIndex = position
            notifyItemChanged(position)
            if (lastSelectIndex != -1) {
                notifyItemChanged(lastSelectIndex)
            }
            if (position < list.size) {
                itemClick?.invoke(list[position], position)
            } else {
                itemClick?.invoke(customConsumerRechargeTier, position)
            }
        }
    }

    inner class ViewHolder(val binding: RechargeLevelItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        selectLevel(it)
                    }
                }
            }
            binding.tvGiftCoupon.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        if (it != -1) {
                            if (it < list.size) {
                                itemCouponClick?.invoke(list[it], it)
                            } else {
                                itemCouponClick?.invoke(customConsumerRechargeTier, it)
                            }
                        }
                    }
                }
            }
            binding.tvEditRecharge.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        if (it != -1) {
                            editCustomCouponClick?.invoke(customConsumerRechargeTier, it)
                        }
                    }
                }
            }
        }

        fun bind(resource: ConsumerRechargeTier?, position: Int) {
            binding.apply {
                val context = root.context
                val isSelected = position == selectIndex
                root.isSelected = isSelected
                tvSelectMark.isVisible = isSelected
                tvRechargeAmount.setTextColor(
                    ContextCompat.getColor(
                        itemView.context,
                        if (isSelected) R.color.primaryColor else R.color.black80
                    )
                )

                tvRechargeAmount.text = resource?.amount?.priceFormatTwoDigitZero1()
                tvGiftAmount.text =
                    context.getString(R.string.free) + resource?.giftAmount?.priceFormatTwoDigitZero1()
                tvGiftAmount.isVisible =
                    resource?.giftAmount != null && resource.giftAmount?.isZero() == false
                tvGiftCoupon.isVisible = !resource?.rechargeTierCouponTemplateList.isNullOrEmpty()
                vLine.isVisible = tvGiftAmount.isVisible && tvGiftCoupon.isVisible

                tvEditRecharge.isVisible =
                    isCustomMembershipAmount && position == list.size && resource != null

                val isShowCustomCoupon = resource == null && isCustomMembershipAmount
                clRechargeLevelItem.isVisible = !isShowCustomCoupon
                tvCustomRecharge.isVisible = isShowCustomCoupon

            }
        }

    }
}