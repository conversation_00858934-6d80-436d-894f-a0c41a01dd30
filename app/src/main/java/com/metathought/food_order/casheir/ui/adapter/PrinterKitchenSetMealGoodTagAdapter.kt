package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterFeedBinding
import com.metathought.food_order.casheir.databinding.ItemPrinterKitchenFeedBinding
import com.metathought.food_order.casheir.databinding.ItemPrinterKitchenMealSetGoodBinding
import com.metathought.food_order.casheir.databinding.ItemPrinterKitchenMealSetGoodTagBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.utils.DisplayUtils
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2025/01/10 17:30
 * @description  厨打小票按80 样式打
 */
class PrinterKitchenSetMealGoodTagAdapter(
    val list: List<GoodsTagItem>,
    val listEn: List<GoodsTagItem>,
    val listKm: List<GoodsTagItem>,
    val templateItem: PrintTamplateResponseItem
) : RecyclerView.Adapter<PrinterKitchenSetMealGoodTagAdapter.PrinterFeedViewHolder>() {

    inner class PrinterFeedViewHolder(val binding: ItemPrinterKitchenMealSetGoodTagBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodsTagItem, position: Int) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
//                tvTagNameEn.text = resource.name
                localeList.forEachIndexed { index, locale ->

                    val name = getNameByLocale(locale, position)
                    if (index == 0) {
                        tvTagNameEn.text = name
                    } else if (index == 1) {
                        tvTagNameKm.text = name
                        tvTagNameKm.isGone =
                            tvTagNameKm.text == tvTagNameEn.text || name.isNullOrEmpty()
                    }
                }
                if (templateItem.informationShow != null) {
                    tvTagNameEn.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                    tvTagNameKm.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                }
            }
        }
    }

    fun getNameByLocale(locale: Locale, position: Int): String? {
        val name = list[position].name
        val nameEn = if (position < listEn.size) listEn[position].name else ""
        val nameKm = if (position < listKm.size) listKm[position].name else ""
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (nameKm.isNullOrEmpty()) {
                name
            } else {
                nameKm
            }
        } else if (locale == Locale.CHINESE) {
            name
        } else if (locale == Locale.ENGLISH) {
            if (nameEn.isNullOrEmpty()) {
                name
            } else {
                nameEn
            }
        } else {
            name
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterKitchenMealSetGoodTagBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterKitchenSetMealGoodTagAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

}