package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import java.util.Locale
import java.util.Objects

data class Feed(

//    val createTime: String,
//    val goodsId: String,
//    val saasStoreId: String,
//    val updateTime: String,
    @SerializedName("alreadyNum")
    var alreadyNum: Int? = null,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("nameEn")
    var nameEn: String?,
    @SerializedName("nameKh")
    var nameKh: String?,
    @SerializedName("restrictNum")
    var restrictNum: Int? = 0,
    @SerializedName("sum")
    val sum: Double?
) {

    fun getNameByLocale(locale: Locale): String? {
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (nameKh.isNullOrEmpty()) {
                name
            } else {
                nameKh
            }
        } else if (locale == Locale.CHINESE) {
            name
        } else if (locale == Locale.ENGLISH) {
            if (nameEn.isNullOrEmpty()) {
                name
            } else {
                nameEn
            }
        } else {
            name
        }
    }


    override fun hashCode(): Int {
        return Objects.hash(id)
    }

    override fun equals(other: Any?): Boolean {
        if (other === this) return true
        if (other !is Feed) return false
        return other.id == this.id
    }
}