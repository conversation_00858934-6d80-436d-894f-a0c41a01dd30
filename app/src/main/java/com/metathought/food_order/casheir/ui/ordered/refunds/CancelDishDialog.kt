//package com.metathought.food_order.casheir.ui.ordered.refunds
//
//import android.content.Context
//import android.hardware.display.DisplayManager
//import android.os.Bundle
//import android.util.DisplayMetrics
//import android.view.Display
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import androidx.core.view.isVisible
//import androidx.core.widget.addTextChangedListener
//import androidx.fragment.app.DialogFragment
//import androidx.fragment.app.FragmentManager
//import androidx.recyclerview.widget.RecyclerView
//import com.metathought.food_order.casheir.R
//import com.metathought.food_order.casheir.constant.DiningStyleEnum
//import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
//import com.metathought.food_order.casheir.databinding.DialogCancelDishBinding
//import com.metathought.food_order.casheir.extension.getScrollPosition
//import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
//import com.metathought.food_order.casheir.extension.setEnableWithAlpha
//import com.metathought.food_order.casheir.helper.OrderHelper
//import com.metathought.food_order.casheir.ui.adapter.PatialRefundsOrderedItemAdapter
//import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
//import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
//import dagger.hilt.android.AndroidEntryPoint
//
//@AndroidEntryPoint
//class CancelDishDialog : DialogFragment() {
//    private var binding: DialogCancelDishBinding? = null
//    private var onConfirmClickListener: ((OrderedInfoResponse, String, Boolean) -> Unit)? = null
//
//    private var currentOrderedInfo: OrderedInfoResponse? = null
//    private var adapter: PatialRefundsOrderedItemAdapter? = null
//
//
//    private var orderedScreen: SecondaryScreenUI? = null
//
//    /**
//     * 将退还商品自动入库 true 是 false 否
//     */
//    private var autoInStock = false
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        savedInstanceState: Bundle?
//    ): View? {
//        binding = DialogCancelDishBinding.inflate(layoutInflater)
//        return binding?.root
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
//        dialog?.setCancelable(true)
//        initData()
//        initListener()
//        initObserver()
//    }
//
//
//    override fun onResume() {
//        super.onResume()
////        context?.let {
////            val displayMetrics = getDisplayMetrics(it)
////            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
////            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
////            dialog?.window?.setLayout(screenWidth, screenHeight)
////        }
//    }
//
//
//    private fun initObserver() {
//
//    }
//
//    private fun initData() {
//        binding?.apply {
//            context?.let {
//                if (currentOrderedInfo?.showAutoInStockBtn == true) {
//                    llAutoInStore.isVisible = true
//                    autoInStock = true
//                    updateSwitch()
//                }
//
//                tvCancelNum.addTextChangedListener {
//                    if (it.toString() == "0") {
//                        btnConfirm.setEnableWithAlpha(false)
//                    } else {
//                        btnConfirm.setEnableWithAlpha(true)
//                    }
//                }
//                currentOrderedInfo?.let { orderDetail ->
//                    orderDetail.refundGoodsJson?.forEach { refundGoods ->
//                        orderDetail.goods?.filter { it.getHash() == refundGoods.getHash() }
//                            ?.forEach { goods ->
//                                goods.alreadyRefundNum = refundGoods.num
//                            }
//                    }
//                    orderDetail.goods?.let { it1 ->
//                        val filterList = it1.filter { it.getCanRefundNum() > 0 }
//                        val list = OrderHelper.sortingGoodsWithMergeOrder(
//                            requireContext(),
//                            orderDetail.mergeOrderIds,
//                            null,
//                            ArrayList(filterList)
//                        ) ?: arrayListOf()
//                        adapter = PatialRefundsOrderedItemAdapter(
//                            list
//                        ) {
//                            //副屏更新列表
//                            orderedScreen?.getCancelDishDialog()?.run {
//                                notifyAdapter(it)
//                            }
//                            calculatePrice()
//                        }
//                        recyclerOrderedFood.adapter = adapter
//
//
//                        //副屏取消退菜dialog
//                        orderedScreen?.getCancelDishDialog()?.run {
//                            initData(list)
//                        }
//
//                        calculatePrice()
//                    }
//                }
//
//            }
//        }
//    }
//
//    private fun initListener() {
//        binding?.apply {
//
//            ivSwitch.setOnClickListener {
//                autoInStock = !autoInStock
//                updateSwitch()
//            }
//
//            btnClose.setOnClickListener {
//                //副屏取消弹窗
////                orderedScreen?.dismissCancelDishDialog()
//                dismissAllowingStateLoss()
//            }
//            btnCancel.setOnClickListener {
//                //副屏取消弹窗
////                orderedScreen?.dismissCancelDishDialog()
//                dismissAllowingStateLoss()
//            }
//            btnConfirm.setOnClickListener {
//                currentOrderedInfo?.let {
//                    onConfirmClickListener?.invoke(
//                        it,
//                        tvCancelAmount.text.toString(),
//                        autoInStock
//                    )
//                }
//                //副屏取消弹窗
////                orderedScreen?.dismissCancelDishDialog()
//                dismissAllowingStateLoss()
//            }
//            //主屏联动副屏滑动
//            recyclerOrderedFood.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                    super.onScrolled(recyclerView, dx, dy)
//                    recyclerView.layoutManager?.getScrollPosition(dy)?.let {
//                        if (it != -1) {
//                            orderedScreen?.getCancelDishDialog()?.run {
//                                updateRecyclerOrderedFood(it)
//                            }
//                        }
//                    }
//                }
//            })
//        }
//    }
//
//    private fun updateSwitch() {
//        binding?.apply {
//            ivSwitch.setImageResource(if (autoInStock) R.drawable.icon_switch_open else R.drawable.icon_switch_close)
//        }
//    }
//
//    private fun calculatePrice() {
//        binding?.apply {
//            var qtyRefund = 0
//            var totalPriceProduct = 0L
//            var totalVatPriceProduct = 0L
//            var totalServicePriceProduct = 0L
//            var totalCancelPackingAmount = 0
//            var isHasNeedWeight = false
//
//            var unWeightNum = 0
//            var unWeightServiceWhiteGoods = 0
//            var unWeightVatWhiteGoods = 0
//
//            currentOrderedInfo?.let {
//                it.goods?.let { goods ->
//                    for (good in goods) {
//                        qtyRefund += good.refundsNum ?: 0
//                        totalPriceProduct += good.totalCancelGoodsPrice()
//                        totalVatPriceProduct += good.totalCancelGoodsVatPrice()
//                        totalCancelPackingAmount += good.totalCancelPackingFee()
//                        totalServicePriceProduct += good.totalCancelGoodsServicePrice()
//                        if (good.isHasNeedWeight() && (good.refundsNum ?: 0) > 0) {
//                            unWeightNum += 1
//                            isHasNeedWeight = true
//                            if (good.vatWhitelisting == true) {
//                                unWeightVatWhiteGoods += 1
//                            }
//
//                            if (good.serviceChargeWhitelisting == true) {
//                                unWeightServiceWhiteGoods += 1
//                            }
//                        }
//                    }
//                }
//            }
//
//
//            tvCancelNum.text = qtyRefund.toString()
//
//            if (currentOrderedInfo?.diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
//                layoutCancelServiceAmount.isVisible = false
//                layoutCancelPackingAmount.isVisible = true
//                tvCancelPackingAmount.text =
//                    totalCancelPackingAmount.priceFormatTwoDigitZero2()
//                //外带没有服务费
//                totalServicePriceProduct = 0
//            } else {
////                layoutCancelServiceAmount.isVisible = true
//                layoutCancelPackingAmount.isVisible = false
//                tvCancelServiceAmount.text =
//                    totalServicePriceProduct.priceFormatTwoDigitZero2()
//                layoutCancelServiceAmount.isVisible =
//                    MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0
//            }
//
//            tvCancelVat.text = totalVatPriceProduct.priceFormatTwoDigitZero2()
//            tvCancelAmount.text = totalPriceProduct.priceFormatTwoDigitZero2()
//            tvCancelTotalAmount.text =
//                totalPriceProduct.plus(totalVatPriceProduct).plus(totalServicePriceProduct)
//                    .plus(totalCancelPackingAmount).priceFormatTwoDigitZero2()
//            if (isHasNeedWeight) {
//
//                tvCancelAmount.text = getString(R.string.to_be_weighed)
//                tvCancelTotalAmount.text =
//                    getString(R.string.to_be_weighed)
//
//                if (unWeightNum > unWeightVatWhiteGoods) {
//                    tvCancelVat.text = getString(R.string.to_be_weighed)
//                }
//                if (unWeightNum > unWeightServiceWhiteGoods) {
//                    tvCancelServiceAmount.text = getString(R.string.to_be_weighed)
//                }
//            }
//
//            layoutCancelVat.isVisible =
//                MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage() != 0
//
//
//            orderedScreen?.getCancelDishDialog()?.run {
//                setCancelNum(tvCancelNum.text.toString())
//                setCancelAmount(tvCancelAmount.text.toString())
//                setCancelVat(tvCancelVat.text.toString(), layoutCancelVat.isVisible)
//                setCancelServiceFee(
//                    tvCancelServiceAmount.text.toString(),
//                    layoutCancelServiceAmount.isVisible
//                )
//                setCancelPackingAmount(
//                    tvCancelPackingAmount.text.toString(),
//                    layoutCancelPackingAmount.isVisible
//                )
//                setCancelTotalAmount(
//                    tvCancelTotalAmount.text.toString()
//                )
//            }
//        }
//    }
//
//    override fun onDestroy() {
//        orderedScreen?.dismissCancelDishDialog()
//        super.onDestroy()
//    }
//
//    companion object {
//        private const val AVAILABLE_TABLE_LIST = "AVAILABLE_TABLE_LIST"
//
//        fun showDialog(
//            fragmentManager: FragmentManager, currentOrderedInfo: OrderedInfoResponse,
////            orderedScreen: OrderedInfoScreenUI? = null,
//            orderedScreen: SecondaryScreenUI? = null,
//            onConfirmClickListener: ((OrderedInfoResponse?, String, Boolean) -> Unit),
//        ) {
//            var fragment = fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST)
//            if (fragment != null) return
//            currentOrderedInfo.goods?.forEach { it.refundsNum = 0 }
//            fragment = newInstance(
//                onConfirmClickListener = onConfirmClickListener,
//                orderedScreen = orderedScreen,
//                currentOrderedInfo = currentOrderedInfo
//            )
//            fragment.show(fragmentManager, AVAILABLE_TABLE_LIST)
//        }
//
//        fun dismissDialog(fragmentManager: FragmentManager) {
//            val fragment =
//                fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST) as? CancelDishDialog
//            fragment?.dismissAllowingStateLoss()
//        }
//
//        private fun newInstance(
//            currentOrderedInfo: OrderedInfoResponse,
////            orderedScreen: OrderedInfoScreenUI? = null,
//            orderedScreen: SecondaryScreenUI? = null,
//            onConfirmClickListener: ((OrderedInfoResponse?, String, Boolean) -> Unit),
//        ): CancelDishDialog {
//            val args = Bundle()
//            val fragment = CancelDishDialog()
//            fragment.onConfirmClickListener = onConfirmClickListener
//            fragment.currentOrderedInfo = currentOrderedInfo
//            fragment.arguments = args
//            fragment.orderedScreen = orderedScreen
//            return fragment
//        }
//    }
//
//
//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }
//
//}
