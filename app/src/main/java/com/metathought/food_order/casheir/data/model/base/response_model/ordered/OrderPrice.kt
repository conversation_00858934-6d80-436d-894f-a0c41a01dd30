package com.metathought.food_order.casheir.data.model.base.response_model.ordered

 import com.metathought.food_order.casheir.constant.WholeDiscountCalculationTypeEnum
import com.metathought.food_order.casheir.constant.WholeReduceType
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import java.math.BigDecimal

data class OrderPrice(
//    val goodsPrice: BigDecimal? = null,
//    val orderGoods: BigDecimal? = null,
    var noParticipatingDiscountsAmount: BigDecimal? = null,
    var participatingWholeDiscountsAmount: BigDecimal? = null,
    val payableAmountNoVat: BigDecimal? = null,
    val serviceChargePercentage: BigDecimal? = null,
    val subTotal: BigDecimal? = null,
    var total: BigDecimal? = null,
    val totalChangeUnitAmount: BigDecimal? = null,
    val totalCouponActivityAmount: BigDecimal? = null,
    var totalCouponAmount: BigDecimal? = null,
    val totalCrossedAmount: BigDecimal? = null,
    val totalDiscountAmount: BigDecimal? = null,
    val totalOnItemDiscountAmount: BigDecimal? = null,
    val totalOriginalAmount: BigDecimal? = null,
    val totalPackingAmount: BigDecimal? = null,
    val totalServiceChargeAmount: BigDecimal? = null,
    var totalWholeDiscountAmount: BigDecimal? = null,
    var totalCommissionAmount: BigDecimal? = null,
    val type: Int? = null,
    var vat: BigDecimal? = null,
    val vatPercentage: BigDecimal? = null,
//    var wholeDiscountReduce: WholeDiscountReduce? = null
) {

    //获取小计 放大100
    fun getSubTotalToLong(): Long {
        return (subTotal ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    //获取打包费 放大100
    fun getTotalPackingAmountToLong(): Long {
        return (totalPackingAmount ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    //获取不带vat 的总金额 放大100
    fun getPayableAmountNoVatToLong(): Long {
        return (payableAmountNoVat ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    //获取没减优惠券 优惠金额前的价格
    fun getNoCouponPayableAmountNoVatToLong(): Long {
        return (payableAmountNoVat ?: BigDecimal.ZERO).plus(totalCouponAmount ?: BigDecimal.ZERO)
            .plus(totalCouponActivityAmount ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    //获取服务费 放大100
    fun getTotalServiceChargeAmountToLong(): Long {
        return (totalServiceChargeAmount ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    //获取服务费 放大100
    fun getTotalWholeDiscountAmountToLong(): Long {
        return (totalWholeDiscountAmount ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    fun getVatToLong(): Long {
        return (vat ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    fun getTotalToLong(): Long {
        return (total ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    fun getVatPercentage(): Int {
        return (vatPercentage ?: BigDecimal.ZERO).times(BigDecimal(100)).toInt()
    }

    //获取优惠活动减免金额
    fun getTotalCouponActivityAmountToLong(): Long {
        return (totalCouponActivityAmount ?: BigDecimal.ZERO).times(BigDecimal(100)).toLong()
    }

    /**
     * 获取佣金
     *
     * @return
     */
    fun getCommissionToLong(): Long {
        return (totalCommissionAmount ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toLong()
    }

    fun getServiceChargePercentage(): Int {
        return (serviceChargePercentage ?: BigDecimal.ZERO).times(BigDecimal(100.0)).toInt()
    }


    //选取优惠券以后金额重新计算
    fun reCalculateAfterChooseCoupon() {

        vat = ((subTotal ?: BigDecimal.ZERO).plus(totalServiceChargeAmount ?: BigDecimal.ZERO)
            .plus(totalPackingAmount ?: BigDecimal.ZERO)
            .minus(totalCouponActivityAmount ?: BigDecimal.ZERO)
            .minus(totalCouponAmount ?: BigDecimal.ZERO)).times(vatPercentage ?: BigDecimal.ZERO)
            .halfUp(2)

        //整单可减免金额= 小计+服务费+打包费-优惠活动金额-优惠券金额-不参与折扣金额
        participatingWholeDiscountsAmount =
            (subTotal ?: BigDecimal.ZERO).plus(totalServiceChargeAmount ?: BigDecimal.ZERO)
                .plus(totalPackingAmount ?: BigDecimal.ZERO)
                .minus(totalCouponActivityAmount ?: BigDecimal.ZERO)
                .minus(totalCouponAmount ?: BigDecimal.ZERO)
                .minus(noParticipatingDiscountsAmount ?: BigDecimal.ZERO)

        if (MainDashboardFragment.CURRENT_USER?.getWholeDiscountCalculationType()?.id == WholeDiscountCalculationTypeEnum.INCLUDE_VAT.id) {
            //如果包含vat 则 整单可减免金额= 小计+服务费+打包费-优惠活动金额-优惠券金额+vat
            participatingWholeDiscountsAmount =
                participatingWholeDiscountsAmount?.plus(vat ?: BigDecimal.ZERO)
        }
        //  .plus(vat ?: BigDecimal.ZERO)

        total = (subTotal ?: BigDecimal.ZERO).plus(totalServiceChargeAmount ?: BigDecimal.ZERO)
            .plus(totalPackingAmount ?: BigDecimal.ZERO)
            .minus(totalCouponActivityAmount ?: BigDecimal.ZERO)
            .minus(totalCouponAmount ?: BigDecimal.ZERO)
            .plus(vat ?: BigDecimal.ZERO)

    }


}

data class WholeDiscountReduce(
    /**
     * 类型 0:默认 1.销售价 2.会员 3:销售价+会员价 4.配置折扣活动 5.USD自定义整单减免 6.KHR自定义整单减免
     */
    var reduceType: Int? = null,

    /**
     * 减免折扣率
     */
    var reduceRate: BigDecimal? = null,

    /**
     * 减免折扣率 对应的减免金额
     */
    var reduceAmount: BigDecimal? = null,

    /**
     * 减免折扣率 对应的vip减免金额
     */
    var reduceVipAmount: BigDecimal? = null,

    /**
     * 减免金额（美元）
     */
    var reduceDollar: BigDecimal? = null,

    /**
     * 减免金额（vip美元）
     */
    var reduceVipDollar: BigDecimal? = null,

    /**
     * 减免金额（瑞尔）
     */
    var reduceKhr: BigDecimal? = null,

    /**
     * 减免金额（vip瑞尔）
     */
    var reduceVipKhr: BigDecimal? = null,

    /**
     * 整单减免原因
     */
    var reduceReason: String? = null,


    /**
     * 以下字段 用于和vat 摊整单减免后展示
     */
    /**
     * 减免折扣率 对应的减免金额(分摊后展示)
     */
    var itemReduceAmount: BigDecimal? = null,
    /**
     * 减免金额（美元）(分摊后展示)
     */
    var itemReduceDollar: BigDecimal? = null,
    /**
     *  减免金额（瑞尔）(分摊后展示)
     */
    var itemReduceKhr: BigDecimal? = null,
    /**
     * 减免折扣率 对应的vip减免金额(分摊后展示)
     */
    var itemReduceVipAmount: BigDecimal? = null,
    /**
     * 减免金额（vip美元）(分摊后展示)
     */
    var itemReduceVipDollar: BigDecimal? = null,
    /**
     * 减免金额（vip瑞尔）(分摊后展示)
     */
    var itemReduceVipKhr: BigDecimal? = null,


//    /**
//     * 减免类型  1-折扣  2-减免
//     */
//    val type: Int? = null
) {
    fun isCustomizeKhr(): Boolean {
        if ((reduceVipKhr ?: BigDecimal.ZERO) > BigDecimal.ZERO || (reduceKhr
                ?: BigDecimal.ZERO) > BigDecimal.ZERO
        ) {
            //这个判断兼容旧单  设置了khr 币种
            return true
        }
        return reduceType == WholeReduceType.CUSTOMIZE_KHR.id
    }
}