package com.metathought.food_order.casheir.data.model.base.response_model.dashboard


import com.google.gson.annotations.SerializedName

data class StoreOrderDetail(
    @SerializedName("customerInfo")
    val customerInfo: CustomerInfo?,
    @SerializedName("diningTable")
    val diningTable: String?,
    @SerializedName("orderInfo")
    val orderInfo: OrderInfo?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("orderStatus")
    val orderStatus: String?,
    @SerializedName("productDetails")
    val productDetails: List<ProductDetail?>?
)