package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.constant.MAXIMUM_QTY
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.databinding.SecondToppingItemBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero


class SecondToppingsAdapter(
    var feeds: List<Feed>,
) : RecyclerView.Adapter<SecondToppingsAdapter.SpecificationViewHolder>() {

    inner class SpecificationViewHolder(val binding: SecondToppingItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(showIndex: Int) {
            binding.apply {
                feeds[showIndex].let { feed ->
                    //change restrict num from unlimited to 999
                    if (feed.alreadyNum == null) {
                        feed.alreadyNum = 0
                    }
                    if (feed.restrictNum == 0)
                        feed.restrictNum = MAXIMUM_QTY
                    tvFeedName.text = feed.name
                    val formatPrice = feed.sum?.priceDecimalFormatTwoDigitZero()
                    tvPrice.text = "+$$formatPrice"
                    tvPrice.isVisible = (feed.sum ?: 0.0) > 0
                    tvQTY.text = feed.alreadyNum.toString()
                    tvQTY.isVisible = (feed.alreadyNum!! > 0)
                    layoutMain.isSelected = feed.alreadyNum!! > 0
                }

            }
        }
    }

    fun getSelectFeed(): ArrayList<Feed> {
        val list = feeds.filter { it.alreadyNum!! > 0 }
        return list as ArrayList<Feed>
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SpecificationViewHolder {
        return SpecificationViewHolder(
            SecondToppingItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return feeds.size
    }

    override fun onBindViewHolder(holder: SpecificationViewHolder, position: Int) {
        holder.bind(position)
    }

}