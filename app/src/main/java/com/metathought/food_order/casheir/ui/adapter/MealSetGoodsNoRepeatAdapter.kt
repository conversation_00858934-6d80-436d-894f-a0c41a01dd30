package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.MealsetGoodNoRepeatItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber

/**
 * 套餐内商品不能重复选
 *
 * @property isShowImage
 * @property mealSetGroup
 * @property mealSetGood
 * @property context
 * @property onClickCallback
 * @property onUpdateCheck
 * @constructor Create empty Meal set goods no repeat adapter
 */

class MealSetGoodsNoRepeatAdapter(
    private var isShowImage: Boolean? = false,
    private var mealSetGroup: MealSetGroup,
    val mealSetGood: Goods,
    val context: Context,
    val onClickCallback: (Int) -> Unit,
    val onUpdateCheck: (Int) -> Unit,
) : RecyclerView.Adapter<MealSetGoodsNoRepeatAdapter.MealSetGoodsNoRepeatViewHolder>() {


    inner class MealSetGoodsNoRepeatViewHolder(val binding: MealsetGoodNoRepeatItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun bind(position: Int) {
            binding.apply {
                val good = mealSetGroup.mealSetGoodsList?.get(position)
                good?.apply {
                    if (isShowImage == true) {
                        cardImage.isVisible = true
                        Timber.e("good.picUrl  ${good.picUrl}")
                        Glide.with(context).load(good.picUrl)
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .placeholder(R.drawable.icon_menu_default2)
                            .transition(DrawableTransitionOptions.withCrossFade())
                            .centerCrop()
                            .error(R.drawable.icon_menu_default2)
                            .into(ivGood)
                    } else {
                        cardImage.isVisible = false
                    }

                    tvDishedName.text = name
                    val number = num ?: 0
                    if (number >= 2) {
                        tvDishedName.text = "${tvDishedName.text} *${number} "
                    }
                    val price = priceMarkup ?: 0
                    if (price > 0) {
                        if (isToBeWeighed()) {
                            val weightUnit = getWeightUnit()
                            val singleDiscount = priceMarkup ?: 0
                            val unitText = "${
                                singleDiscount.priceFormatTwoDigitZero2()
                            }/${weightUnit}"
                            tvDishedName.text = "${tvDishedName.text} +${unitText}"
                        } else {
                            tvDishedName.text =
                                "${tvDishedName.text} +${
                                    FoundationHelper.getPriceStrByUnit(
                                        FoundationHelper.useConversionRatio,
                                        priceMarkup ?: 0L,
                                        FoundationHelper.isKrh
                                    )
                                }"
                        }
                    }
                    val tagStr = getGoodsTagStr()
                    tvTag.text = tagStr
                    if (isToBeWeighed() && isHasCompleteWeight()) {
                        val weightStr = getWeightStr()
                        tvTag.text = if (tagStr.isNullOrEmpty()) weightStr else "$tagStr,$weightStr"
                    }
                    tvTag.isVisible = tvTag.text.isNotEmpty()

                    var isSelect = isSelectItem()
                    vDisable.isVisible = false
                    if (isSelect) {
                        tvDishedName.setTextColor(context.getColor(R.color.primaryColor))
                        tvDishedName.isSelected = true
                        layoutMain.isSelected = true
                    } else {
                        tvDishedName.isSelected = false
                        //如果没选中情况下
                        Timber.e("mealSetGroup.isMaxNum()  ${mealSetGroup.isMaxNum()}  ${mealSetGroup.name}")
                        if (mealSetGroup.isMaxNum() || good.isSoldOutOrTakeDown(mealSetGood.mealSetInfo?.removalDoesNotAffectSale)) {
                            //该分类商品已经选到最大值
                            tvDishedName.setTextColor(context.getColor(R.color.black40))
                            layoutMain.isSelected = false

                            vDisable.isVisible = true
                        } else {
                            tvDishedName.setTextColor(context.getColor(R.color.black))
                            layoutMain.isSelected = false
                        }
                    }

                    binding.root.setOnClickListener {
                        Timber.e("点击点击")
                        SingleClickUtils.isFastDoubleClick(500) {
                            if (!isSelect && (mealSetGroup.isMaxNum() || good.isSoldOutOrTakeDown(
                                    mealSetGood.mealSetInfo?.removalDoesNotAffectSale
                                ))
                            ) {
                                // 未选中 且 已经是最大值就不让点
                                return@isFastDoubleClick
                            }

                            //如果是固定套餐
                            if (mealSetGood.isFixedMealSet()) {
                                if (optionalSpec == true) {
                                    //用户自选,且需要选则
                                    if (tags.isNullOrEmpty() && !isToBeWeighed()) {
                                        //直接选中
                                        good.selectFixedTag(
                                            "modify",
                                        )
                                        onUpdateCheck.invoke(position)
                                    } else {
                                        if (isSelect) {
                                            isSelect = false
                                            clearSelect()
                                            onUpdateCheck.invoke(position)
                                            return@isFastDoubleClick
                                        }
                                        //弹出选择框
                                        onClickCallback.invoke(position)
                                    }
                                } else {
                                    if (!isSelect) {
                                        if (!isToBeWeighed()) {
                                            good.selectFixedTag(
                                                "modify",
                                            )
                                            onUpdateCheck.invoke(position)
                                        } else {
                                            onClickCallback.invoke(position)
                                        }
                                    } else {
                                        //如果是称重的子商品 支持反选
                                        if (isToBeWeighed()) {
                                            isSelect = false
                                            clearSelect()
                                            onUpdateCheck.invoke(position)
                                            return@isFastDoubleClick
                                        }
                                    }
                                }
                            } else {
                                if (isSelect) {
                                    isSelect = false
                                    clearSelect()
                                    onUpdateCheck.invoke(position)
                                    return@isFastDoubleClick
                                }
                                if (optionalSpec == true) {
                                    //没有规格
                                    if (tags.isNullOrEmpty() && !isToBeWeighed()) {
                                        selectCustomTag(
                                            "modify",
                                            mutableListOf(),
                                            goodsNum = if (isSelect) 0 else 1
                                        )
                                        onUpdateCheck.invoke(position)
                                    } else {
                                        onClickCallback.invoke(position)
                                    }
                                    Timber.e("用户自选")
                                } else {
                                    Timber.e("商户配置选")
                                    if (!isToBeWeighed()) {
                                        selectFixedTag("modify", goodsNum = if (isSelect) 0 else 1)
                                        onUpdateCheck.invoke(position)
                                    } else {
                                        onClickCallback.invoke(position)
                                    }
                                }
                            }
                        }
                        notifyItemChanged(position)
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): MealSetGoodsNoRepeatViewHolder {
        return MealSetGoodsNoRepeatViewHolder(
            MealsetGoodNoRepeatItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return mealSetGroup.mealSetGoodsList?.size ?: 0
    }

    override fun onBindViewHolder(holder: MealSetGoodsNoRepeatViewHolder, position: Int) {
        holder.bind(position)

    }

}