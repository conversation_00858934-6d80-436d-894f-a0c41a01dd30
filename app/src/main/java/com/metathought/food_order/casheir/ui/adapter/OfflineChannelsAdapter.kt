package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.databinding.ItemPopupOfflineChannelBinding

/**
 * <AUTHOR>
 * @date 2024/5/911:22
 * @description
 */
class OfflineChannelsAdapter(
    val list: ArrayList<OfflineChannelModel>,
    val paymentClickListener: ((OfflineChannelModel) -> Unit)? = null
) : RecyclerView.Adapter<OfflineChannelsAdapter.OfflineChannelItemViewHolder>() {

    //    private var selectedPosition = 0
    private var selectItem: OfflineChannelModel? = null

    inner class OfflineChannelItemViewHolder(val binding: ItemPopupOfflineChannelBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {

            itemView.setOnClickListener {
                if (bindingAdapterPosition != -1) {
//                    if (selectedPosition != -1) {
//                        notifyItemChanged(selectedPosition)
//                    }
//
//                    selectedPosition = bindingAdapterPosition
//                    notifyItemChanged(selectedPosition)
                    paymentClickListener?.invoke(list[bindingAdapterPosition])
                }
            }
        }

        fun bind(resource: OfflineChannelModel?, position: Int) {
            resource?.let {
                binding.apply {
                    tvChannelName.text = resource.getSpannableStringBuilder(
                        itemView.context,
                        itemView.context.getColor(R.color.black),
                        itemView.context.getColor(R.color.black60)
                    )
//                    root.setBackgroundColor(
//                        if (resource.id == selectItem?.id) itemView.context.getColor(
//                            R.color.color_efefef
//                        ) else itemView.context.getColor(R.color.white)
//                    )
                    root.setBackgroundResource(
                        if (resource.id == selectItem?.id)
                            R.drawable.background_efefef_radius_12
                        else
                            R.drawable.background_transparent

                    )
                }
            }
        }

    }

    fun setSelect(selectItem: OfflineChannelModel? = null) {
        this.selectItem = selectItem
    }

//    fun getSelect(): OfflineChannelModel {
//        return list[selectedPosition]
//    }

    fun updateData(mList: List<OfflineChannelModel>) {
        list.clear()
        val tmp = mList.filter { it.selected == true }
        list.addAll(tmp)

//        if (list.isEmpty()) {
//            list.addAll(mList.filter { it.id == OfflinePaymentChannelEnum.CASH.id })
//        }

        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): OfflineChannelItemViewHolder {
        val itemView = ItemPopupOfflineChannelBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )

        return OfflineChannelItemViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OfflineChannelItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }
}