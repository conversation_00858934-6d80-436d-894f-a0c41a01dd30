package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.request_model.PayInfo

/**
 * <AUTHOR>
 * @date 2024/5/2713:22
 * @description
 */
data class OrderedTranslateResponse(
    @SerializedName("ordersZh")
    val ordersZh: OrderedTranslateVo?,
    @SerializedName("ordersEn")
    val ordersEn: OrderedTranslateVo?,
    @SerializedName("ordersKm")
    val ordersKm: OrderedTranslateVo?,
    @SerializedName("storeNameEn")
    val storeNameEn: String? = null,
    @SerializedName("storeNameKm")
    val storeNameKm: String? = null,

) {


}

data class OrderedTranslateVo(
    @SerializedName("goodsJson")
    val goodsJson: String? = null,
    /**
     * 已退菜列表
     */
    @SerializedName("removeGoodList")
    var removeGoodList: ArrayList<OrderedGoods>?,

    @SerializedName("offlinePaymentChannelsName")
    var offlinePaymentChannelsName: String? = null,

    @SerializedName("combinedPayInfoList")
    val combinedPayInfoList: List<PayInfo>? = null,
    ) {

    private var orderedGoodJson: OrderedGoodJson? = null

    fun getOrderedGoodJson(): OrderedGoodJson? {
        if (orderedGoodJson == null && !goodsJson.isNullOrEmpty()) {
            orderedGoodJson =
                MyApplication.globalGson.fromJson(goodsJson, OrderedGoodJson::class.java)
        }
        return orderedGoodJson
    }
}