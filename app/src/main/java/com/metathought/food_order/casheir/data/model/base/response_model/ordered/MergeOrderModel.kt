package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.AcceptOrderedStatusEnum
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/11/21
 *@desc
 **/

data class MergeOrderModel(
    val diningStyle: Int? = null,
    val payStatus: Int? = null,
    val tableName: String? = null,
    val pickupCode: String? = null,
    val orderId: String? = null,
    val num: Int? = null,
    val createTime: String? = null,
    val isOrdersWeightMark: Boolean? = false,
    val realPrice: Long? = null,
    /**
     * 是否选择 本地
     */
//    var isSelect: Boolean? = false
    /**
     * 订单是否有设置折扣(单品折扣、整单折扣)
     */
    @SerializedName("isSettingDiscount")
    var isSettingDiscount: Boolean? = false,
) {
    fun getShowPrice(context: Context): String {

        if (payStatus == OrderedStatusEnum.BE_CONFIRM.id || payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || payStatus == OrderedStatusEnum.UNPAID.id || payStatus == OrderedStatusEnum.CANCEL_ORDER.id) {
            Timber.e("orderNo: $orderId  ==> hasWeight:${isOrdersWeightMark}")
            if (isOrdersWeightMark == true) {
                return "${context.getString(R.string.to_be_confirmed)}"
            }
            return "${realPrice?.priceFormatTwoDigitZero2()}"
        } else {
            return "${realPrice?.priceFormatTwoDigitZero2()}"
        }
    }

    fun getDiningStyleStr(context: Context): String {
        return if (diningStyle == DiningStyleEnum.DINE_IN.id) {
            context.getString(R.string.dine_in)
        } else {
            context.getString(R.string.take_away)
        }
    }

    fun getDiningStyleIcon(): Int {
        return  if (diningStyle == DiningStyleEnum.DINE_IN.id) {
            R.drawable.icon_dine_in
        } else {
            R.drawable.icon_take_away
        }
    }
}