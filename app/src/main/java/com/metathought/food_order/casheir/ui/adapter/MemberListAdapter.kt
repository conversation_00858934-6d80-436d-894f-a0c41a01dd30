package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.MemberListItemsBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class MemberListAdapter(
    val list: ArrayList<Record>,
//    val onItemClickListener: (Record) -> Unit,
) : RecyclerView.Adapter<MemberListAdapter.MemberListViewHolder>() {

    private var isCanTopUp = MainDashboardFragment.CURRENT_USER?.getPermissionList()
        ?.contains(PermissionEnum.RECHARGE.type) == true

    inner class MemberListViewHolder(val binding: MemberListItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: Record, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
//                        Glide.with(context).load(it.photoUrl).circleCrop()
//                            .diskCacheStrategy(DiskCacheStrategy.ALL)
//                            .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo)
//                            .into(imgMemberProfile)
//                        tvAccountName.text =
//                            if (!it.nickName.isNullOrEmpty()) it.nickName else context.getString(
//                                R.string.n_a
//                            )
                        tvMemberNumber.text = it.memberNumber ?: "N/A"
                        tvAccountNumber.text = it.telephone
                        tvAmount.text = it.balance?.priceFormatTwoDigitZero2()
                        tvRegisterDate.text = it.createTime?.formatDate()
                        tvLastTopUpDate.text =
                            if (!it.lastRechargeTime.isNullOrEmpty()) it.lastRechargeTime.formatDate() else context.getString(
                                R.string.n_a
                            )
                        tvRechargeTimes.text = "${it.rechargeMembersNum}"
                        tvConsumptionTimes.text = "${it.consumerMembersNum}"
                    }
                }
            }

        }

    }

    @SuppressLint("NotifyDataSetChanged")
    fun setCanTopUp(canTopUp: Boolean) {
        isCanTopUp = canTopUp
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberListViewHolder {
        val itemView =
            MemberListItemsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MemberListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: MemberListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    fun replaceData(list: ArrayList<Record>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<Record>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


    private var mOnMemberListAdapterListener: OnMemberListAdapterListener? = null

    fun setOnMemberListAdapterListener(mOnMemberListAdapterListener: OnMemberListAdapterListener) {
        this.mOnMemberListAdapterListener = mOnMemberListAdapterListener
    }

    interface OnMemberListAdapterListener {
        fun onRechargeClick(record: Record)
        fun onDetailClick(record: Record)

        fun onEditClick(record: Record)
    }

}