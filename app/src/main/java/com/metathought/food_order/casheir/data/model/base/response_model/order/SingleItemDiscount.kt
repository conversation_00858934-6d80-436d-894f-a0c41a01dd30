package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/11/18
 *@desc  单品减免 服务端返回的model
 **/

data class SingleItemDiscount(
    /**
     * 商品hashKey
     */
    @SerializedName("goodsHashKey")
    val goodsHashKey: String?,

    /**
     * 商品id
     */
    @SerializedName("goodsId")
    val goodsId: String?,


    /**
     * 减免百分比
     */
    @SerializedName("reduceRatio")
    val reduceRatio: Double? = null,

    /**
     * 现价减免固定金额
     */
    @SerializedName("saleReduce")
    val saleReduce: BigDecimal? = null,

    /**
     * 减免类型  1.百分比 2.固定金额 3:改价
     */
    @SerializedName("type")
    val type: Int? = null,


    /**
     *   会员价减免固定金额
     */
    @SerializedName("vipReduce")
    val vipReduce: BigDecimal? = null,

    /**
     *   销售-改价
     */
    @SerializedName("adjustSalePrice")
    val adjustSalePrice: Double? = null,

    /**
     *   会员-改价
     */
    @SerializedName("adjustVipPrice")
    var adjustVipPrice: Double? = null,

    /**
     *   备注
     */
    @SerializedName("remark")
    var remark: String? = null,

    /**
     * 所选的后台配置的折扣id
     */
    @SerializedName("discountReduceActivityId")
    var discountReduceActivityId: String? = null,

    /**
     * 如果是选择后台配的减免折扣 因为不合并 所以这个要有值
     */
    @SerializedName("uuid")
    var uuid: String? = null,

    /**
     * 原因类型
     */
    @SerializedName("discountType")
    var discountType: Int? = null,

    @SerializedName("discountReduceActivity") //本地的后台配置折扣信息
    var discountReduceActivity: DiscountReduceInfo? = null


) {

    //转成
    fun toCartSingleDiscountGood(): SingleDiscountGoods {
        return SingleDiscountGoods(
            type = type,
            reduceRatio = reduceRatio,
            vipReduce = vipReduce?.toDouble(),
            saleReduce = saleReduce?.toDouble(),
            adjustSalePrice = adjustSalePrice,
            adjustVipPrice = adjustVipPrice,
            name = null,
            num = null,
            singlePrice = null,
            singleVipPrice = null,
            totalPrice = 0L,
            vipPrice = 0L,
            isShowVipPrice = false,
            goodsId = "",
            goodsHashKey = "",
            remark = remark,
            discountType = discountType,
            discountReduceActivityId = discountReduceActivityId,
            discountReduceInfo = discountReduceActivity,
            uuid = uuid
        )
    }

    fun coverToSingleDiscountRequest(): SingleDiscountRequest {
        return SingleDiscountRequest(
            type = type,
            goodsId = goodsId,
            goodsHashKey = null,  //挪进来后 就不用传这个了
            reduceRatio = reduceRatio,
            saleReduce = if (saleReduce != null) saleReduce?.toDouble() else null,
            vipReduce = if (vipReduce != null) vipReduce?.toDouble() else null,
            adjustSalePrice = adjustSalePrice,
            adjustVipPrice = adjustVipPrice,
            remark = remark,
            discountType = discountType,
            discountReduceActivityId = discountReduceActivityId,
            uuid = uuid
        )
    }

}
