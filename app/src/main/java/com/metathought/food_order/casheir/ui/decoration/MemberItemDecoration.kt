package com.metathought.food_order.casheir.ui.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * 会员列表项间距装饰器
 * 为 RecyclerView 中的每个项目添加统一的间距
 */
class MemberItemDecoration(
    private val spacingTop: Int = 10, // 默认 10dp 间距
    private val spacingBottom: Int = 10, // 默认 10dp 间距
    private val spacingLeft: Int = 0, // 默认 10dp 间距,
    private val spacingRight: Int = 0 // 默认 10dp 间距
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val itemCount = state.itemCount

        // 左右间距
        outRect.left = spacingLeft
        outRect.right = spacingRight

        // 顶部间距：第一个项目添加顶部间距
        if (position == 0) {
            outRect.top = spacingTop
        }

        // 底部间距：所有项目都添加底部间距
        outRect.bottom = spacingBottom
    }
}
