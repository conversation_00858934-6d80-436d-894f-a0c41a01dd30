package com.metathought.food_order.casheir.helper

import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.ActivityLabel
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsDiscountInfo
import com.metathought.food_order.casheir.extension.halfUp
import timber.log.Timber
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/11/25
 *@desc  优惠活动相关计算
 **/

object DiscountActivityHelper {

    /**
     * 计算所有的优惠活动
     *
     * @param goodsRequest
     */
    fun calculateAllDiscountAct(goodsRequest: List<GoodsRequest>): MutableList<CouponActivityModel> {

        //所有菜品按优惠活动分组  //过滤出未售罄，且没设置单品折扣的
        val goodsRequestToMatByAct =
            goodsRequest.filter { !it.isSetSingleItemDiscount() && it.goods?.isSoldOut() != true }
                .groupBy { it.goods?.activityLabels?.firstOrNull() }
        val discountActEffectiveModelList = mutableListOf<CouponActivityModel>()
        goodsRequestToMatByAct.forEach { activityLabel, goodsRequests ->
            Timber.e("组activityLabel: ${activityLabel.toString()}")
            /**
             * key 不为 null 的时候 ，说明有优惠活动
             */
            if (activityLabel != null) {
                val act = calculateBuyNGiftN(activityLabel, goodsRequests)
                if (act != null) {
                    discountActEffectiveModelList.add(act)
                }
            }
        }

        Timber.e("购物车菜品一个分了 ${goodsRequestToMatByAct.size} 组")
        return discountActEffectiveModelList
    }

    /**
     * 计算现价第N件优惠
     *
     */
    private fun calculateBuyNGiftN(
        activityLabel: ActivityLabel,
        goodsRequests: List<GoodsRequest>
    ): CouponActivityModel? {
        var discountActEffectiveModel: CouponActivityModel? = null

//        /**
//         * 根据现价 从大到小 排序一下
//         */
//        val sortByPrice = goodsRequests.sortedByDescending {
//            it.singleDiscountPrice()
//        }
//
//        val sortByVipPrice = ArrayList(goodsRequests).sortedByDescending {
//            if (it.isShowVipPrice())
//                it.calculateVipPrice()
//            else
//                it.singleDiscountPrice()
//        }

        /**
         * 根据现价 从大到小 排序，价格相同则按商品id从大到小排序
         */
        val sortByPrice = goodsRequests.sortedWith(
            compareByDescending<GoodsRequest> { it.singleDiscountPrice() }
                .thenByDescending { it.goods?.id ?: "" }  // 新增id排序条件
        )

        /**
         * 根据VIP价 从大到小 排序，价格相同则按商品id从大到小排序
         */
        val sortByVipPrice = ArrayList(goodsRequests).sortedWith(
            compareByDescending<GoodsRequest> {
                if (it.isShowVipPrice()) it.calculateVipPrice() else it.singleDiscountPrice()
            }.thenByDescending { it.goods?.id ?: "" }  // 新增id排序条件
        )

        val rule = activityLabel.typeRule
        if (rule != null) {
            //已经减免金额
            var hasDiscountPrice = 0L
            var totalGroupList = mutableListOf<GoodsRequest>()
            sortByPrice.forEachIndexed { index, goodsRequest ->
                for (i in 0 until (goodsRequest.num ?: 0)) {
                    totalGroupList.add(goodsRequest.copy(num = 1))
                }
            }
            val pair = groupArrayByNAndTail(
                totalGroupList,
                rule.itemSort,
                isContinue = activityLabel.continuous == 1
            )
            // 新增：收集参与优惠的商品信息
//            val discountGoodsMap = mutableMapOf<String, Int>()  // key:商品ID，value:参与优惠数量
            // 现价优惠计算优化（同时记录商品id、数量和总金额）
            val discountGoodsMap =
                mutableMapOf<String, GoodsDiscountInfo>()  // key:商品哈希，value:（商品id，数量，总金额）
            if (pair.first.isNotEmpty()) {
                pair.first.forEach { group ->
                    val data = group.minByOrNull { good -> good.singleDiscountPrice() }
                    data?.let { good ->
                        val hashKey = good.getHash()
                        val goodsId = good.goods?.id       // 获取商品ID
                        val name = good.goods?.name     // 获取商品名称
                        val singleDiscount =
                            BigDecimal(good.singleDiscountPrice() * rule.discount / 100.0)
                                .halfUp(0)
                                .toLong()
                        // 更新或初始化GoodsDiscountInfo
                        val current = discountGoodsMap.getOrElse(hashKey) {
                            GoodsDiscountInfo(
                                id = goodsId,
                                name = name ?: "",
                                num = 0,
                                discountAmount = BigDecimal.ZERO,
                                hashKey = hashKey
                            )
                        }
                        discountGoodsMap[hashKey] = current.copy(
                            num = current.num + 1,
                            discountAmount = current.discountAmount.add(
                                BigDecimal(singleDiscount).divide(
                                    BigDecimal(100.0)
                                )
                            )
                        )

                        hasDiscountPrice += singleDiscount
                    }
                }

                // 转换为现价优惠商品列表（从Triple中提取数据）
                val discountList = discountGoodsMap.values.toList()

                Timber.e("最终优惠金额 : $hasDiscountPrice    isUnWeight:${pair.second}")
                discountActEffectiveModel =
                    CouponActivityModel(
                        activityLabel = activityLabel,
                        activityCouponAmount = hasDiscountPrice,
                        weightMark = pair.second,
                        activityGoodsMap = mapOf(
                            "NORMAL" to discountList
                        )
                    )
            }


            // VIP价优惠计算优化（同时记录数量和金额）
            var hasDiscountVipPrice = 0L
            totalGroupList.clear()
            /**
             *  先调整为和服务端一样 有vip价  vipmark 就置为true
             */
            var isVipMark = false
            sortByVipPrice.forEachIndexed { index, goodsRequest ->
                if (goodsRequest.isShowVipPrice()) {
                    isVipMark = true
                }
                for (i in 0 until (goodsRequest.num ?: 0)) {
                    totalGroupList.add(goodsRequest.copy(num = 1))
                }
            }
            val pairVip = groupArrayByNAndTail(
                totalGroupList.toMutableList(),
                rule.itemSort,
                isContinue = activityLabel.continuous == 1
            )


            // 新增：收集VIP价优惠的商品信息（包含商品id）
            val vipDiscountGoodsMap =
                mutableMapOf<String, GoodsDiscountInfo>()  // key:商品哈希，value:（商品id，数量，总金额）
            if (pairVip.first.isNotEmpty()) {
                pairVip.first.forEach { group ->
                    val data = group.minByOrNull { good ->
                        if (good.isShowVipPrice()) good.calculateVipPrice() else good.singleDiscountPrice()
                    }
                    data?.let { good ->
                        val hashKey = good.getHash()
                        val goodsId = good.goods?.id       // 商品ID
                        val name = good.goods?.name         // 商品名称
                        val price =
                            if (good.isShowVipPrice()) good.calculateVipPrice() else good.singleDiscountPrice()
                        val singleVipDiscount = BigDecimal(price * rule.discount / 100.0)
                            .halfUp(0)
                            .toLong()
                        // 更新或初始化GoodsDiscountInfo
                        val current = vipDiscountGoodsMap.getOrElse(hashKey) {
                            GoodsDiscountInfo(
                                id = goodsId,
                                name = name ?: "",
                                num = 0,
                                discountAmount = BigDecimal.ZERO,
                                hashKey = hashKey
                            )
                        }
                        vipDiscountGoodsMap[hashKey] = current.copy(
                            num = current.num + 1,
                            discountAmount = current.discountAmount.add(
                                BigDecimal(singleVipDiscount).divide(
                                    BigDecimal(100.0)
                                )
                            )
                        )
                        hasDiscountVipPrice += singleVipDiscount
                    }
                }
                // 直接使用Map中的GoodsDiscountInfo作为结果列表
                val vipDiscountList = vipDiscountGoodsMap.values.toList()

                discountActEffectiveModel?.apply {
                    activityVipCouponAmount = hasDiscountVipPrice
                    vipMark = isVipMark
                    activityGoodsMap = activityGoodsMap.toMutableMap().apply {
                        put("VIP", vipDiscountList)
                    }
                }
            }
        }

        return discountActEffectiveModel
    }

    /**
     * Group array by n and tail
     *
     * @param array 菜品列表
     * @param M  第N件商品优惠
     * @param isContinue  是否连续
     * @return
     */
    private fun groupArrayByNAndTail(
        array: MutableList<GoodsRequest>,
        M: Int,
        isContinue: Boolean
    ): Pair<List<List<GoodsRequest>>, Boolean> {
        val finalResult = mutableListOf<List<GoodsRequest>>()
        if (array.size < M) {
            return Pair(finalResult, false)
        }
//        val needWeightList = array.filter { it.goods?.isHasProcessed() == false }
        val needWeightList = array.filter { !it.isProcessed() }
//        if (needWeightList.isNotEmpty()) {
//            return Pair(finalResult, true)
//        }
        while (array.isNotEmpty()) {
            val result = mutableListOf<GoodsRequest>()
            if (array.size < M) {
//            //如果购买的商品不够 活动条件
                array.clear()
            } else {
                val takeFromStart = array.take(M - 1)
                result.addAll(takeFromStart)
                takeFromStart.forEach {
                    if (array.isNotEmpty()) {
                        array.removeFirst()
                    }
                }

                if (array.isNotEmpty()) {
                    val takeFromEnd = array.takeLast(1)
                    result.addAll(takeFromEnd)
                    array.removeLast()
                }
                finalResult.add(result)
                if (!isContinue && finalResult.size == 1) {
                    //如果不连续 有一个值就退出
                    array.clear()
                }
            }
        }
        return Pair(finalResult, needWeightList.isNotEmpty())
    }
}