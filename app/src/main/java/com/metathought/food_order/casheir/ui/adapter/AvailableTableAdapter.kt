package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.TableStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.AvailableTableItemBinding
import com.metathought.food_order.casheir.extension.dp
import timber.log.Timber


class AvailableTableAdapter(
    val list: TableResponse,
    val context: Context,
    val onClickCallback: (TableResponseItem) -> Unit
) : RecyclerView.Adapter<AvailableTableAdapter.TableItemViewHolder>() {
    var viewHight: Int = 0

    var customerPersonNum = 0

    inner class TableItemViewHolder(val binding: AvailableTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.let {
                itemView.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        list.forEach { it.select = false }
                        list[bindingAdapterPosition].select = !list[bindingAdapterPosition].select
                        onClickCallback.invoke(list[bindingAdapterPosition])
                        notifyDataSetChanged()
                    }
                }
            }
        }

        fun bind(resource: TableResponseItem?) {
            resource?.let { _ ->
                binding.apply {
                    tvTableID.text = resource.name
                    tvStatus.text = ""
                    when (resource.status) {
                        TableStatusEnum.AVAILABLE.id -> {
                            layoutWrap.setCardBackgroundColor(
                                ContextCompat.getColor(
                                    context,
                                    R.color.primaryColor
                                )
                            )
                        }

                        TableStatusEnum.RESERVED.id -> {

                            tvStatus.text = context.getString(R.string.reserved)
                            layoutWrap.setCardBackgroundColor(
                                ContextCompat.getColor(
                                    context,
                                    R.color.main_yellow
                                )
                            )
                        }

                        TableStatusEnum.OCCUPIED.id -> {
                            tvStatus.text = context.getString(R.string.dining)
                            layoutWrap.setCardBackgroundColor(
                                ContextCompat.getColor(
                                    context,
                                    R.color.main_red
                                )
                            )
                        }
                    }
                    if (resource.select) {
                        tvChairAvailable.text = "${customerPersonNum}/${resource.maxPeopleCount}"
                        ivSelect.isVisible = true
                    } else {
                        tvChairAvailable.text = "0/${resource.maxPeopleCount}"
                        ivSelect.isVisible = false
                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableItemViewHolder {
        var itemView = AvailableTableItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
        viewHight = (parent.width / 4)
        itemView.root.layoutParams.height = viewHight - 10.dp
        return TableItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: TableItemViewHolder, position: Int) {
//        holder.itemView.minimumHeight = viewHight
        holder.bind(list[position])
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun getSelect(): TableResponseItem? {
        return list.firstOrNull { it.select }
    }

    fun updateItems(newItems: List<TableResponseItem>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

}