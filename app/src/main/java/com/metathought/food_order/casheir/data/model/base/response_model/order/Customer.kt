package com.metathought.food_order.casheir.data.model.base.response_model.order

data class Customer(
    val diningNumber: Int,
    val diningTime: String? = "",
    val mobile: String?,
    val name: String?,
    val areaCode: String?
) {
    fun getMobilePhone(): String {
        if (mobile == null || mobile?.isNullOrEmpty() == true) {
            return ""
        }
        return areaCode + mobile
    }

    fun getMobilePhoneDisplay(): String {
        if (mobile == null || mobile?.isNullOrEmpty() == true) {
            return ""
        }
        return "+$areaCode $mobile"
    }

}
