package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list

import com.google.gson.annotations.SerializedName

data class StoreDataGrabOrderInfo(

    @SerializedName("saasStoreId")
    val saasStoreId: String,

    @SerializedName("id")
    val id: String,

    @SerializedName("createTime")
    val createTime: String,

    @SerializedName("updateTime")
    val updateTime: String,

    @SerializedName("orderId")
    val orderId: String,

    @SerializedName("readyStatus")
    val readyStatus: Int?,

    @SerializedName("grabOrderNo")
    val grabOrderNo: String,

    @SerializedName("readyDate")
    val readyDate: String?,

    @SerializedName("grabFundPromo")
    val grabFundPromo: Long?,

    @SerializedName("merchantFundPromo")
    val merchantFundPromo: Long?,

    @SerializedName("basketPromo")
    val basketPromo: Long?,

    @SerializedName("smallOrderFee")
    val smallOrderFee: Long?,

    @SerializedName("merchantChargeFee")
    val merchantChargeFee: Long?,

    @SerializedName("deliveryFee")
    val deliveryFee: Long?,

    @SerializedName("eaterPayment")
    val eaterPayment: Long?,

    @SerializedName("total")
    val total: Long?,

    @SerializedName("totalKHR")
    val totalKHR: Long?,

    @SerializedName("diningStyle")
    val diningStyle: String?,

    @SerializedName("subtotal")
    val subtotal: Long?,

    @SerializedName("tax")
    val tax: Int,

    @SerializedName("address")
    val address: String?,

    @SerializedName("addressDetail")
    val addressDetail: String?,

    @SerializedName("vatRate")
    val vatRate: Double?,

    @SerializedName("cutlery")
    val cutlery: Boolean,

    @SerializedName("orderStatus")
    val orderStatus: String,

    @SerializedName("failReason")
    val failReason: String?,

    @SerializedName("completeTime")
    val completeTime: String?,

    @SerializedName("cancelTime")
    val cancelTime: String?,

    @SerializedName("failTime")
    val failTime: String?,

    @SerializedName("payment")
    val payment: String

)
