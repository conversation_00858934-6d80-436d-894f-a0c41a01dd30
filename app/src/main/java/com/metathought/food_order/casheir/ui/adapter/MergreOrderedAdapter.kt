package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.MergeOrderedItemBinding
import com.metathought.food_order.casheir.databinding.OrderedItemBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TimeUtils
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class MergreOrderedAdapter(
    val act: Context,
    var list: ArrayList<MergeOrderModel>,
    val onItemClickListener: (MergeOrderModel) -> Unit
) : RecyclerView.Adapter<MergreOrderedAdapter.OrderedViewHolder>() {
    companion object {
        val UPDATE_SELECT = "UPDATE_SELECT"
    }

    val selectList: MutableList<MergeOrderModel> = mutableListOf()

    inner class OrderedViewHolder(val binding: MergeOrderedItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(200) {
                        if (it != -1) {
                            val index =
                                selectList.indexOfFirst { mergeOrderModel -> mergeOrderModel.orderId == list[it].orderId }
                            if (index != -1) {
                                selectList.removeAt(index)
                            } else {
                                Timber.e("插入")
                                selectList.add(list[it])
                            }

                            notifyItemChanged(it, UPDATE_SELECT)
                            onItemClickListener.invoke(list[it])
                        }
                    }
                }

            }
        }

        fun bind(resource: MergeOrderModel, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {

                        tvPickUpNo.isVisible = !it.pickupCode.isNullOrEmpty()
                        tvPickUpNo.text =
                            "${getString(R.string.print_title_pick_up_no)} ${it.pickupCode}"
                        val index =
                            selectList.indexOfFirst { mergeOrderModel -> mergeOrderModel.orderId == resource.orderId }
                        Timber.e("selectList:${selectList.size}   position111:${position}   index: ${index}")
                        ivSelect.isSelected = index > -1
                        ivSelect.isVisible = true


                        tvTableID.text = it.tableName ?: ""

                        val oderIdValue = "${getString(R.string.order_id)}: ${it.orderId}"
                        tvOrderedID.text = oderIdValue

                        val itemsValue =
                            "${getString(R.string.items)}: ${it?.num}"
                        tvItems.text = itemsValue

                        val timeValue =
                            "${getString(R.string.ordering_time)}: ${it.createTime?.formatDate()}"
                        tvTime.text = timeValue

                        tvPrice.text = it.getShowPrice(itemView.context)

                        tvOrderType.text = it.getDiningStyleStr(itemView.context)


                        tvOrderType.setCompoundDrawablesWithIntrinsicBounds(
                            ContextCompat.getDrawable(
                                itemView.context,
                                it.getDiningStyleIcon()
                            ), null, null, null
                        )


                        tvStatus.text = it.payStatus?.getPayText(this@run)
                        tvStatus.setTextColor(
                            ContextCompat.getColor(
                                this@run,
                                it.payStatus?.getPayTypeColor() ?: R.color.ordered_cancel_color
                            )
                        )
                        statusCardView.setCardBackgroundColor(
                            ContextCompat.getColor(
                                this@run,
                                it.payStatus?.getPayTypeBackGroundColor()
                                    ?: R.color.ordered_cancel_color
                            )
                        )

                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedViewHolder {
        val itemView =
            MergeOrderedItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: OrderedViewHolder, position: Int) {
        Timber.e("onBindViewHolder  ${position}")
        holder.bind(list[position], position)
    }

    override fun onBindViewHolder(
        holder: OrderedViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            holder.binding.apply {
                val index =
                    selectList.indexOfFirst { mergeOrderModel -> mergeOrderModel.orderId == list[position].orderId }
                Timber.e("position:${position}   index: ${index}")
                ivSelect.isSelected = index > -1
            }
        }
    }


    fun replaceData(list: ArrayList<MergeOrderModel>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<MergeOrderModel>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


    fun updateStatus(record: MergeOrderModel) {
//        if (selectedOrderId != null && selectedOrderId == record.orderNo) {
        val index = list.indexOfFirst { it.orderId == record.orderId }
        Timber.e("updateStatus  $index")
        if (index != -1) {
            list[index] = record
            notifyItemChanged(index)
        }
//        }
    }


    fun hasModel(orderNo: String): MergeOrderModel? {
        val index = list.indexOf(MergeOrderModel(orderId = orderNo))
        if (index != -1) {
            return list[index]
        }
        return null
    }


}