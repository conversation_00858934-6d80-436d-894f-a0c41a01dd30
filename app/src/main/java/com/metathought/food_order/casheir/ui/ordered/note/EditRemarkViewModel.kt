package com.metathought.food_order.casheir.ui.ordered.note

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.GoodNoteRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateOrderOrGoodNoteRequest
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.QuickRemarkModel
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/5/913:44
 * @description
 */
@HiltViewModel

class EditRemarkViewModel @Inject

constructor(private val repository: Repository) : ViewModel() {


    val uiModeState get() = _uiModeState
    private val _uiModeState = MutableLiveData<UIModel>()

    val uiListModeState get() = _uiListModeState
    private val _uiListModeState = MutableLiveData<UIListModel>()


    fun getShortcutNoteList() {
        viewModelScope.launch {
            try {
                val response = repository.getShortcutNoteList()
                if (response is ApiResponse.Success) {
                    emitUIListState(response)
                }
            } catch (e: Exception) {

            }
        }
    }


    fun updateNote(
        orderNo: String?,
        orderedGoods: OrderedGoods?,
        note: String? = ""
    ) {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val request = if (orderedGoods == null) {
                    UpdateOrderOrGoodNoteRequest(orderNo = orderNo, note = note)
                } else {
                    UpdateOrderOrGoodNoteRequest(
                        orderNo = orderNo, modifyGoodsNotesList = listOf(
                            GoodNoteRequest(
                                hashKey = orderedGoods.hashKey,
                                note = note,

                                goodsId = orderedGoods.id,
                                orderId = orderedGoods.orderId,
                                feeds = orderedGoods.feeds,
                                tagItems = orderedGoods.tagItems,
                                orderMealSetGoodsDTOList = orderedGoods.orderMealSetGoodsDTOList,
                                pricingMethod = orderedGoods.pricingMethod,
                                weight = orderedGoods.weight,
                                acceptOrderId = orderedGoods.acceptOrderId,
                                goodsPriceKey = orderedGoods.goodsPriceKey,
                                discountTypeRemarkKey = orderedGoods.discountRemarkIdKey,
                                packingFeeKey = orderedGoods.packingFeeKey,
                                spuPriceKey = orderedGoods.spuPriceKey,
                                oldNote = orderedGoods.note
                            )
                        )
                    )
                }
                val result = repository.updateOrderOrGoodNote(request)
                emitUIState(result = result)
            } catch (e: Exception) {
                emitUIState(result = ApiResponse.Error(e.message))
            }
        }
    }

    private suspend fun emitUIState(
        result: ApiResponse<OrderedInfoResponse>? = null
    ) {
        withContext(Dispatchers.Main) {
            _uiModeState.value = UIModel(result = result)
        }
    }

    private suspend fun emitUIListState(
        result: ApiResponse<ArrayList<QuickRemarkModel>>? = null
    ) {
        withContext(Dispatchers.Main) {
            _uiListModeState.value = UIListModel(result = result)
        }
    }

    data class UIModel(
        val result: ApiResponse<OrderedInfoResponse>?,
    )

    data class UIListModel(
        val result: ApiResponse<ArrayList<QuickRemarkModel>>?,
    )

}