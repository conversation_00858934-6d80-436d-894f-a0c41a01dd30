package com.metathought.food_order.casheir.ui.store_dashbord.ordered

import android.content.Context
import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.constant.OrderedGrabStatusMergeEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.OrderedStatusMergeEnum
import com.metathought.food_order.casheir.databinding.DialogStoreOrderedBinding
import com.metathought.food_order.casheir.databinding.PopupGrabOrderFilterBinding
import com.metathought.food_order.casheir.databinding.PopupOrderFilterBinding
import com.metathought.food_order.casheir.databinding.PopupPaymentMethod2Binding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.DropPaymentMethodAdapter
import com.metathought.food_order.casheir.ui.adapter.StoreOrderListAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.ordered.OrderedViewModel
import com.metathought.food_order.casheir.ui.store_dashbord.StoreDashboardViewModel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


@AndroidEntryPoint
class StoreOrderListDialog : BaseDialogFragment() {
    private var binding: DialogStoreOrderedBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    private lateinit var dashboardListAdapter: StoreOrderListAdapter
    private var startDateString: String = ""
    private var endDateString: String = ""
    private var paymentMenthod: String = ""
    private var orderStatus: String = ""
    private var type: String = "1"
    private var keyword: String = ""
    private var selectedValue = OrderedStatusEnum.All.id
    private var selectedValuePaymentMethod = OrderedStatusEnum.All.id
    private var offlineChannelId: Int? = null
    private val viewModel: StoreDashboardViewModel by viewModels()
    private val viewModelOrder: OrderedViewModel by viewModels()
    private var isGrab: Boolean = false
    private var selectedGrabValue = OrderedGrabStatusMergeEnum.All.id

    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                keyword = edtSearch.getSearchContent()
                getStoreData()
            }
        } catch (e: Exception) {

        }
    }

    private fun postSearch(duration: Int) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogStoreOrderedBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initData()
        initView()
        initObserver()
        initListener()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.9).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {
        val content = arguments?.getString(CONTENT)

    }

    companion object {
        private const val LOGOUT_DIALOG = "LOGOUT_DIALOG"
        private const val CONTENT = "CONTENT"


        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            type: String?,
            isGrab: Boolean,
            startDate: String?,
            endDate: String?,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener, type, isGrab, startDate, endDate)
            fragment.show(fragmentManager, LOGOUT_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG) as? StoreOrderListDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
            type: String?,
            isGrab: Boolean,
            startDate: String?,
            endDate: String?
        ): StoreOrderListDialog {
            val args = Bundle()
            val fragment = StoreOrderListDialog()
            fragment.positiveButtonListener = iAgreeListener
            type?.let { fragment.type = it }
            startDate?.let { fragment.startDateString = it }
            endDate?.let { fragment.endDateString = it }
            fragment.isGrab = isGrab
            fragment.arguments = args
            return fragment
        }
    }

    private fun initObserver() {
        viewModelOrder.uiInfoState.observe(viewLifecycleOwner) { state ->
            binding?.apply {
                state.orderedInfoResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
                            layoutProgressBar.isVisible = true
                        }

                        is ApiResponse.Error -> {
                            layoutProgressBar.isVisible = false
                            if (!it.message.isNullOrEmpty()) Toast.makeText(
                                context,
                                it.message,
                                Toast.LENGTH_SHORT
                            ).show() else {
                            }
                        }

                        is ApiResponse.Success -> {
                            layoutProgressBar.isVisible = false
                            layoutMain.isVisible = false
                            layoutDetail.isVisible = true
                            layoutDetail.setOrderInfo(it.data)
                            parentFragmentManager
                            layoutDetail.replaceorderedInfo(it.data)
                        }
                    }
                }

                state.record?.let {
                }

            }
        }
        viewModel.uiStoreOrdersState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        layoutProgressBar.isVisible = true
                }

                if (it.isRefresh != false) {
                    //刷新的时候重置一下没有数据状态
                    refreshLayout.resetNoMoreData()
                }

                if (it.showEnd) {
                    layoutProgressBar.isVisible = false
                    if (it.isRefresh != false) {
                        layoutEmpty.root.setVisibleGone(true)
                        dashboardListAdapter.replaceData(arrayListOf())
                        refreshLayout.finishRefresh()
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    layoutProgressBar.isVisible = false
                    if (error.isNotEmpty()) Toast.makeText(context, error, Toast.LENGTH_SHORT)
                        .show()
                }

                it.showSuccess?.let { response ->
                    layoutProgressBar.isVisible = false
                    layoutEmpty.root.setVisibleGone(false)
                    if (it.isRefresh != false) {
                        ArrayList(response.storeDataOrderVos).let { it1 ->
                            dashboardListAdapter.replaceData(
                                it1, response.offlineChannelTotalModel
                            )
                        }
                        refreshLayout.finishRefresh()

                    } else {
                        refreshLayout.finishLoadMore()
                        //delay for waiting hide Footer LoadSuccess
                        viewLifecycleOwner.lifecycleScope.launch {
                            delay(400)
                            ArrayList(response.storeDataOrderVos).let { it1 ->
                                dashboardListAdapter.addData(
                                    it1
                                )
                            }
                        }


                    }
                }
            }
        }

    }


    private fun initView() {
        binding?.apply {
            context?.let {
                getStoreData()
            }
            if (isGrab) {
                llStore.isVisible = false
                llGrab.isVisible = true
            } else {
                llStore.isVisible = true
                llGrab.isVisible = false
            }
            layoutDetail.setFragmentManager(parentFragmentManager)
            dashboardListAdapter = StoreOrderListAdapter(arrayListOf()) {

                it.orderNo?.let { it1 ->
                    viewModelOrder.getOrderedInfoInStoreInfo(it1, isGrab)
                    if (isGrab) {
                        layoutDetail.setTitle(it.grabOrder?.grabOrderNo?:"")
                    } else {
                        layoutDetail.setTitle(it1)
                    }
                }

            }
            if (startDateString.isNotEmpty() && endDateString.isNotEmpty()) {
                val selectedDateRange = "$startDateString - $endDateString"
                tvCalendar.text = selectedDateRange
                tvCalendar.updateCalendarColor()
            }
            recyclerviewStoreOrder.layoutManager = LinearLayoutManager(context)
            recyclerviewStoreOrder.adapter = dashboardListAdapter

            if (isGrab)
                dropdownPaymentMethod.isVisible = false;

        }
    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        val constraintsBuilder = CalendarConstraints.Builder()
        // Set the minimum year
        constraintsBuilder.setStart(Calendar.getInstance().apply {
            set(Calendar.YEAR, 2024)
            set(Calendar.MONTH, Calendar.JANUARY)
        }.timeInMillis)
        val constraints = constraintsBuilder.build()
        builder.setCalendarConstraints(constraints)
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate))
            endDateString = sdf.format(Date(endDate))

            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"

            // Displaying the selected date range in the TextView
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
            type = ""
            getStoreData()
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun initListener() {
        binding?.apply {
//            edtSearch.setOnEditorActionListener { v, actionId, event ->
//                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                    activity?.hideKeyboard(edtSearch)
//                }
//                return@setOnEditorActionListener false
//            }
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getStoreData(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getStoreData(false)
                }

            })
            tvCalendar.setOnClickListener {
                datePickerDialog()
            }
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }
            layoutDetail.onCloseListener = {
                layoutDetail.isVisible = false
                layoutMain.isVisible = true
            }
            layoutDetail.onBackListener = {
                layoutDetail.isVisible = false
                layoutMain.isVisible = true
            }
            edtSearch.setTextChangedListenerCallBack {
                postSearch(800)
            }
//            edtSearch.addTextChangedListener {
////                keyword = it.toString()
////                getStoreData()
//                postSearch(800)
//            }
            tvClearFilter.setOnClickListener {
                tvCalendar.text = ""
                tvCalendar.updateCalendarColor()
                tvOrderStatus.text = getString(R.string.all_order)
                tvDropPaymentMethod.text = getString(R.string.all_payment_method)
                startDateString = ""
                endDateString = ""
                type = "1"
                selectedValuePaymentMethod = OrderedStatusEnum.All.id
                selectedValue = -1
                selectedGrabValue = OrderedGrabStatusMergeEnum.All.id
                offlineChannelId = null
                edtSearch.setSearchContent("")
                edtSearch.removeFocus()
//                activity?.hideKeyboard(edtSearch)
                getStoreData()
            }

            dropdownOrderStatus.setOnClickListener {
                arrowOrderStatus.animate().rotation(180f).setDuration(200)
                dropdownOrderStatus.setBackgroundResource(R.drawable.background_spinner_top)
                if (isGrab) {
                    showPopupFilterGrabStatus(dropdownOrderStatus)
                } else {
                    showPopupFilterStatus(dropdownOrderStatus)
                }
            }

            dropdownPaymentMethod.setOnClickListener {
                arrowPaymentMethod.animate().rotation(180f).setDuration(200)
                dropdownPaymentMethod.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupFilterPaymentMethod2(dropdownPaymentMethod)
            }
        }
    }

    private fun showPopupFilterGrabStatus(anchorView: View) {
        binding?.apply {
            activity?.hideKeyboard(edtSearch.getEditText()!!)
        }

        val popupView = PopupGrabOrderFilterBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root, anchorView.width, ViewGroup.LayoutParams.WRAP_CONTENT, true
        )
        PopupWindowHelper.addPopupWindow(popupWindow)
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrowOrderStatus?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

//        All(""),
//        NO_DRIVER("NO_DRIVER"),
//        DRIVER_ALLOCATED("DRIVER_ALLOCATED"),
//        DRIVER_ARRIVED("DRIVER_ARRIVED"),
//        COLLECTED("COLLECTED"),
//        DELIVERED("DELIVERED"),
//        CANCELLED("CANCELLED"),
//        FAILED("FAILED"),

        context?.let {
            when (selectedGrabValue) {
                OrderedGrabStatusMergeEnum.All.id -> setSelectedLanguages(
                    popupView.tvAllOrder, it
                )

                OrderedGrabStatusMergeEnum.NO_DRIVER.id -> setSelectedLanguages(
                    popupView.tvReceivedOrder, it
                )

                OrderedGrabStatusMergeEnum.DRIVER_ALLOCATED.id -> setSelectedLanguages(
                    popupView.tvHavePostMan, it
                )

                OrderedGrabStatusMergeEnum.DRIVER_ARRIVED.id -> setSelectedLanguages(
                    popupView.tvPostmanArrive, it
                )

                OrderedGrabStatusMergeEnum.DELIVERED.id -> setSelectedLanguages(
                    popupView.tvFinishOrder, it
                )

                OrderedGrabStatusMergeEnum.COLLECTED.id -> setSelectedLanguages(
                    popupView.tvPostmanTakeOrder, it
                )

                OrderedGrabStatusMergeEnum.CANCELLED.id -> setSelectedLanguages(
                    popupView.tvCancel, it
                )

                OrderedGrabStatusMergeEnum.FAILED.id -> setSelectedLanguages(
                    popupView.tvFailOrder, it
                )
            }
        }

        popupView.tvAllOrder.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.All.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvAllOrder.text.toString()
            )
        }

        popupView.tvReceivedOrder.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.NO_DRIVER.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvReceivedOrder.text.toString()
            )
        }
        popupView.tvHavePostMan.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.DRIVER_ALLOCATED.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvHavePostMan.text.toString()
            )
        }
        popupView.tvPostmanArrive.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.DRIVER_ARRIVED.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvPostmanArrive.text.toString()
            )
        }

        popupView.tvPostmanTakeOrder.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.COLLECTED.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvPostmanTakeOrder.text.toString()
            )
        }


        popupView.tvFinishOrder.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.DELIVERED.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvFinishOrder.text.toString()
            )
        }

        popupView.tvCancel.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.CANCELLED.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvCancel.text.toString()
            )
        }

        popupView.tvFailOrder.setOnClickListener {
            selectedGrabValue = OrderedGrabStatusMergeEnum.FAILED.id
            setValueOnclickGrabStatus(
                popupWindow,
                selectedGrabValue,
                popupView.tvFailOrder.text.toString()
            )
        }

    }


    private fun setValueOnclickGrabStatus(popupWindow: PopupWindow, text: String, text2: String) {

        binding?.apply {
            tvOrderStatus.text = text2;
        }

        getStoreData()
        popupWindow.dismiss()
    }


    private fun getStoreData(isRefresh: Boolean? = null) {
        orderStatus =
            if (selectedValue == OrderedStatusEnum.All.id) "" else selectedValue.toString()
        paymentMenthod =
            if (selectedValuePaymentMethod == OrderedStatusEnum.All.id) "" else selectedValuePaymentMethod.toString()

        var grabStatuss = ArrayList<String>()
        if (isGrab) {
            orderStatus = OrderedGrabStatusMergeEnum.All.id
            grabStatuss.add(selectedGrabValue)
        }

        viewModel.getStoreOrderList(
            isRefresh,
            startDate = startDateString,
            endDate = endDateString,
            paymentMethod = paymentMenthod,
            orderStatus = orderStatus,
            isGrab = isGrab,
            keyword = keyword,
            channelsId = offlineChannelId,
            grabStatuss = grabStatuss
        )
    }

    private fun showPopupFilterStatus(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupOrderFilterBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrowOrderStatus?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }


        context?.let {
            when (selectedValue) {
                OrderedStatusMergeEnum.UNPAID_CONFIRM.id ->
                    setSelectedLanguages(
                        popupView.tvUnpaid,
                        it
                    )

                OrderedStatusMergeEnum.PAID.id ->
                    setSelectedLanguages(
                        popupView.tvPaid,
                        it
                    )

                OrderedStatusMergeEnum.All.id ->
                    setSelectedLanguages(
                        popupView.tvAllOrder,
                        it
                    )

                OrderedStatusMergeEnum.CANCEL_ORDER.id ->
                    setSelectedLanguages(
                        popupView.tvCancel,
                        it
                    )

                OrderedStatusMergeEnum.PREORDER.id ->
                    setSelectedLanguages(
                        popupView.tvPreOrder,
                        it
                    )

                OrderedStatusMergeEnum.CANCEL_ORDER.id ->
                    setSelectedLanguages(
                        popupView.tvCancel,
                        it
                    )

                OrderedStatusMergeEnum.REFUNDS.id ->
                    setSelectedLanguages(
                        popupView.tvRefunds,
                        it
                    )
            }
        }
        popupView.tvUnpaid.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.UNPAID_CONFIRM.id
            setValueOnclickStatus(
                popupWindow,
                popupView.tvUnpaid.text.toString(),
                binding?.tvOrderStatus
            )
        }
        popupView.tvPaid.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.PAID.id
            setValueOnclickStatus(
                popupWindow,
                popupView.tvPaid.text.toString(),
                binding?.tvOrderStatus
            )
        }
        popupView.tvRefunds.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.REFUNDS.id
            setValueOnclickStatus(
                popupWindow,
                popupView.tvRefunds.text.toString(),
                binding?.tvOrderStatus
            )
        }
        popupView.tvAllOrder.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.All.id
            setValueOnclickStatus(
                popupWindow,
                popupView.tvAllOrder.text.toString(),
                binding?.tvOrderStatus
            )
        }
        popupView.tvCancel.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.CANCEL_ORDER.id
            setValueOnclickStatus(
                popupWindow,
                popupView.tvCancel.text.toString(),
                binding?.tvOrderStatus
            )
        }

        popupView.tvPreOrder.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.PREORDER.id
            setValueOnclickStatus(
                popupWindow,
                popupView.tvPreOrder.text.toString(),
                binding?.tvOrderStatus
            )
        }
    }

    private fun setValueOnclickStatus(popupWindow: PopupWindow, text: String, textView: TextView?) {
        getStoreData()
        textView?.setText(text)
        popupWindow.dismiss()
    }

    private fun setSelectedLanguages(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }

//    private fun showPopupFilterPaymentMethod(anchorView: View) {
//        activity?.hideKeyboard()
//        val popupView = PopupPaymentMethodBinding.inflate(layoutInflater)
//        val popupWindow = PopupWindow(
//            popupView.root,
//            anchorView.width,
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            true
//        )
//
//        popupWindow.animationStyle = R.style.PopupAnimation
//        popupWindow.showAsDropDown(anchorView)
//        popupWindow.setOnDismissListener {
//            binding?.arrowPaymentMethod?.animate()?.rotation(0f)?.setDuration(200)
//            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
//        }
//
//
//        context?.let {
//            when (selectedValuePaymentMethod) {
//                PayTypeEnum.ONLINE_PAYMENT.id ->
//                    setSelectedLanguages(
//                        popupView.tvOnlinePayment,
//                        it
//                    )
//
//                PayTypeEnum.CASH_PAYMENT.id ->
//                    setSelectedLanguages(
//                        popupView.tvPayByCash,
//                        it
//                    )
//
//                PayTypeEnum.USER_BALANCE.id ->
//                    setSelectedLanguages(
//                        popupView.tvPayByBalance,
//                        it
//                    )
//
//                else ->
//                    setSelectedLanguages(
//                        popupView.tvAllPayment,
//                        it
//                    )
//
//            }
//        }
//        popupView.tvOnlinePayment.setOnClickListener {
//            selectedValuePaymentMethod = PayTypeEnum.ONLINE_PAYMENT.id
//            setValueOnclickStatus(
//                popupWindow,
//                popupView.tvOnlinePayment.text.toString(),
//                binding?.tvDropPaymentMethod
//            )
//        }
//        popupView.tvAllPayment.setOnClickListener {
//            selectedValuePaymentMethod = -1
//            setValueOnclickStatus(
//                popupWindow,
//                popupView.tvAllPayment.text.toString(),
//                binding?.tvDropPaymentMethod
//            )
//        }
//        popupView.tvPayByCash.setOnClickListener {
//            selectedValuePaymentMethod = PayTypeEnum.CASH_PAYMENT.id
//            setValueOnclickStatus(
//                popupWindow,
//                popupView.tvPayByCash.text.toString(),
//                binding?.tvDropPaymentMethod
//            )
//        }
//        popupView.tvPayByBalance.setOnClickListener {
//            selectedValuePaymentMethod = PayTypeEnum.USER_BALANCE.id
//            setValueOnclickStatus(
//                popupWindow,
//                popupView.tvPayByBalance.text.toString(),
//                binding?.tvDropPaymentMethod
//            )
//        }
//    }

    private fun showPopupFilterPaymentMethod2(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupPaymentMethod2Binding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrowPaymentMethod?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

        popupView.paymentMethodsRecyclerView.adapter =
            DropPaymentMethodAdapter(viewModel.paymentMethodList ?: arrayListOf()) {
                selectedValuePaymentMethod = it.paymentMethod
                offlineChannelId = it.offlineChannelId
                getStoreData()
                context?.let { context ->
                    binding?.tvDropPaymentMethod?.text = it.getPayMethod(context)
                }
                popupWindow.dismiss()
            }

    }
}
