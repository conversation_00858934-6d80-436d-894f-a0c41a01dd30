package com.metathought.food_order.casheir.ui.second_display

import android.content.Context
import android.media.MediaRouter
import android.view.Display
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse

/**
 * <AUTHOR>
 * @date 2024/5/1314:02
 * @description
 */
@Deprecated(message = "旧版V2副屏方案")
object SecondaryManagerV2 {

    private var display: Display? = null
    var userInfo: UserLoginResponse? = null
    var secondaryScreenUI: SecondaryScreenUI? = null
    var isDestroy = false
    fun onCreate() {
        isDestroy = false
    }

    fun onDestroy() {
        dismissScreen()
        isDestroy = true
    }

    //0:other
    private var routeIndex = SecondaryRoute.DEFAULT
    private fun getPresentationDisplay(context: Context): Display? {
        if (display == null) {
//            val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager?
//            val displays =displayManager!!.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
            //            if(displays.isNotEmpty()){
//                display=displays[0]
//            }
            val mediaRouter =
                context.getSystemService(Context.MEDIA_ROUTER_SERVICE) as MediaRouter
            val route = mediaRouter.getSelectedRoute(MediaRouter.ROUTE_TYPE_LIVE_VIDEO)
            display = route.presentationDisplay
        }
        return display
    }

    fun getSecondaryUI(context: Context): SecondaryScreenUI? {
        if (!isDestroy) {
            getPresentationDisplay(context)?.apply {
                if (secondaryScreenUI == null) {
                    secondaryScreenUI = SecondaryScreenUI(context, this)
                    secondaryScreenUI?.show()
                }else{
                    if(secondaryScreenUI?.isShowing!=true){
                        secondaryScreenUI?.show()
                    }
                }
            }
        }
        return secondaryScreenUI
    }

    fun showDefault(context: Context) {
        if (userInfo != null) {
            showOtherScreen(context, userInfo)
        }
    }

    fun isOrderedScreen() = routeIndex == SecondaryRoute.ORDERED_INFO
    fun isPaymentQrScreen() =
        routeIndex == SecondaryRoute.PAYMENT_QR || routeIndex == SecondaryRoute.PAYMENT_RESULT

    fun getOrderedScreen(context: Context): SecondaryScreenUI? {
        if (!isDestroy) {
            setRoute(SecondaryRoute.ORDERED_INFO)
            getSecondaryUI(context)
        }
        return secondaryScreenUI
    }

    fun getMenuOrderScreen(context: Context): SecondaryScreenUI? {
        if (!isDestroy) {
            setRoute(SecondaryRoute.MENU_ORDER)
            getSecondaryUI(context)?.showMenuOrder()
        }
        return secondaryScreenUI
    }

    fun showOtherScreen(context: Context, user: UserLoginResponse? = null) {
        if (!isDestroy) {
            if (user != null) {
                <EMAIL> = user
            }
            setRoute(SecondaryRoute.DEFAULT)
            getSecondaryUI(context)?.showDefault(userInfo)
        }
    }

    fun dismissScreen() {
        secondaryScreenUI?.run {
            onDestroy()
            dismiss()
        }
    }



//    fun showPaymentSecond(context: Context, response: PaymentResponse?): SecondaryScreenUI? {
//        if (!isDestroy) {
//            setRoute(SecondaryRoute.PAYMENT_QR)
//            getSecondaryUI(context)?.showQRPayment(response?.qrcode)
//        }
//        return secondaryScreenUI
//    }

    fun showPaymentResult(context: Context, orderedInfo: OrderedInfoResponse?): SecondaryScreenUI? {
        if (!isDestroy) {
            setRoute(SecondaryRoute.PAYMENT_RESULT)
            getSecondaryUI(context)?.showPaymentResult(orderedInfo)
        }
        return secondaryScreenUI
    }

    fun setDefaultRoute() {
        setRoute(SecondaryRoute.DEFAULT)
    }

    private fun setRoute(@SecondaryRoute secondaryRoute: Int) {
        routeIndex = secondaryRoute
    }


}