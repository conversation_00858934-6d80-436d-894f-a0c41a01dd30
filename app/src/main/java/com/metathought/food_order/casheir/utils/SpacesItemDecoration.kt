package com.metathought.food_order.casheir.utils


/**
 *<AUTHOR>
 *@time  2024/7/10
 *@desc
 **/
import android.graphics.Rect
import androidx.recyclerview.widget.RecyclerView

class SpacesItemDecoration(private val space: Int) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(outRect: Rect, itemPosition: Int, parent: RecyclerView) {
//        outRect.left = space
//        outRect.right = space
//        outRect.bottom = space

        // Add top spacing for first item to avoid double space between items
        if (itemPosition != 0) {
            outRect.top = space
        }
    }
}