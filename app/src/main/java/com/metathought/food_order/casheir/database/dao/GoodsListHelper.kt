package com.metathought.food_order.casheir.database.dao

import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.database.GoodsListRecord
import com.metathought.food_order.casheir.database.GoodsRecord
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import org.litepal.LitePal
import org.litepal.extension.findFirst

/**
 * <AUTHOR>
 * @date 2024/3/1910:00
 * @description
 */
object GoodsListHelper {

    fun get(localDiningStyle: Int): GoodsListRecord? {
        return LitePal.where(
            "diningStyle = ? and storeId= ? ",
            localDiningStyle.toString(), MainDashboardFragment.CURRENT_USER?.storeId ?: ""
        ).findFirst<GoodsListRecord>()
    }


    //    fun loadDataToMap(): ArrayList<GoodsRequest>? {
//        val list= LitePal.findAll<GoodsRequest>()
//        if(list.isNotEmpty()){
//            list.forEach {
//
//            }
//        }
//    }

    fun del(localDiningStyle: Int) {
        LitePal.deleteAll(
            GoodsListRecord::class.java,
            "diningStyle = ?",
            localDiningStyle.toString()
        )
    }


    fun update(diningStyle: Int, goodsInfo: String, lastUpdateDate: String) {
        val model = get(diningStyle)
        if (model == null) {
            GoodsListRecord(
                diningStyle = diningStyle,
                goodsInfo,
                lastUpdateDate = lastUpdateDate,
                storeId = MainDashboardFragment.CURRENT_USER?.storeId ?: ""
            ).save()
        } else {
            model.goodsInfo = goodsInfo
            model.lastUpdateDate = lastUpdateDate
            model.save()
        }
    }

}