package com.metathought.food_order.casheir.ui.ordered.weight

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.databinding.DialogMealSetWeightGoodsBinding
import com.metathought.food_order.casheir.ui.adapter.MealSetWeightGoodsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


/**
 * 套餐称重子商品弹窗
 */
@AndroidEntryPoint
class MealSetWeightGoodsDialog : BaseDialogFragment() {
    private var binding: DialogMealSetWeightGoodsBinding? = null

    private var orderMealSetGoods: List<OrderMealSetGood>? = null
    private var onWeightChange: ((OrderMealSetGood) -> Unit)? = null

    //    private var tags:List
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMealSetWeightGoodsBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initObserver()
        initListener()
        initData()
    }

    private var adapter: MealSetWeightGoodsAdapter? = null
    private fun initData() {
        binding?.apply {
            adapter = MealSetWeightGoodsAdapter(
                orderMealSetGoods?.filter { it.isToBeWeighed() } ?: emptyList()
            ) { goods ->
                EditWeightDialog(requireContext()).showDialog(
                    WeightData(
                        weight = goods.weight,
                        weightUnit = goods.getWeightUnit(),
                        singleDiscount = goods.priceMarkup,
                    )
                ) { weight ->
                    if (weight.toDoubleOrNull() != goods.weight) {
                        goods.weight = weight.toDouble()
                        goods.weighingCompleted = true
                        adapter?.updateItem(goods)
                        onWeightChange?.invoke(goods)
                    }
                }
            }
            recyclerView.adapter = adapter
        }
    }

    private fun initObserver() {

    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismiss()
            }


        }
    }

    override fun dismiss() {
        super.dismiss()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
    }

    override fun onResume() {
        super.onResume()
    }

    companion object {
        private const val TAG = "MealSetWeightGoodsDialo"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderMealSetGoods: List<OrderMealSetGood>? = null,
            onConfirmClick: ((OrderMealSetGood) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                orderMealSetGoods,
                onConfirmClick
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(TAG) as? MealSetWeightGoodsDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            orderMealSetGoods: List<OrderMealSetGood>? = null,
            onConfirmClick: ((OrderMealSetGood) -> Unit)? = null
        ): MealSetWeightGoodsDialog {
            val fragment = MealSetWeightGoodsDialog()
            fragment.orderMealSetGoods = orderMealSetGoods
            fragment.onWeightChange = onConfirmClick
            return fragment
        }
    }


}