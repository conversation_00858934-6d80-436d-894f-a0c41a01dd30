package com.metathought.food_order.casheir.network.service

import com.metathought.food_order.casheir.data.model.base.response_model.GoogleResponse
import retrofit2.Call

import retrofit2.http.GET

import retrofit2.http.Query


interface GeocodeApiService {
    // 根据地址查询地理坐标等信息，这里返回类型可根据实际解析后的结果自定义合适的类型，这里暂时用String
    @GET("maps/api/geocode/json")
    fun getGeocodeData(
        @Query("latlng") address: String,
        @Query("key") apiKey: String,
        @Query("language") lang: String
    ): Call<GoogleResponse>
}