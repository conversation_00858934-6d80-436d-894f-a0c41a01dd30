package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood

/**
 * <AUTHOR>
 * @date 2025/04/09 16:15
 * @description
 */
data class UpdateOrderOrGoodNoteRequest(
    //订单id
    val orderNo: String? = null,
    //订单备注
    val note: String? = null,
    //菜品备注列表
    val modifyGoodsNotesList: List<GoodNoteRequest>? = null
)


data class GoodNoteRequest(
    val hashKey: String? = null,
    val note: String? = null,

    val goodsId: String?,
    val orderId: String?,
    val feeds: List<Feed>?,
    val tagItems: List<GoodsTagItem>?,
    val orderMealSetGoodsDTOList: List<OrderMealSetGood>? = null,
    val pricingMethod: Int?,
    val weight: Double?,
    val acceptOrderId: String?,
    val goodsPriceKey: String?,
    val discountTypeRemarkKey: String?,
    val packingFeeKey: String?,
    val spuPriceKey: String?,
    val oldNote: String?
)
