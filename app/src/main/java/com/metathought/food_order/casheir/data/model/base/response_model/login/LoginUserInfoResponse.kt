package com.metathought.food_order.casheir.data.model.base.response_model.login


import com.google.gson.annotations.SerializedName

data class LoginUserInfoResponse(
    @SerializedName("loginTime")
    val loginTime: String?,
    @SerializedName("totalOrderNumber")
    val totalOrderNumber: Int?,
    @SerializedName("unpaidOrderNumber")
    val unpaidOrderNumber: Int?,
    @SerializedName("userId")
    val userId: String?,
    @SerializedName("userName")
    val userName: String?,
    @SerializedName("userType")
    val userType: String?
)