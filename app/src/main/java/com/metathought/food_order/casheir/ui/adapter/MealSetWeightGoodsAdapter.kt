package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list.StoreDataOrderVo
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.databinding.MealSetWeightGoodsItemBinding
import com.metathought.food_order.casheir.databinding.StoreOrderItemBinding
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber
import java.math.BigDecimal
import java.util.Locale


class MealSetWeightGoodsAdapter(
    val orderMealSetGoods: List<OrderMealSetGood>,
    val onItemClickListener: (OrderMealSetGood) -> Unit,
) : RecyclerView.Adapter<MealSetWeightGoodsAdapter.StoreOrdersListViewHolder>() {

//    val list = mutableListOf<Pair<MealSetGoods, OrderMealSetGood>>()
//
//    init {
//        Timber.d("orderMealSetGoods:${orderMealSetGoods.toJson()}")
//        orderMealSetGoods.forEach {
//            val goodsGroup =
//                goods?.mealSetInfo?.mealSetGroupList?.firstOrNull { g -> g.id == it.mealSetGroupId }
//            val childGoods =
//                goodsGroup?.mealSetGoodsList?.firstOrNull { g -> g.goodsId == it.mealSetGoodsId }
//            if (childGoods?.isToBeWeighed() == true) {
//                list.add(Pair(childGoods, it))
//            }
//        }
//    }


    inner class StoreOrdersListViewHolder(val binding: MealSetWeightGoodsItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: OrderMealSetGood?, position: Int) {

            itemView.context.let { context ->
                binding.apply {
                    resource?.apply {
                        tvGoodName.text = mealSetGoodsName
                        val tagStr = getTagStr(Locale.getDefault())
                        tvSpecification.text = tagStr
                        tvSpecification.isVisible = tagStr.isNotEmpty() == true

                        tvWeight.isVisible = false
                        if (isHasCompleteWeight()) {
                            tvWeight.text = "(${getWeightStr()})"
                            tvWeight.isVisible = true

                            val price = priceMarkup ?: 0
                            tvPrice.text = if (price > 0) {
                                val singleDiscount = priceMarkup ?: 0
                                val weight = weight ?: 0.0
                                (singleDiscount * weight).toLong().priceFormatTwoDigitZero2()
                            } else {
                                "$0.00"
                            }
                        } else {
                            tvPrice.text = context.getString(R.string.to_be_weighed)
                        }
                        root.setOnClickListener {
                            SingleClickUtils.isFastDoubleClick(800) {
                                onItemClickListener.invoke(this)
                            }

                        }
                    }

                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StoreOrdersListViewHolder {
        val itemView =
            MealSetWeightGoodsItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return StoreOrdersListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return orderMealSetGoods.size
    }

    override fun onBindViewHolder(holder: StoreOrdersListViewHolder, position: Int) {
        orderMealSetGoods[position].let { holder.bind(it, position) }
    }

    fun updateItem(orderGoods: OrderMealSetGood) {
        val index =
            orderMealSetGoods.indexOfFirst { it.mealSetGroupId == orderGoods.mealSetGroupId && it.mealSetGoodsId == orderGoods.mealSetGoodsId }
        if (index != -1) {
            orderMealSetGoods[index].apply {
                weight = orderGoods.weight
                weighingCompleted = orderGoods.weighingCompleted
            }
            notifyItemChanged(index)
        }
    }


}