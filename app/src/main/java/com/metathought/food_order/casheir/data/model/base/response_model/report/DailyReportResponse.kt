package com.metathought.food_order.casheir.data.model.base.response_model.report

data class DailyReportResponse(
    val reportData: List<DailyReportData>?, //报表数据列表
    val summary: SummaryData?,   //汇总数据
    val paymentChannels: List<DailyReportPaymentChannels>?, //支付渠道列表
    val showCommission: Boolean?, //是否显示佣金
)

data class DailyReportData(
    val id: Long?,
    val date: String?,                  // 日期，格式：yyyy-MM-dd
    val dateDisplay: String?,           // 日期显示文本，按月时显示为"2025-01"
    val subtotal: Double?,               // 小计金额（不含税不含费用）
    val discountAmount: Double?,         // 减免金额（折扣、优惠券等）
    val serviceFee: Double?,             // 服务费
    val packingFee: Double?,             // 打包费
    val vatAmount: Double?,              // 增值税（VAT）
    val commission: Double?,             // 佣金（外卖平台等）
    val refundAmount: Double?,             // 退款金额
    val total: Double?,                  // 总计金额（最终收入）
    val paymentChannelAmounts: Map<String, Double>?, // 各支付渠道的金额分布 渠道代码：渠道金额
    val orderCount: Int?,                // 订单数量
    val hasDiscountDetail: Boolean?      // 是否有折扣详情可查看
)

data class SummaryData(
    val totalCount: Int?,                    // 总记录数（如31天或12个月）
    val totalSubtotal: Double?,              // 小计总额
    val totalDiscountAmount: Double?,        // 减免总额
    val totalServiceFee: Double?,            // 服务费总额
    val totalPackingFee: Double?,            // 打包费总额
    val totalVatAmount: Double?,             // 增值税总额
    val totalRefundAmount: Double?,             // 退款总额
    val totalCommission: Double?,            // 佣金总额
    val grandTotal: Double?,                 // 总计金额
    val totalPaymentChannelAmounts: Map<String, Double>?, // 各支付渠道汇总
    val totalOrderCount: Int?                // 订单总数
)

data class DailyReportPaymentChannels(
    val channelCode: String?,    // 渠道代码（用于数据匹配）
    val channelName: String?,    // 渠道显示名称
    val channelType: String?,    // 渠道类型：ONLINE-线上，OFFLINE-线下，BALANCE-余额，CREDIT-挂账
    val sortOrder: Int?,          // 排序权重（用于前端排序）
    val enabled: Boolean?        // 是否启用
)
