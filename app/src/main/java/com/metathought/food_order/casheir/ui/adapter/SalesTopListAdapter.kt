package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.TopSaleRecord
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesOrdersDetailVo
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleReportListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleTopListBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description 销售报表预览adapter
 */
class SalesTopListAdapter(
    val ctx:Context,
    val list: ArrayList<TopSaleRecord>,
) : RecyclerView.Adapter<SalesTopListAdapter.ProduceReportItemViewHolder>() {


    inner class ProduceReportItemViewHolder(val binding: ItemSaleTopListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: TopSaleRecord, position: Int) {
            binding.apply {

                tvName.text = "${resource.name ?: ""}"
                tvPrice.text = resource.sellPrice?.priceFormatTwoDigitZero2()?:"0"
                tvSaleNum.text = "${resource.soldNum ?: ""}"

                Glide.with(ctx).load(resource.picUrl)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                   // .placeholder(R.drawable.icon_menu_default2)
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .centerCrop()
                  //  .error(R.drawable.icon_menu_default2)
                    .into(binding.image)

                vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemSaleTopListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: SalesTopListAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<TopSaleRecord>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}