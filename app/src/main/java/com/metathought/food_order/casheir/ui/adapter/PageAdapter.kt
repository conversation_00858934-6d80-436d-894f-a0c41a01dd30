package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.PageItemBinding


class PageAdapter(
    private var pageSize: Int,
    val context: Context,
    private var checkedIndex: Int,
    val onClickCallback: (Int) -> Unit
) : RecyclerView.Adapter<PageAdapter.PageViewHolder>() {

    inner class PageViewHolder(val binding: PageItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(showIndex: Int) {
            binding.apply {
                binding.tvValue.text = showIndex.toString()
                binding.root.strokeColor = ContextCompat.getColor(
                    context,
                    if (checkedIndex == showIndex) R.color.primaryColor else R.color.backgroundSpinner
                )
                root.setOnClickListener {
                    setCheckedIndex(showIndex)
                    onClickCallback(showIndex)
                }
            }
        }
    }

    fun moveNextPage() {
        if (checkedIndex < pageSize) {
            checkedIndex++
            notifyDataSetChanged()
            onClickCallback(checkedIndex)
        }
    }

    fun movePreviousPage() {
        if (checkedIndex > 1) {
            checkedIndex--
            notifyDataSetChanged()
            onClickCallback(checkedIndex)
        }
    }

    fun setCheckedIndex(position: Int) {
        checkedIndex = position
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        return PageViewHolder(
            PageItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return pageSize
    }

    override fun onBindViewHolder(holder: PageViewHolder, position: Int) {
        holder.bind(position + 1)
    }

}