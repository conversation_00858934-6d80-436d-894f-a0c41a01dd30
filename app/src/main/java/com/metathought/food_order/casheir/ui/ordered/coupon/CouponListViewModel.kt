package com.metathought.food_order.casheir.ui.ordered.coupon

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PrinterAgainRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MultipleOrderResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import retrofit2.http.Path
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class CouponListViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    fun getCoupon(
        diningStyle: Int? = null,
        goodsList: List<GoodsBo>? = null,
        isPreOrder: Boolean? = null,
        note: String? = null,
        tableUuid: String? = null,
        orderNo: String? = null
    ) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val response = repository.findOrderCanUseCoupon(
                    diningStyle = diningStyle,
                    goodsList = goodsList,
                    isPreOrder = isPreOrder,
                    note = note,
                    tableUuid = tableUuid,
                    orderNo = orderNo
                )
                emitUiState(response)

            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(""))
            }
        }
    }

    fun findTopUpCanUseCoupon(
        addNum: BigDecimal? = null,
        id: String? = null
    ) {
        viewModelScope.launch {
            try {
                emitUiState(ApiResponse.Loading)
                val response = repository.findTopUpCanUseCoupon(addNum, id)
                emitUiState(response)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(""))
            }
        }
    }


    private fun emitUiState(
        success: ApiResponse<List<CouponModel>>? = null,
    ) {
        val uiModel = UIModel(success)
        _uiState.postValue(uiModel)
    }

    data class UIModel(
        val response: ApiResponse<List<CouponModel>>?,
    )

}