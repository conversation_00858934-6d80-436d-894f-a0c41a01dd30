package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.databinding.ItemCouponGoodBinding
import com.metathought.food_order.casheir.databinding.ItemRechrageDetailBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.member.dialog.RechargeDetailDialog

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description
 */
class RechargeDetailAdapter :
    RecyclerView.Adapter<RechargeDetailAdapter.CouponGiftGoodViewHolder>() {

    private val list = mutableListOf<RechargeDetailDialog.RechargeDetailItem>()

    inner class CouponGiftGoodViewHolder(val binding: ItemRechrageDetailBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: RechargeDetailDialog.RechargeDetailItem) {
            binding.apply {
                tvTitle.text = resource.title
                tvContent.text = resource.content
                tvContent.movementMethod = LinkMovementMethod.getInstance()
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CouponGiftGoodViewHolder(
        ItemRechrageDetailBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: RechargeDetailAdapter.CouponGiftGoodViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<RechargeDetailDialog.RechargeDetailItem>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}