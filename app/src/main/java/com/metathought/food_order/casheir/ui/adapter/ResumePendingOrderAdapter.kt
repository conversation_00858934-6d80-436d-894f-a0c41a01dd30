package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingRecord
import com.metathought.food_order.casheir.databinding.ResumeOrderItemBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class ResumePendingOrderAdapter(
    val list: ArrayList<PendingRecord>,
    val onItemClickListener: (PendingRecord) -> Unit,
) : RecyclerView.Adapter<ResumePendingOrderAdapter.PendingOrderViewHolder>() {

    private var selectedPosition = -1

    inner class PendingOrderViewHolder(val binding: ResumeOrderItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: PendingRecord, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvTableID.text = resource.serialNumber
                        val itemsValue =
                            "${getString(R.string.items)}: ${it?.totalNum}"
                        tvItems.text = itemsValue

                        val timeValue =
                            "${getString(R.string.ordering_time)}: ${it.createTime?.formatDate()}"
                        tvTime.text = timeValue

                        var priceValue = "${it.totalPrice?.priceFormatTwoDigitZero2()}"
                        if (it.getPendingGoodJson()?.isHasDiscountPrice() == true) {
                            priceValue =
                                "${it.getPendingGoodJson()?.totalDiscountPrice?.priceFormatTwoDigitZero2()}"
                        }
                        tvPrice.text = priceValue
                        if (it?.isHasNeedProcess() == false) {
                            tvPrice.text = getString(R.string.to_be_confirmed)
                        }
                        tvRemark.isVisible = !resource.note.isNullOrEmpty()
                        tvRemark.text = "${getString(R.string.remark)} : ${it.note}"
                        root.setOnClickListener {
                            onItemClickListener.invoke(resource)
                        }
                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PendingOrderViewHolder {
        val itemView =
            ResumeOrderItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PendingOrderViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: PendingOrderViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


    fun replaceData(list: ArrayList<PendingRecord>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<PendingRecord>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }

}