package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.MemberOperationItemBinding
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment

/**
 * 会员操作按钮适配器 - 简化版
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class MemberOperationAdapter(
    val list: ArrayList<Record>
) : RecyclerView.Adapter<MemberOperationAdapter.OperationViewHolder>() {

    private var isCanTopUp = MainDashboardFragment.CURRENT_USER?.getPermissionList()
        ?.contains(PermissionEnum.RECHARGE.type) == true

    inner class OperationViewHolder(val binding: MemberOperationItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: Record, position: Int) {
            binding.apply {
                tvEdit.setOnClickListener {
                    mOnOperationListener?.onEditClick(resource)
                }

                tvDetail.setOnClickListener {
                    mOnOperationListener?.onDetailClick(resource)
                }

                tvTopUp.isVisible = isCanTopUp
                tvTopUp.setOnClickListener {
                    mOnOperationListener?.onRechargeClick(resource)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OperationViewHolder {
        val itemView = MemberOperationItemBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return OperationViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: OperationViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }

    fun replaceData(newList: ArrayList<Record>?) {
        if (newList != null) {
            this.list.clear()
            this.list.addAll(newList)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<Record>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }

    private var mOnOperationListener: OnOperationListener? = null

    fun setOnOperationListener(listener: OnOperationListener) {
        this.mOnOperationListener = listener
    }

    interface OnOperationListener {
        fun onRechargeClick(record: Record)
        fun onDetailClick(record: Record)
        fun onEditClick(record: Record)
    }
}
