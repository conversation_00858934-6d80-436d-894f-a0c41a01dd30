package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.SelectedSubMenuItemBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import timber.log.Timber


class SubFoodAdapter(
    val diningStyle: Int,
    val list: ArrayList<GoodsRequest>,
    val context: Context,
    val addListener: (GoodsRequest) -> Unit,
    val subListener: (GoodsRequest) -> Unit,
    val closeListener: () -> Unit,
    val reachLimitListener: (Boolean, Int) -> Unit,
) : RecyclerView.Adapter<SubFoodAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: SelectedSubMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.run {
                imgMinus.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
//                        reachLimitListener.invoke(false, 0)
                        val model = list[bindingAdapterPosition]
                        if (model.num == 1) {
                            list.removeAt(bindingAdapterPosition)
                            notifyItemRemoved(bindingAdapterPosition)
                            notifyItemRangeChanged(bindingAdapterPosition, itemCount)
                            notifyDataSetChanged()
                        } else {
                            model.num = (model.num ?: 0) - 1
//                            notifyItemChanged(bindingAdapterPosition)
                            notifyDataSetChanged()
                        }
                        subListener.invoke(model)
                        if (list.isEmpty()) {
                            closeListener.invoke()
                        }

                    }
                }
                imgPlus.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        val model = list[bindingAdapterPosition]
//                        val record = model.goods?.id?.let { GoodsHelper.get(it, diningStyle) }
//                        if(diningStyle == DiningStyleEnum.PRE_ORDER.id){
//                            if ((record?.num ?: 0) >= (model.goods?.restrictNum ?: 0)) {
//                                reachLimitListener.invoke(true, (model.goods?.restrictNum ?: 0))
//                                return@setOnClickListener
//                            }
//                        }
                        model.num = (model.num ?: 0) + 1
//                        notifyItemChanged(bindingAdapterPosition)
                        addListener.invoke(model)
                        notifyDataSetChanged()

                    }

                }
            }
        }

        fun bind(resource: GoodsRequest?, position: Int?) {
            resource?.let { _ ->
                binding.apply {
                    if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                        val record = resource.goods?.id?.let { GoodsHelper.get(it, diningStyle) }
                        val restrictNum = (resource.goods?.restrictNum
                            ?: 0)
                        if (restrictNum > 0) {
                            imgPlus.setImageDrawable(
                                ContextCompat.getDrawable(
                                    context,
                                    if ((record?.num ?: 0) < (resource.goods?.restrictNum
                                            ?: 0)
                                    ) R.drawable.ic_add else R.drawable.ic_add_disable
                                )
                            )
                            imgPlus.setEnable(
                                (record?.num ?: 0) < (resource.goods?.restrictNum ?: 0)
                            )
                            reachLimitListener.invoke(
                                (record?.num ?: 0) >= (resource.goods?.restrictNum ?: 0),
                                (resource.goods?.restrictNum ?: 0)
                            )
                        } else {
                            imgPlus.setImageDrawable(
                                ContextCompat.getDrawable(
                                    context,
                                    R.drawable.ic_add
                                )
                            )
                        }
                    } else {
                        imgPlus.setImageDrawable(
                            ContextCompat.getDrawable(
                                context,
                                R.drawable.ic_add
                            )
                        )
                    }
                    tvName.text = resource.goods?.name
                    tvQTY.text = resource.num.toString()


                    if (resource.goods?.isToBeWeighed() == true) {
                        tvPrice.text = "${resource.goods?.getShowPrice(context)}"
                        tvVipPrice.isVisible = false
                        tvOriginalPrice.isVisible = false
                    } else {
//                        tvPrice.text =
//                            resource.totalPrice().priceFormatTwoDigitZero2()

                        val isShowVipPrice = resource.goods?.isShowVipPrice()
                        val isHasDiscountPrice = resource.goods?.isHasDiscountPrice()
                        Timber.e("isShowVipPrice :${isShowVipPrice}    isHasDiscountPrice:${isHasDiscountPrice}")
                        tvVipPrice.isVisible = isShowVipPrice == true
//                        val sellPrice =
//                            if (isHasDiscountPrice == true) resource.totalDiscountPrice() else resource.totalPrice()
                        tvPrice.text =
                            "${resource.totalDiscountPrice()?.priceFormatTwoDigitZero2()}"
                        if (isShowVipPrice == true) {
                            tvVipPrice.text =
                                "${resource.totalVipPrice().priceFormatTwoDigitZero2()}"
                            tvOriginalPrice.isVisible = false
                        } else {
                            tvOriginalPrice.isVisible = isHasDiscountPrice == true
                            tvOriginalPrice.text =
                                "${resource.totalPrice()?.priceFormatTwoDigitZero2()}"
                        }
                    }


                    val goodsTag = resource.getGoodsTagStr()
                    tvSpecification.isGone = goodsTag.isEmpty()
                    tvSpecification.text = goodsTag
                }
            }
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView = SelectedSubMenuItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
//        viewHight = (parent.height / 3.8f).toInt()
//        itemView.root.layoutParams.height = viewHight
        return ViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
//        holder.itemView.minimumHeight = viewHight
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun updateItems(newItems: ArrayList<GoodsRequest>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }


}