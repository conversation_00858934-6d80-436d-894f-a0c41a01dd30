package com.metathought.food_order.casheir.ui.ordered.coupon

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.member.TopUpCanUseCouponRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.databinding.DialogCouponListBinding
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.MemberCouponListAdapter
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/8/25
 *@desc  优惠券列表
 **/
@AndroidEntryPoint
class CouponListDialog : DialogFragment() {
    companion object {
        private const val TAG = "CouponListDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            diningStyle: Int? = null,
            goodsList: List<GoodsBo>? = null,
            isPreOrder: Boolean? = null,
            note: String? = null,
            tableUuid: String? = null,
            orderNo: String? = null,
            currentCouponId: Long? = null,
            localCouponList: List<CouponModel>? = null,
            topUpCanUseCouponRequest: TopUpCanUseCouponRequest? = null,
            isShowVip: Boolean? = false,
            isHasUnWeight: Boolean? = false,
            identifySuccessListener: ((CouponModel?) -> Unit)
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    diningStyle = diningStyle,
                    goodsList = goodsList,
                    isPreOrder = isPreOrder,
                    note = note,
                    tableUuid = tableUuid,
                    orderNo = orderNo,
                    currentCouponId = currentCouponId,
                    localCouponList = localCouponList,
                    topUpCanUseCouponRequest,
                    isShowVip = isShowVip,
                    isHasUnWeight = isHasUnWeight,
                    identifySuccessListener
                )

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            diningStyle: Int? = null,
            goodsList: List<GoodsBo>? = null,
            isPreOrder: Boolean? = null,
            note: String? = null,
            tableUuid: String? = null,
            orderNo: String? = null,
            currentCouponId: Long? = null,
            localCouponList: List<CouponModel>? = null,
            topUpCanUseCouponRequest: TopUpCanUseCouponRequest? = null,
            isShowVip: Boolean? = false,
            isHasUnWeight: Boolean? = false,
            identifySuccessListener: ((CouponModel?) -> Unit)
        ): CouponListDialog {
            val fragment = CouponListDialog()
            fragment.diningStyle = diningStyle
            fragment.goodsList = goodsList
            fragment.isPreOrder = isPreOrder
            fragment.note = note
            fragment.tableUuid = tableUuid
            fragment.orderNo = orderNo
            fragment.currentCouponId = currentCouponId
            fragment.localCouponList = localCouponList
            fragment.topUpCanUseCouponRequest = topUpCanUseCouponRequest
            fragment.isShowVip = isShowVip
            fragment.isHasUnWeight = isHasUnWeight
            fragment.identifySuccessListener = identifySuccessListener
            return fragment
        }
    }

    private var binding: DialogCouponListBinding? = null
    private val viewModel: CouponListViewModel by viewModels()

    private var identifySuccessListener: ((CouponModel?) -> Unit)? = null

    private var diningStyle: Int? = null
    private var goodsList: List<GoodsBo>? = null
    private var isPreOrder: Boolean? = null
    private var note: String? = null
    private var tableUuid: String? = null
    private var orderNo: String? = null

    private var topUpCanUseCouponRequest: TopUpCanUseCouponRequest? = null

    private var localCouponList: List<CouponModel>? = null
    private var isShowVip: Boolean? = false
    private var isHasUnWeight: Boolean? = false

    //当前选中的优惠券ID
    private var currentCouponId: Long? = null


    private val adapter: MemberCouponListAdapter by lazy {
        MemberCouponListAdapter(
            list = mutableListOf(),
            isShowVip,
            isHasUnWeight,
            topUpCanUseCouponRequest
        ) {

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCouponListBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) { state ->
            when (state.response) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        if (state.response.data.isEmpty()) {
                            adapter.replaceData(mutableListOf())
                            if (!localCouponList.isNullOrEmpty()) {
                                Timber.e("localCouponList:${localCouponList?.toJson()}")
                                adapter.replaceData(localCouponList?.toMutableList())
                                Timber.e("currentCouponId:${currentCouponId}")
                                val index = adapter.selectItemByCouponId(currentCouponId)
                                if (index != -1) {
                                    binding?.apply {
                                        rvCouponList.scrollToPosition(index)
                                    }
                                }
                            }
                        } else {
                            adapter.replaceData(state.response.data.toMutableList())
                            if (!localCouponList.isNullOrEmpty()) {
                                val index = adapter.selectItemByCouponId(currentCouponId)
                                if (index != -1) {
                                    adapter.list[index]?.isSelected = true
                                    binding?.apply {
                                        rvCouponList.scrollToPosition(index)
                                    }
                                } else {
                                    adapter.insertToFirst(localCouponList)
                                }
                            }
                        }

                        if (adapter.list.isEmpty()) {
                            rvCouponList.isVisible = false
                            layoutEmpty.root.isVisible = true
                        } else {
                            rvCouponList.isVisible = true
                            layoutEmpty.root.isVisible = false
                        }


                        val index = adapter.selectItemByCouponId(currentCouponId)
                        if (index != -1) {
                            rvCouponList.scrollToPosition(index)
                        }
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        if (adapter.list.isEmpty()) {
                            rvCouponList.isVisible = false
                            layoutEmpty.root.isVisible = true
                        } else {
                            rvCouponList.isVisible = true
                            layoutEmpty.root.isVisible = false
                        }
                    }

                }

                else -> {

                }
            }

        }
    }

    private fun initView() {
        if (topUpCanUseCouponRequest != null) {
            viewModel.findTopUpCanUseCoupon(
                topUpCanUseCouponRequest?.addNum,
                topUpCanUseCouponRequest?.id
            )

            binding?.apply {
                btnIdentifyCoupon.isVisible = false
            }
        } else {
//            if (!localCouponList.isNullOrEmpty()) {
//                Timber.e("localCouponList:${localCouponList?.toJson()}")
//                adapter.replaceData(localCouponList?.toMutableList())
//                Timber.e("currentCouponId:${currentCouponId}")
//                val index = adapter.selectItemByCouponId(currentCouponId)
//                if (index != -1) {
//                    binding?.apply {
//                        rvCouponList.scrollToPosition(index)
//                    }
//                }
//            } else {
            viewModel.getCoupon(
                diningStyle = diningStyle,
                goodsList = goodsList,
                isPreOrder = isPreOrder,
                note = note,
                tableUuid = tableUuid,
                orderNo = orderNo,

                )
//            }
        }
    }


    private fun initListener() {
        binding?.apply {
            rvCouponList.adapter = adapter

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnIdentifyCoupon.setOnClickListener {
                IdentifyCouponDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    diningStyle = diningStyle,
                    goodsList = goodsList,
                    isPreOrder = isPreOrder,
                    note = note,
                    tableUuid = tableUuid,
                    orderNo = orderNo,
                ) {
                    identifySuccessListener?.invoke(it)
                    dismissAllowingStateLoss()
                }
            }

            btnConfirm.setOnClickListener {
                Timber.e("adapter?.getSelectItm()  ${adapter?.getSelectItm()}")
                identifySuccessListener?.invoke(adapter?.getSelectItm())
                dismissAllowingStateLoss()
            }
        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.7).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}