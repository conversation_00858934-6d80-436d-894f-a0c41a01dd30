package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PaymentChannel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.databinding.ItemPaymentChannelBinding

/**
 * <AUTHOR>
 * @date 2024/5/911:22
 * @description
 */
class PayChannelAdapter(
    val list: ArrayList<OfflineChannelModel>,
    val onItemClickListener: (Int) -> Unit
) : RecyclerView.Adapter<PayChannelAdapter.OfflineChannelItemViewHolder>() {

    private var selectedPosition = 0

    inner class OfflineChannelItemViewHolder(val binding: ItemPaymentChannelBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {
                if (bindingAdapterPosition != -1) {
                    onItemClickListener.invoke(bindingAdapterPosition)
                }
            }
        }

        fun bind(resource: OfflineChannelModel?, position: Int) {
            resource?.let {
                binding.apply {
                    tvChannelType.isVisible = false
                    if (it.id == PaymentChannel.ONLINE.id) {
                        //线上
                        tvChannel.text = itemView.context.getString(R.string.online_payment)
                    } else if (it.id == PaymentChannel.BALANCE.id) {
                        //余额
                        tvChannel.text = itemView.context.getString(R.string.pay_by_balance)
                    } else if (it.id == OfflinePaymentChannelEnum.CASH.id) {
                        tvChannel.text = itemView.context.getString(R.string.cash)
                    } else if (it.id == OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id) {
                        tvChannel.text = itemView.context.getString(R.string.accounts_receivable)
                        tvChannelType.isVisible = true
                        tvChannelType.text = itemView.context.getString(R.string.offline_payments)
                    } else {
                        tvChannel.text = it.channelsName
                        tvChannelType.isVisible = true
                        tvChannelType.text = itemView.context.getString(R.string.offline_payments)
                    }
                    rootView.isSelected = position == selectedPosition
                    tvChannel.isSelected = position == selectedPosition
                    tvChannelType.isSelected = position == selectedPosition
                }
            }
        }

    }

    fun setSelectIndex(index: Int) {
        selectedPosition = index
        notifyDataSetChanged()
    }

    fun getSelect(): OfflineChannelModel? {
        if (selectedPosition >= list.size) {
            return null
        }
        return list[selectedPosition]
    }


    fun updateData(mList: ArrayList<OfflineChannelModel>) {
        list.clear()
        val tmp = mList.filter { it.selected == true }
        list.addAll(tmp)

        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): OfflineChannelItemViewHolder {
        val itemView = ItemPaymentChannelBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )

        return OfflineChannelItemViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OfflineChannelItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }
}