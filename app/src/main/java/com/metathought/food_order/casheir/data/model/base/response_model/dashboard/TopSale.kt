package com.metathought.food_order.casheir.data.model.base.response_model.dashboard

import com.google.gson.annotations.SerializedName

/**
 * 数据主体实体
 */
data class TopSale(
    @SerializedName("records") val records: List<TopSaleRecord> = emptyList(),
    @SerializedName("total") val total: Int = 0,
    @SerializedName("size") val size: Int = 0,
    @SerializedName("current") val current: Int = 0,
    @SerializedName("pages") val pages: Int = 0
)

/**
 * 记录项实体
 */
data class TopSaleRecord(
    @SerializedName("goodsId") val goodsId: String = "",
    @SerializedName("storeName") val storeName: String = "",
    @SerializedName("name") val name: String = "",
    @SerializedName("plan") val plan: String? = null,
    @SerializedName("picUrl") val picUrl: String? = null, // 实际是JSON数组字符串，可根据需要解析
    @SerializedName("sellPrice") val sellPrice: Long? = null,
    @SerializedName("createTime") val createTime: String = "",
    @SerializedName("soldNum") val soldNum: Int = 0
)
