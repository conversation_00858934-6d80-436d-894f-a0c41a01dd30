package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel



/**
 *<AUTHOR>
 *@time  2024/7/10
 *@desc
 **/

data class PaymentChannelModel(
    val paymentMethodId: String,
    val paymentMethodRelId: String,
    val paymentMethodName: String,
    var paymentMethodSelected: Boolean,
    val sort: Int,
    val offlinePaymentChannelsVos: List<OfflineChannelModel>

)