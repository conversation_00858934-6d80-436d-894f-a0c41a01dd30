package com.metathought.food_order.casheir.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.databinding.OrderMergeHeadItemBinding
import com.metathought.food_order.casheir.databinding.RefundsMenuItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableAdd
import com.metathought.food_order.casheir.extension.setEnableMinus
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.utils.DisplayUtils

/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class PatialRefundsOrderedItemAdapter(
    val list: ArrayList<BaseOrderGoods>,
    var isUseDiscount: Boolean? = false,
    val onItemClickListener: (Int) -> Unit
) : RecyclerView.Adapter<PatialRefundsOrderedItemAdapter.Companion.BaseViewHolder<*>>() {

    private lateinit var headerBinding: OrderMergeHeadItemBinding
    private lateinit var contentBinding: RefundsMenuItemBinding

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder<*> {
        return when (viewType) {
            OrderedInfoAdapter.OrderGoodViewType.MERGE_INFO.ordinal -> {
                headerBinding = OrderMergeHeadItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HeaderViewHolder(headerBinding)
            }

            OrderedInfoAdapter.OrderGoodViewType.GOOD.ordinal -> {
                contentBinding =
                    RefundsMenuItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                OrderedInfoViewHolder(contentBinding)
            }

            else -> throw IllegalArgumentException("Not a layout")
        }
    }


    inner class HeaderViewHolder(val binding: OrderMergeHeadItemBinding) :
        BaseViewHolder<OrderMergeHeadItemBinding>(binding.root) {
    }

    inner class OrderedInfoViewHolder(val binding: RefundsMenuItemBinding) :
        BaseViewHolder<RefundsMenuItemBinding>(binding.root) {

        init {
            binding.apply {
                imgPlus.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        val model = list[bindingAdapterPosition] as OrderedGoods
                        if (model.refundsNum!! < model.getCanRefundNum()) {
                            model.refundsNum = model.refundsNum!! + 1
                            notifyItemChanged(bindingAdapterPosition)
                            onItemClickListener.invoke(bindingAdapterPosition)
                        }
                    }
                }
                imgMinus.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        val model = list[bindingAdapterPosition] as OrderedGoods
                        if (model.refundsNum!! > 0) {
                            model.refundsNum = model.refundsNum!! - 1
                            notifyItemChanged(bindingAdapterPosition)
                            onItemClickListener.invoke(bindingAdapterPosition)
                        }
                    }
                }
            }

        }

        fun bind(resource: OrderedGoods) {
            itemView.context.run {
                resource.let { data ->
                    binding.apply {
                        if (data.refundsNum == null) data.refundsNum = 0
                        imgMinus.setEnableMinus(data.refundsNum!! > 0)
                        tvFoodName.text = data.name
                        if (!resource.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                            tvFoodName.addMealSetTag(itemView.context)
                        }
                        tvFoodSubName.isVisible = data.getGoodsTagStr().isNotEmpty()
                        tvFoodSubName.text = data.getGoodsTagStr()
                        tvFoodCount.text = data.getCanRefundNum().toString()

                        val activityLabel = data?.activityLabels?.firstOrNull()
                        if (activityLabel != null) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = Color.parseColor(
                                    activityLabel.color
                                )
                            )
                            tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (data?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }

                        layoutPrice.tvWeight.isVisible = false
                        layoutPrice.tvTimePriceSign.isVisible = false

                        if (data.isToBeWeighed()) {
                            //如果是称重商品
                            if (data.isHasCompleteWeight()) {
                                layoutPrice.tvWeight.isVisible = true
                                layoutPrice.tvWeight.text = "(${data.getWeightStr()})"
                                if (data.isMealSet()) {
                                    layoutPrice.tvWeight.isVisible = false
                                }
                            } else {
                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
                                layoutPrice.tvVipPrice.isVisible = false
                            }
                        }

                        if (data.isTimePriceGood()) {
                            //如果是时价菜
                            if (!data.isHasCompletePricing()) {
                                layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
                            } else {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }
//                        if (data.isTimePriceGood()) {
//                            //时价商品
//                            layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
//                            if (data.isHasProcessed()) {
//                                layoutPrice.tvTimePriceSign.isVisible = true
//                            } else {
//                                if (!data.isHasCompletePricing()) {
//                                    layoutPrice.tvTimePriceSign.isVisible = false
//                                } else if (!data.isHasCompleteWeight()) {
//                                    layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
//                                    layoutPrice.tvTimePriceSign.isVisible = false
//                                }
//                                layoutPrice.tvVipPrice.isVisible = false
//                            }
//                        } else if (data.isToBeWeighed()) {
//                            //称重商品
//                            if (data.isHasProcessed()) {
//                                layoutPrice.tvWeight.isVisible = true
//                                layoutPrice.tvWeight.text = "(${data.getWeightStr()})"
//                            } else {
//                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
//                                layoutPrice.tvVipPrice.isVisible = false
//                            }
//                        }

                        layoutPrice.tvFoodPrice.text =
                            data.totalFinalPriceAfterFirstRefund(isUseDiscount, true)
                                .priceFormatTwoDigitZero2()


                        tvRemark.isVisible = data.getFinalNote().isNotEmpty()
                        tvRemark.text =
                            "${getString(R.string.remark)} : ${data.getFinalNote()}"

                        tvQTY.text = data.refundsNum.toString()
                        tvRefundMinusCount.text = "-${data.refundsNum.toString()}"
                        tvRefundMinusCount.isVisible = data.refundsNum!! > 0
                        imgPlus.setEnableAdd(data.refundsNum!! < data.getCanRefundNum())

                    }
                }
            }
        }

    }

//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedInfoViewHolder {
//        val itemView =
//            RefundsMenuItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
//        return OrderedInfoViewHolder(itemView)
//    }

    override fun getItemCount() = list.size

    override fun getItemViewType(position: Int): Int {
        return when (list[position].type) {
            "merge_info" -> OrderedInfoAdapter.OrderGoodViewType.MERGE_INFO.ordinal
            "add_info" -> OrderedInfoAdapter.OrderGoodViewType.ADD_INFO.ordinal
//            "good" -> OrderGoodViewType.GOOD.ordinal
            else -> {
                OrderedInfoAdapter.OrderGoodViewType.GOOD.ordinal
            }
        }
    }

    //    override fun onBindViewHolder(holder: OrderedInfoViewHolder, position: Int) {
//        if(list[position] is Hear){
//
//        }
//        if (list[position] is OrderedGoods) {
//            holder.bind(list[position] as OrderedGoods)
//        }
//    }
    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                holder.binding.tvOrderId.text = (list[position] as HeaderOrderGoods).orderId
                holder.binding.tvOrderIndex.text = (list[position] as HeaderOrderGoods).title
                holder.binding.llOrderInfo.setPadding(
                    0,
                    DisplayUtils.dp2px(holder.binding.root.context, 10f),
                    0,
                    DisplayUtils.dp2px(holder.binding.root.context, 10f)
                )
                holder.binding.vLine.isVisible = position != 0
                if (position + 1 < list.size) {
                    holder.binding.tvNoGoods.isVisible = list[position + 1] is HeaderOrderGoods
                } else if (position == list.size - 1) {
                    holder.binding.tvNoGoods.isVisible = list[position] is HeaderOrderGoods
                } else {
                    holder.binding.tvNoGoods.isVisible = false
                }
            }

            is OrderedInfoViewHolder -> {
                (list[position] is OrderedGoods).let {
                    holder.bind(list[position] as OrderedGoods)
                }
            }
        }
    }


    fun replaceData(newData: ArrayList<OrderedGoods>?, isUseDiscount: Boolean?) {
        if (newData != null) {
            this.isUseDiscount = isUseDiscount ?: false
            this.list.clear()
            this.list.addAll(newData)
            notifyDataSetChanged()
        }
    }

    companion object {
        abstract class BaseViewHolder<T>(view: View) : RecyclerView.ViewHolder(view)
    }

}