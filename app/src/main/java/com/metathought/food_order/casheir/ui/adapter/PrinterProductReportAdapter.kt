package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.databinding.ItemProductReportBinding



/**

 */
class PrinterProductReportAdapter(
    val list: ArrayList<SalesItemOrdersDetail>
) :
    RecyclerView.Adapter<PrinterProductReportAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemProductReportBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: SalesItemOrdersDetail, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {

                        tvItem.text = resource.itemName
                        tvQty.text = "${resource.itemNum}"
                        tvPrice.text = "${resource.itemTotalPrice}"

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        PrinterTicketMenuViewHolder(
            ItemProductReportBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: List<SalesItemOrdersDetail>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}