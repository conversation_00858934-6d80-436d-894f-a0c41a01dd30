package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_REALIZED
import com.metathought.food_order.casheir.data.model.base.response_model.login.ConversionRatioRecord
import com.metathought.food_order.casheir.databinding.ExchangeRateChangeHistoryItemBinding
import com.metathought.food_order.casheir.extension.formatDateFromTimeZone
import com.metathought.food_order.casheir.extension.formatDateStr
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter


class ExchangeRateChangeHistoryAdapter(
    val list: List<ConversionRatioRecord> = listOf(),
) : RecyclerView.Adapter<ExchangeRateChangeHistoryAdapter.MealSetViewHolder>() {


    inner class MealSetViewHolder(val binding: ExchangeRateChangeHistoryItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: ConversionRatioRecord, position: Int) {
            resource.let { it1 ->
                binding.apply {
                    tvAccount.text = resource.updateUserName
                    tvTime.text = resource.updateTime?.formatDateFromTimeZone()

                    tvExchangeRate.text =
                        "${resource.conversionRatio} → ${resource.newConversionRatio}"
                }
            }
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MealSetViewHolder {
        return MealSetViewHolder(
            ExchangeRateChangeHistoryItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        return list.count()
    }

    override fun onBindViewHolder(holder: MealSetViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


}