package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.databinding.SecondSelectedMenuItemBinding
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.view.text.addTextTag
import androidx.core.graphics.toColorInt
import timber.log.Timber


class SecondMenuOrderFoodAdapter(
    val list: ArrayList<GoodsRequest>,
    val context: Context,
) : RecyclerView.Adapter<SecondMenuOrderFoodAdapter.MenuOrderFoodItemViewHolder>() {
    var diningStyle: Int = 0

    inner class MenuOrderFoodItemViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(resource: GoodsRequest?, position: Int?) {
            resource?.let { _ ->
                binding.apply {
                    tvName.text = resource.goods?.name
                    if (!resource.orderMealSetGoodList.isNullOrEmpty()) {
                        tvName.addMealSetTag(context)
                    }
                    val activityLabel = resource.goods?.activityLabels?.firstOrNull()
                    if (activityLabel != null) {
                        tvDiscountActivity.setStrokeAndColor(color = activityLabel.color.toColorInt())
                        tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                        tvDiscountActivity.text = activityLabel.name
                        tvDiscountActivity.isVisible = true
                    } else {
                        tvDiscountActivity.isVisible = false
                    }

                    if (resource?.goods?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                        tvTmpSign.setStrokeAndColor(color = R.color.black60)
                        tvTmpSign.text = context.getString(R.string.temporary)
                        tvTmpSign.isVisible = true
                    } else {
                        tvTmpSign.isVisible = false
                    }

                    tvQTY.text = "x${resource.num}"
                    tvSpecification.isVisible = resource.getGoodsTagStr().isNotEmpty()
                    tvSpecification.text = resource.getGoodsTagStr()

                    layoutPrice.tvWeight.isVisible = false
                    layoutPrice.tvTimePriceSign.isVisible = false
                    layoutPrice.tvTimePriceSign.text =
                        context.getString(R.string.time_price_parentheses)

//                    val isHasProcessed = if (resource.goods?.isMealSet() == true) {
//                        resource.isMealSetHasCompleteWeight()
//                    } else {
//                        resource.goods?.isHasProcessed() == true
//                    }
                    val isHasProcessed = resource.isProcessed()
                    layoutPrice.tvVipPrice.isVisible = false
                    if (resource.isToBeWeighed()) {
                        //如果是称重商品
                        if (resource.isHasCompleteWeight()) {
                            Timber.e("已称重")
                            layoutPrice.tvWeight.isVisible = true
                            layoutPrice.tvWeight.text = "(${resource.goods?.getWeightStr()})"
                            if (resource.goods?.isMealSet() == true) {
                                layoutPrice.tvWeight.isVisible = false
                            }
                        } else {
                            Timber.e("未称重")
                            layoutPrice.tvFoodPrice.text = context.getString(R.string.to_be_weighed)
                            layoutPrice.tvVipPrice.isVisible = false
                        }
                    }

                    if (resource.goods?.isTimePriceGood() == true) {
                        //如果是时价菜
                        if (!(resource.goods?.isHasCompletePricing() == true)) {
                            layoutPrice.tvFoodPrice.text = context.getString(R.string.time_price)
                        } else {
                            layoutPrice.tvTimePriceSign.isVisible = true
                        }
                    }
                    if (isHasProcessed) {
                        val isShowVipPrice = resource.goods?.isShowVipPrice()
                        layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
                        layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                            FoundationHelper.useConversionRatio,
                            resource.totalDiscountPrice(),
                            FoundationHelper.isKrh
                        )
                        if (isShowVipPrice == true) {
                            layoutPrice.tvVipPrice.text =
                                resource.totalVipPrice().priceFormatTwoDigitZero2()
                        }
                        if (resource.goods?.isTimePriceGood() == true) {
                            layoutPrice.tvTimePriceSign.isVisible = true
                        }
                    }
//                    else {
//                        layoutPrice.tvFoodPrice.text = "${resource.goods?.getShowPrice(context)}"
//                        if (resource.goods?.isTimePriceGood() == true && resource?.goods?.isToBeWeighed() == true && resource?.goods?.isHasCompletePricing() == true && resource?.goods?.isHasCompleteWeight() == false) {
//                            //如果是称重时价菜 且定价未称重
//                            layoutPrice.tvTimePriceSign.isVisible = true
//                        }
//                        layoutPrice.tvVipPrice.isVisible = false
//                    }


                    tvRemark.isVisible = resource.getFinalNote().isNotEmpty()
                    tvRemark.text =
                        "${context.getString(R.string.remark)} : ${resource.getFinalNote()}"
//                    if (resource.goods?.isSoldOut() == true) {
//                        tvName.setTextColor(context.getColor(R.color.black))
//                        tvSpecification.setTextColor(context.getColor(R.color.black))
//                        tvQTY.setTextColor(context.getColor(R.color.black))
//                        tvPrice.setTextColor(context.getColor(R.color.black))
//                        tvWeight.setTextColor(context.getColor(R.color.black))
//                        tvVipPrice.setTextColor(context.getColor(R.color.black))
//                        tvOriginalPrice.setTextColor(context.getColor(R.color.black))
//                        layoutContent.alpha = 0.4f
//                    } else {
//                        tvName.setTextColor(context.getColor(R.color.black))
//                        tvSpecification.setTextColor(context.getColor(R.color.black60))
//                        tvQTY.setTextColor(context.getColor(R.color.black60))
//                        tvPrice.setTextColor(context.getColor(R.color.black))
//                        tvWeight.setTextColor(context.getColor(R.color.black))
//                        tvVipPrice.setTextColor(context.getColor(R.color.member_price_color))
//                        tvOriginalPrice.setTextColor(context.getColor(R.color.black60))
//                        layoutContent.alpha = 1f
//                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuOrderFoodItemViewHolder {
        var itemView = SelectedMenuItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
//        viewHight = (parent.height / 3.8f).toInt()
//        itemView.root.layoutParams.height = viewHight
        return MenuOrderFoodItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: MenuOrderFoodItemViewHolder, position: Int) {
//        holder.itemView.minimumHeight = viewHight
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun updateItems(newItems: ArrayList<GoodsRequest>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateDiningStyle(style: Int) {
        diningStyle = style
    }

}