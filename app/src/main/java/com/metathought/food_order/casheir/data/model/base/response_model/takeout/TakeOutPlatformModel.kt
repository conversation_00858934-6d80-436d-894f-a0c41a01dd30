package com.metathought.food_order.casheir.data.model.base.response_model.takeout

import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2025/3/4
 *@desc
 **/

class TakeOutPlatformModel(
    //平台id
    val id: String? = null,
    //平台名字
    val name: String? = null,
    //启用状态 1-启用
    val enableStatus: Int? = null,
    //佣金比例
    val commissionPercentage: BigDecimal? = null,
    //交易币种类型 1-usd  2-khr
    val type: Int? = null
) {
    //是否khr
    fun isKhr(): Boolean {
        return type == 2
    }

    fun isEnable():<PERSON><PERSON>an{
       return enableStatus == 1
    }
}
