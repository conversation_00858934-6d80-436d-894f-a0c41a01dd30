package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list


import android.content.Context
import android.util.Log
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.PayInfo
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.utils.PaymentUtils
import timber.log.Timber

data class StoreDataOrderVo(
    @SerializedName("createDateTime")
    val createDateTime: String?,
    @SerializedName("customerName")
    val customerName: String?,
    @SerializedName("diningTable")
    val diningTable: String?,
    @SerializedName("mayPayMoney")
    val mayPayMoney: Double?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("orderStatus")
    val orderStatus: Int?,
    @SerializedName("payDateTime")
    val payDateTime: String?,
    @SerializedName("paymentMethod")
    val paymentMethod: Int?,
    //线下支付渠道ID 0:非线下支付
    @SerializedName("paymentChannelsId")
    val paymentChannelsId: Int?,
    //线下支付渠道名称
    @SerializedName("paymentChannelsName")
    val paymentChannelsName: String?,

    /**
     * weightMark 是否所有商品已定价
     */
    @SerializedName("weightMark")
    val weightMark: Boolean?,

    /**
     * 订单汇率
     */
    @SerializedName("conversionRatio")
    var conversionRatio: Long? = null,

    /**
     * 外卖平台币种  1-usd  2-khr
     */
    @SerializedName("deliveryPlatformCurrencyType")
    var deliveryPlatformCurrencyType: Int? = null,

    @SerializedName("combinedPayInfoList")
    var combinedPayInfoList: List<PayInfo>? = null,

    @SerializedName("grabOrder")
    var grabOrder: StoreDataGrabOrderInfo? = null,
    @SerializedName("pickupNo")
    val pickupNo: String?,
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    ) {

    fun isKhr(): Boolean {
        return deliveryPlatformCurrencyType == 2
    }

    fun getPaymentMethod(
        context: Context,
        offlineChannelTotalModel: OfflineChannelTotalModel?
    ): String {
        Timber.e(
            "id:${orderNo}  combinedPayInfoList:${combinedPayInfoList?.toJson()} ${
                PaymentUtils.getMixedPaymentText(
                    context,
                    combinedPayInfoList
                )
            }"
        )
        return if (orderStatus == OrderedStatusEnum.PAID.id || orderStatus == OrderedStatusEnum.PREORDER.id
            || orderStatus == OrderedStatusEnum.FULL_REFUND.id || orderStatus == OrderedStatusEnum.PARTIAL_REFUND.id
            || orderStatus == OrderedStatusEnum.CANCEL_ORDER.id || orderStatus == OrderedStatusEnum.UNPAID.id
            || orderStatus == OrderedStatusEnum.CREDIT_UNPAID.id || orderStatus == OrderedStatusEnum.CREDIT_PAID.id
        ) {
            when (paymentMethod) {
                PayTypeEnum.CREDIT.id -> {
                    context.getString(R.string.credit_payment)
                }

                PayTypeEnum.ONLINE_PAYMENT.id -> {
                    context.getString(R.string.online_payment)
                }

                PayTypeEnum.CASH_PAYMENT.id -> {
                    if (paymentChannelsId == 0) {
                        return "N/A"
                    }
                    getOfflinePayMethod(context, offlineChannelTotalModel)
                }

                PayTypeEnum.USER_BALANCE.id -> {
                    context.getString(R.string.pay_by_balance)
                }

                PayTypeEnum.PAY_OTHER.id -> {
                    context.getString(R.string.pay_by_other)
                }

                PayTypeEnum.MIXED_PAYMENT.id -> {
                    if (combinedPayInfoList.isNullOrEmpty()) {
                        "${context.getString(R.string.pay_by_balance)}\n${
                            getOfflinePayMethod(context, offlineChannelTotalModel)
                        }"
                    } else {
                        PaymentUtils.getMixedPaymentText(context, combinedPayInfoList)
                    }

                }

                else -> "N/A"
            }
        } else {
            "N/A"
        }
    }

    fun getOfflinePayMethod(
        context: Context,
        offlineChannelTotalModel: OfflineChannelTotalModel?
    ): String {
        if (paymentChannelsId == OfflinePaymentChannelEnum.CASH.id
        ) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
        } else if (paymentChannelsId == OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        } else {
            val channelName = if (paymentChannelsId != null) {
                val indexOf =
                    offlineChannelTotalModel?.indexOf(OfflineChannelModel(id = paymentChannelsId))
                        ?: -1
                Timber.e(

                    "getOfflinePayMethod,    paymentChannelsId:${paymentChannelsId},indexOf:${indexOf}   ${offlineChannelTotalModel?.toList()}"
                )
                if (indexOf != -1) {
                    offlineChannelTotalModel!![indexOf!!].channelsName
                } else {
                    paymentChannelsName
                }
            } else {
                paymentChannelsName
            }
            return "${context.getString(R.string.offline_payments)} - $channelName"
        }
    }
}