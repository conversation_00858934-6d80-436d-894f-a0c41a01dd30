package com.metathought.food_order.casheir.utils

import android.widget.HorizontalScrollView
import androidx.recyclerview.widget.RecyclerView

/**
 * 表格滚动控制器
 * 统一管理所有行的水平滚动同步
 */
class TableScrollController {
    
    private var currentScrollX = 0
    private var isUpdating = false
    private val scrollViews = mutableListOf<HorizontalScrollView>()
    private var dateHeaderRecyclerView: RecyclerView? = null
    
    /**
     * 设置日期标题RecyclerView
     */
    fun setDateHeaderRecyclerView(recyclerView: RecyclerView) {
        dateHeaderRecyclerView = recyclerView

        // 为日期标题RecyclerView添加滚动监听器
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isUpdating && dx != 0) {
                    val scrollX = recyclerView.computeHorizontalScrollOffset()
                    println("日期标题滚动: dx=$dx, scrollX=$scrollX")
                    syncAllHorizontalScrollViews(scrollX)
                }
            }
        })
    }
    
    /**
     * 注册一个HorizontalScrollView
     */
    fun registerScrollView(scrollView: HorizontalScrollView) {
        if (!scrollViews.contains(scrollView)) {
            scrollViews.add(scrollView)
            
            // 设置滚动监听器
            scrollView.setOnScrollChangeListener { _, scrollX, _, oldScrollX, _ ->
                if (!isUpdating) {
                    val dx = scrollX - oldScrollX
                    println("HorizontalScrollView 滚动: $oldScrollX -> $scrollX (dx=$dx)")
                    updateAllScrollViews(scrollX, scrollView)
                }
            }
            
            // 同步到当前位置
            scrollView.scrollTo(currentScrollX, 0)
        }
    }
    
    /**
     * 注销一个HorizontalScrollView
     */
    fun unregisterScrollView(scrollView: HorizontalScrollView) {
        scrollViews.remove(scrollView)
        scrollView.setOnScrollChangeListener(null)
    }
    
    /**
     * 更新所有滚动视图到指定位置
     */
    private fun updateAllScrollViews(scrollX: Int, excludeView: HorizontalScrollView?) {
        if (isUpdating) return

        isUpdating = true
        currentScrollX = scrollX

        println("TableScrollController: 同步所有视图到位置 $scrollX")

        // 同步所有HorizontalScrollView
        scrollViews.forEach { scrollView ->
            if (scrollView != excludeView) {
                scrollView.scrollTo(scrollX, 0)
                println("  同步 HorizontalScrollView 到位置 $scrollX")
            }
        }

        // 同步日期标题RecyclerView
        dateHeaderRecyclerView?.let { rv ->
            val currentScrollX = rv.computeHorizontalScrollOffset()
            val dx = scrollX - currentScrollX
            if (dx != 0) {
                rv.scrollBy(dx, 0)
                println("  同步日期标题RecyclerView: $currentScrollX -> $scrollX (dx=$dx)")
            }
        }

        isUpdating = false
    }

    /**
     * 同步所有HorizontalScrollView到指定位置（不包括日期标题）
     */
    private fun syncAllHorizontalScrollViews(scrollX: Int) {
        if (isUpdating) return

        isUpdating = true
        currentScrollX = scrollX

        println("从日期标题同步所有HorizontalScrollView到位置: $scrollX")
        scrollViews.forEach { scrollView ->
            scrollView.scrollTo(scrollX, 0)
        }

        isUpdating = false
    }

    /**
     * 强制同步所有视图到当前位置
     */
    fun forceSync() {
        isUpdating = true
        println("强制同步所有视图到位置: $currentScrollX")

        scrollViews.forEach { scrollView ->
            scrollView.scrollTo(currentScrollX, 0)
            println("  强制同步 HorizontalScrollView 到位置 $currentScrollX")
        }

        dateHeaderRecyclerView?.let { rv ->
            val currentScrollX = rv.computeHorizontalScrollOffset()
            val dx = this.currentScrollX - currentScrollX
            if (dx != 0) {
                rv.scrollBy(dx, 0)
                println("  强制同步日期标题: $currentScrollX -> ${this.currentScrollX} (dx=$dx)")
            }
        }

        isUpdating = false
    }
    
    /**
     * 获取当前滚动位置
     */
    fun getCurrentScrollX(): Int = currentScrollX
    
    /**
     * 清理所有注册的视图
     */
    fun clear() {
        scrollViews.forEach { it.setOnScrollChangeListener(null) }
        scrollViews.clear()
        dateHeaderRecyclerView = null
        currentScrollX = 0
    }
}
