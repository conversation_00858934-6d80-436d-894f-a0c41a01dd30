package com.metathought.food_order.casheir.data.model.base.response_model




/**
 *<AUTHOR>
 *@time  2024/12/2
 *@desc
 **/

data class GoogleResponse(
    val plus_code: PlusCode,
    val results: List<AddressResult>,
    val status: String
)

data class PlusCode(val compound_code: String, val global_code: String)

data class AddressResult(
//    val address_components: List<AddressComponent>,
    val formatted_address: String,
//    val geometry: Geometry,
//    val place_id: String,
//    val plus_code: PlusCode?,
//    val types: List<String>
)