package com.metathought.food_order.casheir.data.model.base.request_model

import android.content.Context
import android.util.Log
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.ServiceChargeCalculationTypeEnum
import com.metathought.food_order.casheir.constant.SingleDiscountType
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import timber.log.Timber
import java.io.Serializable
import java.lang.Math.min
import java.math.BigDecimal

/**
 * <AUTHOR>
 * @date 2024/3/1820:38
 * @description
 */
data class GoodsRequest(
    var num: Int? = null,
    var feedInfoList: ArrayList<Feed>? = null,
    var goodsTagItems: ArrayList<GoodsTagItem>? = null,
    var orderMealSetGoodList: List<OrderMealSetGood>? = null,
    var goods: Goods? = null,
    var finalSinglePrice: Long? = null,
    var singleDiscountGoods: SingleDiscountGoods? = null,
    var note: String? = null,
) : Serializable {


    /**
     * 这个分组选中的商品是否完成称重
     *
     * @return
     */
    fun isMealSetHasCompleteWeight(): Boolean {
        orderMealSetGoodList?.forEach {
            if (it.isToBeWeighed() && !it.isHasCompleteWeight()) {
                //这个子商品需要称重且没有完成称重
                return false
            }
        }
        return true
    }

    fun deepCopy(): GoodsRequest {
        val json = this.toJson()
        return MyApplication.globalGson.fromJson<GoodsRequest>(
            json,
            object : TypeToken<GoodsRequest>() {}.type
        ) as GoodsRequest
    }

    fun getFinalNote(): String {
        return (note?.replace(";<br/>", "\n") ?: "").trimStart()
    }


    /**
     * 规格字符串
     *
     * @return
     */
    fun getGoodsTagStr(): String {
        val sbf = StringBuffer()
        if (goods?.isMealSet() == true) {
            sbf.append(OrderHelper.getSelectMealSetGoodStr(orderMealSetGoodList))
        } else {
            goodsTagItems?.let {
                if (it.isNotEmpty()) {
                    for (i in it.indices) {
                        sbf.append(it[i].name)
                        if (i != it.size - 1)
                            sbf.append(", ")
                    }
                }
            }

            feedInfoList?.let {
                if (it.isNotEmpty()) {
                    if (goodsTagItems?.isNotEmpty() == true) {
                        sbf.append(", ")
                    }
                    for (i in it.indices) {
                        sbf.append(it[i].name + " x" + it[i].alreadyNum)
                        if (i != it.size - 1)
                            sbf.append(", ")
                    }
                }
            }
        }

        return sbf.toString()
    }


    /**
     * 计算单个销售价(包含小料，规格)
     *
     * @return
     */
    fun calculateSinglePrice(
    ): Long {
        val targetPrice = OrderHelper.calculateTagPrice(feedInfoList, goodsTagItems)
        val mealSetAddPrice = getMealSetAddPrice()

        var basePrice = goods?.getCalculateSellPrice() ?: 0L
        if (goods?.isToBeWeighed() == true && goods?.isMealSet() == false) {
            //重量计算 舍去小数位
            basePrice = basePrice.times((goods?.weight ?: 1.0)).toLong()
        }
        val price = basePrice + targetPrice + mealSetAddPrice
        return price
    }

    /**
     * 计算折扣价单价 没折扣价的时候也能替代原价(包含规格，小料)
     *
     * @return
     */
    fun singleDiscountPrice(): Long {
        val targetPrice = OrderHelper.calculateTagPrice(feedInfoList, goodsTagItems)

        val mealSetAddPrice = getMealSetAddPrice()
        var basePrice = (goods?.getCalculateDiscountPrice() ?: 0L)

        if (goods?.isToBeWeighed() == true && goods?.isMealSet() == false) {
            Timber.e("单品计算重量了？")
            //待称重商品 基础价*重量+小料+规格
            basePrice = basePrice.times((goods?.weight ?: 0.0)).toLong()
        }
        Timber.e("basePrice: ${basePrice}")
        //没折扣价拿原价
        return basePrice + targetPrice + mealSetAddPrice
    }


    /**
     * 计算会员价单价(包括小料，规格)
     *
     * @return
     */
    fun calculateVipPrice(
    ): Long {
//        if (goods?.finalVipPrice != null) {
//            return goods?.finalVipPrice!!
//        }
        val targetPrice = OrderHelper.calculateTagPrice(feedInfoList, goodsTagItems)

        var vipPrice = goods?.getCalculateVipPrice()
        Timber.e("goods?.vipPrice: ${goods?.vipPrice}  ")
        if (!isShowVipPrice() || vipPrice == null) {
            vipPrice = (goods?.getCalculateDiscountPrice() ?: 0L)
        }
        val mealSetAddPrice = getMealSetAddPrice()
        if (goods?.isToBeWeighed() == true && goods?.isMealSet() == false) {
            //待称重商品 基础价*重量+小料+规格
            vipPrice = vipPrice.times((goods?.weight ?: 0.0)).toLong()
        }

        return vipPrice + targetPrice + mealSetAddPrice
    }

    /**
     * 是否称重商品
     */
    fun isToBeWeighed(): Boolean {
        if (goods?.isMealSet() == true) {
            return orderMealSetGoodList?.firstOrNull { it.isToBeWeighed() } != null
        } else {
            return goods?.isToBeWeighed() ?: false
        }
    }

    /**
     * 是否已经完成定价
     *
     * @return
     */
    fun isProcessed(): Boolean {
        if (goods?.isMealSet() == true) {
            //如果是套餐,子商品是否都称重了
            return isMealSetHasCompleteWeight()
//            return  orderMealSetGoodList?.firstOrNull { it.isToBeWeighed() && !it.isHasCompleteWeight() } == null
        } else {
            //如果不是套餐,商品是否称重 或者设置时价了
            return goods?.isHasProcessed() ?: true
        }
    }

    /**
     * 是否称重
     */
    fun isHasCompleteWeight(): Boolean {
        if (goods?.isMealSet() == true) {
            //套餐是否称重
            return isMealSetHasCompleteWeight()
        } else {
            //普通商品是否称重
            return goods?.isHasCompleteWeight() ?: false
        }
    }

//    fun getShowPrice(context: Context): String {
//        if (goods?.isTimePriceGood() == true) {
//            if (goods?.isHasCompletePricing() == true && isToBeWeighed() && !isProcessed()) {
//                return context.getString(R.string.to_be_weighed)
//            }
//
//            return context.getString(R.string.time_price)
//        }
//        if (isToBeWeighed() && !isProcessed()) {
//            return context.getString(R.string.to_be_weighed)
//        }
//        //下面不会走到了
//        Timber.e("getShowPrice :  $finalSinglePrice")
//        return "${finalSinglePrice?.priceFormatTwoDigitZero2()}"
//    }


    /**
     * 获取套餐加价
     */
    private fun getMealSetAddPrice(): Long {
        //加价金额
        var addPrice = 0L
        if (!orderMealSetGoodList.isNullOrEmpty()) {
            orderMealSetGoodList?.forEach { goods ->
                var priceMarkup = goods.priceMarkup ?: 0
                if (goods.isToBeWeighed() && goods.isHasCompleteWeight()) {
                    priceMarkup = (priceMarkup * (goods.weight ?: 0.0)).toLong()
                }
                var num = goods.num ?: 0
                var tagPrice = 0.0
                goods.mealSetTagItemList?.forEach { tag ->
                    if ((tag.price ?: 0.0) > 0.0) {
                        tagPrice += (tag.price ?: 0.0)
                    }
                }
                val price = (priceMarkup + tagPrice.times(num)).times(goods.number ?: 0)
                addPrice += (price.toLong())
                Timber.e("mealSetGood:${goods.mealSetGoodsName} 单个子商品的加价  price:${price}")
            }
        }
        return addPrice
    }


    /**
     * 计算用销售价计算的总价(包含单品折扣优惠)
     *
     * @return
     */
    fun totalPrice(): Long {
        val totalPrice = (calculateSinglePrice()) * (num ?: 0)
        return totalPrice
    }

    /**
     * 计算单个折扣价总价
     *
     * @return
     */
    fun totalDiscountPriceWithoutSingleDiscount(): Long {
        val price = singleDiscountPrice().times(num ?: 0)
        return price
    }

    /**
     * 商品总的折扣价(包含单品折扣)
     *
     * @return
     */
    fun totalDiscountPrice(): Long {
        var totalPrice = totalDiscountPriceWithoutSingleDiscount()
        if (singleDiscountGoods != null) {
            Timber.e("改价")
            totalPrice = getPriceAfterSingleDiscount()
        }
        return totalPrice
    }


    /**
     * 计算单个商品会员总价
     *
     * @return
     */
    fun totalVipPriceWithoutSingleDiscount(): Long {
        val price = calculateVipPrice() * (num ?: 0)
        return price
    }

    /**
     * 计算单个商品会员总价(包括单品减免折扣优惠)
     *
     * @return
     */
    fun totalVipPrice(): Long {
        var totalPrice = totalVipPriceWithoutSingleDiscount()
        if (singleDiscountGoods != null) {
            totalPrice = getVipPriceAfterSingleDiscount()
        }
//        Log.e("GoodsRequest", "最终的单品会员价: ${totalPrice}")
        return totalPrice
    }


    /**
     * 原价对应的服务费
     *
     * @return
     */
    fun totalServiceChargePrice(): Long {
        if (goods?.serviceChargeWhitelisting == true) {
            return 0L
        }
        //判断是否
        return calculateSinglePrice().times((goods?.getServiceChargePercentage() ?: 0))
            .div(100)
            .times(num ?: 0)
    }


    /**
     * 会员价对应的服务费
     *
     * @param isIncludeSingleDiscount  是否包含单品折扣 默认包含
     * @return
     */
    fun totalVipServiceChargePrice(isIncludeSingleDiscount: Boolean? = true): Long {
        if (goods?.serviceChargeWhitelisting == true) {
            return 0L
        }
        // 提前获取公共变量
        val serviceChargePct = goods?.getServiceChargePercentage() ?: 0
        val quantity = num ?: 0
        if (serviceChargePct == 0 || quantity == 0) return 0L  // 无服务费比例或数量为0时直接返回

        //服务端是否受单品折扣 和 优惠活动影响
        val serviceEffectByDiscount = (goods?.serviceChargeMode
            ?: MainDashboardFragment.STORE_INFO?.serviceChargeMode) == ServiceChargeCalculationTypeEnum.AFTER_DISCOUNT.id

        if (!serviceEffectByDiscount) {
            return BigDecimal(
                calculateVipPrice().times(serviceChargePct)
                    .div(100.0)
            ).halfUp(0).toLong()
                .times(quantity)
        }

        if (isIncludeSingleDiscount == true) {
            if (singleDiscountGoods != null) {
                val totalPrice = getVipPriceAfterSingleDiscount()
                val singlePrice = totalPrice.div(quantity.toDouble())
                //四舍五入
                return BigDecimal(
                    singlePrice.times(serviceChargePct).div(100.0)
                ).halfUp(0).toLong().times(quantity)
            } else if ((goods?.discountActivityVipPair?.first ?: 0) > 0) {
//                val count = num ?: 0
                //计算出优惠后平均价格
                val singlePrice =
                    (calculateVipPrice() * quantity - (goods?.discountActivityVipPair?.second
                        ?: 0)).div(quantity.toDouble())
                return BigDecimal(
                    singlePrice.times(serviceChargePct)
                        .div(100.0)
                ).halfUp(0).toLong()
                    .times(quantity)
            }
        }

        //四舍五入
        return BigDecimal(
            calculateVipPrice().times(serviceChargePct)
                .div(100.0)
        ).halfUp(0).toLong()
            .times(quantity)
    }


    /**
     * 折扣价对应的服务费
     *
     * @param isIncludeSingleDiscount  是否包含单品折扣 默认包含
     * @return
     */
    fun totalDiscountServiceChargePrice(isIncludeSingleDiscount: Boolean? = true): Long {
        if (goods?.serviceChargeWhitelisting == true) {
            return 0L
        }
        val serviceChargePct = goods?.getServiceChargePercentage() ?: 0
        val quantity = num ?: 0
        if (serviceChargePct == 0 || quantity == 0) return 0L  // 无服务费比例或数量为0时直接返回
        //服务端是否受单品折扣 和 优惠活动影响
        val serviceEffectByDiscount =
            (goods?.serviceChargeMode
                ?: MainDashboardFragment.STORE_INFO?.serviceChargeMode) == ServiceChargeCalculationTypeEnum.AFTER_DISCOUNT.id
        Timber.e("serviceEffectByDiscount:  $serviceEffectByDiscount")
        if (!serviceEffectByDiscount) {
            return BigDecimal(
                singleDiscountPrice().times(serviceChargePct)
                    .div(100.0)
            ).halfUp(0).toLong()
                .times(num ?: 0)
        }

//        if (singleDiscountGoods != null && isIncludeSingleDiscount == true) {
//            val totalPrice = getPriceAfterSingleDiscount()
//            return BigDecimal(
//                totalPrice.times((goods?.getServiceChargePercentage() ?: 0)).div(100.0)
//            ).halfUp(0).toLong()
//        }
        if (isIncludeSingleDiscount == true) {
            if (singleDiscountGoods != null) {
                val totalPrice = getPriceAfterSingleDiscount()
                val singlePrice = totalPrice.div(quantity.toDouble())
                //四舍五入
                return BigDecimal(
                    singlePrice.times(serviceChargePct).div(100.0)
                ).halfUp(0).toLong().times(quantity)
            } else if ((goods?.discountActivityPair?.first ?: 0) > 0) {
                //计算出优惠后平均价格
                val singlePrice =
                    (singleDiscountPrice() * quantity - (goods?.discountActivityPair?.second
                        ?: 0)).div(quantity.toDouble())
                Timber.e("singlePrice:${singlePrice}")
                return BigDecimal(
                    singlePrice.times(serviceChargePct)
                        .div(100.0)
                ).halfUp(0).toLong()
                    .times(quantity)
            }
        }
        Timber.e(
            "${goods?.name},totalDiscountServiceChargePrice:${singleDiscountPrice()} ${
                singleDiscountPrice().times(serviceChargePct)
                    .div(100.0)
            }"
        )
        return BigDecimal(
            singleDiscountPrice().times(serviceChargePct)
                .div(100.0)
        ).halfUp(0).toLong()
            .times(num ?: 0)
    }


    //获取单品折扣后的总价
    fun getPriceAfterSingleDiscount(): Long {
        var totalPrice = totalDiscountPriceWithoutSingleDiscount()

        if (singleDiscountGoods?.type == SingleDiscountType.PERCENTAGE.id) {
            if (singleDiscountGoods?.discountReduceActivityId != null) {
                //后台配置的
                val percent = (singleDiscountGoods?.discountReduceInfo?.reduceRateAmount
                    ?: BigDecimal.ZERO).toDouble()
                val thresholdPrice =
                    singleDiscountGoods?.discountReduceInfo?.getThresholdAmountAfterAmplify()
                //无门槛或者满足门槛
//                if (thresholdPrice == null || (totalPrice >= thresholdPrice)) {
                var reducePrice = BigDecimal(totalPrice * percent / 100.0).halfUp(0).toLong()
                if (singleDiscountGoods?.discountReduceInfo?.getReduceAmountLimitAfterAmplify() != null) {
                    reducePrice = min(
                        reducePrice,
                        singleDiscountGoods?.discountReduceInfo?.getReduceAmountLimitAfterAmplify()!!
                    )
                }
                totalPrice -= reducePrice
//                }
            } else {
                val percent = singleDiscountGoods?.reduceRatio ?: 0.0
                totalPrice -= BigDecimal(totalPrice * percent / 100.0).halfUp(0).toLong()
            }
        } else if (singleDiscountGoods?.type == SingleDiscountType.FIXED_AMOUNT.id) {
            //固定金额折扣
            if (singleDiscountGoods?.discountReduceActivityId != null) {
                totalPrice =
                    if (singleDiscountGoods?.discountReduceInfo?.reduceRateAmount == null) totalPrice else ((BigDecimal(
                        totalPrice
                    ) - singleDiscountGoods?.discountReduceInfo?.reduceRateAmount!!.times(
                        BigDecimal(100)
                    ))).toLong()
            } else {
                totalPrice =
                    if (singleDiscountGoods?.saleReduce == null) totalPrice else ((BigDecimal(
                        totalPrice
                    ) - BigDecimal(singleDiscountGoods?.saleReduce!! * 100.0).halfUp(0))).toLong()
            }
        } else if (singleDiscountGoods?.type == SingleDiscountType.MODIFY_PRICE.id) {
            totalPrice =
                if (singleDiscountGoods?.adjustSalePrice == null) totalPrice else (((singleDiscountGoods?.adjustSalePrice
                    ?: 0.0) * 100.0).toLong()) * (num ?: 0)
        }
        if (totalPrice < 0) {
            totalPrice = 0
        }
        return totalPrice
    }

    //获取单品折扣后的会员价总价
    fun getVipPriceAfterSingleDiscount(): Long {
        var totalPrice = totalVipPriceWithoutSingleDiscount()
        if (singleDiscountGoods?.type == SingleDiscountType.PERCENTAGE.id) {
            if (singleDiscountGoods?.discountReduceActivityId != null) {
                //后台配置的
                val percent = (singleDiscountGoods?.discountReduceInfo?.reduceRateAmount
                    ?: BigDecimal.ZERO).toDouble()

                var reducePrice = BigDecimal(totalPrice * percent / 100.0).halfUp(0).toLong()
                if (singleDiscountGoods?.discountReduceInfo?.getReduceAmountLimitAfterAmplify() != null) {
                    reducePrice = kotlin.math.min(
                        reducePrice,
                        singleDiscountGoods?.discountReduceInfo?.getReduceAmountLimitAfterAmplify()!!
                    )
                }
                totalPrice -= reducePrice
            } else {
                val percent = singleDiscountGoods?.reduceRatio ?: 0.0
                totalPrice -= BigDecimal(totalPrice * percent / 100.0).halfUp(0).toLong()
            }
        } else if (singleDiscountGoods?.type == SingleDiscountType.FIXED_AMOUNT.id) {
            if (singleDiscountGoods?.discountReduceActivityId != null) {
                //后台配置的
                val thresholdPrice =
                    singleDiscountGoods?.discountReduceInfo?.getThresholdAmountAfterAmplify()
                //无门槛或者满足门槛
                if (thresholdPrice == null || (totalPrice >= thresholdPrice)) {
                    totalPrice =
                        if (singleDiscountGoods?.discountReduceInfo?.reduceRateAmount == null) totalPrice else ((BigDecimal(
                            totalPrice
                        ) - singleDiscountGoods?.discountReduceInfo?.reduceRateAmount!!.times(
                            BigDecimal(100)
                        ))).toLong()
                }
            } else {
                //固定金额折扣
                totalPrice = if (goods?.isShowVipPrice() == true) {
                    //固定金额折扣
                    if (singleDiscountGoods?.vipReduce == null) totalPrice else ((BigDecimal(
                        totalPrice
                    ) - BigDecimal(singleDiscountGoods?.vipReduce!! * 100.0).halfUp(0))).toLong()
                } else {
                    if (singleDiscountGoods?.saleReduce == null) totalPrice else ((BigDecimal(
                        totalPrice
                    ) - BigDecimal(singleDiscountGoods?.saleReduce!! * 100.0).halfUp(0))).toLong()
                }
            }
        } else if (singleDiscountGoods?.type == SingleDiscountType.MODIFY_PRICE.id) {
            totalPrice = if (isShowVipPrice()) {
                Timber.e("改价vip有效")
                //如果vip价格有效
                if (singleDiscountGoods?.adjustVipPrice == null) totalPrice else ((singleDiscountGoods?.adjustVipPrice
                    ?: 0.0) * 100.0).toLong() * (num ?: 0)
            } else {
                Timber.e("改价vip无效")
                if (singleDiscountGoods?.adjustSalePrice == null) totalDiscountPriceWithoutSingleDiscount() else (((singleDiscountGoods?.adjustSalePrice
                    ?: 0.0) * 100.0).toLong()) * (num ?: 0)
            }
        }
        if (totalPrice < 0) {
            totalPrice = 0
        }
        return totalPrice
    }

    /**
     * 总的打包费
     *
     * @return
     */
    fun totalPackPrice(): Int {
        return (goods?.getCalculatePackingFee() ?: 0) * (num ?: 0)
    }


    fun getTagItemId(): String? {
        val sbf = StringBuffer()
        (goodsTagItems ?: listOf()).sortedBy { it.id }.forEachIndexed { index, goodsTagItem ->
            sbf.append(goodsTagItem.id)
            if (index != (goodsTagItems?.size ?: 0) - 1) {
                sbf.append(",")
            }
        }
        if (sbf.isEmpty()) {
            return null
        }
        return sbf.toString()
    }


    fun getFeedBo(): List<FeedBo> {
        val feedBoList = ArrayList<FeedBo>()
        feedInfoList?.forEach {
            feedBoList.add(
                FeedBo(
                    id = it.id,
                    num = it.alreadyNum
                )
            )
        }
        return feedBoList
    }


    /**
     * 是否显示vip价格
     *
     * @return
     */
    fun isShowVipPrice(): Boolean {
        return goods?.isShowVipPrice() ?: false
    }


    /**
     * 获取该商品最大可购买数量
     *
     * @param diningStyle
     * @return
     */
    fun getMaxBuyNum(diningStyle: Int): Int {
        var maxNum = GOOD_MAX_NUM
        if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
            //预定数量限制
            val restrictNum = (goods?.restrictNum
                ?: 0)
            //如果有预定数量限制
            if (restrictNum > 0) {
                maxNum = restrictNum
            }
        }
        return maxNum
    }


    fun getHash(): String {
        return HashHelper.getHash(
            feedInfoList,
            goodsTagItems,
            orderMealSetGoodList = orderMealSetGoodList,
            goodsId = goods?.id!!,
            singleItemDiscount = singleDiscountGoods,
            goodsPriceKey = getGoodPriceKey(),
            note = note,
            uuid = goods?.uuid
        )
    }

    /**
     * 获取不带单品折扣的hashkey
     *
     * @return
     */
    fun getHashWithoutSingleDiscount(): String {
        return HashHelper.getHash(
            feedInfoList,
            goodsTagItems,
            orderMealSetGoodList = orderMealSetGoodList,
            goodsId = goods?.id!!,
            null,
            goodsPriceKey = getGoodPriceKeyWithOutSingleDiscount(),
            note = note,
            uuid = goods?.uuid
        )
    }


    fun getGoodPriceKey(): String {
        var finalKey: String = ""
        return finalKey
    }

    private fun getGoodPriceKeyWithOutSingleDiscount(): String {
        return ""
//        return "${singleDiscountPrice()}${calculateVipPrice()}"
    }


    /**
     * 是否有设置过单品减免折扣
     *
     * @return
     */
    fun isSetSingleItemDiscount(): Boolean {
        if (singleDiscountGoods == null) {
            return false
        } else {
            if (singleDiscountGoods?.discountReduceActivityId != null) {
                return true
            }
            if (singleDiscountGoods?.type == null) {
                return false
            } else if (singleDiscountGoods?.type == 1) {
                return singleDiscountGoods?.reduceRatio != null
            } else if (singleDiscountGoods?.type == 2) {
                return singleDiscountGoods?.saleReduce != null || singleDiscountGoods?.vipReduce != null
            } else if (singleDiscountGoods?.type == 3) {
                return singleDiscountGoods?.adjustVipPrice != null || singleDiscountGoods?.adjustSalePrice != null
            }
            return false
        }
    }

    fun isSetModifyPrice(): Boolean {
        if (singleDiscountGoods?.type == 3) {
            return singleDiscountGoods?.adjustVipPrice != null || singleDiscountGoods?.adjustSalePrice != null
        }
        return false
    }


    /**
     * 获取该商品参与门槛的现价金额  计算当前商品价格+服务费/打包费 (不受单品折扣影响)
     *
     */
    fun getTotalDiscountThresholdPrice(diningStyle: Int): Long {
        var price = totalDiscountPriceWithoutSingleDiscount()
        if (diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
            //外带门槛加打包费
            price += totalPackPrice()
        } else {
            //非外带加服务费
            price += totalDiscountServiceChargePrice(false)
        }
        return price
    }

    /**
     * 获取该商品参与门槛的现价金额
     *
     */
    fun getTotalVipThresholdPrice(diningStyle: Int): Long {
        var price = totalVipPriceWithoutSingleDiscount()
        if (diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
            //外带门槛加打包费
            price += totalPackPrice()
        } else {
            //非外带加服务费
            price += totalVipServiceChargePrice(false)
        }
        return price
    }


}


