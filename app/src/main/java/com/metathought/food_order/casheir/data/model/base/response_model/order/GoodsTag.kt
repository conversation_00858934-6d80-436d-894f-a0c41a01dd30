package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.constant.SpecificationTypeEnum

data class GoodsTag(
//    val createTime: String,
//    val mainStoreId: String,
//    val saasStoreId: String,
//    val showSort: Int,
//    val storeId: String,
//    val updateTime: String,
    @SerializedName("id")
    val id: String?,
    @SerializedName("items")
    var goodsTagItems: List<GoodsTagItem>?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("type")
    val type: Int?
) {
    //这个规格属性下是否有选择
    fun isTagSelect(): Boolean {
        return (goodsTagItems?.filter { it.isCheck == true } ?: listOf()).isNotEmpty()
    }

    //非小料必选
    fun isMustSelect(): Boolean {
        return type == SpecificationTypeEnum.FEATURE.id || type == SpecificationTypeEnum.SPECIFICATION.id
    }
}