package com.metathought.food_order.casheir.data.model.base.request_model

import com.google.gson.annotations.SerializedName

class ComprehensiveReportRequest(
//    @SerializedName("storeIdList")
//    val storeIdList: List<String>? = null,
    @SerializedName("diningStyle")
    val diningStyle: Int? = null,
    @SerializedName("mergeType")
    val mergeType: Int? = null,
    @SerializedName("timeType")
    val timeType: Int? = null,
    @SerializedName("startTime")
    val startTime: String? = null,
    @SerializedName("endTime")
    val endTime: String? = null,
//    @SerializedName("diningStyle")
//    val diningStyle: Int
)