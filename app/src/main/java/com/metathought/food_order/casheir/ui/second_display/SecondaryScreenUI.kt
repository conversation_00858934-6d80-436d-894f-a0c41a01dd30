package com.metathought.food_order.casheir.ui.second_display

import android.annotation.SuppressLint
import android.app.Presentation
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.Display
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargePaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Group
import com.metathought.food_order.casheir.data.model.base.response_model.order.ShoppingRecordCalculationResult
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.databinding.LayoutSecondaryScreenBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getDiningStyleString
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.helper.CouponHelper
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.ui.adapter.MenuAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondMenuAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondMenuOrderFoodAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondOrderedInfoAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondPrevoiusOrderedAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.SecondCashConvertDialog
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import com.metathought.food_order.casheir.ui.order.payment.PaymentQrDialog
import com.metathought.food_order.casheir.ui.second_display.menu.food_detail.OrderFoodDetailDialog
import com.metathought.food_order.casheir.ui.second_display.ordered.refunds.OrderedCancelDishDialog
import com.metathought.food_order.casheir.ui.second_display.ordered.refunds.OrderedRefundDialog
import com.metathought.food_order.casheir.ui.widget.DividerItemDecoration
import com.tencent.bugly.crashreport.CrashReport
import kh.org.nbc.bakong_khqr.BakongKHQR
import timber.log.Timber
import java.math.BigDecimal
import java.nio.charset.Charset


/**
 * <AUTHOR>
 * @date 2024/5/2410:51
 * @description
 */
class SecondaryScreenUI(private var mContext: Context, display: Display) :
    Presentation(mContext, display) {

    private var binding: LayoutSecondaryScreenBinding? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        /**
         * 防止杀进程再进副屏不会显示
         */
        try {
            if (window != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                } else {
                    window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                }
            }
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }


        Timber.e("创建 SecondaryScreenUI")
        binding = LayoutSecondaryScreenBinding.inflate(layoutInflater)
        binding?.run {
            setContentView(root)
            //其他
            initOtherResource()
            //点餐
            initMenuResource()
            menuOrderInitView()
            //订单
            initOrderedView()
            initOrderedResource()
            //
            initQRPaymentResource()
        }
    }

    override fun onSaveInstanceState(): Bundle {
        return super.onSaveInstanceState()
    }

//    fun setBitmap(bitmap: Bitmap) {
//        binding?.run {
//            ivPrinter.setImageBitmap(bitmap)
//        }
//    }

    //-------------------------------其他情况 副屏 开始----------------------------------------------------//
    private fun LayoutSecondaryScreenBinding.initOtherResource() {
//        businessSlogan.text = mContext.getString(R.string.your_best_business_partner)
        welcome.text = mContext.getString(R.string.welcome)
    }

    //隐藏其他布局
    private fun LayoutSecondaryScreenBinding.hideDefaultOther() {
        clOtherLayout.isVisible = true
        clMenOrder.isVisible = false
        clOrderedInfo.isVisible = false
        clQRPayment.isVisible = false
        clTopup.isVisible = false
//        clPaymentInfoLayout.isVisible = false
        initOtherResource()
    }

    var userInfo: UserLoginResponse? = null
    fun showDefault(user: UserLoginResponse? = null) {
        if (!isShowing) {
            show()
        }
        binding?.run {
            if (user != null) {
                userInfo = user
            }
            hideDefaultOther()
            val requestOptions = RequestOptions()
                .transform(CenterCrop(), RoundedCorners(16))
            //update
            Glide.with(context).load(userInfo?.url ?: "").apply(requestOptions)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgOtherLogo)
            tvOtherStoreName.text = userInfo?.getStoreNameByLan()
        }

    }

    //-------------------------------其他情况 副屏 结束----------------------------------------------------//


    //-------------------------------点餐 副屏 开始----------------------------------------------------//

    private fun LayoutSecondaryScreenBinding.initMenuResource() {
        //initResource
        tvDeleteAll.text = mContext.getString(R.string.items)
        menuQuantity.text = mContext.getString(R.string.quantity)
        menuAmount.text = mContext.getString(R.string.amount)
//        menuPackingPrice.text = mContext.getString(R.string.packing_price)
//        menuService.text = mContext.getString(R.string.service_fee)
//        menuSubtotal.text = mContext.getString(R.string.subtotal)
//        menuVat.text = mContext.getString(R.string.vat)
//        menuCoupon.text = mContext.getString(R.string.coupon)
//        tvMenuViewCouponGiftGood.text = mContext.getString(R.string.view_give_away_goods)
//        discounted.text = mContext.getString(R.string.discounted)
        menuTotalPrice.text = mContext.getString(R.string.total_price)
    }

    fun showMenu() {
        binding?.apply {
            clOtherLayout.isVisible = false
            clMenOrder.isVisible = true
            clOrderedInfo.isVisible = false
            clQRPayment.isVisible = false
            clTopup.isVisible = false
//            clPaymentInfoLayout.isVisible = false
        }
    }

    //隐藏其他布局
    private fun LayoutSecondaryScreenBinding.hideMenuOther(isInit: Boolean = true) {
        clOtherLayout.isVisible = false
        clMenOrder.isVisible = true
        clOrderedInfo.isVisible = false
        clQRPayment.isVisible = false
//        clPaymentInfoLayout.isVisible = false
        initMenuResource()
        if (isInit) {
            menuOrderInitView()
        }
    }

    fun showMenuOrder(isInit: Boolean = true) {
        if (!isShowing) {
            //没显示就初始化一下
            show()
        }
        binding?.apply {
            hideMenuOther(isInit)
        }
    }

    //    private var adapterCategory: SecondFoodCategoryAdapter? = null
    private var menuAdapter: SecondMenuAdapter? = null
    private var menuOrderFoodAdapter: SecondMenuOrderFoodAdapter? = null
    private var gridLayoutManager: GridLayoutManager? = null
    private var detailDialog: OrderFoodDetailDialog? = null
    private var secondPrevoiusOrderedAdapter: SecondPrevoiusOrderedAdapter? = null
    fun getMenuOrderDetailDialog(): OrderFoodDetailDialog? {
        if (detailDialog == null) {
            detailDialog = OrderFoodDetailDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //传入主屏上下文，解决多语言失效问题
                initResource(mContext)
            }
        } else {
            detailDialog?.show()
            detailDialog?.initResource(mContext)
        }
        return detailDialog
    }


    fun dismissDialog() {
        detailDialog?.dismiss()
    }


    private var secondCashConvertDialog: SecondCashConvertDialog? = null

    fun getSecondCashConvertDialog(): SecondCashConvertDialog? {
        if (secondCashConvertDialog == null) {
            secondCashConvertDialog = SecondCashConvertDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //传入主屏上下文，解决多语言失效问题
                initResource(mContext)
            }
        } else {
            secondCashConvertDialog?.show()
            secondCashConvertDialog?.initResource(mContext)
        }
        return secondCashConvertDialog
    }

    fun dismissSecondCashConvertDialog() {
        secondCashConvertDialog?.dismiss()
    }

    private var menuHoriCount = 3
    private fun LayoutSecondaryScreenBinding.menuOrderInitView() {
        if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == false) {
            menuHoriCount = 4
        }
        gridLayoutManager = GridLayoutManager(mContext, menuHoriCount)
        gridLayoutManager?.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return when (menuAdapter?.getItemViewType(position)) {
                    MenuAdapter.ViewType.Header.ordinal -> menuHoriCount
                    MenuAdapter.ViewType.Content.ordinal -> 1
                    else -> menuHoriCount
                }
            }
        }

        menuAdapter = SecondMenuAdapter(arrayListOf(), mContext)
        recyclerViewMenu.run {
            layoutManager = gridLayoutManager
            adapter = menuAdapter
        }
        Timber.e("MainDashboardFragment.CURRENT_USER?.url: ${MainDashboardFragment.CURRENT_USER?.url}")
        Glide.with(context).load(MainDashboardFragment.CURRENT_USER?.url).circleCrop()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgMenuLogo)
        tvMenuStoreName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
        menuOrderFoodAdapter = SecondMenuOrderFoodAdapter(arrayListOf(), mContext)
        recyclerOrderedFood.run {
            val dividerItemDecoration =
                DividerItemDecoration(mContext, DividerItemDecoration.VERTICAL).apply {
                    setDrawable(mContext.getDrawable(R.drawable.divider_vertical_ddd))
                }
            addItemDecoration(dividerItemDecoration)
            recyclerOrderedFood.adapter = menuOrderFoodAdapter
        }
    }

    fun updateRecyclerOrderedFood(position: Int) {
        binding?.run {
            try {
                recyclerOrderedFood.scrollToPosition(position)
            } catch (e: Exception) {

            }
        }
    }

    fun updateRecyclerPreviousOrderedFood(position: Int) {
        binding?.run {
            try {
                recyclerPreviousOrderedFood.scrollToPosition(position)
            } catch (e: Exception) {

            }
        }
    }

    fun updateRecyclerMenu(position: Int) {
        binding?.run {
            try {
                recyclerViewMenu.scrollToPosition(position)
            } catch (e: Exception) {

            }
        }
    }

    fun updateRecyclerViewCategories(position: Int) {
//        binding?.run {
//            recyclerViewCategories.smoothScrollToPosition(position)
//        }
    }

    fun updateMenuOrderFoodAdapter(newItems: ArrayList<GoodsRequest>?) {
        Timber.e("更新副屏  菜单list ${newItems?.size}")
        newItems?.let {
            menuOrderFoodAdapter?.updateItems(it)
        }
    }

    fun updateMenuOrderFoodAdapterDining(diningStyle: Int) {
        menuOrderFoodAdapter?.updateDiningStyle(diningStyle)
    }

    fun updateMenuWithCart() {
        menuAdapter?.notifyDataSetChanged()
    }


    fun updateByOrderMoreData(
        list: ArrayList<com.metathought.food_order.casheir.data.model.base.response_model.cart.Goods>,
        previousOrderCount: String,
        pricePreviousOrder: String
    ) {
        binding?.run {
            try {
                if (secondPrevoiusOrderedAdapter == null) {
                    secondPrevoiusOrderedAdapter = SecondPrevoiusOrderedAdapter(list, mContext)
                    recyclerPreviousOrderedFood.adapter =
                        SecondPrevoiusOrderedAdapter(list, mContext)
                } else {
                    secondPrevoiusOrderedAdapter?.replaceData(list)
                }

                tvPreviousOrderCount.text = previousOrderCount
                tvPricePreviousOrder.text = pricePreviousOrder
            } catch (e: Exception) {

            }
        }
    }

    fun updateFromObserve(diningStyle: Int) {
        binding?.run {
            val isOrderMore = ShoppingHelper.get(diningStyle)?.isOrderMore == true
            if (isOrderMore) {
                layoutOrderMore.isVisible = true
                layoutNewOrderTitle.isVisible = true
            } else if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                clearOrderMore()
            } else {
                clearOrderMore()
            }
        }
    }

    fun setListenerOrderMore(isNullOrEmpty: Boolean) {
        binding?.run {
            if (recyclerPreviousOrderedFood.isVisible) {
                arrowNewOrder.rotation = 180f
                arrowOldOrder.rotation = 0f
                recyclerOrderedFood.isVisible = true
                recyclerPreviousOrderedFood.isVisible = false
                layoutEmpty.root.isVisible = isNullOrEmpty
                vTopLine.isVisible = !isNullOrEmpty
            } else {
                layoutEmpty.root.isVisible = false
                arrowNewOrder.rotation = 0f
                arrowOldOrder.rotation = 180f
                recyclerOrderedFood.isVisible = false
                recyclerPreviousOrderedFood.isVisible = true
                vTopLine.isVisible = true
            }
            flOrderedFood.isVisible = recyclerOrderedFood.isVisible || layoutEmpty.root.isVisible
        }
    }

    fun setListenerOrderTitle(isNullOrEmpty: Boolean) {
        binding?.run {
            layoutEmpty.root.isVisible = isNullOrEmpty
            vTopLine.isVisible = !isNullOrEmpty
            recyclerOrderedFood.isVisible = true
            recyclerPreviousOrderedFood.isVisible = false
            flOrderedFood.isVisible = recyclerOrderedFood.isVisible || layoutEmpty.root.isVisible
        }
    }

    @SuppressLint("SetTextI18n")
    fun updateShoppingRecord(
        result: ShoppingRecordCalculationResult?,
        shoppingRecord: ShoppingRecord,
        localDingingStyle: Int?,
    ) {
        binding?.apply {
            val goodsVoList = result?.goodsVoList ?: listOf()
            // 1. 适配器数据更新
            menuOrderFoodAdapter?.updateItems(ArrayList(goodsVoList))
            localDingingStyle?.let { diningStyle ->
                menuOrderFoodAdapter?.updateDiningStyle(diningStyle)
            }


            // 2. 状态控制（显示/隐藏）
            layoutEmpty.let {
                it.tvEmptyText.text = mContext.getString(R.string.please_select_item)
                it.root.setVisibleInvisible(goodsVoList.isEmpty())
                it.imgError.setImageDrawable(context?.let { it1 ->
                    ContextCompat.getDrawable(
                        it1, R.drawable.ic_empty_food_order
                    )
                })
                it.imgError.isVisible = !(result?.isOrderMore ?: false)
            }

            layoutMainOrdered.setBackgroundResource(if (goodsVoList.isEmpty()) R.drawable.background_round_top_trasparent else R.drawable.background_round_top_white)

            recyclerOrderedFood.setVisibleInvisible(goodsVoList.isNotEmpty())
            menuLayoutTotal.setVisibleInvisible(goodsVoList.isNotEmpty())
            if (recyclerOrderedFood.isVisible) {
                flOrderedFood.isVisible = true
            }
            vTopLine.isVisible = goodsVoList.isNotEmpty()

            if (shoppingRecord.tableLabel.isNullOrEmpty()) {
                tvSelectTable.text = mContext.getString(R.string.select_table)
            } else {
                tvSelectTable.text = shoppingRecord.tableLabel
            }

            tvMenuDiningStyle.text = localDingingStyle?.getDiningStyleString(mContext)


            tvMenuTotalPrice.text = result?.totalDiscountPrice?.priceFormatTwoDigitZero2()
            tvMenuVipPrice.text = result?.totalVipPrice?.priceFormatTwoDigitZero2()
            tvMenuVipPrice.isVisible = result?.isHasVipPrice == true
            tvMenuTotalKhrPrice.text = "៛${
                FoundationHelper.usdConverToKhr(
                    result?.conversionRatio,
                    result?.totalDiscountPrice
                ).decimalFormatZeroDigit()
            }"
            tvMenuTotalKhrPrice.isVisible = result?.takeOutPlatformModel?.isKhr() != true


            //有待称重的这里统一显示
            if (result?.hasUnProcess == true) {
                tvMenuTotalPrice.text = mContext.getString(R.string.to_be_confirmed)
                tvMenuTotalKhrPrice.isVisible = false
                tvMenuVipPrice.isVisible = false
            }
            Timber.e("hasUnProcess :${result?.hasUnProcess}")

            if (result?.isOrderMore == true) {
                menuLayoutTotal.isVisible = true

                binding?.layoutOrderMore?.isVisible = true
                binding?.layoutNewOrderTitle?.isVisible = true
                if (goodsVoList.isNotEmpty()) {
                    recyclerPreviousOrderedFood.isVisible = false
                }
                //新购物车的总计
                val totalPriceFormatter =
                    result.currentCartTotalDiscountSubPrice.priceFormatTwoDigitZero2()
                tvNewOrderTotalPrice.text = totalPriceFormatter
                if (result.hasUnProcess == true) {
                    tvNewOrderTotalPrice.text = mContext.getString(R.string.to_be_confirmed)
                }
                tvOrderMoreCount.text = mContext.getString(R.string.new_order, result.cartGoodNum)
            } else if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                clearOrderMore()
            } else {
                clearOrderMore()
            }
        }
    }

    private fun clearOrderMore() {
        binding?.run {
            layoutOrderMore.isVisible = false
            layoutNewOrderTitle.isVisible = false
            recyclerPreviousOrderedFood.isVisible = false
        }
    }

    /**
     * 更新菜品滑动距离
     * Update the sliding distance of dishes
     */
    fun updateMenuPosition(groupId: String) {
        binding?.run {
            menuAdapter?.getHeaderPosition(groupId)?.let { it1 ->
                (recyclerViewMenu.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                    it1,
                    0
                )
            }
        }
    }

    /**
     * 更新菜品分类适配器
     * Update the dish classification adapter
     */
    fun updateCategoryAdapter(current: Group) {
//        adapterCategory?.run {
////            val preIndex=categories.indexOf(pre)
////            val nextIndex=categories.indexOf(next)
////            if(preIndex!=-1){
////                notifyItemChanged(preIndex)
////            }
////            if(nextIndex!=-1){
////                notifyItemChanged(nextIndex)
////            }
//            notifyDataSetChanged()
//        }
    }


    fun updateMenuAdapterAndScrollPosition(list: ArrayList<BaseGoods>?) {
        updateMenuAdapter(list)
        binding?.run {
            recyclerViewMenu.layoutManager?.scrollToPosition(0)
        }
    }

    /**
     * 更新菜品适配器
     * Update the dish adapter
     */
    fun updateMenuAdapter(list: ArrayList<BaseGoods>?) {
        list?.let {
            menuAdapter?.updateItems(it)
        }
    }

    fun updateMenuAdapterAndLocalDiningStyle(diningStyle: Int) {
        menuAdapter?.localDiningStyle = diningStyle
        updateMenuAdapter()
    }

    fun updateMenuAdapter(isModifyLayoutManager: Boolean? = false) {
        if (isModifyLayoutManager == true) {
            if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == false) {
                menuHoriCount = 4
            }
            gridLayoutManager = GridLayoutManager(mContext, menuHoriCount)
            gridLayoutManager?.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (menuAdapter?.getItemViewType(position)) {
                        MenuAdapter.ViewType.Header.ordinal -> menuHoriCount
                        MenuAdapter.ViewType.Content.ordinal -> 1
                        else -> menuHoriCount
                    }
                }
            }
            binding?.recyclerViewMenu?.layoutManager = gridLayoutManager
        }
        menuAdapter?.notifyDataSetChanged()
    }

    fun updateCategoryAdapter(newItems: ArrayList<Group>?) {
//        newItems?.let {
//            adapterCategory?.updateItems(newItems)
//        }
    }
    //-------------------------------点餐 副屏 结束----------------------------------------------------//


    //-------------------------------订单 副屏 开始---------------------------------------------------//
    private var orderedInfoAdapter: SecondOrderedInfoAdapter? = null

    private fun LayoutSecondaryScreenBinding.initOrderedView() {
        orderedInfoRecyclerView.run {
            orderedInfoAdapter = SecondOrderedInfoAdapter(arrayListOf(), context = mContext)
            val dividerItemDecoration =
                DividerItemDecoration(mContext, DividerItemDecoration.VERTICAL).apply {
                    setDrawable(mContext.getDrawable(R.drawable.divider_vertical_ddd))
                }
            addItemDecoration(dividerItemDecoration)
            adapter = orderedInfoAdapter
        }


        Glide.with(context).load(MainDashboardFragment.CURRENT_USER?.url).circleCrop()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgOrderedLogo)
        tvOrderedStoreName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
    }

    private fun LayoutSecondaryScreenBinding.initOrderedResource() {
//        items.text = mContext.getString(R.string.items)
//        quantity.text = mContext.getString(R.string.quantity)
//        amount.text = mContext.getString(R.string.amount)

//        tvCustomerTitle.text = mContext.getString(R.string.customer_info)
//        customerName.text = mContext.getString(R.string.customer_name)
//        phoneNumber.text = mContext.getString(R.string.phone_number)
//        orderInfo.text = mContext.getString(R.string.order_info)
//        pickUpNo.text = mContext.getString(R.string.print_title_pick_up_no)
//        orderId.text = mContext.getString(R.string.order_id)
//        invoiceNumber.text = mContext.getString(R.string.print_title_invoiceNumber)
//        orderedTime.text = mContext.getString(R.string.ordered_time)
//        orderType.text = mContext.getString(R.string.order_type)
//        people.text = mContext.getString(R.string.people)
//        orderBy.text = mContext.getString(R.string.order_by)
//        paymentMethod.text = mContext.getString(R.string.payment_method)
//        diningTime.text = mContext.getString(R.string.ordered_dining_time)
//        cancelTime.text = mContext.getString(R.string.cancel_time)
//        cancelReasonTitle.text = mContext.getString(R.string.cancel_reason)
//        paymentTime.text = mContext.getString(R.string.payment_time)
//        refundTime.text = mContext.getString(R.string.refund_time)
//        remark.text = mContext.getString(R.string.remark)

        packingPrice.text = mContext.getString(R.string.packing_price)
        subtotal.text = mContext.getString(R.string.subtotal)
        serviceFee.text = mContext.getString(R.string.service_fee)
        vat.text = mContext.getString(R.string.vat)
        coupon.text = mContext.getString(R.string.coupon)
        total2.text = mContext.getString(R.string.total_price)
        partialRefundAmount.text = mContext.getString(R.string.partial_refund_amount)
        partialRefundServiceFee.text = mContext.getString(R.string.refund_service_fee)
        vatRefund.text = mContext.getString(R.string.vat_refund)
        totalPrice.text = mContext.getString(R.string.receivable)
        actualReceivedAmount.text = mContext.getString(R.string.actual_received_amount)
        refundAmount.text = mContext.getString(R.string.refund_amount)
        tvViewCouponGiftGood.text = mContext.getString(R.string.view_give_away_goods)
        commission.text = mContext.getString(R.string.commission)
    }

    //退款
    private var refundDialog: OrderedRefundDialog? = null
    fun getRefundDialog(): OrderedRefundDialog? {
        if (refundDialog == null) {
            refundDialog = OrderedRefundDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //解决多语言失效问题
                initResource(mContext)
            }
        } else {
            refundDialog?.show()
            refundDialog?.initResource(mContext)
        }
        return refundDialog
    }

    fun dismissRefundDialog() {
        refundDialog?.dismiss()
//        refundDialog = null
    }


    //退菜
    private var cancelDishDialog: OrderedCancelDishDialog? = null
    fun getCancelDishDialog(): OrderedCancelDishDialog? {
        if (cancelDishDialog == null) {
            cancelDishDialog = OrderedCancelDishDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //解决多语言失效问题
                initResource(mContext)
            }
        } else {
            cancelDishDialog?.show()
            cancelDishDialog?.initResource(mContext)
        }
        return cancelDishDialog
    }

    fun dismissCancelDishDialog() {
        cancelDishDialog?.dismiss()
//        cancelDishDialog = null
    }

    fun updateOrderedInfoRecyclerView(position: Int) {
        binding?.run {
            orderedInfoRecyclerView.smoothScrollToPosition(position)
        }
    }

    fun showOrder() {
        binding?.apply {
            clOtherLayout.isVisible = false
            clMenOrder.isVisible = false
            clOrderedInfo.isVisible = true
            clQRPayment.isVisible = false
            clTopup.isVisible = false
//            clPaymentInfoLayout.isVisible = false
        }
    }

    fun showOrderedInfo() {
        binding?.run {
            clOtherLayout.isVisible = false
            clMenOrder.isVisible = false
            clOrderedInfo.isVisible = true
            clQRPayment.isVisible = false
//            clPaymentInfoLayout.isVisible = false
            initOrderedResource()
            initOrderedView()
        }
    }

    @SuppressLint("SetTextI18n")
    fun setOrderInfo(
        ordered: OrderedInfoResponse?,
        orderUsableCouponList: List<CouponModel>? = listOf()
    ) {

        mContext.run {
            binding?.run {
                ordered?.let {
                    orderedInfoAdapter?.replaceData(
                        OrderHelper.sortingGoodsWithMergeOrder(
                            mContext,
                            it.mergeOrderIds, it.acceptOrderIds,
                            it.goods,
                            it.removeGoodList
                        ),
                        it.refundGoodsJson,
                        orderedInfoResponse = it
                    )
                    tvTableID.text = it.tableName

                    tvOrderStyle.text = it.payStatus?.getPayText(mContext)
                    tvOrderStyle.setTextColor(
                        ContextCompat.getColor(
                            mContext,
                            it.payStatus?.getPayTypeColor() ?: R.color.ordered_cancel_color
                        )
                    )

//                    //减免折扣相关
//                    if (it.isShowVipDiscountIcon()) {
//                        val endDrawable = ContextCompat.getDrawable(
//                            context,
//                            R.drawable.icon_vip
//                        )
//                        tvDiscountAmount.setCompoundDrawablesWithIntrinsicBounds(
//                            endDrawable,
//                            null,
//                            null,
//                            null
//                        )
//                        tvDiscount.setCompoundDrawablesWithIntrinsicBounds(
//                            endDrawable,
//                            null,
//                            null,
//                            null
//                        )
//                    } else {
//                        tvDiscountAmount.setCompoundDrawablesWithIntrinsicBounds(
//                            null,
//                            null,
//                            null,
//                            null
//                        )
//                        tvDiscount.setCompoundDrawablesWithIntrinsicBounds(
//                            null,
//                            null,
//                            null,
//                            null
//                        )
//                    }


                    val subTotalPrice = it.getSubTotal()
                    val serviceFeePrice = it.getRealServiceFeePrice()
                    val vipServiceFeePrice = it.getVipServiceFeePrice()
                    val vatPrice = it.getRealVatPrice()
                    val vipVatPrice = it.getVipVat()
                    var totalPrice = it.getRealPayPrice()
                    var totalVipPrice = it.getFinalTotalVipPrice()


                    //tvPackingAmount.text = "${it.totalPackingFee?.priceFormatTwoDigitZero2()}"
                    tvPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                        (it.totalPackingFee ?: 0).toLong(), it.isKhr()
                    )
                    llPackPrice.isVisible = (it.totalPackingFee ?: 0) > 0


                    //小计
                    tvSubtotal.text = FoundationHelper.getPriceStrByUnit(
                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                        subTotalPrice, it.isKhr()
                    )
                    //增值税
                    tvVat.text = FoundationHelper.getPriceStrByUnit(
                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                        vatPrice, it.isKhr()
                    )
                    vat.text =
                        "${mContext.getString(R.string.vat)}(${it.price?.getVatPercentage()}%)"
                    llVat.isVisible =
                        (ordered.price?.vatPercentage ?: BigDecimal.ZERO) > BigDecimal.ZERO

                    //服务费
                    tvServiceFee.text = serviceFeePrice.priceFormatTwoDigitZero2()

                    if (it.isTakeOut()) {
                        llCommission.isVisible = true
                        tvCommissionPrice.text =
                            "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.price?.getCommissionToLong() ?: 0L, it.isKhr()
                                )
                            }"
                    } else {
                        llCommission.isVisible = false
                    }

                    //总计2
                    tvTotalPrice2.text = FoundationHelper.getPriceStrByUnit(
                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                        totalPrice, it.isKhr()
                    )
                    tvTotalKhrPrice2.text = "៛${
                        FoundationHelper.usdConverToKhr(
                            it.conversionRatio ?: FoundationHelper.conversionRatio!!,
                            totalPrice
                        ).decimalFormatZeroDigit()
                    }"

                    //总计1
                    tvTotalPrice.text = FoundationHelper.getPriceStrByUnit(
                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                        totalPrice, it.isKhr()
                    )
                    tvTotalKhrPrice.isVisible = true
                    tvTotalKhrPrice.text = "៛${
                        FoundationHelper.usdConverToKhr(
                            it.conversionRatio ?: FoundationHelper.conversionRatio!!,
                            totalPrice
                        ).decimalFormatZeroDigit()
                    }"

                    if (it.isTakeOut() && it.isKhr()) {
                        tvTotalKhrPrice.isVisible = false
                    }

                    //会员价
                    tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()


                    val isShowVip = it.isShowVipPrice()
                    tvVipPrice.isVisible = isShowVip


                    //=======优惠活动金额================
                    if (it.couponActivityList.isNullOrEmpty()) {
                        llDiscountActivity.isVisible = false
                    } else {
                        if (it.couponActivityList?.size == 1) {
                            tvDiscountActivityTitle.text =
                                it.couponActivityList?.firstOrNull()?.activityLabelName
                        } else {
                            tvDiscountActivityTitle.text =
                                mContext.getString(R.string.discount_activity)
                        }
                        if (it.isAfterPayStatus()) {
                            tvDiscountActivityAmount.text =
                                "-${
                                    it.getTotalCouponActivityAmount()
                                        ?.priceFormatTwoDigitZero2()
                                }"
                        } else {
                            val discountActivityUnWeightList =
                                it.couponActivityList?.filter { it.weightMark == true } ?: listOf()
                            if (discountActivityUnWeightList.isNullOrEmpty()) {
                                tvDiscountActivityAmount.text =
                                    "-${
                                        it.getTotalCouponActivityAmount()
                                            ?.priceFormatTwoDigitZero2()
                                    }"
                            } else {
                                tvDiscountActivityAmount.text = getString(R.string.to_be_confirmed)
                            }
                        }
                        llDiscountActivity.isVisible = true
                    }

                    //===================优惠券相关=================
                    llCoupon.isVisible = false
                    tvCoupon.isVisible = true
                    val isHasNeedProcess = ordered.isHasNeedProcess()
                    tvCoupon.text = it.getCouponDesc(
                        mContext,
                        orderUsableCouponList.isNullOrEmpty(), isHasNeedProcess
                    )
                    tvViewCouponGiftGood.isVisible = false
                    if (it.getCurrentCoupon() != null) {
                        llCoupon.isVisible = true
                    }

                    if (!it.isAfterPayStatus()) {
                        //支付前 总计要自己再计算一遍优惠券的金额
                        it.getTotalVipPriceBySelf()
                        it.getTotalPriceBySelf()
                    }
                    llServiceFee.isVisible = ordered.getServicePercentage() > 0
                    //======================优惠券================================

                    if (isShowVip) {
//                        llServiceFee.isVisible =
//                            serviceFeePrice > 0 || vipServiceFeePrice > BigDecimal.ZERO
//                        llVat.isVisible = vatPrice > 0 || vipVatPrice > BigDecimal.ZERO
//
//                        if (it.isAfterPayStatus()) {
//                            //如果是支付后的状态
//                            llServiceFee.isVisible = serviceFeePrice > 0
//                            llVat.isVisible = vatPrice > 0
//                        }
                    } else {

                        tvVipPrice.isVisible = false

//                        llServiceFee.isVisible = serviceFeePrice > 0
//                        llVat.isVisible = vatPrice > 0

                    }

                    //判断是否有新版整单减免
                    llDiscount.isVisible = false
                    llDiscountAmount.isVisible = false
                    if (it.isSetWholeDiscount()) {
                        if (it.discountReduceActivity != null) {
                            if (it.discountReduceActivity?.isPercentDiscount() == true) {
                                llDiscount.isVisible = true
                                tvDiscount.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                                        it.getWholePercentDiscountAmount()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        it.isKhr()
                                    )
                                }"
                                tvDiscountTitle.text =
                                    "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                            } else if (it.discountReduceActivity?.isFixedAmount() == true) {
                                //设置的是整单减免
                                llDiscountAmount.isVisible = true
                                val title = "${getString(R.string.discounts2)}"
                                val content = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                                        it.getNormalWholeItemReduceDollar()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        it.isKhr()
                                    )
                                }"
                                tvDiscountAmount.text = content
                                tvDiscountAmountTitle.text = title
                            }
                        } else {
                            if (it.getWholePercentDiscount() > BigDecimal.ZERO) {
                                //设置的是整单百分比折扣
                                llDiscount.isVisible = true
                                tvDiscount.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                                        it.getWholePercentDiscountAmount().times(BigDecimal(100))
                                            .toLong(),
                                        it.wholeDiscountReduce?.isCustomizeKhr() == true
                                    )
                                }"
                                tvDiscountTitle.text =
                                    "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                            }

                            if (it.isSetCustomizeReduce()) {
                                //设置的是整单减免
                                llDiscountAmount.isVisible = true
                                var content = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                                        it.getNormalWholeItemReduceDollar().times(BigDecimal(100))
                                            .toLong(),
                                        it.wholeDiscountReduce?.isCustomizeKhr() == true
                                    )
                                }"
                                if (it.getNormalWholeReduceKhr() > BigDecimal.ZERO || it.getVipWholeReduceKhr() > BigDecimal.ZERO) {
                                    content =
                                        "-៛${
                                            it.getNormalWholeItemReduceKhr()
                                                .decimalFormatZeroDigit()
                                        }"
                                }
                                tvDiscountAmount.text = content
                                tvDiscountAmountTitle.text = getString(R.string.discounts2)
                                if (it.isAfterPayStatus()) {
                                    //如果支付完了以后 有设置值才显示
                                    if (it.getNormalWholeReduceDollar() == BigDecimal.ZERO && it.getNormalWholeReduceKhr() == BigDecimal.ZERO) {
                                        llDiscountAmount.isVisible = false
                                    }
                                }
                            }
                        }
                    }
//                    if (it.isSetWholeDiscount()) {
//                        //如果是整单百分比减免
//                        if (it.isSetWholePercentDiscount()) {
//                            llDiscount.isVisible = true
//                            tvDiscount.text = "-${
//                                FoundationHelper.getPriceStrByUnit(
//                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
//                                    it.getWholePercentDiscountAmount().times(BigDecimal(100))
//                                        .toLong(),
//                                    it.isKhr()
//                                )
//                            }"
//                            tvDiscountTitle.text =
//                                "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
//
//                        } else {
//                            //如果是整单固定金额减免
//                            llDiscountAmount.isVisible =
//                                it.isSetWholeReduce() || it.isSetVipWholeReduce()
//                            tvDiscountAmount.text = "-${
//                                FoundationHelper.getPriceStrByUnit(
//                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
//                                    it.getNormalWholeDiscountDollar().times(BigDecimal(100))
//                                        .toLong(),
//                                    it.isKhr()
//                                )
//                            }"
//                            tvDiscountAmountTitle.text = getString(R.string.discounts2)
//                        }
//                    } else {
//
//                        if (it.reduceAmount != null && it.reduceAmount != 0L || ((it.reduceRate
//                                ?: 0.0) > 0.0)
//                        ) {
//                            llDiscount.isVisible = true
//                            tvDiscount.text = "-${it.reduceAmount?.priceFormatTwoDigitZero2()}"
//
//                            tvDiscountTitle.text =
//                                "${getString(R.string.discounts)}(${it?.getReduceRateFomat()})"
//                        } else {
//                            llDiscount.isVisible = false
//                        }
//
//                        if ((it.reduceKhr == null || it.reduceKhr == 0L) && (it.reduceDollar == null || it.reduceDollar == 0L)) {
//                            llDiscountAmount.isVisible = false
//                        } else if (it.reduceKhr != null && it.reduceDollar == null) {
//                            tvDiscountAmount.text =
//                                "- ៛${(it.reduceKhr ?: 0).decimalFormatZeroDigit()}"
//                            llDiscountAmount.isVisible = true
//                            tvDiscountAmountTitle.text = getString(R.string.amount_of_reduction_khr)
//                        } else if (it.reduceKhr == null && it.reduceDollar != null) {
//                            tvDiscountAmount.text =
//                                "- ${it.reduceDollar?.priceFormatTwoDigitZero2()}"
//                            llDiscountAmount.isVisible = true
//                            tvDiscountAmountTitle.text = getString(R.string.amount_of_reduction_usd)
//                        }
//                    }


                    //normal
                    llActualReceiveAmount.isGone = true

                    llPartialRefundAmount.isGone = true
                    llPartialRefundPackFee.isGone = true
                    llPartialRefundServiceFee.isGone = true
                    llVatRefundAmount.isGone = true

                    llTotalPrice2.isGone = true

                    llTotalPrice.isVisible = true
                    llRefundAmount.isGone = true

                    when (it.payStatus) {
                        OrderedStatusEnum.UNPAID.id -> {
                            //un paid
                            showTobeProcess(ordered)
                        }

                        OrderedStatusEnum.PAID.id -> {
                            //paid

                            tvVipPrice.isVisible = false
                        }

                        OrderedStatusEnum.PARTIAL_REFUND.id -> {
                            //partial refund
                            llTotalPrice.isGone = true
                            llTotalPrice2.isVisible = true

                            tvVipPrice.isVisible = false


                            llActualReceiveAmount.isVisible = true
                            tvActualReceiveAmount.text = FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                (totalPrice - (it.refundPrice
                                    ?: 0)), it.isKhr()
                            )

                            val vatRefundAmount = it.getVatRefundAmount()
                            llVatRefundAmount.isVisible = vatRefundAmount > 0
                            tvVatRefundAmount.text =
                                "-${vatRefundAmount.priceFormatTwoDigitZero2()}"

                            val serviceFeeRefundAmount = it.getServiceFeeRefundAmount()
                            llPartialRefundServiceFee.isVisible = serviceFeeRefundAmount > 0
                            tvPartialRefundServiceFee.text =
                                "-${serviceFeeRefundAmount.priceFormatTwoDigitZero2()}"

                            val packRefundAmount = it.getPackRefundAmount()
                            llPartialRefundPackFee.isVisible = packRefundAmount > 0
                            tvPartialRefundPackFee.text =
                                "-${packRefundAmount.priceFormatTwoDigitZero2()}"

                            llPartialRefundAmount.isVisible = true
                            tvPartialRefundAmount.text =
                                "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                                        it.getPartialRefundAmount(), it.isKhr()
                                    )
                                }"

                        }

                        OrderedStatusEnum.FULL_REFUND.id -> {
                            llTotalPrice2.isVisible = true
                            llTotalPrice.isGone = true

                            //refund
                            llRefundAmount.isVisible = true
                            tvRefundAmount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.refundPrice, it.isKhr()
                                )
                            }"
                            tvVipPrice.isVisible = false
                            if (it.isTakeOut() && it.isKhr()) {
                                tvTotalKhrPrice2.isVisible = false
                            } else {
                                tvTotalKhrPrice2.isVisible = true
                            }
                        }

                        OrderedStatusEnum.CANCEL_ORDER.id -> {

                            tvVipPrice.isVisible = false

                            showTobeProcess(ordered)


                        }

                        OrderedStatusEnum.BE_CONFIRM.id -> {
                            //confirm
                            showTobeProcess(ordered)

                        }

                        OrderedStatusEnum.PREORDER.id -> {
                            tvVipPrice.isVisible = false
                        }

                        OrderedStatusEnum.TO_BE_CONFIRM.id -> {
                            //待确认没支付功能
                            showTobeProcess(ordered)
                        }

                        else -> {}
                    }
                }
            }
        }
    }

    /**
     * 待定价 商品
     *
     * @param ordered
     * @return
     */
    private fun showTobeProcess(ordered: OrderedInfoResponse?): Boolean {
        //如果有称重的，且已经全部称重
        val isHasNeedProcess = ordered?.isHasNeedProcess() ?: false
        ordered?.let {
            binding?.apply {
                var unProcessNum = 0
                var unProcessServiceWhiteGoods = 0

                if (it.needShowUnProcessState()) {
                    it.goods?.forEach { good ->
                        if (!good.isHasProcessed()) {
                            unProcessNum += 1
                            if (good.serviceChargeWhitelisting == true) {
                                unProcessServiceWhiteGoods += 1
                            }
                        }
                    }
                }

                if (isHasNeedProcess) {
                    tvSubtotal.text = mContext.getString(R.string.to_be_confirmed)
                    tvTotalPrice.text = mContext.getString(R.string.to_be_confirmed)

                    tvDiscount.text = mContext.getString(R.string.to_be_confirmed)
                    tvDiscountAmount.text = mContext.getString(R.string.to_be_confirmed)

                    tvVat.text = mContext.getString(R.string.to_be_confirmed)
                    llVat.isVisible =
                        (ordered.price?.vatPercentage ?: BigDecimal(0)) > BigDecimal(0)
                    tvTotalKhrPrice.isVisible = false
                    tvVipPrice.isVisible = false

                    if (!ordered.isTakeAway()) {
                        if (unProcessNum > unProcessServiceWhiteGoods && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)) {
                            tvServiceFee.text = mContext.getString(R.string.to_be_confirmed)
                            llServiceFee.isVisible = true
                        }
                    }

                }
            }
        }
        return isHasNeedProcess
    }


    //-------------------------------订单 副屏 结束---------------------------------------------------//


    //-------------------------------QRPayment 副屏 开始----------------------------------------------------//

    private fun LayoutSecondaryScreenBinding.initQRPaymentResource() {
        tvInfo.text = mContext.getString(R.string.scan_to_pay)
        tvTotalCoast.text = mContext.getString(R.string.total_cost)
//        tvDurationTitle.text = mContext.getString(R.string.expired_in)
        tableName.text = mContext.getString(R.string.table_name)
        numberProducts.text = mContext.getString(R.string.number_products)
        qrSubtotal.text = mContext.getString(R.string.subtotal)
        qrService.text = mContext.getString(R.string.service_fee)
        qrVat.text = mContext.getString(R.string.vat)
//        discount.text = mContext.getString(R.string.discount_reduction)
        tvPaymentCouponTitle.text = mContext.getString(R.string.coupon)
        receivable.text = mContext.getString(R.string.total_price)
        paymentResult.text = mContext.getString(R.string.payment_successfully)
        packageFee.text = mContext.getString(R.string.packing_price)
        tvPaymentDiscountTitle.text = mContext.getString(R.string.discounts2)

        paymentTopUpResult.text = mContext.getString(R.string.top_up_successfully)
        tvTotalTopUpCoast.text = mContext.getString(R.string.total_cost)

        tvAcceptedPayment.text = mContext.getString(R.string.accepted_payment)
    }

    private fun hideQROther() {
        binding?.run {
            clQRPayment.isVisible = true
            clOtherLayout.isVisible = false
            clMenOrder.isVisible = false
            clOrderedInfo.isVisible = false
//            clPaymentInfoLayout.isVisible = false
            initQRPaymentResource()
        }
    }

    fun updateTime(time: SpannableStringBuilder) {
        binding?.tvDuration?.text = time
    }

    fun showQRPayment(
        qrData: String,
        amount: BigDecimal,
    ) {
        binding?.run {
            qrData?.let {
                hideQROther()
                clPaymentLayout.isVisible = true
                clPaymentResultLayout.isVisible = false
                clTopup.isVisible = false
                clPaymentTopUpResultLayout.isVisible = false
//                val decode = BakongKHQR.decode(qrcode ?: "")
//                val amountStr = "$${decode.data.transactionAmount}"
                imgQR.setImageDrawable(
                    QrCodeDrawable(
                        QrData.Url(qrData ?: ""),
                        PaymentQrDialog.getQROption(R.mipmap.icon_qr_bakong, mContext),
                        Charset.forName("UTF-8")
                    )
                )
                var amountStr = amount.halfUp(2).priceFormatTwoDigitZero2()
//                try {
//                    val convertAmountDecimal =
//                        decode.data.transactionAmount.toDouble().decimalFormatTwoDigitZero()
//                    amountStr = "$${convertAmountDecimal}"
//                } catch (e: Exception) {
//                    amountStr = "$${decode.data.transactionAmount}"
//                }

                tvScanQRAmount.text = amountStr
                tvTotalCoastAmount.text = amountStr
                tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
            }

        }
    }

    @SuppressLint("SetTextI18n")
    fun showPaymentResult(orderedInfo: OrderedInfoResponse?) {
        mContext.run {
            binding?.run {
                orderedInfo?.let { orderedInfo ->
                    hideQROther()
                    clPaymentLayout.isVisible = false
                    clPaymentResultLayout.isVisible = true
                    tvTableName.text = orderedInfo.tableName
                    tvFoodCount.text = "${orderedInfo.getTotalGoodsNum()}"
                    tvQrSubtotal.text = orderedInfo.getSubTotal().priceFormatTwoDigitZero2()
                    tvQrService.text =
                        orderedInfo.getRealServiceFeePrice().priceFormatTwoDigitZero2()
                    llQrService.isVisible = orderedInfo.getRealServiceFeePrice() > 0L

                    //=======优惠活动金额================
                    if (orderedInfo.couponActivityList.isNullOrEmpty()) {
                        llPaymentResultCouponActivity.isVisible = false
                    } else {
                        if (orderedInfo.couponActivityList?.size == 1) {
                            couponActivity.text =
                                orderedInfo.couponActivityList?.firstOrNull()?.activityLabelName
                        } else {
                            couponActivity.text = mContext.getString(R.string.discount_activity)
                        }
                        tvCouponActivityPrice.text = "-${
                            orderedInfo.getTotalCouponActivityAmount()
                                ?.priceFormatTwoDigitZero2()
                        }"
                        llDiscountActivity.isVisible = true
                    }
                    //==============================


                    tvQrVat.text =
                        "${orderedInfo?.getRealVatPrice()?.priceFormatTwoDigitZero2()}"
                    qrVat.text =
                        "${mContext.getString(R.string.vat)}(${orderedInfo?.price?.getVatPercentage()}%)"
                    llQrVat.isVisible = (orderedInfo?.getRealVatPrice() ?: 0) > 0L

                    tvPackageFee.text =
                        "${orderedInfo.getPackFee().priceFormatTwoDigitZero2()}"
                    llPaymentResultPackageFee.isVisible = orderedInfo.getPackFee() > 0

                    llPaymentCoupon.isVisible = orderedInfo.getCurrentCoupon() != null
                    tvPaymentCoupon.text = orderedInfo.getCouponDesc(mContext, false, false)

                    llPaymentDiscount.isVisible = false
                    llPaymentDiscountAmount.isVisible = false
                    if (orderedInfo.isSetWholeDiscount()) {
                        if (orderedInfo.discountReduceActivity != null) {
                            if (orderedInfo.discountReduceActivity?.isPercentDiscount() == true) {
                                llPaymentDiscount.isVisible = true
                                tvPaymentDiscount.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        orderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        orderedInfo.getWholePercentDiscountAmount()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        orderedInfo.isKhr()
                                    )
                                }"
                                tvPaymentDiscountTitle.text =
                                    "${getString(R.string.discounts2)}(${orderedInfo.getWholePercentDiscountStr()})"
                            } else if (orderedInfo.discountReduceActivity?.isFixedAmount() == true) {
                                //设置的是整单减免
                                llPaymentDiscountAmount.isVisible = true
                                val title = "${getString(R.string.discounts2)}"
                                val content = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        orderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        orderedInfo.getNormalWholeReduceDollar()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        orderedInfo.isKhr()
                                    )
                                }"
                                tvPaymentDiscountAmount.text = content
                                tvPaymentDiscountAmountTitle.text = title
                            }
                        } else {
                            if (orderedInfo.getWholePercentDiscount() > BigDecimal.ZERO) {
                                //设置的是整单百分比折扣
                                llPaymentDiscount.isVisible = true
                                tvPaymentDiscount.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        orderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        orderedInfo.getWholePercentDiscountAmount()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        orderedInfo.wholeDiscountReduce?.isCustomizeKhr() == true
                                    )
                                }"
                                tvPaymentDiscountTitle.text =
                                    "${getString(R.string.discounts2)}(${orderedInfo.getWholePercentDiscountStr()})"
                            }

                            if (orderedInfo.isSetCustomizeReduce()) {
                                //设置的是整单减免
                                llPaymentDiscountAmount.isVisible = true
                                var content = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        orderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        orderedInfo.getNormalWholeItemReduceDollar()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        orderedInfo.wholeDiscountReduce?.isCustomizeKhr() == true
                                    )
                                }"
                                if (orderedInfo.getNormalWholeReduceKhr() > BigDecimal.ZERO || orderedInfo.getVipWholeReduceKhr() > BigDecimal.ZERO) {
                                    content =
                                        "-៛${
                                            orderedInfo.getNormalWholeItemReduceKhr()
                                                .decimalFormatZeroDigit()
                                        }"
                                }
                                tvPaymentDiscountAmount.text = content
                                tvPaymentDiscountAmountTitle.text = getString(R.string.discounts2)
                                if (orderedInfo.isAfterPayStatus()) {
                                    //如果支付完了以后 有设置值才显示
                                    if (orderedInfo.getNormalWholeReduceDollar() == BigDecimal.ZERO && orderedInfo.getNormalWholeReduceKhr() == BigDecimal.ZERO) {
                                        llDiscountAmount.isVisible = false
                                    }
                                }
                            }
                        }
                    }
//                    if (orderedInfo.isSetWholeDiscount()) {
//                        //如果是整单百分比减免
//                        if (orderedInfo.isSetWholePercentDiscount()) {
//                            llPaymentDiscount.isVisible = true
//                            tvPaymentDiscount.text =
//                                "-${
//                                    orderedInfo.getWholePercentDiscountAmount()
//                                        ?.priceFormatTwoDigitZero2()
//                                }"
//                            tvPaymentDiscountTitle.text =
//                                "${getString(R.string.discounts2)}(${orderedInfo.getWholePercentDiscountStr()})"
//                        } else {
//                            //如果是整单固定金额减免
//                            llPaymentDiscountAmount.isVisible =
//                                orderedInfo.isSetWholeReduce() || orderedInfo.isSetVipWholeReduce()
//                            tvPaymentDiscountAmount.text =
//                                "-${
//                                    orderedInfo.getNormalWholeDiscountDollar()
//                                        ?.priceFormatTwoDigitZero2()
//                                }"
//                            tvPaymentDiscountAmountTitle.text = getString(R.string.discounts2)
//                        }
//                    } else {
//                        if (orderedInfo.reduceAmount != null && orderedInfo.reduceAmount != 0L || ((orderedInfo.reduceRate
//                                ?: 0.0) > 0.0)
//                        ) {
//                            llPaymentDiscount.isVisible = true
//                            tvPaymentDiscount.text =
//                                "-${orderedInfo.reduceAmount?.priceFormatTwoDigitZero2()}"
//
//                            tvPaymentDiscountTitle.text =
//                                "${getString(R.string.discounts)}(${orderedInfo?.getReduceRateFomat()})"
//
//                        } else {
//                            llPaymentDiscount.isVisible = false
//                        }
//
//                        if ((orderedInfo.reduceKhr == null || orderedInfo.reduceKhr == 0L) && (orderedInfo.reduceDollar == null || orderedInfo.reduceDollar == 0L)) {
//                            llPaymentDiscountAmount.isVisible = false
//                        } else if (orderedInfo.reduceKhr != null && orderedInfo.reduceDollar == null) {
//                            tvPaymentDiscountAmount.text =
//                                "- ៛${(orderedInfo.reduceKhr ?: 0).decimalFormatZeroDigit()}"
//                            llPaymentDiscountAmount.isVisible = true
//                            tvPaymentDiscountAmountTitle.text =
//                                "${getString(R.string.amount_of_reduction_khr)}"
//                        } else if (orderedInfo.reduceKhr == null && orderedInfo.reduceDollar != null) {
//                            tvPaymentDiscountAmount.text =
//                                "- ${orderedInfo.reduceDollar?.priceFormatTwoDigitZero2()}"
//                            llPaymentDiscountAmount.isVisible = true
//                            tvPaymentDiscountAmountTitle.text =
//                                getString(R.string.amount_of_reduction_usd)
//                        }
//                    }

                    tvReceivable.text =
                        orderedInfo.getRealPayPrice()?.priceFormatTwoDigitZero2()
                    tvKhrReceivable.text =
                        "៛${
                            FoundationHelper.usdConverToKhr(
                                orderedInfo.conversionRatio ?: FoundationHelper.conversionRatio!!,
                                orderedInfo.getRealPayPrice()
                            ).decimalFormatZeroDigit()
                        }"
                    tvQrTotalPrice.text =
                        orderedInfo.getRealPayPrice()?.priceFormatTwoDigitZero()

                }
            }
        }
    }


    fun showTopUpQRPayment(paymentResponse: RechargePaymentResponse?) {
        Timber.e("副屏 展示充值二维码")
        binding?.run {
            paymentResponse?.let {
                hideQROther()
                tvInfo.text = mContext.getString(R.string.scan_to_topup)
                clPaymentLayout.isVisible = true
                clPaymentResultLayout.isVisible = false
                clPaymentTopUpResultLayout.isVisible = false
                val decode = BakongKHQR.decode(it.qrCode ?: "")
//                val amountStr = "$${decode.data.transactionAmount}"
                imgQR.setImageDrawable(
                    QrCodeDrawable(
                        QrData.Url(it.qrCode ?: ""),
                        PaymentQrDialog.getQROption(R.mipmap.icon_qr_bakong, mContext),
                        Charset.forName("UTF-8")
                    )
                )
                var amountStr: String
                try {
                    val convertAmountDecimal =
                        decode.data.transactionAmount.toDouble().decimalFormatTwoDigitZero()
                    amountStr = "$${convertAmountDecimal}"
                } catch (e: Exception) {
                    amountStr = "$${decode.data.transactionAmount}"
                }

                tvScanQRAmount.text = amountStr
                tvTotalCoastAmount.text = amountStr
                tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
            }
        }
    }

    fun showPaymentTopUpResult(paymentResponse: RechargePaymentResponse?) {
        mContext.run {
            binding?.run {
                paymentResponse?.let {
                    hideQROther()
                    clPaymentLayout.isVisible = false
                    clPaymentResultLayout.isVisible = false
                    clPaymentTopUpResultLayout.isVisible = true
                    var amountStr: String
                    val decode = BakongKHQR.decode(it.qrCode ?: "")
                    try {
                        val convertAmountDecimal =
                            decode.data.transactionAmount.toDouble().decimalFormatTwoDigitZero()
                        amountStr = "$convertAmountDecimal"
                    } catch (e: Exception) {
                        amountStr = "${decode.data.transactionAmount}"
                    }
                    tvQrTotalTopUpPrice.text = amountStr
                }
            }
        }
    }


    fun showTopupBalance() {
        binding?.apply {
            clOtherLayout.isVisible = false
            clMenOrder.isVisible = false
            clOrderedInfo.isVisible = false
            clQRPayment.isVisible = false
            clTopup.isVisible = true
//            clPaymentInfoLayout.isVisible = false
        }
    }

    fun initTopupBalance(memberInfo: CustomerMemberResponse?) {
        binding?.run {
            clTopup.isVisible = true
            tvNickname.text = memberInfo?.nickName
            tvPhoneNumber.text = memberInfo?.telephone
            tvBalance.text = "${memberInfo?.balance?.priceFormatTwoDigitZero2()}"
            tvTopupTips.text = mContext.getString(R.string.payable_amount)

            tvNameLabel.text = mContext.getString(R.string.customer_nickname)
            tvPhoneLabel.text = mContext.getString(R.string.customer_account)
            tvBalanceLabel.text = mContext.getString(R.string.customer_balance)
            tvTopupAmountLabel.text = mContext.getString(R.string.top_up_amount)
            tvAddGiftAmountLabel.text = mContext.getString(R.string.additional_gift_amount)
            tvExtraCouponGiveawayLabel.text = mContext.getString(R.string.extra_coupon_giveaway)
            tvTotalRechargeAmountLabel.text = mContext.getString(R.string.total_recharge_amount)
            tvCouponLabel.text = mContext.getString(R.string.coupon)
        }
    }

    fun updateTopupBalance(rechargeData: MemberMainViewModel.RechargeData) {
        binding?.run {
            //当前充值等级
            val rechargeTier = rechargeData.rechargeTier
            //当前优惠券信息
            val currentCouponInfo = rechargeData.currentCouponInfo
            if (rechargeTier == null) {
                tvTopupAmount.text = "$--"
                groupAddGiftAmount.isVisible = false
                groupExtraCouponGiveaway.isVisible = false
                groupTotalRechargeAmount.isVisible = false
                tvPayableAmount.text = "$--"
            } else {
                tvTopupAmount.text = rechargeTier.amount?.priceFormatTwoDigitZero1() ?: "$--"
                //额外赠送金额
                tvAddGiftAmount.text = rechargeTier.giftAmount?.priceFormatTwoDigitZero1()
                groupAddGiftAmount.isVisible =
                    rechargeTier.giftAmount != null && !rechargeTier.giftAmount!!.isZero()
                //额外赠送的优惠卷
                val totalCouponNum =
                    rechargeTier.rechargeTierCouponTemplateList?.sumOf { it.num ?: 0 } ?: 0
                tvExtraCouponGiveaway.text =
                    mContext.getString(R.string.unit_coupon, totalCouponNum.toString())
                groupExtraCouponGiveaway.isVisible = totalCouponNum > 0
                //总充值金额
                val giftAmount = rechargeTier.giftAmount ?: BigDecimal.ZERO
                groupTotalRechargeAmount.isVisible = !giftAmount.isZero()
                val total = rechargeTier.amount?.add(giftAmount) ?: BigDecimal.ZERO
                tvTotalRechargeAmount.text = total.priceFormatTwoDigitZero1()
                //应支付金额
                tvPayableAmount.text = (rechargeTier.amount?.subtract(
                    BigDecimal(currentCouponInfo?.couponPrice ?: 0)
                ))?.priceFormatTwoDigitZero1() ?: "--"
            }
            groupCouponAmount.isVisible = currentCouponInfo != null
            tvCouponAmount.text = CouponHelper.getCouponDesc(
                mContext,
                rechargeData.couponList.isNullOrEmpty(),
                currentCouponInfo,
                rechargeTier?.amount?.divide(BigDecimal(100))?.halfUp(2) ?: BigDecimal.ZERO,
                true
            )
        }
    }

    fun updateTopupSuccess() {
        binding?.run {
            tvTopupTips.text = mContext.getString(R.string.top_up_successfully)
        }
    }

    fun hideTopupBalance() {
        binding?.run {
            clTopup.isVisible = false
        }
    }

    //-------------------------------QRPayment 副屏 结束----------------------------------------------------//

    fun onDestroy() {
        binding?.run {
            clQRPayment.isVisible = false
            clOtherLayout.isVisible = false
            clMenOrder.isVisible = false
            clOrderedInfo.isVisible = false
        }
    }

}