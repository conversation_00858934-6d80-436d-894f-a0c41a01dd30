package com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest


import com.google.gson.annotations.SerializedName

data class SwitchTableRequest(
    @SerializedName("batchAddCartList")
    val batchAddCartList: ArrayList<BatchAddCart>?,
    @SerializedName("newTableUuid")
    val newTableUuid: String?,
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("oldTableUuid")
    val oldTableUuid: String?
)