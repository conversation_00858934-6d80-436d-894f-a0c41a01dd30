package com.metathought.food_order.casheir.data.model.base.response_model.customer


import com.google.gson.annotations.SerializedName

data class CustomerMemberResponse(
    @SerializedName("accountId") val accountId: String? = null,
    @SerializedName("balance") val balance: Long? = null,
    @SerializedName("nickName") val nickName: String? = null,
    @SerializedName("paymentMethod") val paymentMethod: String? = null,
    @SerializedName("telephone") val telephone: String? = null,
    @SerializedName("memberNumber") val memberNumber: String? = null,
    @SerializedName("photoUrl") val photoUrl: String? = null
)