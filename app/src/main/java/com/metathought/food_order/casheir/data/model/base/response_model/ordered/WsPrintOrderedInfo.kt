package com.metathought.food_order.casheir.data.model.base.response_model.ordered


import com.google.gson.annotations.SerializedName

data class WsPrintOrderedInfo(
    @SerializedName("isReprint")
    val isReprint: Boolean?,
    @SerializedName("ticketTypes")
    val ticketTypes: List<Int>?,
    @SerializedName("orderInfo")
    val orderInfo: OrderedInfoResponse?,
    /**
     * 厨房小票: 1-整单打印; 2-最新加购
     */
    @SerializedName("kitchenReceiptType")
    val kitchenReceiptType: Int?,
)