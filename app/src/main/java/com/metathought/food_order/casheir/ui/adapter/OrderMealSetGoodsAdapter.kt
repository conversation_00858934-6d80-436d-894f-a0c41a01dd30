package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.databinding.FoodSubTableItemBinding
import com.metathought.food_order.casheir.helper.OrderHelper

class OrderMealSetGoodsAdapter(val mealSetGoods: List<OrderMealSetGood>) :
    RecyclerView.Adapter<OrderMealSetGoodsAdapter.OrderMealSetGoodsViewHolder>() {

    inner class OrderMealSetGoodsViewHolder(val binding: FoodSubTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: OrderMealSetGood?) {
            resource?.let { it1 ->
                binding.apply {
                    tvGoodsName.text = OrderHelper.getMealSetGoodStr(it1)
                    tvGoodsCount.text = "x${it1.number ?: 0}"
                }
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderMealSetGoodsViewHolder {
        return OrderMealSetGoodsViewHolder(
            FoodSubTableItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        return mealSetGoods.count()
    }

    override fun onBindViewHolder(holder: OrderMealSetGoodsViewHolder, position: Int) {
        holder.bind(mealSetGoods[position])
        holder.itemView.setOnTouchListener { _, _ -> false }
    }

}
