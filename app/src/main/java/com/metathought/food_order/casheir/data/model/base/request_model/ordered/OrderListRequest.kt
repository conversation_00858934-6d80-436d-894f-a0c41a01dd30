package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName

data class OrderListRequest(
    @SerializedName("keyword")
    val keyword: String? = null,
    @SerializedName("page")
    val page: Int,
    @SerializedName("pageSize")
    val pageSize: Int = 50,
    @SerializedName("payStatus")
    var payStatus: Int? = null,
    @SerializedName("isKioskOrder")
    var isKioskOrder: Boolean? = null,
    @SerializedName("tableUuids")
    var tableUuids: List<String>? = null,
    @SerializedName("status")
    var status: Int? = null,
    /**
     * 1:未读; 2:未打印
     */
    @SerializedName("type")
    var type: Int? = null,

    @SerializedName("consumerId")
    var consumerId: String? = null,

    @SerializedName("startTime")
    var  startTime:String?=null,
    @SerializedName("endTime")
    var  endTime:String?=null,
    @SerializedName("isGrab")
    var isGrab:Boolean?=null,
    @SerializedName("grabStatuss")
    var grabStatuss:List<String>? = null
)