package com.metathought.food_order.casheir.data.model.base.request_model.work_handover

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize


data class ShiftReportPrint(
    var startTime: String? = null, // 开始时间
    var endTime: String? = null, // 结束时间
    var exportTime: String? = null, // 导出时间
    var employeeName: String? = null, // 员工
    var printTime: String? = null, // 打印时间
    var saleItemData: SalesItemOrdersReportVo? = null, // 商品报表数据
    var payMethodData: SalesPayMethodReportVo? = null, // 支付方式报表数据
    var openingCashUsd: String? = null, //备用金(USD)
    var openingCashKhr: String? = null,    //备用金(KHR)
    var amountPaidUsd: String? = null, //支出金额(USD)
    var amountPaidKhr: String? = null,  //支出金额(KHR)
    var onlinePaidPrice: String? = null,    //本次登录时间内在线支付金额
    var offlinePaidPrice: String? = null,   //本次登录时间内线下支付金额
    var cashPaidPrice: String? = null,  //本次登录时间内现金支付金额
    var balancePaidPrice: String? = null, //本次登录时间内余额支付UDS金额
    var creditPaidPrice: String? = null, //本次登录时间内挂账支付UDS金额
    val otherPaidPrice: Double?, //本次登录时间内其他支付金额
    var balance: String? = null,    //当班余额
    var remark: String? = null, //备注
    var offlinePayMethodData: ArrayList<OfflinePayMethodData>? = null,  //线下支付数据
    var discrepancyPrice: String? = null, //相差金额
    val usdAmount: String?, //交接金额-美元(单位:分)
    val khrAmount: String?, //交接金额-瑞尔(单位:分)

    var subTotal: String? = null, //小计
    var discountAmount: String? = null, //折扣金额
    var grandTotal: String? = null, //总价
)

@Parcelize
data class OfflinePayMethodData(
    var payMethod: String? = null,
    var payMethodImgUrl: String? = null,
    var amount: String? = null,
    var orderNum: Int? = null,
    var amountRatio: String? = null
) : Parcelable

data class SalesPayMethodReportVo(
    var startTime: String? = null, // 开始时间
    var endTime: String? = null, // 结束时间
    var exportTime: String? = null, // 导出时间
    var totalOnlineAmount: String? = null, // 在线支付总金额
    var totalOfflineAmount: String? = null, // 线下支付总金额
    var totalCashAmount: String? = null, // 现金支付总金额
    var totalBalanceAmount: String? = null, // 余额支付总金额
    var salesPayMethodReportDetails: List<SalesPayMethodReportDetail>? = null // 支付方式明细Vo
)

data class SalesPayMethodReportDetail(
    var payMethod: String? = null, // 支付方式
    var payMethodImgUrl: String? = null, // 支付方式图片地址
    var amount: String? = null, // 支付金额
    var orderNum: Int? = null, // 订单数量
    var amountRatio: String? = null //  金额占比
)

data class SalesItemOrdersReportVo(
    var startTime: String? = null, // 开始时间
    var endTime: String? = null, // 结束时间
    var exportTime: String? = null, // 导出时间
    var totalAmount: String? = null, // 总金额(含附加税)
    var totalReceiveAmount: String? = null, // 应收金额
    var totalVat: String? = null, // vat总额
    var salesItemOrdersDetailList: List<SalesItemOrdersDetailVo>? = null, // 销售商品订单明细Vo
    var totalItemNum: Int? = null, // 商品数量(总)
    var totalOrderNum: Int? = null,
    val totalDiscountAmount: String?,
    val totalServiceChargeAmount: String?,
    val totalPackingFee: String?,
    val totalCommission: String?,
)

data class SalesItemOrdersDetailVo(
    var index: Int? = null, // 序号
    val goodId: Long?,// 商品id
    var goodType: Int? = 0, //商品类型, 0-普通商品；1-临时商品
    val orderId: Long?, //订单id
    val itemNo: Int?,   //商品编号
    val itemGroupName: String?, //商品所属分组名称
    val itemName: String?,  // 商品名称
    val itemTotalPrice: String?,    //商品总价
    val packingFee: String?,    //打包费
    val serviceCharge: String?, // 堂食服务费
    val vat: String?,   //vat
    val itemNum: Int?,  //商品数量
    val orderNum: Int?, //订单数量
    val amountRatio: String?    //金额占比
)