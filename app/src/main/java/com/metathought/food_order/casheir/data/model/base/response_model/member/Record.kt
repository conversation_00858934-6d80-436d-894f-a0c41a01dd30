package com.metathought.food_order.casheir.data.model.base.response_model.member


import com.google.gson.annotations.SerializedName

data class Record(
    @SerializedName("balance")
    val balance: Long?,
    @SerializedName("consumeRecord")
    val consumeRecord: ConsumeRecord?,
    @SerializedName("consumerPayAccountId")
    val consumerPayAccountId: String?,
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("lastLoginTime")
    val lastLoginTime: String?,
    @SerializedName("lastRechargeTime")
    val lastRechargeTime: String?,
    @SerializedName("nickName")
    val nickName: String?,
    @SerializedName("paymentMethod")
    val paymentMethod: String?,
    @SerializedName("photoUrl")
    val photoUrl: String?,
    @SerializedName("rechargeRecord")
    val rechargeRecord: List<RechargeRecord?>?,
    @SerializedName("storeId")
    val storeId: Any?,
    @SerializedName("telephone")
    val telephone: String?,
    @SerializedName("type")
    val type: Int?,
    //充值次数
    @SerializedName("rechargeMembersNum")
    val rechargeMembersNum: Int?,
    //消费次数
    @SerializedName("consumerMembersNum")
    val consumerMembersNum: Int?,
    //会员号
    @SerializedName("memberNumber") val memberNumber: String?
)