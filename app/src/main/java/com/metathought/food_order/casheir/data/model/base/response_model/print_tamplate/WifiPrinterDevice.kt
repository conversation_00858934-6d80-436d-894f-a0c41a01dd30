package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate

import android.hardware.usb.UsbDevice
import net.posprinter.IDeviceConnection
import net.posprinter.POSPrinter
import net.posprinter.TSPLPrinter
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference


/**
 *<AUTHOR>
 *@time  2024/11/14
 *@desc  usb打印配置
 **/

class WifiPrinterDevice {
    /**
     *USB Device
     */
    var ipAddress: String? = null

    /**
     *USB Connection
     */
    var iDeviceConnection = AtomicReference<IDeviceConnection?>(null)

    /**
     *wifi 小票打印
     */
    val pOSPrinter = AtomicReference<POSPrinter?>(null)

    /**
     *wifi 标签打印
     */
    var tSPLPrinter: TSPLPrinter? = null


    // 使用AtomicBoolean确保isConnect的原子性操作
    val isConnect = AtomicBoolean(false)

    val isConnecting = AtomicBoolean(false)  // 新增：标记是否正在连接中
}