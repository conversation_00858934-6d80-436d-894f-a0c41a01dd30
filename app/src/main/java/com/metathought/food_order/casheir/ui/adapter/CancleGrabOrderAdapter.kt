package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.CancleReason
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.AcceptOrderedItemBinding
import com.metathought.food_order.casheir.databinding.CancelGrabReasonItemBinding
import com.metathought.food_order.casheir.databinding.OrderedItemBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.formatShortDate
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TimeUtils
import dagger.multibindings.ElementsIntoSet
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class CancleGrabOrderAdapter(
    val act: Context,
    var list: List<CancleReason>,
    var select: Int = 0,
    val onItemClickListener: (CancleReason) -> Unit
) : RecyclerView.Adapter<CancleGrabOrderAdapter.OrderedViewHolder>() {




    inner class OrderedViewHolder(val binding: CancelGrabReasonItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {

                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        select=bindingAdapterPosition
                        onItemClickListener.invoke(list.get(it))
                        notifyDataSetChanged()
                    }
                }
            }
        }

        fun bind(resource: CancleReason, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        if (select == position)
                            radio1.isChecked = true;
                        else
                            radio1.isChecked = false
                        radio1.text = resource.reason
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedViewHolder {
        val itemView =
            CancelGrabReasonItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: OrderedViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


}