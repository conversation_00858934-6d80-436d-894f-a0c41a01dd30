package com.metathought.food_order.casheir.network.service

import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.POST

interface TableService {

    @POST("cash-register/login")
    suspend fun postLogin(@Body body: RequestBody): BaseResponse<UserLoginResponse>

}