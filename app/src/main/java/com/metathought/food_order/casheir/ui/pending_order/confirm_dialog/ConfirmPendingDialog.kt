package com.metathought.food_order.casheir.ui.pending_order.confirm_dialog

import android.os.Bundle

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogConfirmPendingBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class ConfirmPendingDialog : BaseDialogFragment() {
    private var binding: DialogConfirmPendingBinding? = null
    private var callBackPayClick: ((String, String) -> Unit)? = null
    private var localDiningStyleEnum: Int? = 0
    private val viewModel: ConfirmPendingViewModel by viewModels()

    private val maxRemarkLength = 100

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogConfirmPendingBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        initData()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) {
            it.result?.let { result ->
                when (result) {
                    is ApiResponse.Loading -> {
                        binding?.apply {
                            progressBar.isVisible = true
                            btnPending.setEnableWithAlpha(false)
                        }
                    }

                    is ApiResponse.Error -> {
                        binding?.apply {
                            progressBar.isVisible = false
                            btnPending.setEnableWithAlpha(false)
                        }
                        if (result.message?.isNotEmpty() == true) {
                            Toast.makeText(
                                context,
                                result.message,
                                Toast.LENGTH_SHORT
                            ).show()
                        } else ""
                    }

                    is ApiResponse.Success -> {
                        binding?.apply {

                            progressBar.isVisible = false
                            btnPending.setEnableWithAlpha(true)
                            tvSeriesNumber.text = result.data.serialNumber
                        }
                    }
                }
            }

            it.shoppingRecord?.let {
                binding?.apply {
                    tvItemsCount.text = "${it.getGoodsCount()}"

                    val totalPrice = arguments?.getLong(TOTAL_PRICE, 0L)
                    tvTotal.text = totalPrice?.priceFormatTwoDigitZero2()

                    if (it.isHasNeedProcess()) {
                        tvTotal.text = getString(R.string.to_be_confirmed)
                    }
                }
            }
        }
    }


    private fun initData() {

        arguments?.let {
            localDiningStyleEnum = it.getInt(CONTENT, 0)
            viewModel.getData(localDiningStyleEnum!!)
            viewModel.getSerialNumber()
        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            btnPending.setOnClickListener {
                if (tvSeriesNumber.text.toString().isEmpty()) {
                    return@setOnClickListener
                }
                val remark =
                    if (edtRemark.text.toString().trim().length > maxRemarkLength) edtRemark.text.toString()
                        .trim().subSequence(0, edtRemark.text.toString().trim().length - 1)
                        .toString() else edtRemark.text.toString().trim()
                callBackPayClick?.invoke(tvSeriesNumber.text.toString(), remark)
                dismissCurrentDialog()
            }
            edtRemark.addTextChangedListener {
                if (it?.length.toString().trim().length > maxRemarkLength) {
                    textInputLayoutRemark.error = getString(R.string.maximum_100_characters_allowed)
                    textInputLayoutRemark.isErrorEnabled = true
                } else {
                    textInputLayoutRemark.error = ""
                    textInputLayoutRemark.isErrorEnabled = false
                }
            }
        }
    }


    companion object {
        private const val DIALOG_TAG = "CONFIRM_PENDING_DIALOG"
        private const val CONTENT = "CONTENT"
        private const val TOTAL_PRICE = "TOTAL_PRICE"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85
        fun showDialog(
            fragmentManager: FragmentManager,
            diningStyle: Int? = null,
            totalPrice: Long? = null,
            callBackClickListener: ((String, String) -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(DIALOG_TAG)
            if (fragment != null) return
            fragment = newInstance(diningStyle = diningStyle, totalPrice, callBackClickListener)
            fragment.show(fragmentManager, DIALOG_TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(DIALOG_TAG) as? ConfirmPendingDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            diningStyle: Int? = null,
            totalPrice: Long? = 0,
            callBackClickListener: ((String, String) -> Unit)? = null,
        ): ConfirmPendingDialog {
            val args = Bundle()
            diningStyle?.let {
                args.putInt(CONTENT, it)
                args.putLong(TOTAL_PRICE, totalPrice!!)
            }
            val fragment = ConfirmPendingDialog()
            fragment.arguments = args
            fragment.callBackPayClick = callBackClickListener
            return fragment
        }
    }
}
