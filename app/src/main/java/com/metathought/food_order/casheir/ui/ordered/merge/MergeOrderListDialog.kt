package com.metathought.food_order.casheir.ui.ordered.merge

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.databinding.DialogFilterTableListBinding
import com.metathought.food_order.casheir.databinding.DialogMergeOrderListBinding
import com.metathought.food_order.casheir.databinding.PopupOrderFilterBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.FilterTableAdapter
import com.metathought.food_order.casheir.ui.adapter.FloorAdapter
import com.metathought.food_order.casheir.ui.adapter.MergreOrderedAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.ordered.OrderedFragment
import com.metathought.food_order.casheir.ui.ordered.receive.WaitReceiveOrderGoodsListDialog
import com.metathought.food_order.casheir.ui.ordered.receive.WaitReceiveOrderGoodsListDialog.Companion
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/11/21 15:10
 * @description 并单
 */
@AndroidEntryPoint
class MergeOrderListDialog : BaseDialogFragment() {

    private var binding: DialogMergeOrderListBinding? = null

    private var mergeSuccessListener: ((orderRecord: OrderedRecord?) -> Unit)? = null
    private var mergeErrorListener: ((orderRecord: OrderedRecord?) -> Unit)? = null
    private val viewModel: MergeOrderListViewModel by viewModels()

    private var orderRecord: OrderedRecord? = null

    private var selectedValue = OrderedStatusEnum.All.id
    private var selectedTableItem = ArrayList<OrderedTableListItem>()

    private var filterTableAdapter: FilterTableAdapter? = null
    private var floorAdapter: FloorAdapter? = null
    private var popupViewTable: DialogFilterTableListBinding? = null

    private var tablePopupWindow: PopupWindow? = null

    private var adapter: MergreOrderedAdapter? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMergeOrderListBinding.inflate(layoutInflater)
        return binding?.root
    }

    companion object {
        private const val TAG = "MergeOrderListDialog"


        fun showDialog(
            fragmentManager: FragmentManager,
            orderRecord: OrderedRecord? = null,
            mergeSuccessListener: ((orderRecord: OrderedRecord?) -> Unit),
            mergeErrorListener: ((orderRecord: OrderedRecord?) -> Unit)
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                orderRecord = orderRecord,
                mergeSuccessListener = mergeSuccessListener,
                mergeErrorListener = mergeErrorListener
            )
            fragment.show(fragmentManager, TAG)
        }


        private fun newInstance(
            orderRecord: OrderedRecord? = null,
            mergeSuccessListener: ((orderRecord: OrderedRecord?) -> Unit),
            mergeErrorListener: ((orderRecord: OrderedRecord?) -> Unit)
        ): MergeOrderListDialog {
            return MergeOrderListDialog().apply {
                this.orderRecord = orderRecord
                this.mergeSuccessListener = mergeSuccessListener
                this.mergeErrorListener = mergeErrorListener
            }
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            Timber.e("dismissDialog")
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? MergeOrderListDialog
            fragment?.dismissAllowingStateLoss()
        }

        fun getCurrentMergeOrderListDialog(parentFragmentManager: FragmentManager): MergeOrderListDialog? {
            val fragment =
                parentFragmentManager.findFragmentByTag(TAG) as? MergeOrderListDialog?
            return fragment
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
        initPopuptableView()
    }

    fun getCurrentOrderNo(): String? {
        return orderRecord?.orderNo
    }

    private fun initView() {
        binding?.apply {
            orderRecord?.apply {
                layoutOrder.tvPickUpNo.isVisible = !pickupCode.isNullOrEmpty()
                layoutOrder.tvPickUpNo.text =
                    "${getString(R.string.print_title_pick_up_no)} $pickupCode"

                layoutOrder.tvTableID.text = tableName ?: ""

                val oderIdValue = "${getString(R.string.order_id)}: $orderNo"
                layoutOrder.tvOrderedID.text = oderIdValue

                val itemsValue =
                    "${getString(R.string.items)}: $goodsTotalNum"
                layoutOrder.tvItems.text = itemsValue

                val timeValue =
                    "${getString(R.string.ordering_time)}: ${createTime?.formatDate()}"
                layoutOrder.tvTime.text = timeValue

                layoutOrder.tvPrice.text = getShowPrice(requireContext())

                layoutOrder.tvOrderType.text = getDiningStyleStr(requireContext())


                layoutOrder.tvOrderType.setCompoundDrawablesWithIntrinsicBounds(
                    ContextCompat.getDrawable(
                        requireContext(),
                        getDiningStyleIcon()
                    ), null, null, null
                )

                layoutOrder.tvStatus.text = payStatus?.getPayText(requireContext())
                layoutOrder.tvStatus.setTextColor(
                    ContextCompat.getColor(
                        requireContext(),
                        payStatus?.getPayTypeColor() ?: R.color.ordered_cancel_color
                    )
                )
                layoutOrder.statusCardView.setCardBackgroundColor(
                    ContextCompat.getColor(
                        requireContext(),
                        payStatus?.getPayTypeBackGroundColor()
                            ?: R.color.ordered_cancel_color
                    )
                )
                adapter =
                    MergreOrderedAdapter(requireContext(), ArrayList()) {
                        binding?.apply {
                            val size = adapter?.selectList?.size ?: 0
                            tvMerge.text =
                                if (size > 0) "${getString(R.string.merge_order)}(${size + 1})" else getString(
                                    R.string.merge_order
                                )
                            btnMerge.setEnableWithAlpha(size > 0)
                        }
                    }
                rvList.adapter = adapter
                btnMerge.setEnableWithAlpha(false)
            }
        }

        viewModel.getMergeList(orderRecord)
        viewModel.getOrderedTableList()
    }

    private fun initPopuptableView() {
        initObserverTable()
        popupViewTable = DialogFilterTableListBinding.inflate(layoutInflater)
        tablePopupWindow = PopupWindow(
            popupViewTable?.root,
            (Resources.getSystem().displayMetrics.widthPixels / 2.2).toInt(),
            (Resources.getSystem().displayMetrics.heightPixels / 1.5).toInt(),
            true
        )
        tablePopupWindow?.elevation = 20f
        tablePopupWindow?.animationStyle = R.style.PopupAnimation
        viewModel.getOrderedTableList(tableUUID = arguments?.getString(OrderedFragment.TABLE_UUID))
        var tableList = OrderedTableListResponse()
        popupViewTable?.apply {
            filterTableAdapter =
                context?.let {
                    FilterTableAdapter(
                        tableList,
                        it,
                        selectedTableItem
                    ) {
                        btnConfirm.text =
                            getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
                    }
                }
            recyclerViewTable.adapter = filterTableAdapter
            btnConfirm.text =
                getString(R.string.apply_filter).plus(" (${selectedTableItem.size})")
            btnConfirm.setOnClickListener {
                selectedTableItem = ArrayList(filterTableAdapter?.getSelectedArrayList())
                binding?.apply {
                    cardViewFilterTable.setVisibleGone(selectedTableItem.size > 1)
                    if (selectedTableItem.isEmpty()) {
                        tvTable.text = getString(R.string.all_table)
                    } else {
                        tvTable.text = selectedTableItem.first().name
                        tvCount.text = "+${selectedTableItem.size - 1}"
                    }
                }
                binding?.apply {
                    viewModel.getMergeList(
                        orderRecord,
                        if (selectedValue == OrderedStatusEnum.All.id) null else listOf(
                            selectedValue
                        ),
                        listTable = selectedTableItem
                    )
                }

                tablePopupWindow?.dismiss()
            }

            btnCancel.setOnClickListener {
                filterTableAdapter?.resetSelect()
                btnConfirm.text =
                    getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
            }

        }
    }

    private fun initObserver() {
        viewModel.uiListState.observe(viewLifecycleOwner) {
            it.showLoading?.let {

            }

            it.showSuccess?.let {
                binding?.apply {
                    if (it.mergeAbleOrderList.isNullOrEmpty()) {
                        layoutEmptyDetail.root.isVisible = true
                        layoutEmptyDetail.imgError.setImageResource(R.drawable.ic_empty_box_no_padding)
                        layoutEmptyDetail.tvEmptyText.text = getString(R.string.no_mergeable_order)
                        rvList.isVisible = false
                    } else {
                        layoutEmptyDetail.root.isVisible = false
                        adapter?.replaceData(ArrayList(it.mergeAbleOrderList))
                        rvList.isVisible = true
                    }
                }
            }
        }

        viewModel.uiMergeState.observe(viewLifecycleOwner) {
            if (it is ApiResponse.Success) {
                if (it.data.isSuccess()) {
                    mergeSuccessListener?.invoke(orderRecord)
                    dismissAllowingStateLoss()
                }
            }
            if (it is ApiResponse.Error) {
                if (!it.message.isNullOrEmpty()) {
                    Toast.makeText(context, it.message, Toast.LENGTH_SHORT).show()
                }
                mergeErrorListener?.invoke(orderRecord)
                dismissAllowingStateLoss()
            }
        }
    }

    private fun initObserverTable() {

        viewModel.uiTableState.observe(viewLifecycleOwner) { it ->
            it.response?.let { it ->
                when (it) {
                    is ApiResponse.Loading -> {
//                        showProgress()
                    }

                    is ApiResponse.Success -> {
//                        dismissProgress()
                        popupViewTable?.apply {
                            if (it.data.isEmpty()) {
                                btnConfirm.isEnabled = false
                            }
                            val floorArray: ArrayList<String> = arrayListOf()
                            floorArray.add(getString(R.string.all))
                            it.data.filter { it.name?.uppercase() != "DEFAULT" }.sortedBy { it.id }
                                .forEach { res ->
                                    if (!floorArray.contains(res.location)) {
                                        res.location?.let { it1 -> floorArray.add(it1) }
                                    }
                                }
                            floorAdapter = context?.let { it1 ->
                                FloorAdapter(floorArray, it1, 0) { floor ->
                                    viewModel.filterFloor(
                                        if (floor == 0) "" else floorArray[floor],
                                    )
                                }
                            }
                            recyclerViewPage?.adapter = floorAdapter
                        }
                    }

                    is ApiResponse.Error -> {
//                        dismissProgress()
//                        showToast(it.message ?: "")
                    }

                    else -> {

                    }
                }
            }

            it.orderedTableItem?.let {
                selectedTableItem = arrayListOf(it)
                binding?.apply {
                    if (selectedTableItem.isEmpty()) {
                        tvTable.text = getString(R.string.all_table)
                    } else {
                        tvTable.text = selectedTableItem.first().name
                        tvCount.text = "+${selectedTableItem.size - 1}"
                    }
                }
//                if (arguments?.containsKey(OrderedFragment.PAY_STATUS) == true) {
//                    selectedValue = OrderedStatusMergeEnum.UNPAID_CONFIRM.id
//                    binding?.tvFilter?.text = getString(R.string.unpaid)
//                }
                binding?.apply {
                    viewModel.getMergeList(
                        orderRecord,
                        if (selectedValue == OrderedStatusEnum.All.id) null else listOf(
                            selectedValue
                        ),
                        listTable = selectedTableItem
                    )
                }

            }
        }

        viewModel.filteredTableResponse.observe(viewLifecycleOwner)
        {
            filterTableAdapter?.updateItems(it)
            popupViewTable?.btnConfirm?.text =
                getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
        }
    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            dropdownFilter.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    arrowFilter.animate().rotation(180f).setDuration(200)
                    dropdownFilter.setBackgroundResource(R.drawable.background_spinner_top)
                    showPopupFilterStatus(dropdownFilter)
                }
            }

            dropdownTable.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    arrowTable.animate().rotation(180f).setDuration(200)
                    dropdownTable.setBackgroundResource(R.drawable.background_spinner_top)
                    showPopupTableView(dropdownTable)
                }
            }

            tvClearFilter.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    resetFilter()
                }

            }

            btnMerge.setOnClickListener {
                checkDiscountAndShowDialog {
                    ConfirmDialog.showDialog(
                        parentFragmentManager,
                        content = getString(R.string.sure_to_merge_order),
                        positiveButtonTitle = getString(R.string.sure)
                    ) {
                        viewModel.mergeOrder(orderRecord?.orderNo, adapter?.selectList)
                        //如果存在未完成的订单，则跳转到订单页，查询当前桌台下的订单
                        //  jumpOrdered(it.tableUuid)
                    }
                }
            }
        }
    }

    private fun showPopupFilterStatus(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupOrderFilterBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        PopupWindowHelper.addPopupWindow(popupWindow)
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            PopupWindowHelper.deletePopupWindow(popupWindow)
            binding?.arrowFilter?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_white_radius_100)
        }
        requireContext().apply {
            when (selectedValue) {
                OrderedStatusEnum.UNPAID.id ->
                    setSelectedLanguages(
                        true,
                        popupView.tvUnpaid,
                        this,
                    )

                OrderedStatusEnum.BE_CONFIRM.id ->
                    setSelectedLanguages(
                        true,
                        popupView.tvBeConfirm,
                        this
                    )

                OrderedStatusEnum.All.id ->
                    setSelectedLanguages(
                        true,
                        popupView.tvAllOrder,
                        this
                    )

            }

        }

        popupView.tvUnpaid.setOnClickListener {
            selectedValue = OrderedStatusEnum.UNPAID.id
            setValueOnclickStatus(popupWindow, popupView.tvUnpaid.text.toString())
        }

        popupView.tvBeConfirm.isVisible = true
        popupView.tvBeConfirm.setOnClickListener {
            selectedValue = OrderedStatusEnum.BE_CONFIRM.id
            setValueOnclickStatus(popupWindow, popupView.tvBeConfirm.text.toString())
        }

        popupView.tvPaid.isVisible = false

        popupView.tvRefunds.isVisible = false

        popupView.tvAllOrder.setOnClickListener {
            selectedValue = OrderedStatusEnum.All.id
            setValueOnclickStatus(popupWindow, popupView.tvAllOrder.text.toString())
        }
        popupView.tvCancel.isVisible = false

        popupView.tvPreOrder.isVisible = false
    }

    private fun resetFilter() {
        binding?.apply {
            tvFilter.text = getString(R.string.all_order)
            tvTable.text = getString(R.string.all_table)
            selectedValue = OrderedStatusEnum.All.id
            selectedTableItem.clear()
            cardViewFilterTable.isVisible = false
            viewModel.getMergeList(orderRecord, null, selectedTableItem)
        }
    }


    private fun setSelectedLanguages(isSelect: Boolean, textView: TextView, context: Context) {
        if (isSelect) {
            textView.setBackgroundResource(R.drawable.background_language_selected)
        } else {
            textView.setBackgroundColor(Color.TRANSPARENT)
        }
        textView.setTextColor(
            ContextCompat.getColor(
                context,
                if (isSelect) R.color.primaryColor else R.color.black
            )
        )
    }

    private fun setValueOnclickStatus(popupWindow: PopupWindow, text: String) {
        binding?.apply {
            viewModel.getMergeList(
                orderRecord,
                if (selectedValue == OrderedStatusEnum.All.id) null else listOf(selectedValue),
                listTable = selectedTableItem
            )
            tvFilter.text = text
        }
        popupWindow.dismiss()
    }

    private fun showPopupTableView(anchorView: View) {
        activity?.hideKeyboard()
        filterTableAdapter?.updateItemCheck(selectedTableItem)
        popupViewTable?.apply {
            btnConfirm.text =
                getString(R.string.apply_filter).plus(" (${selectedTableItem.size})")
        }
        tablePopupWindow?.showAsDropDown(anchorView)
        tablePopupWindow?.setOnDismissListener {
            binding?.arrowTable?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_white_radius_100)
        }

    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.35).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun checkDiscountAndShowDialog(
        onConfirm: () -> Unit
    ) {
        if (viewModel.isHasDiscount(adapter?.selectList ?: mutableListOf())) {
            ConfirmDialog.showDialog(
                parentFragmentManager,
                content = getString(R.string.this_action_may_be_clear_discounts),
                customTag = ConfirmDialog.CLEAR_DISCOUNT_CUE_TAG,
                orderId = orderRecord?.orderNo,
                positiveButtonTitle = getString(R.string.confirm),
                negativeButtonTitle = getString(R.string.cancel),
            ) {
                onConfirm.invoke()
            }
        } else {
            onConfirm.invoke()
        }
    }

}