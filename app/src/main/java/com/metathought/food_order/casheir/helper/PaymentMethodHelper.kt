package com.metathought.food_order.casheir.helper

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.PaymentChannelModel
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/7/9
 *@desc
 **/

object PaymentMethodHelper {

    private val payChannelList = mutableListOf<PaymentChannelModel>()

    suspend fun getPaymentMethod(
        repository: Repository,
        needRequest: Boolean? = true,
        success: ((List<PaymentChannelModel>) -> Unit)? = null
    ) {
        try {

            PreferenceDataStoreHelper.getInstance().apply {
                val jsonStr = getFirstPreference(
                    PreferenceDataStoreConstants.DATA_STORE_KEY_PAYMENT_CHANNEL,
                    ""
                )
                if (!jsonStr.isNullOrEmpty()) {
                    val listType = object : TypeToken<List<PaymentChannelModel>?>() {}.type
                    val list = Gson().fromJson<List<PaymentChannelModel>?>(
                        jsonStr, listType
                    )
                    payChannelList.clear()
                    payChannelList.addAll(list)
                }
                Timber.e("DATA_STORE_KEY_PAYMENT_CHANNEL: ${payChannelList.size}")
            }
            if (payChannelList.isEmpty() || needRequest == true) {
                getPaymentMethodOnline(repository, success)
            } else {
                success?.invoke(payChannelList)
            }
        } catch (e: Exception) {

        }
    }

    suspend fun getPaymentMethodOnline(
        repository: Repository,
        success: ((List<PaymentChannelModel>) -> Unit)? = null
    ) {
        val response = repository.getPaymentMethodList()
        if (response is ApiResponse.Success) {
//            //根据sort 排序一下
            val tmp1 = response.data.filter { it.paymentMethodSelected }
                .sortedWith(compareBy { it.sort * -1 })
            val tmp2 = response.data.filter { !it.paymentMethodSelected }

            val finalList = mutableListOf<PaymentChannelModel>()
            finalList.addAll(tmp1)
            finalList.addAll(tmp2)
            PreferenceDataStoreHelper.getInstance().apply {
                this.putPreference(
                    PreferenceDataStoreConstants.DATA_STORE_KEY_PAYMENT_CHANNEL,
                    finalList.toJson()
                )
            }

            payChannelList.clear()
            payChannelList.addAll(finalList)
            success?.invoke(finalList)
        }
    }


    //获取现金支付model
    fun getCashPaymentModel(): OfflineChannelModel? {
        val model =
            payChannelList.firstOrNull { it.paymentMethodId == PayTypeEnum.CASH_PAYMENT.id.toString() }
        val cashModel =
            model?.offlinePaymentChannelsVos?.firstOrNull { it.id == OfflinePaymentChannelEnum.CASH.id }
        return cashModel
    }

    //是否支持线上支付
    fun isSupportOnline(): Boolean {
        return true
    }


}