package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.RecordBalance
import com.metathought.food_order.casheir.databinding.BalanceNameItemBinding

/**
 * 余额界面客户昵称适配器 - 固定列
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class BalanceNameAdapter(
    val list: ArrayList<RecordBalance>
) : RecyclerView.Adapter<BalanceNameAdapter.NameViewHolder>() {

    inner class NameViewHolder(val binding: BalanceNameItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: RecordBalance, position: Int) {
            binding.apply {
//                tvAccountName.text = resource.nickName
                tvAccountName.text =
                    if (!resource.nickName.isNullOrEmpty()) resource.nickName else resource.telephone?.takeLast(
                        4
                    )
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NameViewHolder {
        val itemView = BalanceNameItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return NameViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: NameViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }

    fun replaceData(newList: ArrayList<RecordBalance>?) {
        if (newList != null) {
            this.list.clear()
            this.list.addAll(newList)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<RecordBalance>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }
}
