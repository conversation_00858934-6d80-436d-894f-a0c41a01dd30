package com.metathought.food_order.casheir.helper

import android.content.Context
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.PaymentChannelResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.QuickRemarkModel
import com.metathought.food_order.casheir.extension.md5
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/8/9
 *@desc
 **/

object PreferenceHelper {

    suspend fun getCashBoxPwd(context: Context): String {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CASH_BOX_PWD.name}_${storeId}_${account}")
        var result = ""

        PreferenceDataStoreHelper.getInstance(context).apply {
            result = getFirstPreference(
                key,
                ""
            )
        }
        return result
    }

    suspend fun setCashBoxPwd(context: Context, pwd: String) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CASH_BOX_PWD.name}_${storeId}_${account}")
        Timber.e("setCashBoxPwd  ${key}")
        PreferenceDataStoreHelper.getInstance(context).apply {
            putPreference(key, pwd.md5())
        }
    }

    suspend fun deleteCashBoxPwd(context: Context) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CASH_BOX_PWD.name}_${storeId}_${account}")
        Timber.e("deleteCashBoxPwd  ${key}")
        PreferenceDataStoreHelper.getInstance(context).apply {
            removePreference(key)
        }
    }


    suspend fun getLocalNoticeId(context: Context): String {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_NOTICE_ID.name}_${storeId}_${account}")
        var result = ""
        PreferenceDataStoreHelper.getInstance(context).apply {
            result = getFirstPreference(
                key,
                ""
            )
        }
        return result
    }

    suspend fun setLocalNoticeId(context: Context, noticeId: String) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_NOTICE_ID.name}_${storeId}_${account}")

        PreferenceDataStoreHelper.getInstance(context).apply {
            putPreference(key, noticeId)
        }
    }

    suspend fun getCurrentTabId(): Int {
        val key =
            intPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_TAB_INDEX.name}")
        var result = -1
        PreferenceDataStoreHelper.getInstance().apply {
            result = getFirstPreference(
                key,
                -1
            )
        }
        return result
    }


    suspend fun setCurrentTabId(tabId: Int) {
        val key =
            intPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_TAB_INDEX.name}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, tabId)
        }
    }

    suspend fun getUniversalTableInfo(): String {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_UNIVERSAL_TABLE.name}_${storeId}_${account}")
        var result = ""
        PreferenceDataStoreHelper.getInstance().apply {
            result = getFirstPreference(
                key,
                ""
            )
        }
        return result
    }


    suspend fun setUniversalTableInfo(universalTableInfoJson: String) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_UNIVERSAL_TABLE.name}_${storeId}_${account}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, universalTableInfoJson)
        }
    }

    suspend fun getOpenManualInputWeight(): Boolean {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val key =
            booleanPreferencesKey("${PreferenceDataStoreConstants.OPEN_MANUAL_INPUT_WEIGHT.name}_${account}")
        var result: Boolean
        PreferenceDataStoreHelper.getInstance().apply {
            result = getFirstPreference(key, false)
        }
        return result
    }


    suspend fun setOpenManualInputWeight(isOpenManualInputWeight: Boolean) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val key =
            booleanPreferencesKey("${PreferenceDataStoreConstants.OPEN_MANUAL_INPUT_WEIGHT.name}_${account}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, isOpenManualInputWeight)
        }
    }

    suspend fun getStoreInfo(): StoreInfoResponse? {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_STORE_INFO.name}_${storeId}_${account}")
        var model: StoreInfoResponse? = null
        PreferenceDataStoreHelper.getInstance().apply {
            model = Gson().fromJson(
                getFirstPreference(
                    key,
                    ""
                ), StoreInfoResponse::class.java
            )
        }
        return model
    }


    suspend fun setStoreInfo(storeInfo: StoreInfoResponse) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_STORE_INFO.name}_${storeId}_${account}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, storeInfo.toJson())
        }
    }


    suspend fun getCustomerInputKhrList(): MutableList<Long> {
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CUSTOM_KHR_INPUT.name}_${storeId}")
        val listType = object : TypeToken<MutableList<Long>?>() {}.type
        var list = mutableListOf<Long>()
        PreferenceDataStoreHelper.getInstance().apply {
            val json = this.getFirstPreference(
                key,
                ""
            )

            if (json.isNotEmpty()) {
                list = Gson().fromJson(json, listType)
            }
        }
        return list
    }

    suspend fun setCustomerInputKhrList(info: MutableList<Long>) {
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CUSTOM_KHR_INPUT.name}_${storeId}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, info.toJson())
        }
    }


    suspend fun getQuickRemarkList(): ArrayList<QuickRemarkModel> {
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_QUICK_REMARK_LIST.name}_${storeId}")
        val listType = object : TypeToken<ArrayList<QuickRemarkModel>?>() {}.type
        var list = ArrayList<QuickRemarkModel>()
        PreferenceDataStoreHelper.getInstance().apply {
            val json = this.getFirstPreference(
                key,
                ""
            )

            if (json.isNotEmpty()) {
                list = Gson().fromJson(json, listType)
            }
        }
        return list
    }

    suspend fun setQuickRemarkList(info: ArrayList<QuickRemarkModel>) {
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_QUICK_REMARK_LIST.name}_${storeId}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, info.toJson())
        }
    }


    suspend fun getCartDataVersion(): Long {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            longPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CART_DATA_VERSION.name}_${storeId}_${account}")
        var result = 0L
        PreferenceDataStoreHelper.getInstance().apply {
            result = getFirstPreference(
                key,
                0L
            )
        }
        return result
    }


    suspend fun setCartDataVersion(version: Long) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            longPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_CART_DATA_VERSION.name}_${storeId}_${account}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, version)
        }
    }


    // 新增：获取仅查看有充值记录会员的状态（默认 true）
    suspend fun getOnlyViewRechargeMember(): Boolean {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val key =
            booleanPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_ONLY_VIEW_RECHARGE_MEMBER.name}_${account}")
        var result = false
        PreferenceDataStoreHelper.getInstance().apply {
            result = getFirstPreference(key, true)
        }
        return result
    }

    // 新增：设置仅查看有充值记录会员的状态
    suspend fun setOnlyViewRechargeMember(isOnlyView: Boolean) {
        val account = MainDashboardFragment.CURRENT_USER?.userAccount
        val key =
            booleanPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_ONLY_VIEW_RECHARGE_MEMBER.name}_${account}")
        PreferenceDataStoreHelper.getInstance().apply {
            putPreference(key, isOnlyView)
        }
    }

    // 获取支付方式缓存（返回ArrayList<OfflineChannelModel>）
    suspend fun getPaymentMethod(): PaymentChannelResponse? {
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_ALL_PAYMENT_METHOD}_${storeId}")
        val json = PreferenceDataStoreHelper.getInstance().getFirstPreference(key, "")
        try {
            val paymentChannelResponse = Gson().fromJson(json, PaymentChannelResponse::class.java)
            return paymentChannelResponse
        } catch (e: Exception) {
            return null
        }
    }

    //设置支付方式缓存（接收ArrayList<OfflineChannelModel>）
    suspend fun setPaymentMethod(methodList: PaymentChannelResponse) {
        val storeId = MainDashboardFragment.CURRENT_USER?.storeId
        val key =
            stringPreferencesKey("${PreferenceDataStoreConstants.DATA_STORE_KEY_ALL_PAYMENT_METHOD}_${storeId}")
        PreferenceDataStoreHelper.getInstance().putPreference(key, methodList.toJson())
    }


}