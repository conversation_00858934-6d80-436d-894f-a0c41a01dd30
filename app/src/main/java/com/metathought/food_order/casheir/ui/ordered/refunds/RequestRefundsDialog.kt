package com.metathought.food_order.casheir.ui.ordered.refunds


import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogRefundsBinding
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getScrollPosition
import com.metathought.food_order.casheir.extension.parseDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.ui.adapter.PatialRefundsOrderedItemAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.utils.PaymentUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.math.BigDecimal
import java.util.Calendar


@AndroidEntryPoint
class RequestRefundsDialog : BaseDialogFragment() {
    private var binding: DialogRefundsBinding? = null
    private var fullRefundClickListener: ((OrderedInfoResponse, PayTypeEnum, String, Boolean) -> Unit)? =
        null
    private var partialRefundRequest: ((OrderedInfoResponse, PayTypeEnum, String, Boolean) -> Unit)? =
        null
    private var currentOrderedInfo: OrderedInfoResponse? = null
    private var adapter: PatialRefundsOrderedItemAdapter? = null

    //可以退的总数
    private var canRefundTotalNum = 0

    /**
     * 将退还商品自动入库 true 是 false 否
     */
    private var autoInStock = false


    private var orderedScreen: SecondaryScreenUI? = null

    //全额退款未退款金额
    private var unreturnedPrice = 0L

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogRefundsBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)


        initListener()
        initData()
        initObserver()
    }


    private fun setDialogLayout() {
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
////            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
//            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
//            binding?.apply {
////                Timber.e(" layoutMain.measuredHeight111 ${layoutMain.measuredHeight}")
//                layoutMain.post {
//                    Timber.e(" layoutMain.measuredHeight2222 ${layoutMain.measuredHeight}")
//                    dialog?.window?.setLayout(screenWidth, layoutMain.measuredHeight)
//                }
//            }
//        }
    }

    private fun initObserver() {

    }

    private fun initData() {
        binding?.apply {
            context?.let {
                if (currentOrderedInfo?.showAutoInStockBtn == true) {
                    llAutoInStore.isVisible = true
                    autoInStock = true
                    updateSwitch()
                }
                tvRefundQTY.addTextChangedListener {
                    if (it.toString() == "0") {
                        btnConfirmRefund.setEnableWithAlpha(false)
                    } else {
                        btnConfirmRefund.setEnableWithAlpha(true)
                    }
                }
                currentOrderedInfo?.let { orderDetail ->
                    orderDetail.refundGoodsJson?.forEach { refundGoods ->
                        orderDetail.goods?.filter { it.getHash() == refundGoods.getHash() }
                            ?.forEach { goods ->
                                goods.alreadyRefundNum = refundGoods.num
                            }
                    }
                    orderDetail.goods?.let { it1 ->
                        val filterList = it1.filter { it.getCanRefundNum() > 0 }
                        val list = ArrayList(filterList)
                        list.forEach {
                            canRefundTotalNum += it.getCanRefundNum()
                        }
                        val list2 = OrderHelper.sortingGoodsWithMergeOrder(
                            requireContext(),
                            orderDetail.mergeOrderIds,
                            null,
                            list
                        ) ?: arrayListOf()
                        adapter = PatialRefundsOrderedItemAdapter(
                            list2,
                            orderDetail.isUseDiscount,
                        ) {
                            orderedScreen?.getRefundDialog()?.run {
                                notifyAdapter(it)
                            }
                            calculatePrice()
                        }
                        recyclerOrderedFood.adapter = adapter
                        //副屏退款dialog : Second screen refund dialog
                        orderedScreen?.getRefundDialog()?.run {
                            initData(currentOrderedInfo, list2)
                        }
                        calculatePrice()
                    }
                }
                llPaymentLayout.isVisible = false
//                    currentOrderedInfo?.payType == PayTypeEnum.ONLINE_PAYMENT.id

                val payDate = currentOrderedInfo?.getPayTimeFormat()?.parseDate(FORMAT_DATE)

                val today = Calendar.getInstance().apply {
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.time

                if (today.after(payDate)) {
                    radioOnline.setEnable(false)
                    radioOnline.isChecked = false
                    radioCash.isChecked = true
                }


//                when (currentOrderedInfo?.payType) {
//                    PayTypeEnum.ONLINE_PAYMENT.id -> tvPaymentMethod.text =
//                        getString(R.string.online_payment)
//
//                    PayTypeEnum.CASH_PAYMENT.id -> tvPaymentMethod.text = getString(R.string.cash)
//                    PayTypeEnum.USER_BALANCE.id -> tvPaymentMethod.text =
//                        getString(R.string.balance)
//                }

                if (currentOrderedInfo?.isTakeOut() == true) {
                    radioRefundType.check(R.id.radioFullRefund)
//                    radioPartialRefund.setEnable(false)
                    radioPartialRefund.isVisible = false
                }

                context?.let { context ->
                    tvPaymentMethod.text = currentOrderedInfo?.getRefundType(context)
                    updateMixPayView()
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateMixPayView() {
        binding?.apply {
            if (currentOrderedInfo?.isPayByMix() == true) {
                when (radioRefundType.checkedRadioButtonId) {
                    R.id.radioFullRefund -> {
                        if (currentOrderedInfo?.refundGoodsJson.isNullOrEmpty() && PaymentUtils.isMixHasBalance(
                                currentOrderedInfo?.combinedPayInfoList
                            )
                        ) {
                            //混合支付 没退过款的 显示 2种支付方式
                            llPaymentMethod1.isVisible = true
                            llBalanceRefund.isVisible = true
                            balanceRefund.text = getString(R.string.refund_amount)
                            refundTotalAmount.text = getString(R.string.refund_amount)

                            tvPaymentMethod1.text = getString(R.string.balance)
                            tvRefundTotalAmount1.text =
                                currentOrderedInfo?.balancePayAmount?.priceFormatTwoDigitZero1()
                            tvPaymentMethod.text = getString(R.string.offline_refund)
                            tvRefundTotalAmount.text =
                                currentOrderedInfo?.offlinePayAmount?.priceFormatTwoDigitZero1()

                            tvRefundTotalKhrAmount.text = "៛${
                                FoundationHelper.usdConverToKhr(
                                    currentOrderedInfo?.conversionRatio ?: FoundationHelper.conversionRatio!!,
                                    (currentOrderedInfo?.offlinePayAmount ?: BigDecimal.ZERO).toLong()
                                ).decimalFormatZeroDigit()
                            }"
                        } else {
                            //混合支付  退过款的只显示 线下退款
                            llPaymentMethod1.isVisible = false
                            llBalanceRefund.isVisible = false
                            tvPaymentMethod.text = getString(R.string.offline_refund)
                            refundTotalAmount.text = getString(R.string.refund_total_price)
                        }
                    }

                    R.id.radioPartialRefund -> {
                        //混合支付  退过款的只显示 线下退款
                        tvPaymentMethod.text = getString(R.string.offline_refund)
                        llPaymentMethod1.isVisible = false
                        llBalanceRefund.isVisible = false
                        refundTotalAmount.text = getString(R.string.refund_total_price)
                    }
                }
            }
        }
    }


    private fun initListener() {
        binding?.apply {

            ivSwitch.setOnClickListener {
                autoInStock = !autoInStock
                updateSwitch()
            }

            topBar.getCloseBtn()?.setOnClickListener {
//                orderedScreen?.dismissRefundDialog()
                dismissAllowingStateLoss()
            }
            btnCancel.setOnClickListener {
//                orderedScreen?.dismissRefundDialog()
                dismissAllowingStateLoss()
            }
            btnConfirmRefund.setOnClickListener {
                when (radioRefundType.checkedRadioButtonId) {
                    R.id.radioFullRefund -> {
                        currentOrderedInfo?.let {
                            fullRefundClickListener?.invoke(
                                it,
                                getPaymentTypeID(),
                                FoundationHelper.getPriceStrByUnit(
                                    currentOrderedInfo?.conversionRatio
                                        ?: FoundationHelper.conversionRatio,
                                    unreturnedPrice, currentOrderedInfo?.isKhr() == true
                                ),
//                                tvRefundTotalAmount.text.toString(),
                                autoInStock
                            )
                        }
                    }

                    R.id.radioPartialRefund -> {
                        currentOrderedInfo?.let {
                            partialRefundRequest?.invoke(
                                it,
                                getPaymentTypeID(),
                                tvRefundTotalAmount.text.toString(), autoInStock
                            )
                        }
                    }
                }
//                orderedScreen?.dismissRefundDialog()
                dismissAllowingStateLoss()
            }
            //主屏联动副屏滑动
            recyclerOrderedFood.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    recyclerView.layoutManager?.getScrollPosition(dy)?.let {
                        if (it != -1) {
                            orderedScreen?.getRefundDialog()?.run {
                                updateRecyclerOrderedFood(it)
                            }
                        }
                    }
                }
            })

            radioRefundType.setOnCheckedChangeListener { group, checkedId ->

                when (checkedId) {
                    R.id.radioPartialRefund -> {
                        setDialogLayout()
                        layoutFood.isVisible = true
                        layoutRefundVat.isVisible = true
                        layoutRefundPrice.isVisible = true
                        orderedScreen?.getRefundDialog()?.run {
                            refundType(false)
                        }

                        calculatePrice()
//                        context?.let {
//                            val displayMetrics = getDisplayMetrics(it)
//                            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
//                            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
//                            dialog?.window?.setLayout(screenWidth, screenHeight)
//                        }
                    }

                    R.id.radioFullRefund -> {
                        setDialogLayout()
                        orderedScreen?.getRefundDialog()?.run {
                            refundType(true)
                        }
                        currentOrderedInfo?.let {
                            var qtyRefund = 0

                            unreturnedPrice = (currentOrderedInfo?.realPrice
                                ?: 0) - (currentOrderedInfo?.refundPrice ?: 0)

                            it.goods?.let { goods ->
                                for (good in goods) {
                                    qtyRefund += good.getCanRefundNum()
                                }
                            }

                            layoutRefundVat.isVisible = false
                            layoutRefundService.isVisible = false
                            layoutRefundPack.isVisible = false
                            layoutRefundPrice.isVisible = false
                            tvRefundQTY.text = qtyRefund.toString()
                            tvRefundTotalAmount.text = FoundationHelper.getPriceStrByUnit(
                                currentOrderedInfo?.conversionRatio
                                    ?: FoundationHelper.conversionRatio,
                                unreturnedPrice, currentOrderedInfo?.isKhr() == true
                            )
//                                unreturnedPrice.priceFormatTwoDigitZero2()
                            tvRefundTotalKhrAmount.text = "៛${
                                FoundationHelper.usdConverToKhr(
                                    currentOrderedInfo?.conversionRatio ?: FoundationHelper.conversionRatio!!,
                                    unreturnedPrice
                                ).decimalFormatZeroDigit()
                            }"
                            layoutFood.isVisible = false
                            updateMixPayView()
                            orderedScreen?.getRefundDialog()?.run {
                                setFullRefundVisible(
                                    llPaymentMethod1.isVisible,
                                    llBalanceRefund.isVisible,
                                    llPaymentMethod.isVisible
                                )
                                radioFullRefund(
                                    tvRefundQTY.text.toString(),
                                    tvRefundTotalAmount.text.toString(),
                                    tvRefundTotalKhrAmount.text.toString(),
                                    paymentMethod1Str = paymentMethod1.text.toString(),
                                    tvPaymentMethod1Str = tvPaymentMethod1.text.toString(),
                                    paymentMethod2Str = paymentMethod2.text.toString(),
                                    balanceRefundStr = balanceRefund.text.toString(),
                                    tvRefundTotalAmount1Str = tvRefundTotalAmount1.text.toString(),
                                    tvPaymentMethodStr = tvPaymentMethod.text.toString()
                                )
                            }
                        }


                    }
                }
            }
        }
    }

    private fun updateSwitch() {
        binding?.apply {
            ivSwitch.setImageResource(if (autoInStock) R.drawable.icon_switch_open else R.drawable.icon_switch_close)
        }
    }

    private fun calculatePrice() {
        binding?.apply {
            var qtyRefund = 0
            var totalPriceProduct = 0L
            var totalVatPriceProduct = 0L
            var totalServicePriceProduct = 0L
            var totalPackPrice = 0L
            //剩余可退数量
            var remainderRefundNum = 0

            currentOrderedInfo?.let {
                it.goods?.let { goods ->
                    for (good in goods) {
                        qtyRefund += good.refundsNum ?: 0
                        remainderRefundNum += good.getCanRefundNum()
                        if (good.finalReduceSinglePrice != null) {
                            //说明有设置减免金额
                            totalPriceProduct += good.totalRefundReducePrice()
                            totalVatPriceProduct += good.totalRefundReduceVatPrice()
                            totalPackPrice += good.totalRefundReducePackingFee()
                            totalServicePriceProduct += good.totalRefundReduceServiceFee()
                        } else {
                            if (it.isUseDiscount == true) {
                                totalPriceProduct += good.totalRefundVipPrice()
                                totalVatPriceProduct += good.totalRefundVipVatPrice()
                                totalServicePriceProduct += good.totalRefundVipServiceFee()
                            } else {
                                totalPriceProduct += good.totalRefundPrice()
                                totalVatPriceProduct += good.totalRefundVatPrice()
                                totalServicePriceProduct += good.totalRefundServiceFee()
                            }
                            totalPackPrice += good.totalCancelPackingFee()
                        }
                    }
                }
                if (it.diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
                    totalServicePriceProduct = 0L
                }
            }


            var totalRefundPrice =
                totalPriceProduct + totalVatPriceProduct + totalServicePriceProduct + totalPackPrice

            layoutRefundPrice.isVisible = true
            layoutRefundVat.isVisible = true

            if (currentOrderedInfo?.diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
                layoutRefundPack.isVisible = true
                layoutRefundService.isVisible = false
            } else {
                layoutRefundPack.isVisible = false
                layoutRefundService.isVisible = true
            }

            /**
             * 当有设置减免折扣 获取有使用优惠券时
             */
//            if (currentOrderedInfo?.reduceRate != null || currentOrderedInfo?.reduceDollar != null || currentOrderedInfo?.reduceKhr != null || currentOrderedInfo?.coupon != null || !currentOrderedInfo?.couponActivityList.isNullOrEmpty()) {
//                //如果选择的数量等于全部退完，就直接用realPrice 去展示，不然累加可能有误差
//
//            }
            if (remainderRefundNum == qtyRefund) {
                //剩余未退金额
                totalRefundPrice = (currentOrderedInfo?.realPrice
                    ?: 0) - (currentOrderedInfo?.refundPrice ?: 0)
                totalVatPriceProduct =
                    totalRefundPrice - totalPackPrice - totalPriceProduct - totalServicePriceProduct

            }

            if (remainderRefundNum == qtyRefund) {
                Timber.e("部分退款全选")
                layoutRefundPrice.isVisible = false
                layoutRefundPack.isVisible = false
                layoutRefundService.isVisible = false
                layoutRefundVat.isVisible = false
            }



            tvRefundPrice.text = totalPriceProduct.priceFormatTwoDigitZero2()
            tvRefundService.text = totalServicePriceProduct.priceFormatTwoDigitZero2()
            tvRefundVat.text = totalVatPriceProduct.priceFormatTwoDigitZero2()
            tvRefundQTY.text = qtyRefund.toString()
            tvRefundPack.text = totalPackPrice.priceFormatTwoDigitZero2()


            tvRefundTotalAmount.text = FoundationHelper.getPriceStrByUnit(
                currentOrderedInfo?.conversionRatio ?: FoundationHelper.conversionRatio,
                totalRefundPrice, currentOrderedInfo?.isKhr() == true
            )
            if (currentOrderedInfo?.isKhr() == true) {
                tvRefundTotalKhrAmount.isVisible = false
            }
            tvRefundTotalKhrAmount.text = "៛${
                FoundationHelper.usdConverToKhr(
                    currentOrderedInfo?.conversionRatio ?: FoundationHelper.conversionRatio!!,
                    totalRefundPrice
                ).decimalFormatZeroDigit()
            }"

            updateMixPayView()

            orderedScreen?.getRefundDialog()?.run {
                setRefundAmount(
                    tvRefundPrice.text.toString(),
                    tvRefundVat.text.toString(),
                    tvRefundQTY.text.toString(),
                    tvRefundPack.text.toString(),
                    layoutRefundPack.isVisible,
                    tvRefundTotalAmount.text.toString(),
                    tvRefundTotalKhrAmount.text.toString(),
                    tvRefundService.text.toString(),
                    paymentMethod1Str = paymentMethod1.text.toString(),
                    tvPaymentMethod1Str = tvPaymentMethod1.text.toString(),
                    paymentMethod2Str = paymentMethod2.text.toString(),
                    balanceRefundStr = balanceRefund.text.toString(),
                    tvRefundTotalAmount1Str = tvRefundTotalAmount1.text.toString(),
                    tvPaymentMethodStr = tvPaymentMethod.text.toString()
                )
                setPartRefundViewVisible(
                    layoutRefundPack.isVisible,
                    layoutRefundVat.isVisible,
                    layoutRefundPrice.isVisible,
                    layoutRefundService.isVisible,

                    llPaymentMethod1.isVisible,
                    llBalanceRefund.isVisible,
                    llPaymentMethod.isVisible
                )
            }

        }
    }


    override fun onDestroy() {
        orderedScreen?.dismissRefundDialog()
        super.onDestroy()
    }

    companion object {
        private const val TAG = "RequestRefundsDialog"

        fun showDialog(
            fragmentManager: FragmentManager, currentOrderedInfo: OrderedInfoResponse,
//            orderedScreen: OrderedInfoScreenUI? = null,
            orderedScreen: SecondaryScreenUI? = null,
            fullRefundClickListener: ((OrderedInfoResponse?, PayTypeEnum, String, Boolean) -> Unit),
            partialRefundClickListener: ((OrderedInfoResponse?, PayTypeEnum, String, Boolean) -> Unit)
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            currentOrderedInfo.goods?.forEach { it.refundsNum = 0 }
            fragment =

                newInstance(
                    fullRefundClickListener = fullRefundClickListener,
                    partialRefundClickListener = partialRefundClickListener,
                    orderedScreen = orderedScreen,
                    currentOrderedInfo = currentOrderedInfo
                )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? RequestRefundsDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            currentOrderedInfo: OrderedInfoResponse,
            orderedScreen: SecondaryScreenUI? = null,
            fullRefundClickListener: ((OrderedInfoResponse?, PayTypeEnum, String, Boolean) -> Unit),
            partialRefundClickListener: ((OrderedInfoResponse?, PayTypeEnum, String, Boolean) -> Unit)
        ): RequestRefundsDialog {
            val args = Bundle()
            val fragment = RequestRefundsDialog()
            fragment.fullRefundClickListener = fullRefundClickListener
            fragment.partialRefundRequest = partialRefundClickListener
            fragment.currentOrderedInfo = currentOrderedInfo
            fragment.orderedScreen = orderedScreen
            fragment.arguments = args
            return fragment
        }
    }

    private fun getPaymentTypeID(): PayTypeEnum {
        when (currentOrderedInfo?.payType) {
//            PayTypeEnum.ONLINE_PAYMENT.id -> {
//                when (binding?.radioGroupPaymentMethod?.checkedRadioButtonId) {
//                    R.id.radioCash -> return PayTypeEnum.CASH_PAYMENT
//                    R.id.radioOnline -> return PayTypeEnum.ONLINE_PAYMENT
//                }
//            }
            PayTypeEnum.ONLINE_PAYMENT.id -> return PayTypeEnum.CASH_PAYMENT
            PayTypeEnum.CASH_PAYMENT.id -> return PayTypeEnum.CASH_PAYMENT
            PayTypeEnum.USER_BALANCE.id -> return PayTypeEnum.USER_BALANCE
            PayTypeEnum.PAY_OTHER.id -> return PayTypeEnum.PAY_OTHER
            PayTypeEnum.MIXED_PAYMENT.id -> return PayTypeEnum.CASH_PAYMENT
        }

        return PayTypeEnum.ONLINE_PAYMENT
    }
}
