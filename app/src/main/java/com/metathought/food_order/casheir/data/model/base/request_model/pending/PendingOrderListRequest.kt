package com.metathought.food_order.casheir.data.model.base.request_model.pending

import com.google.gson.annotations.SerializedName

data class PendingOrderListRequest(
    @SerializedName("keyword")
    val keyword: String? = null,
    @SerializedName("page") val page: Int,
    @SerializedName("pageSize") val pageSize: Int = 1,
    @SerializedName("diningStyleList") val diningStyleList: ArrayList<Int> = arrayListOf(1, 2, 3)
)
