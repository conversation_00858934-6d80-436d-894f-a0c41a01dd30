package com.metathought.food_order.casheir.data.model.base.request_model


//时间段模式 按日还是按月
enum class TimeSlotMode(val value: Int) {
    Day(1), Month(2)
}


enum class DateRangeType {
    CURRENT_MONTH,  // 本月
    LAST_MONTH,  // 上月
    RECENT_3_MONTHS,  // 最近三个月
    RECENT_6_MONTHS,  // 最近六个月
    CURRENT_QUARTER,  // 本季度
    LAST_QUARTER,  // 上个季度
    RECENT_YEAR // 最近一年
}

data class DailyReportRequest(
    var displayMode: Int,                    // 【必填】展示模式，详见下方说明   - 1: 按日展示 - 返回每天的统计数据  - 2: 按月展示 - 返回每月的统计数据
    var startDate: String?,          // 【可选】开始日期，格式：yyyy-MM-dd
    var endDate: String?,             // 【可选】结束日期，格式：yyyy-MM-dd
    var timePeriodType: String?,    // 【可选】时间段类型，详见 DateRangeType
    //收银端不用传
//var storeId:Int?,                     // 【可选】门店ID，管理员可指定，员工自动取当前门店
//var forceRefresh:Boolean?,              // 【可选】是否强制刷新缓存，默认false
)