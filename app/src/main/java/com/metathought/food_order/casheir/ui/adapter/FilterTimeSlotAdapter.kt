package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.DateRangeType
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding


class FilterTimeSlotAdapter(
    val list: List<DateRangeType>,
    val selected: DateRangeType?,
    val context: Context,
) : RecyclerView.Adapter<FilterTimeSlotAdapter.TableItemViewHolder>() {

    var onItemClickCallback: ((DateRangeType) -> Unit)? = null

    inner class TableItemViewHolder(val binding: FilterTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: DateRangeType?, index: Int) {
            resource?.let { _ ->
                binding.apply {
                    tvTableID.gravity = Gravity.CENTER
                    tvTableID.text = getTimeSlotString(context, resource)
                    tvTableID.setTextColor(
                        ContextCompat.getColor(
                            context,
                            if (selected == resource) R.color.primaryColor else android.R.color.black
                        )
                    )
                    root.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            if (selected == resource) R.color.filter_table_background else android.R.color.transparent
                        )
                    )
                    root.setOnClickListener {
                        onItemClickCallback?.invoke(resource)
                    }

                }
            }
        }


        private fun getTimeSlotString(
            context: Context,
            timeSlot: DateRangeType
        ): CharSequence {
            return when (timeSlot) {
                DateRangeType.CURRENT_MONTH -> context.getString(R.string.this_month)
                DateRangeType.LAST_MONTH -> context.getString(R.string.last_month)
                DateRangeType.RECENT_3_MONTHS -> context.getString(R.string.recently_three_months)
                DateRangeType.RECENT_6_MONTHS -> context.getString(R.string.recently_six_months)
                DateRangeType.CURRENT_QUARTER -> context.getString(R.string.this_quarter)
                DateRangeType.LAST_QUARTER -> context.getString(R.string.last_quarter)
                DateRangeType.RECENT_YEAR -> context.getString(R.string.recently_year)
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableItemViewHolder {
        var itemView = FilterTableItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
        return TableItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: TableItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }


}