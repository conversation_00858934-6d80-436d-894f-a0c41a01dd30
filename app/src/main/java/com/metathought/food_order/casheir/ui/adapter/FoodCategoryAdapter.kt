package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Group
import com.metathought.food_order.casheir.databinding.CategoryItemBinding
import timber.log.Timber


class FoodCategoryAdapter(
    var categories: ArrayList<Group>,
    val context: Context,
    val onClickCallback: (Group) -> Unit
) : RecyclerView.Adapter<FoodCategoryAdapter.FoodCategoryViewHolder>() {
    var defaultCheckPosition = 0

    inner class FoodCategoryViewHolder(val binding: CategoryItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                if (bindingAdapterPosition != -1) {
                    setCheckedIndex(bindingAdapterPosition)
                    onClickCallback.invoke(categories[bindingAdapterPosition])
                }
            }
        }

        fun bind(showIndex: Group, position: Int) {
            binding.apply {
                binding.tvValue.text = showIndex.name
                binding.tvValue.setBackgroundResource(if (showIndex.checked == true) R.drawable.background_primarycolor10_radius_6dp else R.drawable.background_white_radius_6dp)
                binding.tvValue.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (showIndex.checked == true) R.color.primaryColor else R.color.black
                    )
                )
            }
        }
    }


    fun setCheckedIndex(position: Int) {
        Timber.e("position====>${position}")
        for (i in 0..<categories.size) {
            if (categories[i].checked == true) {
                categories[i].checked = false
                notifyItemChanged(i)
            }
        }
        categories[position].checked = true
        notifyItemChanged(position)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FoodCategoryViewHolder {
        return FoodCategoryViewHolder(
            CategoryItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return categories.size
    }

    override fun onBindViewHolder(holder: FoodCategoryViewHolder, position: Int) {
        holder.bind(categories[position], position)
    }

    fun updateItems(newItems: ArrayList<Group>) {
        newItems.forEachIndexed { index, group ->
            if (index == 0) {
                group.checked = true
            } else {
                group.checked = false
            }
        }
        categories.clear()
        categories.addAll(newItems)
        notifyDataSetChanged()
    }
}