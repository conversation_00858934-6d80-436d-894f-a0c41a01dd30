package com.metathought.food_order.casheir.data.model.base.response_model.dashboard


import com.google.gson.annotations.SerializedName

data class OrderInfo(
    @SerializedName("additionalTax")
    val additionalTax: Double?,
    @SerializedName("dinersNum")
    val dinersNum: Int?,
    @SerializedName("discountReduction")
    val discountReduction: Double?,
    @SerializedName("mayPayCost")
    val mayPayCost: Double?,
    @SerializedName("note")
    val note: String?,
    @SerializedName("orderDateTime")
    val orderDateTime: String?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("orderType")
    val orderType: String?,
    @SerializedName("subtotal")
    val subtotal: Double?
)