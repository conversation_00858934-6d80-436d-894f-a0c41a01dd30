package com.metathought.food_order.casheir.helper

import android.hardware.usb.UsbDevice
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.UsbPrinterDevice
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.listener.SettableFuture
import com.metathought.food_order.casheir.utils.PrinterLogUtils
import net.posprinter.POSPrinter
import net.posprinter.TSPLPrinter
import net.posprinter.POSConnect
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.concurrent.ExecutionException

object NewPrinterUsbDeviceHelper {
    private const val TAG = "NewPrinterUsbDeviceHelp"

    /**
     *USB Connection. 地址作为key
     */
    var usbDeviceMap: MutableMap<String, UsbPrinterDevice> = mutableMapOf()

    var usbDeviceConnectStatusMap: MutableMap<String, Boolean> = mutableMapOf()

    /**
     * vendorId_productId 作为key
     */
    private var localPrinterInfoMap = mutableMapOf(
        "1155_22339" to LocalPrinterEnum.TICKET_PRINTER.id,
        "8137_8214" to LocalPrinterEnum.LABEL_PRINTER.id,
    )

    /**
     * 根据vendorId和productId获取打印机类型
     * Get printer type by vendorId and productId
     */
    private fun getPrinterTypeByDevice(vendorId: Int, productId: Int): Int? {
        val deviceKey = "${vendorId}_${productId}"
        return localPrinterInfoMap[deviceKey]
    }

    /**
     * 检查设备是否为支持的打印机
     * Check if device is a supported printer
     */
    fun isSupportedPrinter(vendorId: Int, productId: Int): Boolean {
        val deviceKey = "${vendorId}_${productId}"
        return localPrinterInfoMap.containsKey(deviceKey)
    }

    /**
     * 初始化所有USB打印机连接并创建实例
     * Initialize all USB printer connections and create instances
     */
    fun initUsbPrintAndConnectUSB(): Boolean {
        return try {
            // 获取所有USB设备
            val usbDevices = getAllUsbPrinterDevices()
            releaseAllConnections()
            if (usbDevices.isNullOrEmpty()) {
                Timber.d("未发现任何USB打印机设备")
                return false
            }

            var successCount = 0
            usbDevices.forEach { usbDevice ->
                val vendorId = usbDevice.vendorId
                val productId = usbDevice.productId

                // 检查是否为支持的打印机
//                if (isSupportedPrinter(vendorId, productId)) {
                val deviceName = usbDevice.deviceName
//                val printerType = getPrinterTypeByDevice(vendorId, productId)
                val printerType = null
                if (createPrinterConnection(deviceName, usbDevice, printerType)) {
                    successCount++
                    Timber.d("成功创建打印机连接: $deviceName, 类型: $printerType")
                } else {
                    Timber.w("创建打印机连接失败: $deviceName")
                }
//                } else {
//                    Timber.d("跳过不支持的设备: ${vendorId}_${productId}")
//                }
            }

            PrinterDeviceHelper.dealUsbPrinterList()
            EventBus.getDefault().post(
                SimpleEvent(
                    SimpleEventType.UPDATE_PRINTER_MANAGER_LIST,
                    null
                )
            )
            Timber.d("USB打印机初始化完成，成功连接 $successCount/${usbDevices.size} 个设备")
            successCount > 0
        } catch (e: Exception) {
            Timber.e(e, "初始化USB打印机连接失败")
            false
        }
    }


    /**
     * 获取所有USB打印机设备连接
     * Get all USB printer device connections
     */
    private fun getAllUsbPrinterDevices(): List<UsbDevice>? {
        return try {
            val usbDevices = POSConnect.getUsbDevice(MyApplication.myAppInstance)
            if (usbDevices != null && usbDevices.isNotEmpty()) {
                Timber.d("发现 ${usbDevices.size} 个USB设备")
                usbDevices.forEach { device ->
                    val vendorId = device.vendorId
                    val productId = device.productId
                    val deviceKey = "${vendorId}_${productId}"
                    Timber.d("USB设备: $deviceKey, 设备名: ${device.deviceName}")
                }
                usbDevices
            } else {
                Timber.d("未发现USB打印机设备")
                emptyList()
            }
        } catch (e: Exception) {
            Timber.e(e, "获取USB打印机设备失败")
            null
        }
    }


    /**
     * 为指定设备创建打印机连接实例
     * Create printer connection instance for specified device
     */
    fun createPrinterConnection(
        deviceName: String,
        usbDevice: UsbDevice,
        printerType: Int?
    ): Boolean {
        return try {
            Timber.d("[$TAG] 开始为设备创建打印机连接: $deviceName")
            Timber.d("[$TAG] 设备信息 - VendorId: ${usbDevice.vendorId}, ProductId: ${usbDevice.productId}, 打印机类型: $printerType")

            // 创建设备连接
            Timber.d("[$TAG] 正在创建USB设备连接实例...")
            val deviceConnection = POSConnect.createDevice(POSConnect.DEVICE_TYPE_USB)
            if (deviceConnection == null) {
                Timber.e("[$TAG] 创建USB设备连接失败: $deviceName - POSConnect.createDevice返回null")
                return false
            }
            Timber.d("[$TAG] USB设备连接实例创建成功: $deviceName")

            // 同步连接设备
            try {
                PrinterLogUtils.writeUsbPrintLog(
                    printerIp = deviceName,
                    taskInfo = "USB设备同步连接开始",
                    status = PrinterLogUtils.PrintStatus.CONNECT_START,
                    message = "开始同步连接设备: $deviceName"
                )
            } catch (e: Exception) {
                Timber.e(e, "记录同步连接开始日志失败")
            }

            val connectResult = deviceConnection.connectSync(
                deviceName
            ) { status, p1, p2 ->
                when (status) {
                    POSConnect.CONNECT_SUCCESS -> {
                        usbDeviceConnectStatusMap[deviceName] = true
                        try {
                            PrinterLogUtils.writeUsbPrintLog(
                                printerIp = deviceName,
                                taskInfo = "USB设备连接回调",
                                status = PrinterLogUtils.PrintStatus.CONNECT_SUCCESS,
                                message = "设备连接回调成功: $deviceName, 状态码: $status"
                            )
                        } catch (e: Exception) {
                            Timber.e(e, "记录连接成功回调日志失败")
                        }
                    }

                    POSConnect.CONNECT_FAIL -> {
                        usbDeviceConnectStatusMap[deviceName] = false
                        try {
                            PrinterLogUtils.writeUsbPrintLog(
                                printerIp = deviceName,
                                taskInfo = "USB设备连接回调",
                                status = PrinterLogUtils.PrintStatus.CONNECT_FAIL,
                                message = "设备连接回调失败: $deviceName, 状态码: $status, 参数1: $p1, 消息: $p2"
                            )
                        } catch (e: Exception) {
                            Timber.e(e, "记录连接失败回调日志失败")
                        }
                    }

                    else -> {
                        usbDeviceConnectStatusMap[deviceName] = false
                        try {
                            PrinterLogUtils.writeUsbPrintLog(
                                printerIp = deviceName,
                                taskInfo = "USB设备连接回调",
                                status = PrinterLogUtils.PrintStatus.CONNECT_FAIL,
                                message = "设备连接回调未知状态: $deviceName, 状态码: $status, 参数1: $p1, 消息: $p2"
                            )
                        } catch (e: Exception) {
                            Timber.e(e, "记录连接未知状态回调日志失败")
                        }
                    }
                }
            }

            if (!connectResult) {
                try {
                    PrinterLogUtils.writeUsbPrintLog(
                        printerIp = deviceName,
                        taskInfo = "USB设备同步连接结果",
                        status = PrinterLogUtils.PrintStatus.CONNECT_FAIL,
                        message = "设备同步连接失败: $deviceName - connectSync返回false"
                    )
                } catch (e: Exception) {
                    Timber.e(e, "记录同步连接失败日志失败")
                }
                return false
            } else {
                try {
                    PrinterLogUtils.writeUsbPrintLog(
                        printerIp = deviceName,
                        taskInfo = "USB设备同步连接结果",
                        status = PrinterLogUtils.PrintStatus.CONNECT_SUCCESS,
                        message = "设备同步连接成功: $deviceName - connectSync返回true"
                    )
                } catch (e: Exception) {
                    Timber.e(e, "记录同步连接成功日志失败")
                }
            }

            // 创建UsbPrinterDevice实例
            Timber.d("[$TAG] 正在创建UsbPrinterDevice实例: $deviceName")
            val usbPrinterDevice = UsbPrinterDevice().apply {
                this.usbDevice = usbDevice
                this.iDeviceConnection = deviceConnection
            }
            Timber.d("[$TAG] UsbPrinterDevice实例创建完成: $deviceName")

            // 根据打印机类型创建对应的打印机实例
            Timber.d("[$TAG] 开始根据类型创建打印机实例: $deviceName, 类型: $printerType")
            if (printerType != null) {
                when (printerType) {
                    LocalPrinterEnum.TICKET_PRINTER.id -> {
                        Timber.d("[$TAG] 正在创建小票打印机实例: $deviceName")
                        try {
                            val posPrinter = POSPrinter(usbPrinterDevice.iDeviceConnection!!)
                            usbPrinterDevice.pOSPrinter = posPrinter
                            Timber.d("[$TAG] 小票打印机实例创建成功: $deviceName")
                        } catch (e: Exception) {
                            Timber.e(e, "[$TAG] 创建小票打印机实例失败: $deviceName")
                            return false
                        }
                    }

                    LocalPrinterEnum.LABEL_PRINTER.id -> {
                        Timber.d("[$TAG] 正在创建标签打印机实例: $deviceName")
                        try {
                            val tsplPrinter = TSPLPrinter(usbPrinterDevice.iDeviceConnection!!)
                            usbPrinterDevice.tSPLPrinter = tsplPrinter
                            Timber.d("[$TAG] 标签打印机实例创建成功: $deviceName")
                        } catch (e: Exception) {
                            Timber.e(e, "[$TAG] 创建标签打印机实例失败: $deviceName")
                            return false
                        }
                    }

                    else -> {
                        Timber.w("[$TAG] 未知的打印机类型: $printerType, 设备: $deviceName")
                        return false
                    }
                }
            }

            // 将设备添加到映射表中
            try {
                PrinterLogUtils.writeUsbPrintLog(
                    printerIp = deviceName,
                    taskInfo = "USB设备映射表管理",
                    status = PrinterLogUtils.PrintStatus.CONNECT_SUCCESS,
                    message = "正在将设备添加到映射表: $deviceName"
                )
            } catch (e: Exception) {
                Timber.e(e, "记录设备添加映射表开始日志失败")
            }

            usbDeviceMap[deviceName] = usbPrinterDevice

            try {
                PrinterLogUtils.writeUsbPrintLog(
                    printerIp = deviceName,
                    taskInfo = "USB打印机连接实例创建完成",
                    status = PrinterLogUtils.PrintStatus.CONNECT_SUCCESS,
                    message = "打印机连接实例创建完成: $deviceName, 类型: $printerType, 当前映射表大小: ${usbDeviceMap.size}"
                )
            } catch (e: Exception) {
                Timber.e(e, "记录打印机实例创建完成日志失败")
            }

            true
        } catch (e: Exception) {
            Timber.e(e, "[$TAG] 创建打印机连接实例异常: $deviceName")
            false
        }
    }


    /**
     * 释放单个USB打印机连接
     * Release single USB printer connection
     */
    fun releaseSingleConnection(deviceName: String): Boolean {
        return try {
            val device = usbDeviceMap[deviceName]
            if (device != null) {
                try {
                    PrinterLogUtils.writeUsbPrintLog(
                        printerIp = deviceName,
                        taskInfo = "释放单个USB设备连接",
                        status = PrinterLogUtils.PrintStatus.CONNECT_RELEASE,
                        message = "开始释放设备连接: $deviceName"
                    )
                } catch (e: Exception) {
                    Timber.e(e, "记录释放连接开始日志失败")
                }

                // 释放打印机实例
                device.pOSPrinter = null
                device.tSPLPrinter = null

                // 关闭设备连接
                device.iDeviceConnection?.closeSync()

                try {
                    PrinterLogUtils.writeUsbPrintLog(
                        printerIp = deviceName,
                        taskInfo = "释放单个USB设备连接完成",
                        status = PrinterLogUtils.PrintStatus.CONNECT_RELEASE,
                        message = "设备连接释放成功: $deviceName, 剩余设备数量: ${usbDeviceMap.size}"
                    )
                } catch (e: Exception) {
                    Timber.e(e, "记录释放连接成功日志失败")
                }

                Timber.d("释放设备连接成功: $deviceName")
                true
            } else {
                try {
                    PrinterLogUtils.writeUsbPrintLog(
                        printerIp = deviceName,
                        taskInfo = "释放单个USB设备连接",
                        status = PrinterLogUtils.PrintStatus.CONNECT_RELEASE,
                        message = "设备不存在于映射表中: $deviceName"
                    )
                } catch (e: Exception) {
                    Timber.e(e, "记录设备不存在日志失败")
                }

                Timber.w("设备不存在，无法释放连接: $deviceName")
                false
            }
        } catch (e: Exception) {
            try {
                PrinterLogUtils.writeUsbPrintLog(
                    printerIp = deviceName,
                    taskInfo = "释放单个USB设备连接异常",
                    status = PrinterLogUtils.PrintStatus.CONNECT_RELEASE,
                    message = "释放设备连接异常: $deviceName, 错误: ${e.message}"
                )
            } catch (logE: Exception) {
                Timber.e(logE, "记录释放连接异常日志失败")
            }

            Timber.e(e, "释放设备连接失败: $deviceName")
            false
        }
    }

    /**
     * 释放所有USB打印机连接
     * Release all USB printer connections
     */
    fun releaseAllConnections() {
        try {
            usbDeviceMap.forEach { (deviceName, device) ->
                try {
                    device.pOSPrinter = null
                    device.tSPLPrinter = null
                    device.iDeviceConnection?.closeSync()
                    Timber.d("释放设备连接: $deviceName")
                } catch (e: Exception) {
                    Timber.e(e, "释放设备连接失败: $deviceName")
                }
            }
            usbDeviceMap.clear()
            PrinterDeviceHelper.clearUsbPrintInPrintList()
            Timber.d("所有USB打印机连接已释放")
        } catch (e: Exception) {
            Timber.e(e, "释放USB打印机连接失败")
        }
    }

    /**
     * 获取当前连接的设备数量
     * Get current connected device count
     */
    fun getConnectedDeviceCount(): Int {
        return usbDeviceMap.size
    }

    /**
     * 检查指定设备是否已连接
     * Check if specified device is connected
     */
    fun isDeviceConnected(deviceName: String): Boolean {
        return usbDeviceMap.containsKey(deviceName)
    }

    /**
     * XPrinter是否连接成功
     *Is XPrinter connected successfully
     */
    fun isConnectUSB(deviceName: String?): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
        if (usbDeviceMap.contains(deviceName)) {
            val device = usbDeviceMap[deviceName]
            if (device?.pOSPrinter != null) {
                /**
                 * 校验小票打印机
                 */
                device?.pOSPrinter?.isConnect { status -> //1: connected 0: disconnect
                    future.set(status == 1)
                }
            } else if (device?.tSPLPrinter != null) {
                /**
                 * 校验标签打印机
                 */
                device?.tSPLPrinter?.isConnect { status -> //1: connected 0: disconnect
                    future.set(status == 1)
                }
            } else if (device?.iDeviceConnection != null) {
                device?.iDeviceConnection?.isConnect(byteArrayOf(0)) { status -> //1: connected 0: disconnect
                    future.set(status == 1)
                }
            } else {
                future.set(false)
            }
        } else {
            future.set(false)
        }
        return future
    }


    /**
     * XPrinter 本地小票打印机是否有链接
     *Is XPrinter connected successfully
     */
    fun isPosPrinterConnectUSB(): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
//        future.set(false)
        usbDeviceMap.forEach { s, usbPrinterDevice ->
            if (usbPrinterDevice.pOSPrinter != null) {
                usbPrinterDevice.pOSPrinter?.isConnect { status -> //1: connected 0: disconnect
                    //如果有一个链接了那就连接了
                    if (status == 1 && !future.isDone) {
                        future.set(true)
                    }
                }
            }
        }
        if (!future.isDone) {
            future.set(false)
        }
        Timber.e("====== ${future.get()}")
        return future
    }

    /**
     * 获取USB TSPLPrinter打印机实例
     * Get Label Printer instance
     */
    fun getLabelPrinter(usbDevice: UsbDevice?): SettableFuture<TSPLPrinter?> {
        val future = SettableFuture<TSPLPrinter?>()
        if (usbDevice == null) {
            future.setException(Exception("USB设备为空"))
            return future
        }

        val deviceName = usbDevice.deviceName
        val connectUSB = isConnectUSB(deviceName)
        connectUSB.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                Timber.d("USB连接状态检查结果: $result, 设备: $deviceName")
                if (result) {
                    // 设备已连接，检查是否有现有的打印机实例
                    if (usbDeviceMap[deviceName]?.tSPLPrinter != null) {
                        Timber.d("使用现有TSPLPrinter实例: $deviceName")
                        future.set(usbDeviceMap[deviceName]?.tSPLPrinter)
                    } else {
                        // 创建新的打印机实例前，确保释放小票打印机实例
                        Timber.d("创建新的TSPLPrinter实例: $deviceName")
                        val posPrinter = TSPLPrinter(usbDeviceMap[deviceName]?.iDeviceConnection!!)
                        usbDeviceMap[deviceName]?.tSPLPrinter = posPrinter
                        future.set(usbDeviceMap[deviceName]?.tSPLPrinter)
                    }
                } else {
                    // 设备未连接，尝试连接
                    Timber.d("设备未连接，尝试连接: $deviceName")

                    // 如果设备已存在，先释放资源
                    if (usbDeviceMap.containsKey(deviceName)) {
                        releaseSingleConnection(deviceName)
                    }
                    val printerType = LocalPrinterEnum.LABEL_PRINTER.id
                    if (createPrinterConnection(deviceName, usbDevice, printerType)) {
                        Timber.d("成功创建打印机连接: $deviceName, 类型: $printerType")
                    } else {
                        Timber.w("创建打印机连接失败: $deviceName")
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {
                Timber.e(e, "USB连接状态检查失败: $deviceName")
                future.setException(e)
            }
        })

        return future
    }

    /**
     * 获取USB POSPrinter打印机实例
     * Get XPrinter instance
     */
    fun getPosPrinter(usbDevice: UsbDevice?): SettableFuture<POSPrinter?> {
        val future = SettableFuture<POSPrinter?>()
        if (usbDevice == null) {
            future.setException(Exception("USB设备为空"))
            return future
        }

        val deviceName = usbDevice.deviceName
        val connectUSB = isConnectUSB(deviceName)

        connectUSB.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                Timber.d("USB连接状态检查结果: $result, 设备: $deviceName")
                if (result) {
                    // 设备已连接，检查是否有现有的打印机实例
                    if (usbDeviceMap[deviceName]?.pOSPrinter != null) {
                        Timber.d("使用现有POSPrinter实例: $deviceName")
                        future.set(usbDeviceMap[deviceName]?.pOSPrinter)
                    } else {
                        // 创建新的打印机实例前，确保释放标签打印机实例
                        Timber.d("创建新的POSPrinter实例: $deviceName")
//                        createPOSPrinter(deviceName)
                        val posPrinter = POSPrinter(usbDeviceMap[deviceName]?.iDeviceConnection!!)
                        usbDeviceMap[deviceName]?.pOSPrinter = posPrinter
                        future.set(usbDeviceMap[deviceName]?.pOSPrinter)
                    }

                } else {
                    // 设备未连接，尝试连接
                    Timber.d("设备未连接，尝试连接: $deviceName")
                    // 如果设备已存在，先释放资源
                    if (usbDeviceMap.containsKey(deviceName)) {
                        releaseSingleConnection(deviceName)
                    }

                    // 创建新的设备连接
                    val printerType = LocalPrinterEnum.TICKET_PRINTER.id
                    if (createPrinterConnection(deviceName, usbDevice, printerType)) {
                        Timber.d("成功创建打印机连接: $deviceName, 类型: $printerType")
                    } else {
                        Timber.w("创建打印机连接失败: $deviceName")
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {
                Timber.e(e, "USB连接状态检查失败: $deviceName")
                future.setException(e)
            }
        })

        return future
    }

    /**
     * 创建 USB 小票打印
     *
     * @param deviceName
     */
    fun createPOSPrinter(deviceName: String?): UsbPrinterDevice? {
        if (usbDeviceMap.contains(deviceName)) {
            usbDeviceMap[deviceName]?.tSPLPrinter = null
            if (usbDeviceMap[deviceName]?.iDeviceConnection != null) {
                Timber.e("创建小票打印实例")
                usbDeviceMap[deviceName]?.pOSPrinter =
                    POSPrinter(usbDeviceMap[deviceName]?.iDeviceConnection!!)
            }
            return usbDeviceMap[deviceName]
        }
        return null
    }

    /**
     * 创建 USB 标签打印
     *
     * @param deviceName
     */
    fun createTSPLPrinter(deviceName: String?): UsbPrinterDevice? {
        if (usbDeviceMap.contains(deviceName)) {
            usbDeviceMap[deviceName]?.pOSPrinter = null
            if (usbDeviceMap[deviceName]?.iDeviceConnection != null) {
                Timber.e("创建标签打印实例")
                usbDeviceMap[deviceName]?.tSPLPrinter =
                    TSPLPrinter(usbDeviceMap[deviceName]?.iDeviceConnection!!)
            }
            return usbDeviceMap[deviceName]
        }
        return null
    }

    /**
     *  是否 标签打印
     *
     */
    fun isLabelPrinter(vendorId: Int? = null, productId: Int? = null): Boolean {
        if (vendorId == null || productId == null) {
            return false
        }
        val key = "${vendorId}_${productId}"
        if (localPrinterInfoMap.containsKey(key)) {
            return localPrinterInfoMap[key] == LocalPrinterEnum.LABEL_PRINTER.id
        }
        return false
    }

    /**
     * 清空所有打印设备 对应的实例
     *
     */
    fun cleatALlPrinterObject() {
        usbDeviceMap.forEach {
            it.value.pOSPrinter = null
            it.value.tSPLPrinter = null
        }
    }
}