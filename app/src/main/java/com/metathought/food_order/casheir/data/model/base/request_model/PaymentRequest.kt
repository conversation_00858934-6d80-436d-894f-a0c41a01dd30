package com.metathought.food_order.casheir.data.model.base.request_model

import android.content.Context
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.doubleStrToIntStr
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import kotlinx.parcelize.Parcelize
import timber.log.Timber
import java.math.BigDecimal
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2024/3/2114:37
 * @description
 */

/**
 * 支付信息
 */
data class PayInfo(
    // 支付排序
    var sort: Int?,
    // 支付类型
    var payType: Int?,
    // 价格 -- 组合支付时要传入该值   现金支付的不给
    var price: Long?,
    // 线下支付的信息
    var offlinePayment: CashConvertModel? = null,
    // 余额支付的账户信息
    var accountPaymentDTO: AccountPaymentDTO? = null,
    // 在线支付时的网关信息
    var onlinePaymentDTO: OnlinePaymentDTO? = null,
) {
    //是否余额支付
    fun isBalancePay(): Boolean {
        return accountPaymentDTO != null
    }

    fun isOfflinePay(): Boolean {
        return offlinePayment != null
    }

    //是否现金支付
    fun isCash(): Boolean {
        if (offlinePayment != null) {
            val channelId = try {
                offlinePayment?.offlinePayChannelsId?.doubleStrToIntStr()
            } catch (e: Exception) {
                offlinePayment?.offlinePayChannelsId
            }
            return channelId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true
        }
        return false
    }

    /**
     * 获取现金收款的金额
     *
     * @return
     */
    fun getReceiveAmountToTicket(): String {
        return if (offlinePayment?.collectCashDollar != null && offlinePayment!!.collectCashDollar!! > BigDecimal.ZERO && offlinePayment?.collectCash != null && offlinePayment!!.collectCash!! > 0L) {
            "${offlinePayment?.collectCashDollar?.priceFormatTwoDigitZero1()} + ៛${offlinePayment?.collectCash?.decimalFormatZeroDigit()}"
        } else if (offlinePayment?.collectCashDollar != null && offlinePayment!!.collectCashDollar!! > BigDecimal.ZERO) {
            offlinePayment?.collectCashDollar?.priceFormatTwoDigitZero1() ?: ""
        } else if (offlinePayment?.collectCash != null && offlinePayment!!.collectCash!! > 0L) {
            "៛${offlinePayment?.collectCash?.decimalFormatZeroDigit()}"
        } else {
            "$0.00"
        }
    }


    fun getOfflinePayMethod(context: Context): String {
        return if (offlinePayment?.offlinePayChannelsId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true) {
            "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
        } else if (offlinePayment?.offlinePayChannelsId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true) {
            "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        } else {
            "${context.getString(R.string.offline_payments)} - ${offlinePayment?.offlinePayChannelsName}"
        }
    }

    //是否挂账
    private fun isAccountsReceivable(): Boolean {
        val channelId = try {
            offlinePayment?.offlinePayChannelsId?.doubleStrToIntStr()
        } catch (e: Exception) {
            offlinePayment?.offlinePayChannelsId
        }
        return channelId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true
    }

    fun getOfflinePayMethodByLocal(context: Context, locale: Locale): String {
        Timber.e("locale  ${locale.language}")
        return "${context.getStringByLocale(R.string.offline, locale)}-${
            if (isCash()) {
                context.getStringByLocale(R.string.cash, locale)
            } else if (isAccountsReceivable()) {
                context.getStringByLocale(R.string.accounts_receivable, locale)
            } else {
                if (locale.language.startsWith("zh")) {
                    offlinePayment?.offlinePayChannelsName ?: ""
                } else if (locale.language.startsWith("en")) {
                    offlinePayment?.offlinePayChannelsNameEn ?: ""
                } else if (locale.language.startsWith("km")) {
                    offlinePayment?.offlinePayChannelsNameKm ?: ""
                } else {
                    offlinePayment?.offlinePayChannelsName ?: ""
                }
            }
        }"
    }

    fun getOfflinePayMethodByLocal2(context: Context, locale: Locale): String {
        Timber.e("locale  ${locale.language}")

        return "${context.getStringByLocale(R.string.offline, locale)}-${
            if (isCash()) {
                return context.getStringByLocale(R.string.cash, locale)
            }else if (isAccountsReceivable()) {
                context.getStringByLocale(R.string.accounts_receivable, locale)
            } else {
                offlinePayment?.offlinePayChannelsName ?: ""
            }
        }"
    }
}

///**
// * 线下支付信息
// */
//data class OfflinePaymentDTO(
//    var offlinePayChannelsId: Int?,
//    var offlinePayChannelsName: String?,
//    var collectCash: Long?,
//    var changeAmount: Long?,
//    var collectCashDollar: BigDecimal?,
//    var changeAmountDollar: BigDecimal?
//)

/**
 * 余额支付账户信息
 */
data class AccountPaymentDTO(
    var accountId: String?
)

/**
 * 在线支付网关信息
 */
data class OnlinePaymentDTO(
    var paymentGateway: String?
)

data class PaymentReserveRequest(
    var diningStyle: Int?,
    var tableUuid: String?,
    var plan: Boolean,
    //支付类型: 1-线上支付; 2-现金支付; 3-用户余额支付;
    //Payment type: 1-online payment; 2-cash payment; 3-user balance payment;
    var payType: Int,
    //会员账户id（consumer_pay_account表id）
    //Member account id
    var accountId: String? = null,
    var note: String = "",
    var goodsJson: GoodsReserveJsonVo,
    var customerInfoVo: CustomerInfoVo
)


data class GoodsReserveJsonVo(
    var peopleDate: String?,
    var peopleNum: Int?,
    var serviceCharge: Long?,
    var totalPrice: Long?,
    var goodsTotalNum: Int?,
    var goodsReserveVoList: List<GoodsRequest>?
) {


    fun getSubtotal(): Long {
        return (totalPrice ?: 0) - (serviceCharge ?: 0)
    }

}

data class PaymentRequest(
    //就餐方式:0-堂食  1-外带  2-预定  3-外卖
    var diningStyle: Int?,
    var tableUuid: String?,
    //支付类型: 1-线上支付; 2-现金支付; 3-用户余额支付;
    //Payment type: 1-online payment; 2-cash payment; 3-user balance payment;
    var payType: Int? = null,
    //会员账户id（consumer_pay_account表id）
    //Member account id
    var accountId: String? = null,
    //混合支付 传的余额支付多少
    var balancePayAmount: BigDecimal? = null,
    var note: String = "",
    var isPosPrint: Boolean = true,
//    var isReservation: Boolean,
    var isPreOrder: Boolean?,
//    var goodsJson: GoodsJsonVo,
    var goodsList: List<GoodsBo>,
    var customerInfoVo: CustomerInfoVo? = null,
    //Pre-order only required
    var peopleDate: String? = null,
    var peopleNum: Int? = null,
    //线下支付渠道ID Offline payment channel ID
    var offlinePayChannelsId: Int? = null,
    //线下收款渠道名  Offline payment channel name
    var offlinePayChannelsName: String? = null,
    //收取现金 现金支付时使用,瑞尔 Cash payment is accepted when using KHR
    var collectCash: Long? = null,
    //找零金额 现金支付时使用，瑞尔 Change amount is used when paying in cash, KHR
    var changeAmount: Long? = null,
    //收取现金 现金支付时使用,美元 Cash Payment: USD
    var collectCashDollar: BigDecimal? = null,
    //找零金额 现金支付时使用，美元 Change amount used when paying in cash, USD
    var changeAmountDollar: BigDecimal? = null,


    //@Schema(description = "类型 0:默认 1.销售价 2.会员 3:销售价+会员价 4.配置折扣活动 5.USD自定义整单减免 6.KHR自定义整单减免")
    var reduceType: Int? = null,
    //减免百分比
    val reduceRate: Double? = null,
    //减免金额（美元）
    val reduceDollar: BigDecimal? = null,
    //减免vip金额（美元）
    val reduceVipDollar: BigDecimal? = null,
    //减免金额（khr）
    val reduceKhr: BigDecimal? = null,
    //减免vip金额（khr）
    val reduceVipKhr: BigDecimal? = null,

    //减免原因：0：discount(正常折扣) 1:void(菜品有问题时原因必填写)
    val discountType: Int? = null,
    //原因
    var reduceReason: String? = null,
    //折扣id
    val discountReduceActivityId: String? = null,


    //优惠券码
    var couponCode: String? = null,

    //订单号
    var orderNo: String? = null,

    //单品减免原因
    var singleReduceReason: String? = null,

    //就餐方式为外卖，外卖平台id要有值
    var deliveryPlatformId: String? = null,

    //外卖订单号
    var deliveryOrderNo: String? = null,

    var consumerPayRegisterInfo: ConsumerPayRegisterInfo? = null,//会员注册信息

    var isCredit: Boolean = false,    //是否挂账

    var creditReason: String? = null,//挂账原因

    //支付信息列表
    var payInfoList: List<PayInfo>? = null,

    @SerializedName("dataVersion")
    var dataVersion: Long = 0,

    )

data class ConsumerPayRegisterInfo(
    var telephone: String? = null,    //电话<账号>
    var nickName: String? = null, //用户昵称
)

@Parcelize
data class GoodsBo(
    var id: String?,
    var num: Int?,

    var tagItemId: String?,
    var feeds: List<FeedBo>?,
    var pricingMethod: Int?,
    var weight: Double?,
    var uuid: String?,
    var discountPrice: Long?,
    //商品类型:0-普通商品， 1-临时商品"
    var goodsType: Int?,
    /**
     * 单品减免
     */
    var singleDiscountGoods: SingleDiscountGoods? = null,

    /**
     * 套餐内容
     */
    var mealSetGoodsDTOList: List<MealSetGood>? = null,

    /**
     * 请求给服务端的单品折扣参数
     */
    var singleItemDiscount: SingleDiscountRequest? = null,

    /**
     * 请求给服务端的本地生成的hashKey
     */
    var goodsHashKey: String?,
    /**
     * 备注
     */
    var note: String?,

    /**
     * pricingCompleted sellPrice vipPrice  时价菜才传
     */
    var pricingCompleted: Boolean? = null,
    var sellPrice: Long? = null,
    var vipPrice: Long? = null,

    //本地用的
    var isProcessed: Boolean? = false,


    var name: String? = null

) : Parcelable {


}

@Parcelize
data class FeedBo(
    var id: String?,
    var num: Int?
) : Parcelable

//套餐请求的model
@Parcelize
data class MealSetGood(
    var mealSetGroupId: String?,
    var mealSetGoodsId: String?,
    var number: Int?,
    val tagItemIds: String?,

    //TODO 2.16.10 新增字段
    //商品重量(称重商品使用)
    val weight: Double?,
    //商品唯一标识(称重商品使用)
    val uuid: String?,
) : Parcelable

data class GoodsJsonVo(
    var peopleDate: String?,
    var peopleNum: Int?,
    var serviceCharge: Long?,
    var totalPrice: Long?,
    var goodsTotalNum: Int?,
    var goodsList: List<GoodsRequest>?
) {
    fun getSubtotal(): Long {
        return (totalPrice ?: 0) - (serviceCharge ?: 0)
    }
}

data class CustomerInfoVo(
    var name: String?,
    var areaCode: String?,
    var mobile: String?,
    var diningNumber: Int?,
    var diningTime: String?
) {

    fun getMobilePhone(): String {
        return areaCode + mobile
    }

    fun getMobilePhoneDisplay(): String {
        return "+$areaCode $mobile"
    }
}


data class PendingRequest(
    var diningStyle: Int?,
    /**
     * 存单备注
     */
    var note: String? = "",
//    var isReservation: Boolean,
    var isPreOrder: Boolean,
    var goodsList: List<GoodsBo>,
    var customerInfoVo: CustomerInfoVo,
    var serialNumber: String?,
    /**
     * 订单的备注
     */
    var orderNote: String? = "",

    var tableUuid: String? = null
)