package com.metathought.food_order.casheir.data.model.base.response_model.cart


import android.content.Context
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PricingMethodEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.ActivityLabel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.SingleItemDiscount
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods as OrderGoods
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.OrderHelper
import timber.log.Timber
import java.math.BigDecimal

data class Goods(
    @SerializedName("orderId")
    val orderId: String?,

    @SerializedName("cartsId")
    val cartsId: String?,
    @SerializedName("feeds")
    val feeds: List<Feed>?,
    @SerializedName("finalSinglePrice")
    val finalSinglePrice: Long?,
    @SerializedName("finalVipPrice")
    val finalVipPrice: Long?,
    @SerializedName("finalVipVatPrice")
    val finalVipVatPrice: Long?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("labels")
    val labels: Any?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("num")
    val num: Int?,
    @SerializedName("picUrl")
    val picUrl: String?,
    @SerializedName("sellPrice")
    var sellPrice: Long?,
    @SerializedName("vatPercentage")
    var vatPercentage: Int? = null,
    @SerializedName("vatWhitelisting")
    val vatWhitelisting: Boolean?,
    @SerializedName("serviceChargeWhitelisting")
    val serviceChargeWhitelisting: Boolean?,
    @SerializedName("tagItems")
    val tagItems: List<GoodsTagItem>?,
    @SerializedName("vipPrice")
    var vipPrice: Long?,
    @SerializedName("packingFee")
    val packingFee: Int?,
    /**
     * 计价方式
     * WHOLE_UNIT(0, "整份计费", ""),
     * PER_KILOGRAM(1, "每公斤", "KG"),
     * PER_POUND(2, "每磅", "LB"),
     * PER_LITER(3, "每升", "L"),
     * PER_OUNCE(4, "每盎司", "OZ"),
     * PER_GALLON(5, "每加仑", "GAL"),
     * PER_GRAM(6, "每克", "G");
     */
    @SerializedName("pricingMethod")
    val pricingMethod: Int? = null,
    /**
     * 重量
     */
    @SerializedName("weight")
    val weight: Double?,

    //TODO 2.16.10 新增字段
    @SerializedName("uuid")
    val uuid: String?,
    //菜品类型: 0-单品，1-固定套餐，2-可选套餐
    @SerializedName("type")
    val type: Int? = null,

    //堂食服务费
    @SerializedName("finalServiceCharge")
    val finalServiceCharge: Long?,
    //折扣堂食服务费
    @SerializedName("finalDiscountServiceCharge")
    val finalDiscountServiceCharge: Long?,
    //Vip堂食服务费
    @SerializedName("finalVipServiceCharge")
    val finalVipServiceCharge: Long?,

//    @SerializedName("withVipPrice")
//    val withVipPrice: Boolean?,

    //单个商品折扣价 ，如果有这个价格就把原价展示为划线价
    @SerializedName("discountPrice")
    var discountPrice: Long? = null,

    @SerializedName("finalDiscountPrice")
    val finalDiscountPrice: Long?,
    @SerializedName("finalDiscountVatPrice")
    val finalDiscountVatPrice: Long?,

    /**
     * 商品参与优惠活动标签
     */
    @SerializedName("activityLabels")
    var activityLabels: List<ActivityLabel>? = null,

    //商品类型:0-普通商品， 1-临时商品"
    @SerializedName("goodsType")
    var goodsType: Int? = null,

    @SerializedName("finalSingleGoodsReducePrice")
    var finalSingleGoodsReducePrice: Long? = null,

    @SerializedName("finalSingleGoodsReduceServiceCharge")
    var finalSingleGoodsReduceServiceCharge: Long? = null,

    @SerializedName("finalSingleGoodsReduceVatPrice")
    var finalSingleGoodsReduceVatPrice: Long? = null,

    @SerializedName("finalSingleGoodsReduceVipPrice")
    var finalSingleGoodsReduceVipPrice: Long? = null,

    @SerializedName("finalSingleGoodsReduceVipServiceCharge")
    var finalSingleGoodsReduceVipServiceCharge: Long? = null,

    @SerializedName("finalSingleGoodsReduceVipVatPrice")
    var finalSingleGoodsReduceVipVatPrice: Long? = null,


    /**
     *总最终单品价(包含单品减免*数量)
     */

    @SerializedName("totalSingleGoodsReducePrice")
    var totalSingleGoodsReducePrice: Long? = null,

    /**
     *总vip-最终单品价(包含单品减免*数量)
     */
    @SerializedName("totalSingleGoodsReduceVipPrice")
    var totalSingleGoodsReduceVipPrice: Long? = null,

    /**
     *总最终单品附加税(单品减免*数量)
     */

    @SerializedName("totalSingleGoodsReduceVatPrice")
    var totalSingleGoodsReduceVatPrice: Long? = null,

    /**
     *总vip-最终单品附加税(单品减免*数量)
     */
    @SerializedName("totalSingleGoodsReduceVipVatPrice")
    var totalSingleGoodsReduceVipVatPrice: Long? = null,

    /**
     *总最终单品服务费(单品减免*数量)
     */

    @SerializedName("totalSingleGoodsReduceServiceCharge")
    var totalSingleGoodsReduceServiceCharge: Long? = null,

    /**
     *总vip-最终单品服务费(单品减免*数量)
     */
    @SerializedName("totalSingleGoodsReduceVipServiceCharge")
    var totalSingleGoodsReduceVipServiceCharge: Long? = null,

    /**
     * 套餐内容
     */
    @SerializedName("orderMealSetGoodsDTOList")
    var orderMealSetGoodsDTOList: List<OrderMealSetGood>? = null,

    @SerializedName("hashKey")
    var hashKey: String? = null,

    //单品减免信息
    @SerializedName("singleItemDiscount")
    val singleItemDiscount: SingleItemDiscount? = null,

    /**
     * 佣金比例
     */
    @SerializedName("commissionPercentage")
    val commissionPercentage: BigDecimal? = null,

    /**
     * 备注
     */
    @SerializedName("note")
    val note: String? = null,

    /**
     * 是否已称重/时价菜已经设置价格
     */
    @SerializedName("isProcessed")
    var isProcessed: Boolean? = true,

    /**
     * 是否称重完成true是false否
     */
    @SerializedName("weighingCompleted")
    var weighingCompleted: Boolean? = false,

    /**
     * 是否时价菜定价定价完成
     */
    @SerializedName("pricingCompleted")
    var pricingCompleted: Boolean? = false,

    /**
     * 是否时价菜
     */
    @SerializedName("currentPrice")
    var currentPrice: Boolean? = true,

    /**
     * 打包费显示，0:独立显示，1:计入商品价格
     */
    @SerializedName("packingFeeDisplay")
    val packingFeeDisplay: Boolean? = null,

    ) {

    fun getHash(): String {
        return HashHelper.getHash(
            ArrayList(feeds ?: emptyList()),
            ArrayList(tagItems ?: emptyList()),
            orderMealSetGoodList = orderMealSetGoodsDTOList ?: emptyList(),
            goodsId = id ?: "",
            singleItemDiscount = singleItemDiscount?.toCartSingleDiscountGood(),
            goodsPriceKey = "",
            note = note,
            uuid = uuid
        )
    }
//    /**
//     * 套餐内容已选描述
//     *
//     * @return
//     */
//    private fun getSelectMealSetGoodStr(): String {
//        var desc = ""
//        orderMealSetGoodsDTOList?.forEach { item ->
//            val number = item.number ?: 0
//            if (number >= 0) {
//                var name = item.mealSetGoodsName
//                var tagStr = ""
//                item.mealSetTagItemList?.forEach { tag ->
//                    if (tagStr.isEmpty()) {
//                        tagStr = "${tag.name}"
//                    } else {
//                        tagStr = "${tagStr},${tag.name}"
//                    }
//                }
//
//                if (tagStr.isNotEmpty()) {
//                    tagStr = "(${tagStr})"
//                }
//                if (number > 1) {
//                    tagStr = "${tagStr}x${number}"
//                }
//
//                if (desc.isEmpty()) {
//                    desc = "${name}${tagStr}"
//                } else {
//                    desc = "${desc},${name}${tagStr}"
//                }
//            }
//        }
//        return desc
//    }

    fun getFinalNote(): String {
        return (note?.replace(";<br/>", "\n") ?: "").trimStart()
    }

    fun getGoodsTagStr(): String {
        val sbf = StringBuffer()

        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            Timber.e("获取已选套餐 ${orderMealSetGoodsDTOList?.size}")
            sbf.append(OrderHelper.getSelectMealSetGoodStr(orderMealSetGoodsDTOList))
        } else {
            tagItems?.let {
                if (it.isNotEmpty()) {
                    for (i in it.indices) {
                        sbf.append(it[i].name)
                        if (i != it.size - 1)
                            sbf.append(", ")
                    }
                }
            }

            feeds?.let {
                if (it.isNotEmpty()) {
                    if (tagItems?.isNotEmpty() == true) {
                        sbf.append(", ")
                    }
                    for (i in it.indices) {
                        sbf.append(it[i].name + " x" + it[i].alreadyNum)
                        if (i != it.size - 1)
                            sbf.append(", ")
                    }
                }
            }
        }
        return sbf.toString()
    }

    //获取现价 如果有折扣价是折扣价， 没有就是原价
    fun getCurrentSinglePrice(): Long {
        var price = (finalSinglePrice ?: 0)
        if (isHasDiscountPrice()) {
            price = (finalDiscountPrice ?: 0)
        }
        return price
    }

    fun totalSalePrice(): Long {
        return (finalSinglePrice ?: 0) * (num ?: 0)
    }

    fun totalPrice(): Long {
        if (totalSingleGoodsReducePrice != null) {
            return totalSingleGoodsReducePrice ?: 0L
        }
        return (getCurrentSinglePrice() ?: 0) * (num ?: 0)
    }

    fun totalVipPrice(): Long {
        var price = (finalVipPrice ?: 0L) * (num ?: 0)
        if (totalSingleGoodsReduceVipPrice != null) {
            price = totalSingleGoodsReduceVipPrice ?: 0
        }
        return price
    }

    fun isShowVipPrice(): Boolean {
        if (vipPrice == null) {
            return false
        }
        return (vipPrice ?: 0) >= 0 && (vipPrice ?: 0) != (sellPrice
            ?: 0L)   //&& withVipPrice == true
    }


    /**
     * 是否时价菜
     *
     * @return
     */
    fun isTimePriceGood(): Boolean {
        if (currentPrice == null) {
            return false
        }
        return currentPrice == true
    }

    /**
     * 是否已经设置时价菜价格
     *
     * @return
     */
    fun isHasCompletePricing(): Boolean {
        return pricingCompleted ?: false
    }

    /**
     *  设置是否已经设置时价
     *
     * @return
     */
    fun setPriceCompletedFlag(flag: Boolean) {
        pricingCompleted = flag
    }

    /**
     * 是否称重菜
     *
     * @return
     */
    fun isToBeWeighed(): Boolean {
        if (isMealSet()) {
            val mealSetGroupList = orderMealSetGoodsDTOList ?: return false
            val isToBeWeighed = mealSetGroupList?.firstOrNull { it.isToBeWeighed() }
            if (isToBeWeighed != null) {
                return true
            } else {
                return false
            }
        }
        return (pricingMethod ?: 0) > PricingMethodEnum.WHOLE_UNIT.id
    }

    /**
     * 是否已经设置重量
     *
     * @return
     */
    fun isHasCompleteWeight(): Boolean {
        if (isMealSet()) {
            val goods = orderMealSetGoodsDTOList?.firstOrNull { !it.isHasCompleteWeight() }
            return goods == null
        } else {
            if (weighingCompleted == null) {
                return ((weight ?: 0.0) > 0.0)
            }
            return weighingCompleted ?: false
        }
    }

    /**
     *  设置是否已经设置重量
     *
     * @return
     */
    fun setWeighingCompletedFlag(flag: Boolean) {
        weighingCompleted = flag
    }

    fun getWeightStr(): String {
        if (weight?.isInt() == true) {
            return "${weight.toInt()}${getWeightUnit()}"
        }
        return "${weight ?: 0}${getWeightUnit()}"
    }

    /**
     * 是否套餐
     */
    fun isMealSet(): Boolean {
        return isSelectedMealSet() || isFixedMealSet()
    }

    /**
     * 是否可选套餐
     */
    fun isSelectedMealSet(): Boolean {
        return type == 2
    }

    /**
     * 是否固定套餐
     */
    fun isFixedMealSet(): Boolean {
        return type == 1
    }

    private fun getWeightUnit(): String {
        return PricingMethodEnum.entries.find {
            it.id == (pricingMethod ?: 0)
        }?.unit ?: ""
    }

    /**
     * 是否需要称重
     *
     * @return
     */
    fun isHasNeedWeight(): Boolean {
        val isNeedWeight = isToBeWeighed() && !isHasCompleteWeight()
        return isNeedWeight
    }

    fun isHasProcessed(): Boolean {
        isProcessed = true
        if (isTimePriceGood()) {
            /**
             * 如果过是时价菜
             */
            //如果时价菜还未设置价格
            if (!isHasCompletePricing()) {
                isProcessed = false
            }
            //如果称重时价菜还未设置重量
            if (isToBeWeighed() && !isHasCompleteWeight()) {
                isProcessed = false
            }
        } else if (isToBeWeighed()) {
            //如果称重菜 还未设置重量
            if (!isHasCompleteWeight()) {
                isProcessed = false
            }
        }
        return isProcessed == true
    }

    fun getShowPrice(context: Context): String {
        if (isTimePriceGood()) {
            if (isHasCompletePricing() && isToBeWeighed() && !isHasCompleteWeight()) {
                return "${context.getString(R.string.to_be_weighed)}"
            }

            return "${context.getString(R.string.time_price)}"
        }
        if (isToBeWeighed() && !isHasCompleteWeight()) {
            return "${context.getString(R.string.to_be_weighed)}"
        }
        //下面不会走到了
        Timber.e("getShowPrice :  ${totalPrice()}")
        return "${totalPrice()?.priceFormatTwoDigitZero2()}"
    }


    //是否有折扣价
    fun isHasDiscountPrice(): Boolean {
        return discountPrice != null
    }

    fun getTotalService(): Long {
        return (finalServiceCharge ?: 0L).times(num ?: 0)
    }

    fun getTotalVipService(): Long {
        var price = (finalVipServiceCharge ?: 0L).times(num ?: 0)
//        if (finalSingleGoodsReduceVipServiceCharge != null && finalSingleGoodsReduceVipServiceCharge != finalVipServiceCharge) {
//            price = finalSingleGoodsReduceVipServiceCharge ?: 0
//        }
        if (totalSingleGoodsReduceVipServiceCharge != null) {
            price = totalSingleGoodsReduceVipServiceCharge ?: 0L
        }
        return price
    }

    fun getTotalDiscountService(): Long {
        var price = (finalDiscountServiceCharge ?: 0L).times(num ?: 0)
        if (totalSingleGoodsReduceServiceCharge != null) {
            price = totalSingleGoodsReduceServiceCharge ?: 0L
        }
        return price
    }

    /**
     * 计算佣金
     *
     */
//    fun getTotalCommissionPrice(): Long {
//        return BigDecimal(getCurrentSinglePrice()).times(commissionPercentage ?: BigDecimal.ZERO)
//            .divide(BigDecimal(100))
//            .halfUp(0).times(
//                BigDecimal(num ?: 0)
//            ).toLong()
//    }


    private fun toOrderGoods(): OrderGoods {
        return MyApplication.globalGson.fromJson<OrderGoods>(
            this.toJson(),
            object : TypeToken<OrderGoods>() {}.type
        ) as OrderGoods
    }

    fun toGoodsRequestModel(): GoodsRequest {

        val good = toOrderGoods()
//        good.isOrderMoreGoods = true
        /**
//         * 转的时候把重量 计算上去
//         */
//        if ((pricingMethod ?: 0) > 0 && weight != null) {
//            if (discountPrice != null) {
//                good.discountPrice =
//                    ((discountPrice ?: 0) * (weight.times(1000.0)).toLong()).div(1000)
//            }
//            good.sellPrice = ((sellPrice ?: 0) * (weight.times(1000.0)).toLong()).div(1000)
//            good.vipPrice = ((vipPrice ?: 0) * (weight.times(1000.0)).toLong()).div(1000)
//        }

        /**
         *  把单品减免的金额也放进去
         */
//        if (finalSingleGoodsReducePrice != null && finalSingleGoodsReducePrice != finalDiscountPrice) {
//            good.discountPrice = finalSingleGoodsReducePrice
//        }
//
//        if (finalSingleGoodsReduceVipPrice != null && finalSingleGoodsReduceVipPrice != finalVipPrice) {
//            good.vipPrice = finalSingleGoodsReduceVipPrice
//        }

        return GoodsRequest(
            num = num,
            feedInfoList = feeds?.toMutableList()?.let { ArrayList(it) },
            goodsTagItems = tagItems?.toMutableList()?.let { ArrayList(it) },
            goods = good,
            finalSinglePrice = this.finalSinglePrice,
            orderMealSetGoodList = orderMealSetGoodsDTOList,
            singleDiscountGoods = singleItemDiscount?.toCartSingleDiscountGood(),
            note = note
        )
    }
}