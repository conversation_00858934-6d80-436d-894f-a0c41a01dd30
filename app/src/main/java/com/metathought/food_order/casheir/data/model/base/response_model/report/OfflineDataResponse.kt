package com.metathought.food_order.casheir.data.model.base.response_model.report

data class UnreadCashierMsgResponse(
    val id: String?,   //消息id
    val orderId: String?,   // 订单id
    val type: Int?,
    val content: String?,// 消息内容
    val isAck: Boolean?,
//  val   saasStoreId:String,   //: "1",
//"createTime": "2024/12/10 17:23:50",
//"updateTime": "2024/12/10 17:23:50",
//"storeId": "1813775134527918082",
//  "retryNum": 0
)
