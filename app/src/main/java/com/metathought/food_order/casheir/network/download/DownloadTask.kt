package com.metathought.food_order.casheir.network.download


import io.reactivex.disposables.Disposable

class DownloadTask {
    var disposable: Disposable? = null
    var localFilePath: String? = null
    private val listeners = arrayListOf<DownloadListener>()
    //用于防止同一页面重复添加回调事件
    private val sourceTagSet = mutableSetOf<String>()

    fun cancel() {
        listeners.clear()
        disposable?.dispose()
        localFilePath = null
    }

    fun register(sourceTag: String?, listener: DownloadListener) {
        if (sourceTag != null) {
            if(sourceTagSet.contains(sourceTag)){
                return
            }
            sourceTagSet.add(sourceTag)
        }
        listeners.add(listener)
    }

    fun remove(listener: DownloadListener) {
        listeners.remove(listener)
    }

    fun loadFailure(message: String) {
        listeners.forEach {
            it.onFail(message)
        }
    }

    fun progress(progress: Long, max: Long) {
        listeners.forEach {
            it.onProgress(progress, max)
        }
    }

    fun loadSuccess() {
        listeners.forEach {
            it.onSuccess(localFilePath ?: "")
        }
    }
}