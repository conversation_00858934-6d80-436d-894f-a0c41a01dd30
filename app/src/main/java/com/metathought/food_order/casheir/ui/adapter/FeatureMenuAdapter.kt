package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.bingoogolapple.badgeview.BGABadgeViewHelper
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.data.model.FeatureMenu
import com.metathought.food_order.casheir.databinding.FeaturesItemsBinding
import com.metathought.food_order.casheir.helper.UnReadAndUnPrintHelper
import com.metathought.food_order.casheir.ui.adapter.FeatureMenuAdapter.FeatureMenuViewHolder
import com.metathought.food_order.casheir.utils.SingleClickUtils
import okhttp3.internal.notifyAll
import timber.log.Timber


class FeatureMenuAdapter(
    val list: ArrayList<FeatureMenu>,
    val context: Context,
    val onClickCallback: (FeatureMenu) -> Unit
) : RecyclerView.Adapter<FeatureMenuViewHolder>() {


    inner class FeatureMenuViewHolder(val binding: FeaturesItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: FeatureMenu?) {
            resource?.let { it1 ->
                //prevent double click on same menu

                binding.apply {
                    rootView.setBackgroundResource(
                        if (it1.isSelected == true) R.drawable.background_white_16 else ContextCompat.getColor(
                            context,
                            android.R.color.transparent
                        )
                    )
                    badgeFrame.badgeViewHelper.setBadgeGravity(BGABadgeViewHelper.BadgeGravity.RightTop)
                    badgeFrame.badgeViewHelper.setBadgeTextSizeSp(12)

//                    Timber.e("badgePadding: ${badgeFrame.badgeViewHelper.badgePadding}")
                    badgeFrame.badgeViewHelper.setBadgePaddingDp(3)
                    if (FeatureMenuEnum.ORDER_MANAGEMENT.id == it1.id) {
                        badgeFrame.showTextBadge(UnReadAndUnPrintHelper.getAllUnReadNumStr())
                        if (UnReadAndUnPrintHelper.getAllUnReadNum() == 0) {
                            badgeFrame.hiddenBadge()
                        }
                    } else if (FeatureMenuEnum.RECEIVING_ORDER.id == it1.id) {
                        badgeFrame.showTextBadge(UnReadAndUnPrintHelper.getUnReceiveOrderNumStr())
                        if (UnReadAndUnPrintHelper.getUnReceiveOrderNum() == 0) {
                            badgeFrame.hiddenBadge()
                        }
                    } else if (FeatureMenuEnum.NOTICE.id == it1.id) {
                        badgeFrame.showTextBadge(UnReadAndUnPrintHelper.getNoticeUnReadNumStr())
                        if (UnReadAndUnPrintHelper.getNoticeUnReadNum() == 0) {
                            badgeFrame.hiddenBadge()
                        }
                    } else {
                        badgeFrame.hiddenBadge()
                    }
                    val badgeText = (badgeFrame.badgeViewHelper.badgeText ?: "")
                    badgeFrame.badgeViewHelper.setBadgeVerticalMarginDp(0)
                    if (badgeText.length < 2) {
                        badgeFrame.badgeViewHelper.setBadgeHorizontalMarginDp(12)
                    } else if (badgeText.length in 2 until 3) {
                        badgeFrame.badgeViewHelper.setBadgeHorizontalMarginDp(10)
                    } else {
                        badgeFrame.badgeViewHelper.setBadgeHorizontalMarginDp(0)
                    }




                    imgIcon.setImageResource(it1.iconDrawable)
                    tvTitle.text = it1.title
//                    imgIcon.setColorFilter(
//                        if (it1.isSelected == true) ContextCompat.getColor(
//                            context,
//                            R.color.primaryColor
//                        ) else ContextCompat.getColor(context, R.color.white)
//                    )
//
//                    tvTitle.setTextColor(
//                        if (it1.isSelected == true) ContextCompat.getColor(
//                            context,
//                            R.color.primaryColor
//                        ) else ContextCompat.getColor(context, R.color.white)
//                    )
                    imgIcon.setColorFilter(ContextCompat.getColor(context, R.color.white))
                    tvTitle.setTextColor(ContextCompat.getColor(context, R.color.white))

                    root.setOnClickListener {
                        val selectedID = list.firstOrNull { it.isSelected == true }?.id
                        if (selectedID != it1.id) {
                            SingleClickUtils.isFastDoubleClick(1000) {
                                if (resource.id != FeatureMenuEnum.OPEN_CASH_BOX.id && resource.id != FeatureMenuEnum.NOTICE.id) {
                                    updateSelectedState(resource)
                                }
                                onClickCallback(it1)
                            }
                        }
                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FeatureMenuViewHolder {
        return FeatureMenuViewHolder(
            FeaturesItemsBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    private fun updateSelectedState(resource: FeatureMenu?) {
        list.forEach {
            it.isSelected = false
        }
        resource?.isSelected = true
        notifyDataSetChanged()
    }

    fun clearSelect() {
        list.forEach {
            it.isSelected = false
        }
        notifyDataSetChanged()
    }

    fun updateSelectedState(id: Int) {
        list.forEach {
            it.isSelected = id == it.id
        }

        notifyDataSetChanged()
    }

    //更新订单item
    fun updateOrderItem() {
        val index = list.indexOfFirst { it.id == FeatureMenuEnum.ORDER_MANAGEMENT.id }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }

    fun updateAcceptOrderItem() {
        val index = list.indexOfFirst { it.id == FeatureMenuEnum.RECEIVING_ORDER.id }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }

    fun updateNoticeItem() {
        val index = list.indexOfFirst { it.id == FeatureMenuEnum.NOTICE.id }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }


    override fun getItemCount(): Int {
        return list.count()
    }

    override fun onBindViewHolder(holder: FeatureMenuViewHolder, position: Int) {
        holder.bind(list[position])
    }

    fun updateItems(newItems: ArrayList<FeatureMenu>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

}