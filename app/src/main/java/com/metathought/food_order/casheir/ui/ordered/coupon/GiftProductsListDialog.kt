package com.metathought.food_order.casheir.ui.ordered.coupon

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.databinding.DialogGiftProductListBinding
import com.metathought.food_order.casheir.ui.adapter.CouponGoodListAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


/**
 *<AUTHOR>
 *@time  2024/8/25
 *@desc 赠送商品弹窗
 **/
@AndroidEntryPoint
class GiftProductsListDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "GiftProductsListDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            giftGoods: List<UsageGoods>? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(giftGoods)

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            giftGoods: List<UsageGoods>? = null
        ): GiftProductsListDialog {
            val fragment = GiftProductsListDialog()
            fragment.giftGoods = giftGoods
            return fragment
        }
    }

    private var binding: DialogGiftProductListBinding? = null

    private var giftGoods: List<UsageGoods>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogGiftProductListBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {

    }

    private fun initView() {

    }


    private fun initListener() {
        binding?.apply {
            rvList.adapter = CouponGoodListAdapter(giftGoods ?: listOf(),true)

            topBar.getCloseBtn()?.setOnClickListener { dismissAllowingStateLoss() }
        }
    }

}