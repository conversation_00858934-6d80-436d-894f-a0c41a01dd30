package com.metathought.food_order.casheir.database.dao

import com.metathought.food_order.casheir.constant.SingleDiscountReasonTypeEnum
import com.metathought.food_order.casheir.constant.SingleDiscountType
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.database.HashRecord
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.toHexStr
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import org.litepal.LitePal
import org.litepal.extension.findFirst
import timber.log.Timber
import java.util.UUID
import kotlin.random.Random


/**
 * <AUTHOR>
 * @date 2024/3/1916:09
 * @description
 */
object HashHelper {

    /**
     * 这个类下的hashkey 是本地购物车下使用
     *
     * @param feedList
     * @param goodsTagItemList
     * @param orderMealSetGoodList
     * @param goodsId
     * @param singleItemDiscount
     * @param goodsPriceKey
     * @param note
     * @return
     */
    fun getHash(
        feedList: ArrayList<Feed>?,
        goodsTagItemList: ArrayList<GoodsTagItem>?,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        goodsId: String,
        singleItemDiscount: SingleDiscountGoods?,
        goodsPriceKey: String,
        note: String?,
        uuid: String?,
    ): String {
        val sbf = StringBuffer()
        sbf.append(goodsId)
        if (uuid != null) {
            sbf.append(uuid)
        }

        // 获取商品设置过单品减免、折扣、改价类型+原因key下划线拼接,没设置过单品改价则返回空字符串
        if (singleItemDiscount != null) {
            val discountTypeRemarkKey =
                "${singleItemDiscount.discountType ?: SingleDiscountReasonTypeEnum.DISCOUNT.code}${singleItemDiscount.remark}${singleItemDiscount.discountReduceActivityId ?: ""}"
            sbf.append("$discountTypeRemarkKey")
            if (singleItemDiscount.type == SingleDiscountType.PERCENTAGE.id) {
                if (singleItemDiscount.reduceRatio != null) {
                    sbf.append("${singleItemDiscount.reduceRatio?.decimalFormatTwoDigitZero2()}${singleItemDiscount.type}")
                }
                val singleItemDiscountUUid = if (singleItemDiscount.uuid.isNullOrEmpty()) {
                    UUID.randomUUID().toString()
                } else {
                    singleItemDiscount.uuid
                }
                sbf.append(singleItemDiscountUUid)
            } else if (singleItemDiscount.type == SingleDiscountType.FIXED_AMOUNT.id) {
                if (singleItemDiscount.saleReduce != null || singleItemDiscount.vipReduce != null) {
                    sbf.append(singleItemDiscount.type)
                }
                if (singleItemDiscount.discountReduceActivityId != null) {
                    //如果是选择后台减免的那就加个随机数不合并
                    val singleItemDiscountUUid = if (singleItemDiscount.uuid.isNullOrEmpty()) {
                        UUID.randomUUID().toString()
                    } else {
                        singleItemDiscount.uuid
                    }
                    sbf.append(singleItemDiscountUUid)
                }
            } else if (singleItemDiscount.type == SingleDiscountType.MODIFY_PRICE.id) {
                if (singleItemDiscount.adjustSalePrice != null) {
                    sbf.append(
                        "${
                            (singleItemDiscount.adjustSalePrice ?: 0.0).times(100.0).toLong()
                        }"
                    )
                }
                if (singleItemDiscount.adjustVipPrice != null) {
                    sbf.append(
                        "${
                            (singleItemDiscount.adjustVipPrice ?: 0.0).times(100.0).toLong()
                        }"
                    )
                }
                if (singleItemDiscount.adjustSalePrice != null || singleItemDiscount.adjustVipPrice != null) {
                    sbf.append(singleItemDiscount.type)
                }
            }
        }

        if (goodsPriceKey.isNotEmpty()) {
            sbf.append("${goodsPriceKey}")
        }

        if (!feedList.isNullOrEmpty()) {
            feedList.sortedBy { it.id }.forEach {
                sbf.append("${it.id}${it.alreadyNum}")
            }
        }

        if (!goodsTagItemList.isNullOrEmpty()) {
            goodsTagItemList.sortedBy { it.id }.forEach {
                sbf.append(it.id)
            }
        }

        //先对套餐内商品分组排序， 在对分组内商品排序,在拼接分组id+商品id+数量+uuid+规格（排序）
        if (!orderMealSetGoodList.isNullOrEmpty()) {
            orderMealSetGoodList.sortedWith(
                compareBy<OrderMealSetGood> { it.mealSetGroupId }
                    .thenBy { it.mealSetGoodsId }
            ).forEach {
                sbf.append(it.mealSetGroupId)
                sbf.append(it.mealSetGoodsId)
                sbf.append(it.number)
                if (!it.uuid.isNullOrEmpty()) {
                    sbf.append(it.uuid)
                }
                //获取 套餐内商品的tagItems 列表，排序后拼接成字符串
                if (!it.mealSetTagItemList.isNullOrEmpty()) {
                    it.mealSetTagItemList?.sortedBy { it.id }?.forEach {
                        sbf.append(it.id)
                    }
                }
            }
        }

        if (!note.isNullOrEmpty()) {
            sbf.append(note)
        }

        Timber.e("最后要hash的字符串 ${sbf.toString()}")
        val hash = sbf.toString().toHexStr()
        Timber.e("最后要hash结果 ${hash}")
        return hash
    }

    fun getHashByTagStr(
        feedList: ArrayList<FeedBo>?,
        goodsTagItemStr: String?,
        mealSetGoodList: List<MealSetGood>?,
        goodsId: String,
        singleItemDiscount: SingleDiscountGoods?,
        goodsPriceKey: String,
        note: String?,
        uuid: String?,
    ): String {
        val sbf = StringBuffer()
        sbf.append(goodsId)
        if (uuid != null) {
            sbf.append(uuid)
        }

        // 获取商品设置过单品减免、折扣、改价类型+原因key下划线拼接,没设置过单品改价则返回空字符串
        if (singleItemDiscount != null) {
            val discountTypeRemarkKey =
                "${singleItemDiscount.discountType ?: SingleDiscountReasonTypeEnum.DISCOUNT.code}${singleItemDiscount.remark}${singleItemDiscount.discountReduceActivityId ?: ""}"
            sbf.append("$discountTypeRemarkKey")
            if (singleItemDiscount.type == SingleDiscountType.PERCENTAGE.id) {
                if (singleItemDiscount.reduceRatio != null) {
                    sbf.append("${singleItemDiscount.reduceRatio?.decimalFormatTwoDigitZero2()}${singleItemDiscount.type}")
                }
                val singleItemDiscountUUid = if (singleItemDiscount.uuid.isNullOrEmpty()) {
                    UUID.randomUUID().toString()
                } else {
                    singleItemDiscount.uuid
                }
                sbf.append(singleItemDiscountUUid)
            } else if (singleItemDiscount.type == SingleDiscountType.FIXED_AMOUNT.id) {
                if (singleItemDiscount.saleReduce != null || singleItemDiscount.vipReduce != null) {
                    sbf.append(singleItemDiscount.type)
                }
                if (singleItemDiscount.discountReduceActivityId != null) {
                    //如果是选择后台减免的那就加个随机数不合并
                    val singleItemDiscountUUid = if (singleItemDiscount.uuid.isNullOrEmpty()) {
                        UUID.randomUUID().toString()
                    } else {
                        singleItemDiscount.uuid
                    }
                    sbf.append(singleItemDiscountUUid)
                }
            } else if (singleItemDiscount.type == SingleDiscountType.MODIFY_PRICE.id) {
                if (singleItemDiscount.adjustSalePrice != null) {
                    sbf.append(
                        "${
                            (singleItemDiscount.adjustSalePrice ?: 0.0).times(100.0).toLong()
                        }"
                    )
                }
                if (singleItemDiscount.adjustVipPrice != null) {
                    sbf.append(
                        "${
                            (singleItemDiscount.adjustVipPrice ?: 0.0).times(100.0).toLong()
                        }"
                    )
                }
                if (singleItemDiscount.adjustSalePrice != null || singleItemDiscount.adjustVipPrice != null) {
                    sbf.append(singleItemDiscount.type)
                }
            }
        }


        if (goodsPriceKey.isNotEmpty()) {
            sbf.append("${goodsPriceKey}")
        }

        if (!feedList.isNullOrEmpty()) {
            feedList.sortedBy { it.id }.forEach {
                sbf.append("${it.id}${it.num}")
            }
        }
        if (!goodsTagItemStr.isNullOrEmpty()) {
            sbf.append(goodsTagItemStr)
        }
        //先对套餐内商品分组排序， 在对分组内商品排序,在拼接分组id+商品id+数量+uuid+规格（排序）
        if (!mealSetGoodList.isNullOrEmpty()) {
            mealSetGoodList.sortedWith(
                compareBy<MealSetGood> { it.mealSetGroupId }
                    .thenBy { it.mealSetGoodsId }
            ).forEach {
                sbf.append(it.mealSetGroupId)
                sbf.append(it.mealSetGoodsId)
                sbf.append(it.number)
                if (!it.uuid.isNullOrEmpty()) {
                    sbf.append(it.uuid)
                }
                //获取 套餐内商品的tagItems 列表，排序后拼接成字符串
                if (!it.tagItemIds.isNullOrEmpty()) {
                    sbf.append(it.tagItemIds.replace(",", ""))
                }
            }
        }

        if (!note.isNullOrEmpty()) {
            sbf.append(note)
        }

        Timber.e("最后要hash的字符串 ${sbf.toString()}")
        val hash = sbf.toString().toHexStr()
        Timber.e("最后要hash结果 ${hash}")
        return hash
    }


    // 为称重商品生成UUID
    fun generateWeightGoodsUUID(): String {
        return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(Regex("[xy]")) { match ->
            val r = Random.nextInt(0, 16)
            val v = when (match.value) {
                "y" -> (r and 0x3) or 0x8 // 确保符合 UUID v4 变体规则 (1000-1011)
                else -> r
            }
            v.toString(16) // 转换为十六进制字符
        }
    }
//
//    fun getGoodSkyKey(
//        goodsId: Long,
//        orderId: Long?,
//        feeds: List<GoodFeedInfoDTO>,
//        tagItems: List<OrderGoodsDTO.TagItems?>,
//        orderMealSetGoodsDTOList: List<OrderGoodsDTO.OrderMealSetGoodsDTO>,
//        pricingMethod: Int?,
//        weight: BigDecimal?,
//        acceptOrderId: Long?
//    ): String {
//        var key: String? = goodsId.toString() + ""
//        if (acceptOrderId != null) {
//            key += acceptOrderId
//        }
//        if (orderId != null) {
//            key += orderId
//        }
//        //套餐
//        if (CollUtil.isNotEmpty(orderMealSetGoodsDTOList)) {
//            //先对套餐内商品分组排序， 在对分组内商品排序,在拼接分组id+商品id+数量+规格（排序）
//            val strIdInfo = orderMealSetGoodsDTOList.stream()
//                .sorted(
//                    Comparator.comparingLong<Any>(OrderGoodsDTO.OrderMealSetGoodsDTO::getMealSetGroupId)
//                        .thenComparingLong(OrderGoodsDTO.OrderMealSetGoodsDTO::getMealSetGoodsId)
//                )
//                .map<String>(
//                    Function<OrderGoodsDTO.OrderMealSetGoodsDTO, String> { orderMealSetGoodsDTO: OrderGoodsDTO.OrderMealSetGoodsDTO ->
//                        val mealSetStr: String =
//                            (orderMealSetGoodsDTO.getMealSetGroupId() + orderMealSetGoodsDTO.getMealSetGoodsId() + orderMealSetGoodsDTO.getNumber()).toString() + ""
//                        // 获取 套餐内商品的tagItems 列表，排序后拼接成字符串
//                        val mealSetTagsId: String =
//                            orderMealSetGoodsDTO.getMealSetTagItemList().stream()
//                                .map(OrderGoodsDTO.TagItems::getId).sorted()
//                                .map(java.lang.String::valueOf)
//                                .collect(Collectors.joining(""))
//                        if (StringUtils.isBlank(mealSetTagsId)) mealSetStr else mealSetStr + mealSetTagsId
//                    }
//                ).collect(Collectors.joining(""))
//            key += strIdInfo
//            //单商品
//        } else {
//            if (CollUtil.isNotEmpty(feeds)) {
//                val feedId = feeds.stream()
//                    .sorted(Comparator.comparingLong<GoodFeedInfoDTO>(GoodFeedInfoDTO::getId))
//                    .map<String>(
//                        Function<GoodFeedInfoDTO, String> { feed: GoodFeedInfoDTO -> feed.getId() + "" + feed.getAlreadyNum() })
//                    .map<String> { o: String? ->
//                        java.lang.String.valueOf(
//                            o
//                        )
//                    }.collect(Collectors.joining(""))
//                key += feedId
//            }
//            if (CollUtil.isNotEmpty(tagItems)) {
//                val tagsId = tagItems.stream().map<Any>(OrderGoodsDTO.TagItems::getId).sorted()
//                    .map<String> { o: Any? ->
//                        java.lang.String.valueOf(
//                            o
//                        )
//                    }.collect(Collectors.joining(""))
//                key += tagsId
//            }
//        }
//        if (pricingMethod != null && pricingMethod > 0) {
//            key += weight
//            // 随机数，称重商品是分开称重的，所以需要加上随机数,防止两个称重商品的hashKey一样
//            key += UUID.fastUUID()
//        }
//        return DigestUtil.sha1Hex(key)
//    }








//    fun get(
//        feedList: ArrayList<Feed>?,
//        goodsTagItemList: ArrayList<GoodsTagItem>?,
//        orderMealSetGoodList: List<OrderMealSetGood>? = null,
//        goodsId: String,
//        singleItemDiscount: SingleDiscountGoods?,
//        goodsPriceKey: String,
//        note: String?,
//        uuid: String?,
//    ): HashRecord? {
//        return get(
//            getHash(
//                feedList,
//                goodsTagItemList,
//                orderMealSetGoodList,
//                goodsId,
//                singleItemDiscount,
//                goodsPriceKey,
//                note,
//                uuid
//            )
//        )
//    }



//    fun get(feedHash: String): HashRecord? {
//        return LitePal.where(
//            "hash = ?",
//            feedHash
//        ).findFirst<HashRecord>()
//    }

//    fun save(
//        feedList: ArrayList<Feed>?,
//        goodsTagItemList: ArrayList<GoodsTagItem>?,
//        orderMealSetGoodList: List<OrderMealSetGood>? = null,
//        goodsId: String,
//        singleItemDiscount: SingleDiscountGoods?,
//        goodsPriceKey: String,
//        note: String?,
//        uuid: String?,
//    ) {
////        if (feedList.isNullOrEmpty() && goodsTagItemList.isNullOrEmpty()) {
////            return
////        }
//        val hash =
//            getHash(
//                feedList,
//                goodsTagItemList,
//                orderMealSetGoodList,
//                goodsId,
//                singleItemDiscount,
//                goodsPriceKey,
//                note,
//                uuid
//            )
////        Timber.e("goodsId -> $goodsId  hash->${hash} ")
//        val record = get(hash)
//        if (record == null) {
//            Timber.e("HashCode  不存在添加")
//            HashRecord(
//                goodsId = goodsId,
//                hash = hash
//            ).save()
//        }
//    }
}