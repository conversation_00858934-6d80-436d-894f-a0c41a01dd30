package com.metathought.food_order.casheir.helper

import android.content.Context
import android.content.pm.PackageInfo


/**
 *<AUTHOR>
 *@time  2024/7/17
 *@desc
 **/

object VersionHelper {
    fun getLocalVersionName(context: Context): String {
        val packageInfo: PackageInfo =
            context.packageManager.getPackageInfo(context.packageName, 0)
        val versionCode = packageInfo.versionCode // 版本号（整数）
        val versionName = packageInfo.versionName // 版本名称（字符串）
        return versionName
    }
}