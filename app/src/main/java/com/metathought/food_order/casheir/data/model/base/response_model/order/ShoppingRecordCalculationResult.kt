package com.metathought.food_order.casheir.data.model.base.response_model.order

import android.text.SpannableStringBuilder
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetail
import java.math.BigDecimal

data class ShoppingRecordCalculationResult(
    /**
     * 购物车商品列表
     */
    val goodsVoList: List<GoodsRequest>,
    /**
     * 总商品列表（购物车+订单）
     */
    val totalGoodsList: List<GoodsRequest>,
    /**
     * 总商品数量（购物车+订单）
     */
    val cartGoodNum: Int,
    /**
     * 会员价小计
     */
    val totalVipPrice: Long,
    /**
     * 现价小计
     */
    val totalDiscountPrice: Long,
    /**
     * 会员价小计
     */
    val totalVipSubPrice: Long,
    /**
     * 现价小计
     */
    val totalDiscountSubPrice: Long,
    /**
     * 当亲购物车内商品现价小计
     */
    val currentCartTotalDiscountSubPrice: Long,
    /**
     * 打包费
     */
    val totalPackPrice: Long,
    /**
     * 现价服务费总计
     */
    val totalDiscountServiceFeePrice: Long,
    /**
     * vip服务费总计
     */
    val totalVipServiceFeePrice: Long,
    /**
     * 现价增值税总计
     */
    val totalVatPrice: Long,
    /**
     * vip增值税总计
     */
    val totalVipVatPrice: Long,
    /**
     * 总佣金
     */
    val totalCommissionPrice: Long,
    /**
     * 不带优惠券的价格
     */
    val finalTotalPriceWithoutCoupon: Long,
    /**
     * 是否有会员价
     */
    val isHasVipPrice: Boolean,
    /**
     * 是否未定价
     */
    val hasUnProcess: Boolean,
    /**
     * 汇率
     */
    val conversionRatio: Long,
    /**
     * 外卖平台
     */
    val takeOutPlatformModel: TakeOutPlatformModel?,
    /**
     * 优惠券描述
     */
    val couponDesc: SpannableStringBuilder,
    val orderAmountDetail: OrderAmountDetail,
    /**
     * 是否加购
     */
    val isOrderMore: Boolean,
    /**
     * 备注
     */
    val remark: String,

    //佣金详情
    val commissionPair: Pair<BigDecimal, Map<String, BigDecimal>>? = null,

    //整单减免的金额
    val wholeDiscountPrice: Long = 0L
)

