package com.metathought.food_order.casheir.ui.ordered.receive

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogAddGoodsListBinding
import com.metathought.food_order.casheir.databinding.DialogGiftProductListBinding
import com.metathought.food_order.casheir.ui.adapter.CouponGoodListAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.ui.dialog.CancelOrderDialog
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/09/29
 *@desc 待接单商品列表
 **/
@AndroidEntryPoint
class WaitReceiveOrderGoodsListDialog : DialogFragment() {
    companion object {
        private const val TAG = "WaitReceiveOrderGoodsLi"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderInfo: OrderedInfoResponse? = null,
            onNoAcceptClick: ((OrderedInfoResponse) -> Unit)? = null,
            onAcceptClick: ((OrderedInfoResponse) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(orderInfo, onNoAcceptClick, onAcceptClick)

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            orderInfo: OrderedInfoResponse? = null,
            onNoAcceptClick: ((OrderedInfoResponse) -> Unit)? = null,
            onAcceptClick: ((OrderedInfoResponse) -> Unit)? = null
        ): WaitReceiveOrderGoodsListDialog {
            val fragment = WaitReceiveOrderGoodsListDialog()
            fragment.orderInfo = orderInfo
            fragment.onNoAcceptClick = onNoAcceptClick
            fragment.onAcceptClick = onAcceptClick

            return fragment
        }


        fun getCurrentWaitReceiveOrderGoodsListDialog(fragmentManager: FragmentManager): WaitReceiveOrderGoodsListDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? WaitReceiveOrderGoodsListDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            Timber.e("dismissDialog")
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? WaitReceiveOrderGoodsListDialog
            fragment?.dismissAllowingStateLoss()
        }
    }


    private var binding: DialogAddGoodsListBinding? = null

    private var onNoAcceptClick: ((OrderedInfoResponse) -> Unit)? = null
    private var onAcceptClick: ((OrderedInfoResponse) -> Unit)? = null

    private var orderInfo: OrderedInfoResponse? = null

    fun getCurrentOrderNo(): String? {
        return orderInfo?.orderNo
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogAddGoodsListBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {

    }

    private fun initView() {
        binding?.apply {
            btnNoAccept.setOnClickListener {
                onNoAcceptClick?.invoke(orderInfo!!)

            }

            btnAcceptOrder.setOnClickListener {
                onAcceptClick?.invoke(orderInfo!!)
                dismissAllowingStateLoss()
            }
        }
    }

    fun updateCountDown(text: String) {
        binding?.apply {
            btnAcceptOrder?.text = text
        }
    }


    private fun initListener() {
        binding?.apply {
            rvList.adapter = OrderedInfoAdapter(
                (orderInfo?.waitAcceptAddOrder?.goods
                    ?: arrayListOf()) as ArrayList<BaseOrderGoods>,
            )

            btnClose.setOnClickListener { dismissAllowingStateLoss() }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}