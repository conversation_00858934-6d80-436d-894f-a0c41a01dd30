package com.metathought.food_order.casheir.data.model.base.response_model.member

import android.content.Context
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponTemplateSDK
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.Rule
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import java.math.BigDecimal


data class RechargeDetailResponse(
    var consumerPayAccountId: Long?,    //用户支付账号id
    var paymentMethod: String?,    //支付方式：INTEGRAL-积分; STORE_BALANCE-门店余额,可用值:INTEGRAL,STORE_BALANCE
    var balance: Int?,    //储值余额,单位:分
    var nickName: String?,    //用户昵称
    var telephone: String?,    //upay账户/注册的账号
    var isCustomMembershipAmount: Boolean?,    //	是否允许自定义会员充值金额
    var consumerRechargeTierVoList: List<ConsumerRechargeTier>   //启用状态下的档位
)


data class ConsumerRechargeTier(
    var id: Long?,    //	id	integer
    var storeId: String?,    //	分店店铺id	integer
    var name: String?,    //	档位名称	string
    var amount: BigDecimal?,    //	储值金额（单位：分）	number
    var giftAmount: BigDecimal?,    //	储值赠送金额（单位：分）	number
    var status: Boolean?,    //	0:关闭 1:开启	boolean
    var isDefault: Boolean?,    //	是否默认，只允许有一个默认档位	boolean
    var remark: String?,    //	备注	string
    var createTime: String?,    //	创建时间	string
    var rechargeTierCouponTemplateList: List<RechargeTierCouponTemplate>?   //	额外赠送优惠券列表详情
)


data class RechargeTierCouponTemplate(
    var couponTemplate: CouponTemplateSDK?,    //	优惠券模板详情	object
    var num: Int?    //	数量
){
    //赠送商品是否展开
    var isGiftGoodsExpand: Boolean? = false

}


data class ConsumerRechargeInfo(
    var id: Long? = null,    //id
    var accountId: Long? = null,    //账号id	integer(int64)
    var amount: Long? = null,    //例如门店余额支付，金额就是余额	integer(int32)
    var type: Int? = null,    //操作类型: 1-收入;2-支出;3-原路退款; 4-充值余额; 5-扣除余额(废弃)
    var status: Int? = null,    //操作状态：1-成功；2-失败； 3-取消;	integer(int32)
    var payChannel: Int? = null,    //支付渠道：1-在线支付 2-现金支付 3-门店余额	integer(int32)
    var consumerId: Long? = null,    //用户id    integer(int64)
    var telephone: String? = null,    //upay账户(电话)    string
    var nickName: String? = null,    //昵称    string
    var photoUrl: String? = null,    //照片url    string
    var createTime: String? = null,    //创建时间    string(date-time)
    var couponId: Long? = null,    //        integer(int64)
    var templateId: Long? = null,    //        integer(int64)
    var couponName: String? = null,    //        string
    var couponAmount: Long? = null,  //integer(int32)
    var realAmount: Int? = null,          //integer(int32)
    var offlinePaymentChannelsId: Int? = null, //线下支付渠道id    integer(int64)
    var offlinePaymentChannelsName: String? = null,    // 线下支付渠道名称    string
    var collectCash: BigDecimal? = null,    //  收取现金, 现金支付时使用 瑞尔    integer(int32)
    var collectCashDollar: BigDecimal? = null,    //收取现金 现金支付时使用, 美元 单位分    integer(int32)
    var changeAmount: BigDecimal? = null,    //找零金额, 现金支付时使用 瑞尔    integer(int32)
    var changeAmountDollar: BigDecimal? = null,    //找零金额 现金支付时使用，美元 单位分    integer(int32)
    var conversionRatio: Long? = null,    // 换算比例 瑞尔:美元，现金支付:4000-4200    integer(int32)
    var giftAmount: BigDecimal? = null,    //    储值赠送金额（单位：分）    number
    var giftCouponIds: String? = null,    //   储值赠送的优惠券ids    string
    var rechargeTierCouponTemplateList: List<RechargeTierCouponTemplate>? = null,    //    额外赠送优惠券列表详情    array    RechargeTierCouponTemplateDTO
    var remark: String? = null,    //    备注    string,
    var memberNumber: String? = null,    //    会员号    string
){
    fun getPaymentMethod(context: Context): String {
        return when (payChannel) {
            PayTypeEnum.CREDIT.id -> {
                context.getString(R.string.credit_payment)
            }
            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getString(R.string.online_payment)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                 getOfflinePayMethod(context)
//                "${context.getString(R.string.offline_payments)}"
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getString(R.string.pay_by_balance)
            }

            else -> ""
        }
    }

    fun getOfflinePayMethod(context: Context): String {
        if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
        } else if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        } else {
            return "${context.getString(R.string.offline_payments)} - $offlinePaymentChannelsName"
        }
    }
}
