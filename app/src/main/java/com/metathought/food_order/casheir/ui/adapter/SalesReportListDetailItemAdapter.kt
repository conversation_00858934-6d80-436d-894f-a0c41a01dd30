package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductBasicVo
import com.metathought.food_order.casheir.databinding.ItemSalesReportListDetailItemBinding

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description 销售报表预览adapter
 */
class SalesReportListDetailItemAdapter(
    val list: ArrayList<ProductBasicVo>,
) : RecyclerView.Adapter<SalesReportListDetailItemAdapter.ProduceReportItemViewHolder>() {

    inner class ProduceReportItemViewHolder(val binding: ItemSalesReportListDetailItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: ProductBasicVo, position: Int) {
            binding.apply {
                tvName.text = "${resource.itemName ?: ""}"
                tvNum.text = "${resource.quantity ?: ""}"
                tvSinglePrice.text = "${resource.unitPrice ?: ""}"
                tvServiceFee.text = "${resource.serviceCharge ?: ""}"
                tvPackingFee.text = "${resource.packingFee ?: ""}"
                vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemSalesReportListDetailItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: SalesReportListDetailItemAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

//    fun replaceData(list: List<ProductBasicVo>) {
//        this.list.clear()
//        this.list.addAll(list)
//        notifyDataSetChanged()
//    }

}