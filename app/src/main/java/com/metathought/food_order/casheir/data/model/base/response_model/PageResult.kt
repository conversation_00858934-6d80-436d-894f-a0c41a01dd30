package com.metathought.food_order.casheir.data.model.base.response_model

import com.google.gson.annotations.SerializedName

data class PageResult<T>(

    @SerializedName("records")
    val records: List<T>? = emptyList(),
    @SerializedName("total")
    val total: Int? = 0,
    @SerializedName("size")
    val size: Int? = 0,
    @SerializedName("current")
    val current: Int? = 1,
    @SerializedName("pages")
    val pages: Int? = 0,
    @SerializedName("basketPromo")
    val basketPromo: Int? = 0,
    @SerializedName("cancelledAmount")
    val cancelledAmount: Int? = 1,
    @SerializedName("cancelledOrderNum")
    val cancelledOrderNum: Int? = 1,
    @SerializedName("failedAmount")
    val failedAmount: Int? = 0,
    @SerializedName("failedOrderNum")
    val failedOrderNum: Int? = 0,
    @SerializedName("orderNum")
    val orderNum: Int? = 0,
    @SerializedName("promoOrderNum")
    val promoOrderNum: Int? = 0,
    @SerializedName("turnover")
    val turnover: Int? = 0,
)
