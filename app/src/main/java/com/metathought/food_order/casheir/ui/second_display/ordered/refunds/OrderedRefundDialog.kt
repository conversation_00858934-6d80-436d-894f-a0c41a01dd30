package com.metathought.food_order.casheir.ui.second_display.ordered.refunds

import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogSecondOrderedRefundBinding
import com.metathought.food_order.casheir.ui.adapter.SecondPartialRefundItemAdapter
import com.tencent.bugly.crashreport.CrashReport
import timber.log.Timber

/**
 * 副屏退款
 * Secondary screen refund
 * <AUTHOR>
 * @date 2024/5/1510:44
 * @description
 */

class OrderedRefundDialog : Dialog {
    constructor(context: Context) : this(context, 0)
    constructor(context: Context, themeResId: Int) : super(context, themeResId)

    private lateinit var binding: DialogSecondOrderedRefundBinding
    private var mAdapter: SecondPartialRefundItemAdapter? = null
    private lateinit var mContext: Context
    override fun onCreate(savedInstanceState: Bundle?) {
        /**
         * 防止子弹窗不会显示，还不知道有啥问题
         */
        try {
            if (window != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                } else {
                    window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                }
            }
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }
        super.onCreate(savedInstanceState)
        binding = DialogSecondOrderedRefundBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    fun initResource(mContext: Context) {
        this.mContext = mContext
        binding.run {
            tvDishedName.text = mContext.getString(R.string.request_refund)
            items.text = mContext.getString(R.string.items)
            quantity.text = mContext.getString(R.string.quantity)
            amount.text = mContext.getString(R.string.amount)
            refundQty.text = mContext.getString(R.string.refund_qty)
            refundQuantity.text = mContext.getString(R.string.refund_quantity)
            refundService.text = mContext.getString(R.string.service_fee)
            refundVat.text = mContext.getString(R.string.refund_vat)
            refundAmount.text = mContext.getString(R.string.refund_amount)
            refundPack.text = mContext.getString(R.string.refund_pack_price)
            totalRefundAmount.text = mContext.getString(R.string.refund_total_price)
        }
    }

    fun initData(currentOrderedInfo: OrderedInfoResponse?, filterList: ArrayList<BaseOrderGoods>) {
        binding.run {
            currentOrderedInfo?.let { orderDetail ->
                recyclerOrderedFood.post {
                    mAdapter = SecondPartialRefundItemAdapter(
                        filterList,
                        orderDetail.isUseDiscount,
                        mContext = mContext,
                        width = recyclerOrderedFood.measuredWidth,
                        currentOrderedInfo
                    )
                    recyclerOrderedFood.adapter = mAdapter
                }

            }
        }
    }

    fun refundType(isFullRefund: Boolean) {
        binding.run {
            layoutFood.isGone = isFullRefund
            layoutRefundVat.isGone = isFullRefund
            layoutRefundPrice.isGone = isFullRefund
            layoutRefundService.isGone = isFullRefund
            llFullView.isVisible = isFullRefund
        }
    }

    fun updateRecyclerOrderedFood(position: Int) {
        binding.run {
            recyclerOrderedFood.smoothScrollToPosition(position)
        }
    }

    fun radioFullRefund(
        refundQTY: String,
        refundTotalAmount: String,
        refundTotalKhrAmount: String,

        paymentMethod1Str: String,
        tvPaymentMethod1Str: String,
        balanceRefundStr: String,
        tvRefundTotalAmount1Str: String,
        paymentMethod2Str: String,
        tvPaymentMethodStr: String,
    ) {
        binding.run {
            layoutRefundVat.isVisible = false
            layoutRefundPack.isVisible = false
            layoutRefundPrice.isVisible = false
            layoutRefundService.isVisible = false
            layoutFood.isVisible = false
            tvRefundQTY.text = refundQTY
            tvRefundTotalAmount.text = refundTotalAmount
            tvRefundTotalKhrAmount.text = refundTotalKhrAmount

            paymentMethod1.text = paymentMethod1Str
            tvPaymentMethod1.text = tvPaymentMethod1Str
            balanceRefund.text = balanceRefundStr
            tvRefundTotalAmount1.text = tvRefundTotalAmount1Str
            paymentMethod2.text = paymentMethod2Str
            tvPaymentMethod.text = tvPaymentMethodStr
        }
    }

    fun setFullRefundVisible(
        llPaymentMethod1Visible: Boolean,
        llBalanceRefundVisible: Boolean,
        llPaymentMethodVisible: Boolean,
    ) {
        binding?.apply {
            llPaymentMethod1.isVisible = llPaymentMethod1Visible
            llBalanceRefund.isVisible = llBalanceRefundVisible
            llPaymentMethod.isVisible = llPaymentMethodVisible
        }
    }

    fun setRefundAmount(
        refundPrice: String,
        refundVat: String,
        refundQTY: String,
        refundPack: String,
        refundPackVisible: Boolean,
        refundTotalAmount: String,
        refundTotalKhrAmount: String,
        refundServiceFee: String,

        paymentMethod1Str: String,
        tvPaymentMethod1Str: String,
        balanceRefundStr: String,
        tvRefundTotalAmount1Str: String,
        paymentMethod2Str: String,
        tvPaymentMethodStr: String,

    ) {
        binding.run {
            tvRefundPrice.text = refundPrice
            tvRefundVat.text = refundVat
            tvRefundQTY.text = refundQTY
            tvRefundPack.text = refundPack
            layoutRefundPack.isVisible = refundPackVisible
            tvRefundTotalAmount.text = refundTotalAmount
            tvRefundTotalKhrAmount.text = refundTotalKhrAmount
            tvRefundService.text = refundServiceFee
            Timber.e("tvRefundService $refundServiceFee   ${layoutRefundService.isVisible}")
            paymentMethod1.text = paymentMethod1Str
            tvPaymentMethod1.text = tvPaymentMethod1Str
            balanceRefund.text = balanceRefundStr
            tvRefundTotalAmount1.text = tvRefundTotalAmount1Str
            paymentMethod2.text = paymentMethod2Str
            tvPaymentMethod.text = tvPaymentMethodStr
        }
    }

    fun setPartRefundViewVisible(
        layoutRefundPackVisible: Boolean,
        layoutRefundVatVisible: Boolean,
        layoutRefundPriceVisible: Boolean,
        layoutRefundServiceVisible: Boolean,

        llPaymentMethod1Visible: Boolean,
        llBalanceRefundVisible: Boolean,
        llPaymentMethodVisible: Boolean,

    ) {
        binding.run {
            layoutRefundVat.isVisible = layoutRefundVatVisible
            layoutRefundPack.isVisible = layoutRefundPackVisible
            layoutRefundPrice.isVisible = layoutRefundPriceVisible
            layoutRefundService.isVisible = layoutRefundServiceVisible

            llPaymentMethod1.isVisible = llPaymentMethod1Visible
            llBalanceRefund.isVisible = llBalanceRefundVisible
            llPaymentMethod.isVisible = llPaymentMethodVisible
        }
    }

    fun notifyAdapter(bindingAdapterPosition: Int) {
//        mAdapter?.notifyItemChanged(bindingAdapterPosition)
        mAdapter?.notifyDataSetChanged()
    }


}

