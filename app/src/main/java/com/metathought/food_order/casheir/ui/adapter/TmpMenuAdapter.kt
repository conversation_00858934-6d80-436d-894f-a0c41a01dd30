package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.LinearLayout.LayoutParams
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.bingoogolapple.badgeview.BGABadgeViewHelper
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderGoods
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.MenuItemBinding
import com.metathought.food_order.casheir.databinding.NewMenuItemBinding
import com.metathought.food_order.casheir.databinding.StickerHeaderItemBinding
import com.metathought.food_order.casheir.databinding.TmpMenuItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.utils.DisplayUtils
import timber.log.Timber


class TmpMenuAdapter(
    val list: ArrayList<BaseGoods>,
    val context: Context,
    val onClickCallback: (Goods, Int) -> Unit,
    val onPlusCallback: (Goods) -> Unit,
    val onEditCallback: (Goods) -> Unit,
) : RecyclerView.Adapter<TmpMenuAdapter.Companion.BaseViewHolder<*>>() {

    private lateinit var headerBinding: StickerHeaderItemBinding
    private lateinit var contentBinding: TmpMenuItemBinding
    var localDiningStyle: Int = 0

    private var goodsList = listOf<Goods>()

    inner class MenuViewHolder(val binding: TmpMenuItemBinding) :
        BaseViewHolder<TmpMenuItemBinding>(binding.root) {

        fun bind(resource: Goods?, indexBind: Int) {
            resource?.let { _ ->

                binding.apply {
                    tvName.text = resource.name
                    rootView.badgeViewHelper.setBadgeGravity(BGABadgeViewHelper.BadgeGravity.RightTop)

                    val index = goodsList.indexOfFirst { resource.id == it.id }
                    var totalCount = 0
                    if (index != -1) {
                        totalCount = goodsList[index].totalCount ?: 0
                        if (totalCount > 0) {
                            rootView.badgeViewHelper.showTextBadge("$totalCount")
                        } else {
                            rootView.badgeViewHelper.hiddenBadge()
                        }
                    } else {
                        rootView.badgeViewHelper.hiddenBadge()
                    }

                    /**
                     * 售罄状态
                     */
                    tvSoldOut.isVisible = false
                    if (resource.soldOut == true) {
                        tvSoldOut.isVisible = true
                        tvPrice.setTextColor(context.getColor(R.color.black40))
                    } else {
                        tvPrice.setTextColor(context.getColor(R.color.primaryColor))
                    }

                    tvPrice.text =
                        "${resource.sellPrice?.priceFormatTwoDigitZero2()}"

                    ivEdit.setOnClickListener {
                        onEditCallback?.invoke(resource)
                    }

                    cardViewMain.setOnClickListener {
                        if (resource.soldOut == true) {
                            return@setOnClickListener
                        }
                        Timber.e("totalCount :${totalCount}")

                        val index = goodsList.indexOfFirst { resource.id == it.id }
                        totalCount = 0
                        if (index != -1) {
                            totalCount = goodsList[index].totalCount ?: 0
                        }
                        val maxNum = GOOD_MAX_NUM
                        val canAddNum = maxNum - totalCount
                        if (canAddNum <= 0) {
                            Toast.makeText(
                                context,
                                context.getString(
                                    R.string.you_have_reached_the_maximum_quantity_limit_of,
                                    maxNum
                                ), Toast.LENGTH_SHORT
                            ).show()
                            return@setOnClickListener
                        }
                        onPlusCallback.invoke(resource.apply { position = indexBind })
                    }
//                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<*> {
        return when (viewType) {
            ViewType.Header.ordinal -> {
                headerBinding = StickerHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HeaderViewHolder(headerBinding)
            }

            ViewType.Content.ordinal -> {
                contentBinding =
                    TmpMenuItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                MenuViewHolder(contentBinding)
            }

            else -> throw IllegalArgumentException("Not a layout")
        }
    }

    private var menuWidth = 0;

    override fun onViewAttachedToWindow(holder: BaseViewHolder<*>) {
        super.onViewAttachedToWindow(holder)
        if (holder is MenuViewHolder) {
            if (holder.itemView.width > menuWidth) {
                menuWidth = holder.itemView.width
            }
//            println("Item 宽度: $menuWidth")
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                holder.binding.tvValue.text = (list[position] as HeaderGoods).name

                holder.binding.tvValue.setPadding(
                    0,
                    if (position == 0) 0 else DisplayUtils.dp2px(context, 12f),
                    0,
                    DisplayUtils.dp2px(context, 12f)
                )
            }

            is MenuViewHolder -> {
                (list[position] is Goods).let {
                    holder.bind(list[position] as Goods, position)
                }

            }
        }
    }


    override fun onBindViewHolder(
        holder: BaseViewHolder<*>,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            when (holder) {
                is HeaderViewHolder -> {
                    holder.binding.tvValue.text = (list[position] as HeaderGoods).name
                    holder.binding.tvValue.setPadding(
                        0,
                        if (position == 0) 0 else DisplayUtils.dp2px(context, 12f),
                        0,
                        DisplayUtils.dp2px(context, 12f)
                    )
                }

                is MenuViewHolder -> {
                    holder.binding.apply {
                        (list[position] as Goods).let { good ->

                            val index = goodsList.indexOfFirst { good.id == it.id }
                            var totalCount = 0
                            if (index != -1) {
                                totalCount = goodsList[index].totalCount ?: 0
                                if (totalCount > 0) {
                                    rootView.badgeViewHelper.showTextBadge("$totalCount")
                                } else {
                                    rootView.badgeViewHelper.hiddenBadge()
                                }
                            } else {
                                rootView.badgeViewHelper.hiddenBadge()
                            }
                        }
                    }
                }
            }
        }
    }


    override fun getItemViewType(position: Int): Int {
        return when (list[position].header) {
            true -> ViewType.Header.ordinal
            false -> ViewType.Content.ordinal
            else -> {
                ViewType.Content.ordinal
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    //是否有优惠活动
    private var isHasCouponActivity = false
    fun updateItems(newItems: ArrayList<BaseGoods>) {
        isHasCouponActivity =
            newItems.firstOrNull { (it is Goods) && !it.activityLabels.isNullOrEmpty() } != null
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateItem(goods: Goods) {
        list.forEachIndexed { index, baseGoods ->
            if (baseGoods is Goods && baseGoods.id == goods.id) {
                baseGoods.totalCount = goods.totalCount
                notifyItemChanged(index)
            }
        }
    }

    fun getHeaderPosition(groupId: String): Int {
        for (i in 0..<list.size) {
            if (list[i] is HeaderGoods) {
                if ((list[i] as HeaderGoods).id == groupId)
                    return i
            }
        }
        return 0
    }

    fun updateTotalCount(goods: Goods, goodsList: List<Goods>) {
        this.goodsList = goodsList
        val index = list.indexOfFirst { it is Goods && it.id == goods.id }
        Timber.e("index11 :${index}")
        if (index != -1) {
            notifyItemChanged(index, "update")
        }
    }

    fun setCartGoods(goodsList: List<Goods>) {
        this.goodsList = goodsList
    }

    inner class HeaderViewHolder(val binding: StickerHeaderItemBinding) :
        BaseViewHolder<StickerHeaderItemBinding>(binding.root) {

    }

    companion object {
        abstract class BaseViewHolder<T>(view: View) : RecyclerView.ViewHolder(view)
    }

    enum class ViewType {
        Header,
        Content
    }
}