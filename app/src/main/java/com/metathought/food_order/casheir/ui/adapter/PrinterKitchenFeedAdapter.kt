package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterFeedBinding
import com.metathought.food_order.casheir.databinding.ItemPrinterKitchenFeedBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.utils.DisplayUtils
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2024/08/21 14:31
 * @description  厨打小票按80 样式打
 */
class PrinterKitchenFeedAdapter(
    val list: List<Feed>,
    val templateItem: PrintTamplateResponseItem
) : RecyclerView.Adapter<PrinterKitchenFeedAdapter.PrinterFeedViewHolder>() {


    inner class PrinterFeedViewHolder(val binding: ItemPrinterKitchenFeedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: Feed) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
                localeList.forEachIndexed { index, locale ->
                    val name = resource.getNameByLocale(locale)
                    if (index == 0) {
                        tvFeedNameEn.text = "$name"
                        tvFeedNameEn.isVisible = !name.isNullOrEmpty()
                    } else if (index == 1) {
                        tvFeedNameKm.text = "$name"
                        tvFeedNameKm.isGone =
                            tvFeedNameKm.text == tvFeedNameEn.text || name.isNullOrEmpty()
                    }
                }

                tvFeedNameKm.isVisible =
                    !tvFeedNameKm.text.isNullOrEmpty() && tvFeedNameEn.text != tvFeedNameKm.text
                tvFeedNameEn.text = "+ ${tvFeedNameEn.text}"
                (tvFeedNameKm.layoutParams as MarginLayoutParams).marginStart =
                    DisplayUtils.dp2px(tvFeedNameKm.context, 25f)
                Timber.e("tvFeedName.text-> ${tvFeedNameEn.text}  tvFeedNameKm.text->${tvFeedNameKm.text} ")
                tvFeedFoodCount.text = "x${resource.alreadyNum}"

                if (templateItem.informationShow != null) {
                    tvFeedNameEn.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                    tvFeedNameKm.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                    tvFeedFoodCount.textSize =
                        templateItem.informationShow.getProductNumFontRealSize(itemView.context, isEightyWidth = true)
                }
            }
        }


    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterKitchenFeedBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterKitchenFeedAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}