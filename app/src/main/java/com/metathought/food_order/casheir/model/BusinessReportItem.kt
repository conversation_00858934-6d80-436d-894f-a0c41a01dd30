package com.metathought.food_order.casheir.model

import java.math.BigDecimal

data class BusinessReportItem(
//    val date: String, // 日期
//    val balance: String, // 营业余额
//    val income: String, // 收入金额
//    val reserve: String, // 备用金
//    val expense: String, // 支出金额
//    val creditCustomers: String // 信用客户
    //门店营业时间
    val businessDate: String = "",
    //营业利润
    val operatingIncome: BigDecimal? = null,
    //收入金额
    val revenueAmount: BigDecimal? = null,
    //备用金USD
    val openingCashUSD: BigDecimal? = null,
    //备用金KHR
    val openingCashKHR: BigDecimal? = null,
    //支出金额USD
    val amountPaidUSD: BigDecimal? = null,
    //支出金额KHR
    val amountPaidKHR: BigDecimal? = null,
    //信用客户
    val creditAmount: BigDecimal? = null,
    //支出金额
    val amountPaid: BigDecimal? = null,
)