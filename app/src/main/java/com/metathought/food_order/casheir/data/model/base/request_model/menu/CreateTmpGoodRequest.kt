package com.metathought.food_order.casheir.data.model.base.request_model.menu


import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class CreateTmpGoodRequest(
    @SerializedName("id")
    var id: Long? = null,
    /**
     * 菜品名
     */
    @SerializedName("name")
    var name: String? = null,
    /**
     * 售价  输入啥 给服务端啥
     */
    @SerializedName("sellPrice")
    var sellPrice: BigDecimal? = null,

    /**
     * 备注
     */
    @SerializedName("note")
    var note: String? = null,

//    /**
//     * 归属的分店
//     */
//    @SerializedName("storeId")
//    val storeId: Long?,
//    /**
//     * 是否厨房制作
//     */
//    @SerializedName("kitchenMaking")
//    val kitchenMaking: Boolean?,
//    /**
//     * 关联的厨房id
//     */
//    @SerializedName("storeKitchenId")
//    val storeKitchenId: Long?,
//    /**
//     * 是否需要标签打印
//     */
//    @SerializedName("printLabel")
//    val printLabel: Boolean?,
)