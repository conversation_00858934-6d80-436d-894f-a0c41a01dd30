package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.view.text.addTextTag
import timber.log.Timber
import androidx.core.graphics.toColorInt
import com.metathought.food_order.casheir.database.GoodsRecord


class MenuOrderFoodAdapter(
    val list: ArrayList<GoodsRequest>,
    val context: Context,
    val onPlusCallback: (GoodsRequest) -> Unit,
    val onSubCallback: (GoodsRequest) -> Unit,
    val onNumCallback: (GoodsRequest) -> Unit,
    val onSelectCallback: (GoodsRequest?) -> Unit,
    val onWarnClick: ((View, GoodsRequest) -> Unit)? = null
) : RecyclerView.Adapter<MenuOrderFoodAdapter.MenuOrderFoodItemViewHolder>() {
    var diningStyle: Int = 0

    private var selectGoodsRequest: GoodsRequest? = null

    inner class MenuOrderFoodItemViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.run {
                root.setOnClickListener {
                    // 检查位置是否有效
                    val position = bindingAdapterPosition
                    if (position != RecyclerView.NO_POSITION) {
                        SingleClickUtils.isFastDoubleClick(500) {
                            val previousSelectedPosition =
                                list.indexOfFirst { it.getHash() == selectGoodsRequest?.getHash() }
                            if (selectGoodsRequest?.getHash() != list[position].getHash()) {
                                selectGoodsRequest = list[position]
                                onSelectCallback?.invoke(selectGoodsRequest)
                                // 只刷新前后选中的项
                                if (previousSelectedPosition != -1) {
                                    notifyItemChanged(previousSelectedPosition)
                                }
                                notifyItemChanged(position)
                                Timber.e("selectItem1111: ${selectGoodsRequest}. $position")
                            } else {
                                Timber.e("selectItem222222electGoodsRequest}. $position")
                                onSelectCallback?.invoke(null)
                                clearSelect(position)
                            }
                        }
                    }
                }
            }
        }

        @SuppressLint("SetTextI18n")
        fun bind(resource: GoodsRequest?, index: Int?) {
            resource?.let { _ ->
                binding.apply {
//                    if (selectGoodsRequest != null) {
//                        layoutBg.isSelected = resource.getHash() == selectGoodsRequest?.getHash()
//                    } else {
//                        layoutBg.isSelected = false
//                    }
                    layoutBg.isSelected = resource.getHash() == selectGoodsRequest?.getHash()

                    var maxNum = GOOD_MAX_NUM
                    val record = resource.goods?.id?.let { GoodsHelper.get(it, diningStyle) }
//                    val record:GoodsRecord? = null
                    var canAddNum = maxNum - (record?.num ?: 0)
                    if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                        //预定数量限制
                        val restrictNum = (resource.goods?.restrictNum
                            ?: 0)
                        //如果有预定数量限制
                        if (restrictNum > 0) {
                            maxNum = restrictNum
                            canAddNum = maxNum - (record?.num ?: 0)
                        }
                    }

                    tvName.text = resource.goods?.name
                    if (!resource.orderMealSetGoodList.isNullOrEmpty()) {
                        tvName.addMealSetTag(context)
                    }

                    val activityLabel = resource.goods?.activityLabels?.firstOrNull()
                    if (activityLabel != null) {
                        tvDiscountActivity.setStrokeAndColor(color = activityLabel.color.toColorInt())
                        tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                        tvDiscountActivity.text = activityLabel.name
                        tvDiscountActivity.isVisible = true
                    } else {
                        tvDiscountActivity.isVisible = false
                    }

                    if (resource.goods?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                        tvTmpSign.setStrokeAndColor(color = R.color.black60)
                        tvTmpSign.isVisible = true
                    } else {
                        tvTmpSign.isVisible = false
                    }

                    tvQTY.text = "x${resource.num}"

                    tvSpecification.isVisible = resource.getGoodsTagStr().isNotEmpty()
                    tvSpecification.text = resource.getGoodsTagStr()

                    layoutPrice.tvWeight.isVisible = false
                    layoutPrice.tvTimePriceSign.isVisible = false

                    val isHasProcessed = resource.isProcessed()
                    layoutPrice.tvVipPrice.isVisible = false
                    Timber.e("菜单 isMealSet：${resource.goods?.isMealSet()}  ${resource.goods?.isHasProcessed()} isMealSetHasCompleteWeight：${resource.isMealSetHasCompleteWeight()} isHasProcessed:$isHasProcessed")

                    if (resource.isToBeWeighed()) {
                        //如果是称重商品
                        if (resource.isHasCompleteWeight()) {
                            Timber.e("已称重")
                            layoutPrice.tvWeight.isVisible = true
                            layoutPrice.tvWeight.text = "(${resource.goods?.getWeightStr()})"
                            if (resource.goods?.isMealSet() == true) {
                                layoutPrice.tvWeight.isVisible = false
                            }
                        } else {
                            Timber.e("未称重")
                            layoutPrice.tvFoodPrice.text = context.getString(R.string.to_be_weighed)
                            layoutPrice.tvVipPrice.isVisible = false
                        }
                    }

                    if (resource.goods?.isTimePriceGood() == true) {
                        //如果是时价菜
                        if (!(resource.goods?.isHasCompletePricing() == true)) {
                            layoutPrice.tvFoodPrice.text = context.getString(R.string.time_price)
                        } else {
                            layoutPrice.tvTimePriceSign.isVisible = true
                        }
                    }

                    if (isHasProcessed) {
                        val isShowVipPrice = resource.goods?.isShowVipPrice()
                        layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
                        layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                            FoundationHelper.useConversionRatio,
                            resource.totalDiscountPrice(),
                            FoundationHelper.isKrh
                        )
                        if (isShowVipPrice == true) {
                            layoutPrice.tvVipPrice.text =
                                resource.totalVipPrice().priceFormatTwoDigitZero2()
                        }
                        if (resource.goods?.isTimePriceGood() == true) {
                            layoutPrice.tvTimePriceSign.isVisible = true
                        }
                    }

                    if (resource.goods?.isSoldOut() == true) {
                        tvName.setTextColor(context.getColor(R.color.black))
                        tvSpecification.setTextColor(context.getColor(R.color.black))
                        tvQTY.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvFoodPrice.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvWeight.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvVipPrice.setTextColor(context.getColor(R.color.black))
//                        tvOriginalPrice.setTextColor(context.getColor(R.color.black))
                        layoutBg.alpha = 0.4f
                    } else {
                        tvName.setTextColor(context.getColor(R.color.black))
                        tvSpecification.setTextColor(context.getColor(R.color.black60))
                        tvQTY.setTextColor(context.getColor(R.color.black60))
                        layoutPrice.tvFoodPrice.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvWeight.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvVipPrice.setTextColor(context.getColor(R.color.member_price_color))
//                        tvOriginalPrice.setTextColor(context.getColor(R.color.black60))
                        layoutBg.alpha = 1f
                    }

                    layoutPrice.ivWarn.isVisible =
                        resource.singleDiscountGoods != null // && !resource.singleDiscountGoods?.remark.isNullOrEmpty()

                    layoutPrice.ivWarn.setOnClickListener {
                        SingleClickUtils.isFastDoubleClick {
                            onWarnClick?.invoke(layoutPrice.ivWarn, resource)
                        }
                    }


                    tvRemark.isVisible = resource.getFinalNote().isNotEmpty()
                    tvRemark.text =
                        "${context.getString(R.string.remark)} : ${resource.getFinalNote()}"
                }
            }
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuOrderFoodItemViewHolder {
        val itemView = SelectedMenuItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )

        return MenuOrderFoodItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: MenuOrderFoodItemViewHolder, position: Int) {

        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun updateItems(newItems: List<GoodsRequest>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateDiningStyle(style: Int) {
        diningStyle = style
    }

    fun clearSelect(position: Int? = null) {
        Timber.e("清空选择")
        val previousSelectedPosition =
            list.indexOfFirst { it.getHash() == selectGoodsRequest?.getHash() }
        selectGoodsRequest = null
        if (previousSelectedPosition != -1) {
            notifyItemChanged(previousSelectedPosition)
        } else {
            position?.let { notifyItemChanged(it) }
        }
    }

    fun getSelectItem(): GoodsRequest? {
        return selectGoodsRequest
    }

    fun updateSelectItem(item: GoodsRequest?) {
        selectGoodsRequest = item
    }

}