package com.metathought.food_order.casheir.database

import android.content.Context
import android.os.Parcelable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.OrderCouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.CouponHelper
import kotlinx.parcelize.Parcelize
import org.litepal.crud.LitePalSupport
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/3/1816:39
 * @description
 */
@Parcelize
class ShoppingRecord(
    //0: Dining 1: take-away 2: reserve
    var diningStyle: Int,
    var storeId: String? = null,
    var peopleNum: Int? = null,
    var peopleDate: String? = null,
    var totalPrice: Long? = null,  //不包含打包费
//    var serviceCharge: Long? = null, //废弃 原来的vat  以后某个版本删掉
    var vatCharge: Long? = null, //增值税
    var serviceFeeCharge: Long? = null, //服务费
    var tableUuid: String? = null,
    var tableLabel: String? = null,
    var tableType: Int? = null,
    var diningNumber: Int? = null,
    var diningTime: String? = null,
    var areaCode: String? = null,
    var mobile: String? = null,
    var name: String? = null,
    var goodsVoStr: String? = null,
    var isOrderMore: Boolean? = false,
    var orderMoreID: String? = null,
    //打包费
    var packPrice: Long? = null,

    //订单状态
    var payStatus: Int? = null,

    //备注
    var note: String? = null,

    //优惠券信息
    var couponModel: String? = null,

    //外带平台
    var takeOutPlatformModel: String? = null,
    var takeOutOrderId: String? = null,

    /**
     * 购物车版本号
     */
    var dataVersion: Long? = null,

    /**
     * 购物车 变动时价戳
     */
    var timestamp: String? = null,


    ) : LitePalSupport(), Parcelable {

    //    @Column(ignore = true)
//    var goodsVoList: ArrayList<GoodsRequest>? = null
    fun getGoodsVoList(): ArrayList<GoodsRequest> {
        return MyApplication.globalGson.fromJson<List<GoodsRequest>>(
            goodsVoStr,
            object : TypeToken<List<GoodsRequest>>() {}.type
        ) as ArrayList<GoodsRequest>
    }

    //获取优惠券信息
    fun getCouponInfo(): CouponModel? {
        if (couponModel.isNullOrEmpty()) {
            return null
        }
        return MyApplication.globalGson.fromJson<CouponModel>(
            couponModel,
            object : TypeToken<CouponModel>() {}.type
        )
    }

    fun getTakeOutPlatformModel(): TakeOutPlatformModel? {
        if (takeOutPlatformModel.isNullOrEmpty()) {
            return null
        }
        return MyApplication.globalGson.fromJson<TakeOutPlatformModel>(
            takeOutPlatformModel,
            object : TypeToken<TakeOutPlatformModel>() {}.type
        )
    }


    fun isUniversalQr(): Boolean {
        return tableType == 2
    }

    fun getGoodsCount(): Int {
        var count = 0
        getGoodsVoList().forEach {
            count += (it.num ?: 0)
        }
        return count
    }

    /**
     * 是否有待定价商品
     *
     * @return
     */
    fun isHasNeedProcess(): Boolean {
        var hasUnProcess = false
        //判断是否含有待定价商品
        getGoodsVoList().forEach {
            if (it.isProcessed() == false) {
                hasUnProcess = true
            }
        }
        return hasUnProcess
    }


    //获取优惠券展示内容
    fun getCouponDesc(
        context: Context,
        isCouponListEmpty: Boolean,
        isShowVipPrice: Boolean,
        hasUnProcess: Boolean,
        couponPair: Pair<Pair<Long, Boolean>, Pair<Long, Boolean>>
    ): SpannableStringBuilder {
        val desc = SpannableStringBuilder()
        val coupon = getCouponInfo()
//        Timber.e("coupon  ${coupon?.toJson()}")
        if (coupon == null) {
            if (isCouponListEmpty) {
                //无可用优惠券
                val hint = context.getString(R.string.identify_coupons)
                desc.append(hint)
                desc.setSpan(
                    ForegroundColorSpan(context.getColor(R.color.black40)),
                    0,
                    hint.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                //有可用优惠券
                val hint = context.getString(R.string.hint_can_use_coupon)
                desc.append(hint)
                desc.setSpan(
                    ForegroundColorSpan(context.getColor(R.color.color_ff7f00)),
                    0,
                    hint.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        } else {
//            val giftGoods = coupon.giftGoods
            if (!coupon.isZsCoupon()) {
                if (hasUnProcess) {
                    //待称重
                    desc.append(context.getString(R.string.to_be_confirmed))
                } else {
                    //未支付
                    val price = "    -${couponPair.first.first.priceFormatTwoDigitZero2()}"
                    val vipPrice = "-${couponPair.second.first.priceFormatTwoDigitZero2()}"
                    if (couponPair.first.first == 0L && couponPair.second.first == 0L) {
                        desc.clear()
                    } else {
                        if (couponPair.second.second) {
                            //如果优惠卷会员价减免金额有效
                            val drawable = context.resources.getDrawable(R.drawable.icon_vip)
                            drawable.setBounds(
                                0,
                                0,
                                drawable.intrinsicWidth,
                                drawable.intrinsicHeight

                            )
                            val imageSpan = ImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
                            desc.append("  ")
                            desc.setSpan(imageSpan, 0, 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                            desc.append(vipPrice)
                            desc.setSpan(
                                ForegroundColorSpan(context.getColor(R.color.member_price_color)),
                                desc.length - vipPrice.length,
                                desc.length,
                                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                        if (couponPair.first.second) {
                            //如果优惠卷现价减免金额有效
                            desc.append(price)
                        }
                    }
                }
                Timber.e("couponPair.first.second:${couponPair.first.second}   couponPair.second.second:${couponPair.second.second}")
                if (!couponPair.first.second && !couponPair.second.second) {
                    desc.clear()
                    desc.append(context.getString(R.string.this_coupon_not_support))
                }

            } else {
                //有赠品
//                val couponPair =
//                    CouponHelper.calculateZPCouponPrice(
//                        getGoodsVoList(),
//                        coupon.templateSDK,
//                        diningStyle
//                    )
                if (couponPair.first.second || couponPair.second.second) {
                    val hint = context.getString(R.string.give_away_goods)
                    desc.append(hint)
                    desc.setSpan(
                        ForegroundColorSpan(context.getColor(R.color.black)),
                        0,
                        hint.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                } else {
                    desc.append(context.getString(R.string.this_coupon_not_support))
                }

            }
        }
        return desc
    }


}