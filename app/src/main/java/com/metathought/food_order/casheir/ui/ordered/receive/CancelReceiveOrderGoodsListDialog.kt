package com.metathought.food_order.casheir.ui.ordered.receive

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogAddGoodsListBinding
import com.metathought.food_order.casheir.databinding.DialogCancelReceiveOrderListBinding
import com.metathought.food_order.casheir.databinding.DialogGiftProductListBinding
import com.metathought.food_order.casheir.ui.adapter.CancelReceiveOrderListAdapter
import com.metathought.food_order.casheir.ui.adapter.CouponGoodListAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.ui.dialog.CancelOrderDialog
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/09/29
 *@desc 已取消接单 列表
 **/
@AndroidEntryPoint
class CancelReceiveOrderGoodsListDialog : DialogFragment() {
    companion object {
        private const val TAG = "CancelReceiveOrderGoods"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderInfo: OrderedInfoResponse? = null,

            ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(orderInfo)

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            orderInfo: OrderedInfoResponse? = null,
        ): CancelReceiveOrderGoodsListDialog {
            val fragment = CancelReceiveOrderGoodsListDialog()
            fragment.orderInfo = orderInfo

            return fragment
        }


        fun getCurrentCancelReceiveOrderGoodsListDialog(fragmentManager: FragmentManager): CancelReceiveOrderGoodsListDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CancelReceiveOrderGoodsListDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            Timber.e("dismissDialog")
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CancelReceiveOrderGoodsListDialog
            fragment?.dismissAllowingStateLoss()
        }
    }


    private var binding: DialogCancelReceiveOrderListBinding? = null


    private var orderInfo: OrderedInfoResponse? = null

    fun getCurrentOrderNo(): String? {
        return orderInfo?.orderNo
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCancelReceiveOrderListBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {

    }

    private fun initView() {

    }


    private fun initListener() {
        binding?.apply {
            val adapter =  CancelReceiveOrderListAdapter(
                orderInfo?.cancelAcceptAddOrderList ?: listOf(),
                requireContext()
            )
            rvList.adapter = adapter

            btnClose.setOnClickListener { dismissAllowingStateLoss() }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}