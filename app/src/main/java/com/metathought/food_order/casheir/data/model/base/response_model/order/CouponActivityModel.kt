package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.extension.toJson
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/11/25
 *@desc
 **/

data class CouponActivityModel(

    @SerializedName("activityTemplateId")
    var activityTemplateId: String? = null,
    @SerializedName("activityLabelName")
    var activityLabelName: String? = null,

    /**
     * 优惠活动 现价优惠掉的金额
     */
    @SerializedName("activityCouponAmount")
    var activityCouponAmount: Long? = null,
    /**
     * 优惠活动 会员价优惠掉的金额  这个到美分
     */
    @SerializedName("activityVipCouponAmount")
    var activityVipCouponAmount: Long? = null,
    /**
     * 优惠活动 是否含有待定价商品  true 就是含有未定价商品
     */
    @SerializedName("weightMark")
    var weightMark: Boolean? = null,

    /**
     * 优惠活动 是否有使用会员价
     */
    @SerializedName("vipMark")
    var vipMark: Boolean? = null,


    /// 以下字段是本地计算的时候使用的
    /**
     * 优惠活动  本地购物车用
     */
    var activityLabel: ActivityLabel? = null,

//    /**
//     * 参与优惠的商品及数量（购物车本地计算的时候有）
//     */
//    var goodsDiscountList: List<GoodsDiscountInfo> = emptyList(),
//    /**
//     * vip价参与优惠的商品及数量（购物车本地计算的时候有）
//     */
//    var goodsVipDiscountList: List<GoodsDiscountInfo> = emptyList(),

    //该优惠活动参与的商品及数量  "NORMAL,VIP"
    var activityGoodsMap: Map<String, List<GoodsDiscountInfo>?> = emptyMap()
) {

    fun deepCopy(): CouponActivityModel {
        val json = this.toJson()
        return MyApplication.globalGson.fromJson<CouponActivityModel>(
            json,
            object : TypeToken<CouponActivityModel>() {}.type
        ) as CouponActivityModel
    }


    /**
     * 获取普通优惠类型的商品优惠信息（对应键"NORMAL"）
     * @return 普通优惠商品信息（可能为null）
     */
    fun getNormalGoodsDiscountInfo(): List<GoodsDiscountInfo> {
        if (!activityGoodsMap.containsKey("NORMAL")) {
            return listOf()
        }
        return activityGoodsMap["NORMAL"] ?: listOf()
    }

    /**
     * 获取会员优惠类型的商品优惠信息（对应键"VIP"）
     * @return 会员优惠商品信息（可能为null）
     */
    fun getVipGoodsDiscountInfo(): List<GoodsDiscountInfo> {
        if (!activityGoodsMap.containsKey("VIP")) {
            return listOf()
        }
        return activityGoodsMap["VIP"] ?: listOf()
    }

}


data class GoodsDiscountInfo(
    val discountAmount: BigDecimal,  //优惠金额  这个到美元
    val hashKey: String,  // hashKey
    val id: String? = null,  //id
    val name: String? = null,  //商品名
    var num: Int,  // 参与优惠的数量
//    var availableNum: Int,  // 剩余可计算金额
)