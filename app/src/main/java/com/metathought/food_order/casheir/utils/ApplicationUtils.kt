package com.metathought.food_order.casheir.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.annotation.RequiresApi
import androidx.core.content.FileProvider
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.helper.DialogHelper
import rx_activity_result2.RxActivityResult
import timber.log.Timber
import java.io.File


/**
 * Created by <PERSON><PERSON><PERSON> on 2018/4/12 10:20.
 */
object ApplicationUtils {


    fun getMetaData(context: Context, name: String): String {
        val appInfo = context.packageManager
            .getApplicationInfo(
                context.packageName,
                PackageManager.GET_META_DATA
            )
        val metaData = appInfo.metaData
        val string = metaData.get(name).toString()
        return string.replace("\"", "")
    }

    //安装应用的流程
    fun installApk(activity: Activity, apk: File) {
        val haveInstallPermission: Boolean
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //先获取是否有安装未知来源应用的权限
            haveInstallPermission = activity.packageManager.canRequestPackageInstalls()
            if (!haveInstallPermission) {//没有权限
                showInstallDialog(activity, apk)
                return
            }
        }
        //有权限，开始安装应用程序
        doInstallApk(activity, apk)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun showInstallDialog(activity: Activity, apk: File) {

        DialogHelper.showSimpleDialog(
            activity,
            activity.getString(R.string.install_application_need_open_unkown_permission_tips),
            { d, _ ->
                d.dismiss()
                startInstallPermissionSettingActivity(activity, apk) {
                    doInstallApk(activity, apk)
                }
            },
            false
        ).show()
    }


    @SuppressLint("CheckResult")
    @RequiresApi(api = Build.VERSION_CODES.O)
    private fun startInstallPermissionSettingActivity(
        activity: Activity,
        apk: File,
        method: () -> Unit
    ) {
        val packageURI = Uri.parse("package:" + activity.packageName)
        //注意这个是8.0新API
        val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, packageURI)
        RxActivityResult.on(activity)
            .startIntent(intent)
            .subscribe {
                val canRequestPackageInstalls = activity.packageManager.canRequestPackageInstalls()
                Timber.d("startInstallPermissionSettingActivity:${it.resultCode()}  ${canRequestPackageInstalls}")
                if (canRequestPackageInstalls) {
                    method.invoke()
                } else {
                    showInstallDialog(activity, apk)
                }
            }
    }

    //安装应用
    private fun doInstallApk(context: Context, apk: File) {
        val intent = Intent(Intent.ACTION_VIEW)
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            intent.setDataAndType(Uri.fromFile(apk), "application/vnd.android.package-archive")
        } else {//Android7.0之后获取uri要用contentProvider
            val uri =
                FileProvider.getUriForFile(context, "${context.packageName}.fileProvider", apk)
            intent.setDataAndType(uri, "application/vnd.android.package-archive")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

}
