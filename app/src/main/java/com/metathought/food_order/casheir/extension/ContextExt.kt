package com.metathought.food_order.casheir.extension

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.os.Handler
import android.os.Looper

fun Context.safeUnregisterReceiver(receiver: BroadcastReceiver?) {
    if (receiver == null) {
        return
    }

    try {
        unregisterR<PERSON>eiver(receiver)
    } catch (e: IllegalArgumentException) {
    }
}

//为了让页面跳转之间顺滑，
fun Context.HandlerDelayed(run: () -> Unit, delayedTime: Long = 200) {
    Handler(this.mainLooper).postDelayed({
        if ((this as? Activity)?.isDestroyed == true) {
            //如果页面已经销毁就不执行，防止闪退
            return@postDelayed
        }
        run()
    }, delayedTime)
}

fun Context.HandlerPost(run: () -> Unit) {
    Handler(this.mainLooper).post {
        run()
    }
}