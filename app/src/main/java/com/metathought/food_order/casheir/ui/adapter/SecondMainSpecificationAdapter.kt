package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTag
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.databinding.SecondSpecificationMainItemBinding


class SecondMainSpecificationAdapter(
    val list: List<GoodsTag>,
    ) : RecyclerView.Adapter<SecondMainSpecificationAdapter.MainSpecificationViewHolder>() {


    inner class MainSpecificationViewHolder(val binding: SecondSpecificationMainItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodsTag?) {
            resource?.let { it1 ->
                //prevent double click on same menu
                binding.tvTitle.text = resource.name
//                val chipsLayoutManager = ChipsLayoutManager.newBuilder(itemView.context).build()
                FlexboxLayoutManager(itemView.context).run {
                    flexDirection=FlexDirection.ROW
                    flexWrap=FlexWrap.WRAP
                    justifyContent=JustifyContent.FLEX_START
                    binding.recyclerSpecificationItem.layoutManager=this
                }
                resource.goodsTagItems.let {
                    resource.goodsTagItems?.let {item ->
                        binding.recyclerSpecificationItem.adapter = SecondSpecificationSubItemAdapter(
                            resource.type ?: 0,
                             item,
                        )
                    }

                }
            }
        }
    }

    fun getSelectGoodsTag(): ArrayList<GoodsTagItem> {
        val selectList = arrayListOf<GoodsTagItem>()
        list.forEach { tag ->
            val goodsTagItems =
                tag.goodsTagItems?.filter { it.isCheck == true }
            val list = goodsTagItems as ArrayList<GoodsTagItem>
            if (list.isNotEmpty()) {
                selectList.addAll(list)
            }
        }
        return selectList
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MainSpecificationViewHolder {
        return MainSpecificationViewHolder(
            SecondSpecificationMainItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        return list.count()
    }

    override fun onBindViewHolder(holder: MainSpecificationViewHolder, position: Int) {
        holder.bind(list[position])
    }


}