package com.metathought.food_order.casheir.data.model.base.response_model.table


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.order.ActivityLabel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.SingleItemDiscount
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.extension.halfUp
import java.math.BigDecimal

data class Goods(
    @SerializedName("cartsId")
    val cartsId: String?,
    @SerializedName("discountsAvailable")
    val discountsAvailable: Boolean?,
    @SerializedName("feeds")
    val feeds: List<Feed>?,
    @SerializedName("finalSinglePrice")
    val finalSinglePrice: Int?,
    @SerializedName("finalSingleVatPrice")
    val finalSingleVatPrice: Int?,
    @SerializedName("finalVipPrice")
    val finalVipPrice: Long?,
    @SerializedName("finalVipVatPrice")
    val finalVipVatPrice: Long?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("kitchenMaking")
    val kitchenMaking: Boolean?,
    @SerializedName("labels")
    val labels: Any?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("num")
    val num: Int?,
    @SerializedName("packingFee")
    val packingFee: Int?,
    @SerializedName("picUrl")
    val picUrl: String?,
    @SerializedName("sellPrice")
    val sellPrice: Long?,
    @SerializedName("vatPercentage")
    var vatPercentage: Int? = null,
    @SerializedName("vatWhitelisting")
    val vatWhitelisting: Boolean?,
    @SerializedName("serviceChargeWhitelisting")
    val serviceChargeWhitelisting: Boolean?,
    @SerializedName("tagItems")
    val tagItems: List<GoodsTagItem>?,
    @SerializedName("vipPrice")
    val vipPrice: Long?,
    /**
     * 计价方式
     * WHOLE_UNIT(0, "整份计费", ""),
     * PER_KILOGRAM(1, "每公斤", "KG"),
     * PER_POUND(2, "每磅", "LB"),
     * PER_LITER(3, "每升", "L"),
     * PER_OUNCE(4, "每盎司", "OZ"),
     * PER_GALLON(5, "每加仑", "GAL"),
     * PER_GRAM(6, "每克", "G");
     */
    @SerializedName("pricingMethod")
    val pricingMethod: Int? = null,
    /**
     * 重量
     */
    @SerializedName("weight")
    val weight: Double?,


    //单个商品折扣价 ，如果有这个价格就把原价展示为划线价
    @SerializedName("discountPrice")
    val discountPrice: Long? = null,


    //堂食服务费
    @SerializedName("finalServiceCharge")
    val finalServiceCharge: Long?,
    //折扣堂食服务费
    @SerializedName("finalDiscountServiceCharge")
    val finalDiscountServiceCharge: Long?,
    //Vip堂食服务费
    @SerializedName("finalVipServiceCharge")
    val finalVipServiceCharge: Long?,

    /**
     * 商品参与优惠活动标签
     */
    @SerializedName("activityLabels")
    var activityLabels: List<ActivityLabel>? = null,

    //商品类型:0-普通商品， 1-临时商品"
    @SerializedName("goodsType")
    var goodsType: Int? = null,

    /**
     * 已选套餐内容
     */
    @SerializedName("orderMealSetGoodsDTOList")
    var orderMealSetGoodsDTOList: List<OrderMealSetGood>? = null,

    //单品减免信息
    @SerializedName("singleItemDiscount")
    val singleItemDiscount: SingleItemDiscount? = null,

    /**
     * 佣金比例
     */
    @SerializedName("commissionPercentage")
    val commissionPercentage: BigDecimal? = null,

    ) {

    fun getGoodsTagStr(): String {
        val sbf = StringBuffer()
        tagItems?.let {
            if (it.isNotEmpty()) {
                for (i in it.indices) {
                    sbf.append(it[i].name)
                    if (i != it.size - 1)
                        sbf.append(", ")
                }
            }
        }

        feeds?.let {
            if (it.isNotEmpty()) {
                if (tagItems?.isNotEmpty() == true) {
                    sbf.append(", ")
                }
                for (i in it.indices) {
                    sbf.append(it[i].name + " x" + it[i].alreadyNum)
                    if (i != it.size - 1)
                        sbf.append(", ")
                }
            }
        }
        return sbf.toString()
    }

    //是否有折扣价
    fun isHasDiscountPrice(): Boolean {
        return discountPrice != null
    }

    fun getTotalService(): Long {
        return (finalServiceCharge ?: 0L).times(num ?: 0)
    }

    fun getTotalVipService(): Long {
        return (finalVipServiceCharge ?: 0L).times(num ?: 0)
    }

    fun getTotalDiscountService(): Long {
        return (finalDiscountServiceCharge ?: 0L).times(num ?: 0)
    }

    /**
     * 计算佣金
     *
     */
//    fun getTotalCommissionPrice(): Long {
//        return BigDecimal(discountPrice ?: 0L).times(commissionPercentage ?: BigDecimal.ZERO)
//            .divide(BigDecimal(100))
//            .halfUp(0).times(
//                BigDecimal(num ?: 0)
//            ).toLong()
//    }

}