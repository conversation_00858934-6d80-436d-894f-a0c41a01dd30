package com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood
import java.math.BigDecimal

data class AddToCartRequest(
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("goodsId")
    val goodsId: String?,
    @SerializedName("num")
    val num: Int?,
    @SerializedName("tableUuid")
    val tableUuid: String?,
    @SerializedName("feedInfoList")
    val feedInfoList: List<FeedBo?>?,
    @SerializedName("tagItemIds")
    val tagItemIds: String?,
    @SerializedName("goodsType")
    val goodsType: Int?,

    //商品重量(称重商品使用)
    @SerializedName("weight")
    val weight: Double?,
    //商品唯一标识(称重商品使用)
    @SerializedName("uuid")
    val uuid: String?,

    /**
     * 套餐内容
     */
    var mealSetGoodsInfoList: List<MealSetGood>? = null,
    var note: String? = null,

    /**
     * pricingCompleted sellPrice vipPrice  时价菜才传
     */
    var pricingCompleted: Boolean? = null,
    var sellPrice: Long? = null,
    var vipPrice: Long? = null,

)