package com.metathought.food_order.casheir.utils

import android.icu.util.Calendar
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_REALIZED
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 *<AUTHOR>
 *@time  2024/9/24
 *@desc
 **/

object TimeUtils {
    fun convertMillisecondsToTime(milliseconds: Long): String {
        val totalSeconds = milliseconds / 1000
        val seconds = totalSeconds % 60
        val totalMinutes = totalSeconds / 60
        val minutes = totalMinutes % 60
        val hours = totalMinutes / 60

        return String.format("%02d:%02d", minutes, seconds)
    }

    /**
     * 获取今天的时间范围
     * @return Pair<开始时间(yyyy-MM-dd HH:mm:ss), 结束时间(yyyy-MM-dd HH:mm:ss)>
     *     如果今天不在营业时间 则获取昨天的营业时间到今天营业时间
     */
    suspend fun getTodayTimeRange(): Pair<String, String> {
        val storeInfo = PreferenceHelper.getStoreInfo()
        val startCalendar = Calendar.getInstance()
        var startTime = Date()
        var endTime = Date()
        if (storeInfo != null && storeInfo.isSetOpenStartTime()) {
            Timber.e("有设置营业时间")
            val startTimeList = storeInfo.getStartTime()
            //当前时间在营业时间内
            //有设置营业时间 营业开始时间- 第二天营业开始时间
            //然后设置营业开始时间
            startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
            startCalendar.set(Calendar.MINUTE, startTimeList[1])
            startCalendar.set(Calendar.SECOND, startTimeList[2])
            startCalendar.set(Calendar.MILLISECOND, 0)
            startTime = (startCalendar.clone() as Calendar).time

            // 先将当前日期调整为明天  的营业开始时间为结束时间
            startCalendar.add(Calendar.DAY_OF_MONTH, +1)
            startCalendar.set(Calendar.MILLISECOND, 0)
            endTime = (startCalendar.clone() as Calendar).time
        } else {
            Timber.e("没设置营业时间")
            //今日0点-当前时间
            val endCalendar = Calendar.getInstance()
            endTime = (endCalendar.clone() as Calendar).time
            endCalendar.set(Calendar.HOUR_OF_DAY, 0)
            endCalendar.set(Calendar.MINUTE, 0)
            endCalendar.set(Calendar.SECOND, 0)
            endCalendar.set(Calendar.MILLISECOND, 0)
            startTime = endCalendar.time
        }
        return Pair(
            startTime.formatDateStr(FORMAT_DATE_TIME_REALIZED),
            endTime.formatDateStr(FORMAT_DATE_TIME_REALIZED)
        )
    }


    /**
     * 获取昨天的时间范围
     * @return Pair<开始时间(yyyy-MM-dd HH:mm:ss), 结束时间(yyyy-MM-dd HH:mm:ss)>
     *     如果设置了营业时间，则返回昨天营业时间开始到结束的范围；否则返回昨天00:00:00到23:59:59
     */
    suspend fun getYesterdayTimeRange(): Pair<String, String> {
        val storeInfo = PreferenceHelper.getStoreInfo()
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, -1) // 设置为昨天
        var startTime = Date()
        var endTime = Date()

        if (storeInfo != null && storeInfo.isSetOpenStartTime()) {
            val startTimeList = storeInfo.getStartTime()
            calendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天

            //然后设置营业开始时间
            calendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
            calendar.set(Calendar.MINUTE, startTimeList[1])
            calendar.set(Calendar.SECOND, startTimeList[2])
            calendar.set(Calendar.MILLISECOND, 0)
            startTime = (calendar.clone() as Calendar).time

            // 先将当前日期调整为今天  的营业开始时间为结束时间
            calendar.add(Calendar.DAY_OF_MONTH, +1)
            calendar.set(Calendar.MILLISECOND, 0)
            endTime = (calendar.clone() as Calendar).time

        } else {
            calendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
            // 获取昨天开始时间（零点整）
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            startTime = (calendar.clone() as Calendar).time

            // 获取昨天结束时间（23点59分59秒）
            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            endTime = calendar.time
        }

        return Pair(
            startTime.formatDateStr(FORMAT_DATE_TIME_REALIZED),
            endTime.formatDateStr(FORMAT_DATE_TIME_REALIZED)
        )
    }


}