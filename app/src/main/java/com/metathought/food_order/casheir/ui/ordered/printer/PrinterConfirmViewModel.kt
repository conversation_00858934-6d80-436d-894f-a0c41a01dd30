package com.metathought.food_order.casheir.ui.ordered.printer

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.WholeDiscountType
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PreSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PrinterAgainRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MultipleOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import retrofit2.http.Path
import timber.log.Timber
import java.math.BigDecimal
import java.math.RoundingMode
import javax.inject.Inject

@HiltViewModel
class PrinterConfirmViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _multiplePrinterState = MutableLiveData<ApiResponse<MultipleOrderResponse>>()
    val multiplePrinterState get() = _multiplePrinterState

    private val _postPrintState = MutableLiveData<ApiResponse<BaseBooleanResponse>>()
    val postPrintState get() = _postPrintState
    private val _postPreSettlementState = MutableLiveData<ApiResponse<OrderedInfoResponse>>()
    val postPreSettlementState get() = _postPreSettlementState
    fun getMultiplePrinter(orderID: String?) {
        viewModelScope.launch {
            _multiplePrinterState.value = ApiResponse.Loading
            _multiplePrinterState.value = repository.getMultiplePrint(orderID)
        }
    }

    fun postPrinterAgain(request: PrinterAgainRequest) {
        viewModelScope.launch {
            try {
                _postPrintState.value = ApiResponse.Loading
                _postPrintState.value = repository.postPrintTicket(request)
            } catch (e: Exception) {
                _postPrintState.value = ApiResponse.Error(e.message)

            }

        }
    }

    fun preSettlement(orderInfo: OrderedInfoResponse?, shouldCloudPrinterPrint: Boolean? = true) {
        viewModelScope.launch {
            try {
//                Timber.e(
//                    "BigDecimal(orderInfo.reduceDollar!!).div(BigDecimal(100.0)) ${
//                        BigDecimal(
//                            orderInfo?.reduceDollar!!
//                        ).div(BigDecimal(100.0))
//                    }"
//                )
                //reduceDollar 这边需要除以100 yi
                _postPreSettlementState.value = ApiResponse.Loading
//                _postPrintState.value = repository.preSettlement(
//                    orderInfo?.orderNo, orderInfo?.getCurrentCoupon()?.code,
//                    if (orderInfo?.reduceDollar != null) BigDecimal(
//                        orderInfo.reduceDollar!!.div(
//                            100.0
//                        )
//                    ).halfUp(2).stripTrailingZeros() else null,
//                    if (orderInfo?.reduceKhr != null) BigDecimal(orderInfo.reduceKhr!!) else null,
//                    orderInfo?.reduceRate,
//                    orderInfo?.reduceType
//                )
                _postPreSettlementState.value = repository.preSettlement(
//                    PreSettlementRequest(
//                        orderNo = orderInfo?.orderNo,
//                        couponCode = orderInfo?.getCurrentCoupon()?.code,
//                        reduceType = orderInfo?.getWholeDiscountType(),
//                        reduceRate = if (orderInfo?.getWholeDiscountType() == WholeDiscountType.PERCENTAGE.id) orderInfo?.price?.wholeDiscountReduce?.reduceRate else null,
//                        reduceDollar = if (orderInfo?.getWholeDiscountType() == WholeDiscountType.FIXED_AMOUNT.id) orderInfo?.price?.wholeDiscountReduce?.reduceDollar else null,
//                        reduceVipDollar = if (orderInfo?.getWholeDiscountType() == WholeDiscountType.FIXED_AMOUNT.id) orderInfo?.vipPrice?.wholeDiscountReduce?.reduceDollar else null,
//                        discountReduceActivityId = orderInfo?.discountReduceActivity?.id
//                    )
                    PreSettlementRequest(
                        orderNo = orderInfo?.orderNo,
                        couponCode = orderInfo?.getCurrentCoupon()?.code,
                        reduceType = orderInfo?.getWholeDiscountType(),
                        reduceRate = orderInfo?.wholeDiscountReduce?.reduceRate,
                        reduceDollar = orderInfo?.wholeDiscountReduce?.reduceDollar,
                        reduceKhr = orderInfo?.wholeDiscountReduce?.reduceKhr,
                        reduceVipDollar = orderInfo?.wholeDiscountReduce?.reduceVipDollar,
                        reduceVipKhr = orderInfo?.wholeDiscountReduce?.reduceVipKhr,
                        discountReduceActivityId = orderInfo?.discountReduceActivity?.id,

                        shouldCloudPrinterPrint = shouldCloudPrinterPrint
                    )
                )

            } catch (e: Exception) {
                _postPreSettlementState.value = ApiResponse.Error(e.message)
            }

        }
    }

}