package com.metathought.food_order.casheir.data.model.base.response_model.dashboard


import com.google.gson.annotations.SerializedName

data class ProductDetail(
    @SerializedName("feedsNameList")
    val feedsNameList: List<Any?>?,
    @SerializedName("finalPrice")
    val finalPrice: Double?,
    @SerializedName("num")
    val num: Int?,
    @SerializedName("originPrice")
    val originPrice: Double?,
    @SerializedName("productName")
    val productName: String?,
    @SerializedName("tagsItemsNameList")
    val tagsItemsNameList: List<String?>?
)