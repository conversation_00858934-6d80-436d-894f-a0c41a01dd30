package com.metathought.food_order.casheir.database.dao

import android.annotation.SuppressLint
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.response_model.cart.CartInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import org.litepal.LitePal
import org.litepal.extension.findFirst
import timber.log.Timber
import java.math.BigDecimal
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2024/3/1816:54
 * @description
 */
object ShoppingHelper {


    fun get(diningStyle: Int): ShoppingRecord? {
        return LitePal.where(
            "diningStyle = ?",
            diningStyle.toString()
        ).findFirst<ShoppingRecord>()
    }

    fun del(diningStyle: Int): ShoppingRecord? {
        val all = get(diningStyle)
        Timber.e("del  ->  ${diningStyle}")
        //这里执行删除太耗时久不删了
//        val goodsVoList = all?.getGoodsVoList()
//        goodsVoList?.forEach {
//            HashHelper.delete(
//                it.feedInfoList,
//                it.goodsTagItems,
//                it.orderMealSetGoodList,
//                it.goods!!.id,
//                it.singleDiscountGoods,
//                it.getGoodPriceKey(),
//                it.note,
//                uuid = it.goods?.uuid,
//            )
//        }
        hashCache.clear()
        GoodsHelper.del(diningStyle)
        all?.apply {
            totalPrice = 0L
            vatCharge = 0L
            serviceFeeCharge = 0L
            packPrice = 0L
            goodsVoStr = "[]"
            dataVersion = null
            timestamp = null
            all.save()
        }
        return all
    }

    fun delAndCustomerAndTable(diningStyle: Int): ShoppingRecord? {
        Timber.e("删除delAndCustomerAndTable  $diningStyle")
        val all = get(diningStyle)
        hashCache.clear()
        GoodsHelper.del(diningStyle)
        //change dinningNumber to 0 and peopleNum to 0 for fix ticket https://chandao.metathought.co/bug-view-2781.html
        all?.apply {
            totalPrice = 0L
            vatCharge = 0L
            serviceFeeCharge = 0L
            packPrice = 0L
            goodsVoStr = "[]"
            name = null
            diningNumber = 0
            diningTime = ""
            mobile = ""
            areaCode = ""
            peopleNum = 0
            tableUuid = ""
            name = ""
            tableLabel = ""
            tableType = 0
            isOrderMore = false
            orderMoreID = ""
            payStatus = null
            takeOutOrderId = ""
            dataVersion = null
            timestamp = null
            save()
        }
        return all
    }


    fun getDefault(diningStyle: Int): ShoppingRecord {
        return LitePal.where(
            "diningStyle = ?",
            diningStyle.toString()
        ).findFirst<ShoppingRecord>() ?: getNullRecord(diningStyle)
    }

    private fun getNullRecord(diningStyle: Int): ShoppingRecord {
        return ShoppingRecord(
            diningStyle = diningStyle,
            totalPrice = 0L,
            vatCharge = 0L,
            serviceFeeCharge = 0L,
            packPrice = 0L,
            goodsVoStr = "[]"
        )
    }

    private fun calculateTotalPrice(
        goodsRequestList: List<GoodsRequest>,
        localDingingStyle: Int?
    ): List<Long> {
        var totalPrice = 0L
        var vatFee = 0L
        var serviceFee = 0L
        var packPrice = 0L
        goodsRequestList.forEach {
            /**
             * 没售罄才做计算
             */
            if (it.goods?.isSoldOut() != true) {
                /**
                 * 2.12.0 开始增值税最后计算整单
                 */
//                if (it.goods?.vatWhitelisting != true) {
//                    val rate = (it.goods?.getVatPercentage() ?: 0) / 100.0
//                    //服务端是单个算的 ，所以这里要和服务端一样单个算 舍去小数点2位以后的
//                    val fee = (it.finalSinglePrice!! * rate).toInt() * it.num!!
////                    val fee = (it.totalPrice() * rate).toInt()
//                    vatFee += fee
//                }

                if (it.goods?.serviceChargeWhitelisting != true) {
                    val rate = (it.goods?.getServiceChargePercentage() ?: 0) / 100.0
                    //服务端是单个算的 ，所以这里要和服务端一样单个算 舍去小数点2位以后的
                    val fee =
                        BigDecimal(it.finalSinglePrice!! * rate).halfUp(0)
                            .toInt() * it.num!!
//                    val fee = (it.totalPrice() * rate).toInt()
                    serviceFee += fee
                }

                totalPrice += it.totalPrice()

                packPrice += it.totalPackPrice()
            }
        }

        if (localDingingStyle == DiningStyleEnum.TAKE_AWAY.id || (localDingingStyle
                ?: DiningStyleEnum.DINE_IN.id) >= TakeOutPlatformToDiningHelper.BASE_INDEX
        ) {
            //外面不计算服务费
            serviceFee = 0
        }

        Timber.e("vatFee  =>${vatFee}")
        return arrayListOf(totalPrice + vatFee + serviceFee, vatFee, packPrice, serviceFee)

    }

    fun getSubListByGoods(goodsId: String, localDingingStyle: Int): ArrayList<GoodsRequest> {
        val first = get(localDingingStyle)
        if (first != null) {
            val goodsVoRequestList = first.getGoodsVoList()
            val filter = goodsVoRequestList.filter { request ->
                request.goods?.id == goodsId
            }
            return filter as ArrayList<GoodsRequest>
        }
        return arrayListOf()
    }

    fun updateTotalPrice(first: ShoppingRecord) {
        val list = first.getGoodsVoList()
        if (list.isNotEmpty()) {
            val values = calculateTotalPrice(list, first.diningStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = list.toJson()
            first.save()
        }
    }

    fun updateTotalPriceAndName(first: ShoppingRecord, goods: ArrayList<Goods>) {
        val list = first.getGoodsVoList()
        if (list.isNotEmpty()) {
            list.forEach {
//
                val indexOf = goods.indexOfFirst { value -> value.id == it.goods?.id }
                if (indexOf != -1) {
                    /**
                     * 更新菜品翻译 价格之类的
                     */
                    it.goods?.name = goods[indexOf].name
                    it.goods?.vatPercentage = goods[indexOf].getVatPercentage()
                    it.goods?.vatWhitelisting = goods[indexOf].vatWhitelisting
                    it.goods?.serviceChargeWhitelisting = goods[indexOf].serviceChargeWhitelisting
                    it.goods?.serviceChargePercentage = goods[indexOf].serviceChargePercentage
                    it.goods?.soldOut = goods[indexOf].soldOut
                    it.goods?.activityLabels = goods[indexOf].activityLabels
                    it.goods?.discountItemWhitelisting = goods[indexOf].discountItemWhitelisting
                    it.goods?.commissionPercentage = goods[indexOf].commissionPercentage
                    it.goods?.pricingMethod = goods[indexOf].pricingMethod
                    it.goods?.packingFeeDisplay = goods[indexOf].packingFeeDisplay
                    it.goods?.type = goods[indexOf].type
                    //判断价格类型是否有变更
                    if (it.goods?.currentPrice == goods[indexOf].currentPrice) {
                        it.goods?.currentPrice = goods[indexOf].currentPrice
                        //如果没有价格类型没有变动
                        if (it.goods?.isTimePriceGood() == false) {
                            //非时价菜要更新金额
                            it.goods?.sellPrice = goods[indexOf].sellPrice
                            it.goods?.discountPrice = goods[indexOf].discountPrice
                            it.goods?.vipPrice = goods[indexOf].vipPrice
                            it.goods?.setPriceCompletedFlag(false)
                        }
                    } else {
                        //如果价格类型有变动
                        it.goods?.currentPrice = goods[indexOf].currentPrice
                        if (it.goods?.isTimePriceGood() == false) {
                            //非时价菜要更新金额
                            it.goods?.sellPrice = goods[indexOf].sellPrice
                            it.goods?.discountPrice = goods[indexOf].discountPrice
                            it.goods?.vipPrice = goods[indexOf].vipPrice
                        } else {
                            it.goods?.sellPrice = null
                            it.goods?.discountPrice = null
                            it.goods?.vipPrice = null
                        }
                        it.goods?.setPriceCompletedFlag(false)
                    }


                    it.goodsTagItems?.forEach { tagItem ->
                        goods[indexOf].tags?.forEach { tags ->
                            tags.goodsTagItems?.forEach { tag ->
                                if (tagItem.id == tag.id) {
                                    tagItem.name = tag.name
                                    tagItem.price = tag.price
                                }
                            }
                        }
                    }

                    it.feedInfoList?.forEach { feed ->
                        goods[indexOf].feeds?.forEach { localFeed ->
                            if (feed.id == localFeed.id) {
                                localFeed.name = localFeed.name
                            }
                        }
                    }

                    it.orderMealSetGoodList?.forEach { goodsDto ->
                        goods[indexOf].mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
                            mealSetGroup.mealSetGoodsList?.forEach { mealSetGoods ->
                                //找到本地菜单里面套餐里面的这个菜品
                                if (goodsDto.mealSetGoodsId == mealSetGoods.goodsId) {
                                    //修改套餐内对应菜品名字翻译
                                    goodsDto.mealSetGoodsName = mealSetGoods.name
                                    goodsDto.num = mealSetGoods.num
                                    goodsDto.mealSetTagItemList?.forEach { tag ->
                                        mealSetGoods.tags?.forEach { localTag ->
                                            val index =
                                                localTag.goodsTagItems?.indexOfFirst { it.id == tag.id }
                                                    ?: -1
                                            if (index != -1) {
                                                tag.name =
                                                    localTag.goodsTagItems!![index].name
                                                tag.price = localTag.goodsTagItems!![index].price

                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            val values = calculateTotalPrice(list, first.diningStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = list.toJson()
            first.save()
        }
    }

    /**
     *更新临时菜品名字和价格
     *
     * @param first
     * @param goods
     */
    fun updateTmpGoodTotalPriceAndName(first: ShoppingRecord, goods: ArrayList<Goods>) {
        val list = first.getGoodsVoList()
        if (list.isNotEmpty()) {
            list.forEach {
                val indexOf = goods.indexOfFirst { value -> value.id == it.goods?.id }
                if (indexOf != -1) {
                    it.goods?.name = goods[indexOf].name
                    it.goods?.sellPrice = goods[indexOf].sellPrice
                }
            }
            val values = calculateTotalPrice(list, first.diningStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = list.toJson()
            first.save()
        }
    }


    /**
     * 根据服务端返回购物车的数据 批量更新购物车
     *
     * @param cartInfoResponse
     * @param localDingingStyle
     * @param goodsRequestList
     * @return
     */
    fun updateGoods(
        cartInfoResponse: CartInfoResponse,
        localDingingStyle: Int,
        goodsRequestList: ArrayList<GoodsRequest>?
    ): ShoppingRecord? {

        val start = System.nanoTime()
        val first = get(localDingingStyle)
        if (first != null) {
//            val goodsMap = mutableMapOf<String, GoodsRequest>()
            if (!goodsRequestList.isNullOrEmpty()) {
                //将修改后的菜直接设置到list进行更新
                //Set the modified dish directly to the list for update
                // 使用并行处理来优化大量菜品的处理
                processGoodsRequestsOptimized(goodsRequestList, localDingingStyle)
            }
            Log.e("购物车耗时", "菜品 循环 Time: ${(System.nanoTime() - start) / 1_000_000} ms")
            Timber.e("保存 购物车版本号：${cartInfoResponse.dataVersion}")
            if (goodsRequestList.isNullOrEmpty()) {
                first.goodsVoStr = "[]"
//                first.delete()
                first.totalPrice = 0L
                first.vatCharge = 0L
                first.serviceFeeCharge = 0L
                first.packPrice = 0L
                first.dataVersion = cartInfoResponse.dataVersion
                first.timestamp = cartInfoResponse.timestamp
                first.save()
            } else {
                val values = calculateTotalPrice(goodsRequestList, first.diningStyle)
                //本地购物车totalPrice 不带打包费 所以要删除掉
//                first.totalPrice = (cartInfoResponse.totalPrice
//                    ?: 0) - (if (localDingingStyle == DiningStyleEnum.TAKE_AWAY.id) (cartInfoResponse.totalPackingFee
//                    ?: 0) else 0) //  values[0]
//                first.vatCharge = cartInfoResponse.totalVatPrice  //values[1]
//                first.packPrice = cartInfoResponse.totalPackingFee  //values[2]
//                first.serviceFeeCharge = cartInfoResponse.totalServiceCharge //values[3]
                first.totalPrice = values[0]
                first.vatCharge = values[1]
                first.packPrice = values[2]
                first.serviceFeeCharge = values[3]
                first.goodsVoStr = goodsRequestList.toJson()
                first.dataVersion = cartInfoResponse.dataVersion
                first.timestamp = cartInfoResponse.timestamp
                first.save()
            }

        }

        Log.e("购物车耗时", "菜品 落库 Time: ${(System.nanoTime() - start) / 1_000_000} ms")
        return first
    }

    /**
     * 优化的商品请求处理方法 - 使用批量处理和预计算
     */
    fun processGoodsRequestsOptimized(
        goodsRequestList: ArrayList<GoodsRequest>,
        localDingingStyle: Int
    ) {
        val processStart = System.nanoTime()

        // 第一步：预先计算每个商品ID的总数量 - O(n)复杂度
        val goodsTotalMap = goodsRequestList
            .groupBy { it.goods?.id }
            .mapValues { (_, requests) -> requests.sumOf { it.num ?: 0 } }
            .filterKeys { it != null }

        Log.e(
            "购物车耗时",
            "预计算商品数量 Time: ${(System.nanoTime() - processStart) / 1_000_000} ms"
        )

        // 第二步：批量保存HashHelper数据
        val hashStart = System.nanoTime()

        // 对于大量商品，使用批量处理
        val batchSize = 20 // 可以根据实际情况调整批量大小
        goodsRequestList.chunked(batchSize).forEach { batch ->
            batch.forEach { request ->
                request.goods?.id?.let { goodsId ->
                    val hashKey = request.getHash()
                    hashCache.getOrPut(hashKey) {
                        hashKey
                    }
                }
            }
        }

        Log.e("购物车耗时", "保存Hash数据 Time: ${(System.nanoTime() - hashStart) / 1_000_000} ms")

        // 第三步：批量更新GoodsHelper
        val updateStart = System.nanoTime()

//        // 只处理每个商品ID一次
//        goodsTotalMap.forEach { (goodsId, total) ->
//            goodsId?.let { id ->
//                goodsRequestList.find { it.goods?.id == id }?.goods?.let { goods ->
//                    GoodsHelper.update(goods, localDingingStyle, total)
//                }
//            }
//        }
        GoodsHelper.clearGoodsRecords()
        // 创建一个异步任务来处理GoodsHelper.update操作
        val updateJob = CoroutineScope(Dispatchers.IO).launch {
            // 只处理每个商品ID一次
            goodsTotalMap.forEach { (goodsId, total) ->
                goodsId?.let { id ->
                    goodsRequestList.find { it.goods?.id == id }?.goods?.let { goods ->
                        GoodsHelper.update(goods, localDingingStyle, total)
                    }
                }
            }
        }

        Log.e(
            "购物车耗时",
            "更新商品数据 Time: ${(System.nanoTime() - updateStart) / 1_000_000} ms"
        )
        Log.e("购物车耗时", "菜品处理 总Time: ${(System.nanoTime() - processStart) / 1_000_000} ms")
    }


    // 缓存购物车记录和商品哈希值
//    private var cachedShoppingRecord: ShoppingRecord? = null
//    private var cachedDiningStyle: Int = -1
    private val hashCache = mutableMapOf<String, String>()

//    private fun getHashCacheKey(goodsId: String, note: String?, uuid: String?): String {
//        return "${goodsId}_${note ?: ""}_${uuid ?: ""}"
//    }

    // 清理缓存
    private fun clearCache() {
//        cachedShoppingRecord = null
//        cachedDiningStyle = -1
        hashCache.clear()
    }

    // 在适当的时机清理缓存，比如切换用餐方式、清空购物车等
//    private fun clearCacheIfNeeded(localDiningStyle: Int) {
//        if (cachedDiningStyle != localDiningStyle) {
//            clearCache()
//        }
//    }

    //单个加购
    fun plusAndAdd(
        goods: Goods,
        localDingingStyle: Int,
        goodsTagItemList: ArrayList<GoodsTagItem>? = null,
        feedList: ArrayList<Feed>? = null,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        singleDiscountGoods: SingleDiscountGoods?,
        note: String?,
        cartCount: Int = 1,
    ) = CoroutineScope(Dispatchers.IO).run {
        val start = System.nanoTime()

        // 从缓存获取购物车记录，如果缓存失效则从数据库获取
        val first = get(localDingingStyle)

        Log.e(
            "购物车耗时",
            "plusAndAdd 使用缓存记录 Time: ${(System.nanoTime() - start) / 1_000_000} ms.  cartCount:${cartCount}"
        )

        val nowGoodsRequest = GoodsRequest(
            num = cartCount,
            feedInfoList = feedList,
            goodsTagItems = goodsTagItemList,
            orderMealSetGoodList = orderMealSetGoodList,
            goods = goods,
            singleDiscountGoods = singleDiscountGoods,
            note = note
        ).apply {
            finalSinglePrice = calculateSinglePrice()
        }

        // 从缓存获取或计算商品哈希值
        val hashCacheKey = HashHelper.getHash(
            feedList,
            goodsTagItemList,
            orderMealSetGoodList,
            goods.id,
            singleDiscountGoods,
            goodsPriceKey = nowGoodsRequest.getGoodPriceKey(),
            note = note,
            uuid = goods.uuid
        )

        if (first == null || first.goodsVoStr == "[]") {
            Log.e(
                "购物车耗时",
                "plusAndAdd 初始化 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )
            // 预先计算并缓存商品哈希值
//            val hashCacheKey = HashHelper.getHash(
//                feedList,
//                goodsTagItemList,
//                orderMealSetGoodList,
//                goods.id,
//                singleDiscountGoods,
//                goodsPriceKey = nowGoodsRequest.getGoodPriceKey(),
//                note = note,
//                uuid = goods.uuid
//            )
            val goodsHash = hashCache.getOrPut(hashCacheKey) { hashCacheKey }

            val goodsRequestList = arrayListOf(nowGoodsRequest)
            val values = calculateTotalPrice(goodsRequestList, localDingingStyle)

            val record = first?.apply {
                totalPrice = values[0]
                vatCharge = values[1]
                packPrice = values[2]
                serviceFeeCharge = values[3]
                storeId = goods.storeId
                diningStyle = localDingingStyle
                goodsVoStr = goodsRequestList.toJson()
                save()
                // 更新缓存
//                cachedShoppingRecord = this
            } ?: ShoppingRecord(
                totalPrice = values[0],
                vatCharge = values[1],
                packPrice = values[2],
                serviceFeeCharge = values[3],
                storeId = goods.storeId,
                diningStyle = localDingingStyle,
                goodsVoStr = goodsRequestList.toJson()
            ).apply {
                save()
                // 更新缓存
//                cachedShoppingRecord = this
//                cachedDiningStyle = localDingingStyle
            }

            // 更新菜单关联查询数据
            GoodsHelper.update(goods, localDingingStyle, cartCount)

            Log.e(
                "购物车耗时",
                "plusAndAdd 空购物车添加完成 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )
            return@run record
        } else {
            Log.e(
                "购物车耗时",
                "plusAndAdd 开始处理已有购物车 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )

            val goodsVoRequestList = first.getGoodsVoList()

            val goodsHash = hashCache.getOrPut(hashCacheKey) {
                Log.e(
                    "购物车耗时",
                    "plusAndAdd 计算商品哈希值 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
                )
                hashCacheKey
            }

            // 使用 indexOfFirst 直接查找完全相同的商品（基于商品ID和哈希值）
            val existingIndex = goodsVoRequestList.indexOfFirst { request ->
                request.goods?.id == goods.id && request.getHash() == goodsHash
            }

            var isAdd = false
            if (existingIndex != -1) {
                // 找到完全相同的商品，直接更新数量
                val existingRequest = goodsVoRequestList[existingIndex]
                existingRequest.num = existingRequest.num!! + cartCount
                isAdd = true

                // 更新菜单关联查询数据
                val sumTotal = goodsVoRequestList
                    .filter { it.goods?.id == goods.id }
                    .sumOf { it.num ?: 0 }
                GoodsHelper.update(goods, localDingingStyle, sumTotal)

                Log.e(
                    "购物车耗时",
                    "plusAndAdd 更新已有商品数量 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
                )
            }
            if (!isAdd) {
                // 添加新商品并保存哈希值到缓存和数据库
//                val hashCacheKey = HashHelper.getHash(
//                    feedList,
//                    goodsTagItemList,
//                    orderMealSetGoodList,
//                    goods.id,
//                    singleDiscountGoods,
//                    goodsPriceKey = nowGoodsRequest.getGoodPriceKey(),
//                    note = note,
//                    uuid = goods.uuid
//                )
                val goodsHash = hashCache.getOrPut(hashCacheKey) { hashCacheKey }

                goodsVoRequestList.add(nowGoodsRequest)

                // 更新菜单关联查询数据
                val sumTotal = goodsVoRequestList
                    .filter { it.goods?.id == goods.id }
                    .sumOf { it.num ?: 0 }
                GoodsHelper.update(goods, localDingingStyle, sumTotal)

                Log.e(
                    "购物车耗时",
                    "plusAndAdd 添加新商品 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
                )
            }

            // 批量更新购物车记录并更新缓存
            val values = calculateTotalPrice(goodsVoRequestList, localDingingStyle)
            first.apply {
                totalPrice = values[0]
                vatCharge = values[1]
                packPrice = values[2]
                serviceFeeCharge = values[3]
                goodsVoStr = goodsVoRequestList.toJson()
                save()
                // 更新缓存
//                cachedShoppingRecord = this
            }

            Log.e(
                "购物车耗时",
                "plusAndAdd 完成 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )
            return@run first
        }
    }

    fun subAndDel(
        goods: Goods, localDingingStyle: Int,
        goodsTagItemList: ArrayList<GoodsTagItem>? = null,
        feedList: ArrayList<Feed>? = null,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        singleDiscountGoods: SingleDiscountGoods?,
        note: String?,
        cartCount: Int = 1
    ) = CoroutineScope(Dispatchers.IO).run {
        val start = System.nanoTime()
        val first = get(localDingingStyle)
        if (first == null) {
            Log.e(
                "购物车耗时",
                "subAndDel 购物车为空 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )
            return@run null
        }

        singleDiscountGoods?.num = cartCount
        val nowGoodsRequest = GoodsRequest(
            num = cartCount,
            feedInfoList = feedList,
            goodsTagItems = goodsTagItemList,
            orderMealSetGoodList = orderMealSetGoodList,
            goods = goods,
            singleDiscountGoods = singleDiscountGoods,
            note = note
        ).apply {
            finalSinglePrice = calculateSinglePrice()
        }

        val goodsVoList = first.getGoodsVoList()

        // 预先计算商品哈希值
        val goodsHash = HashHelper.getHash(
            feedList,
            goodsTagItemList,
            orderMealSetGoodList,
            goods.id,
            singleDiscountGoods,
            goodsPriceKey = nowGoodsRequest.getGoodPriceKey(),
            note = note,
            uuid = goods.uuid
        )

        // 使用 indexOfFirst 直接查找完全相同的商品（基于商品ID和哈希值）
        val existingIndex = goodsVoList.indexOfFirst { request ->
            request.goods?.id == goods.id && request.getHash() == goodsHash
        }

        if (existingIndex != -1) {
            val existingRequest = goodsVoList[existingIndex]
            existingRequest.num = existingRequest.num!! - cartCount

            Log.e(
                "购物车耗时",
                "subAndDel 更新商品数量 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )
            // 如果商品数量减少到零或以下，从列表中移除
            if (existingRequest.num!! <= 0) {
                goodsVoList.removeAt(existingIndex)
                hashCache.remove(goodsHash)
                Log.e(
                    "购物车耗时",
                    "subAndDel 移除商品 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
                )
            }

            // 更新菜单关联查询数据
            val sumTotal = goodsVoList
                .filter { it.goods?.id == goods.id }
                .sumOf { it.num ?: 0 }
            GoodsHelper.update(goods, localDingingStyle, sumTotal)
            Log.e(
                "购物车耗时",
                "subAndDel 更新菜单关联数据 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )

            // 处理购物车记录更新
            if (goodsVoList.isEmpty()) {
                // 购物车为空，重置所有字段
                first.apply {
                    goodsVoStr = "[]"
                    totalPrice = 0L
                    vatCharge = 0L
                    serviceFeeCharge = 0L
                    packPrice = 0L
                    // 非加购，如果删除了最后一个菜，清掉优惠券
                    if (isOrderMore == false) {
                        couponModel = ""
                    }
                    save()
                }
                Log.e(
                    "购物车耗时",
                    "subAndDel 清空购物车 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
                )
            } else {
                // 更新购物车记录
                val values = calculateTotalPrice(goodsVoList, localDingingStyle)
                first.apply {
                    totalPrice = values[0]
                    vatCharge = values[1]
                    packPrice = values[2]
                    serviceFeeCharge = values[3]
                    goodsVoStr = goodsVoList.toJson()
                    save()
                }
                Log.e(
                    "购物车耗时",
                    "subAndDel 更新购物车 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
                )
            }
            return@run first
        } else {
            Log.e(
                "购物车耗时",
                "subAndDel 未找到商品 Time: ${(System.nanoTime() - start) / 1_000_000} ms"
            )
            return@run null
        }
    }


    //修改菜品数量
    fun updateGoodsNum(
        goods: Goods,
        localDingingStyle: Int,
        goodsTagItemList: ArrayList<GoodsTagItem>? = null,
        feedList: ArrayList<Feed>? = null,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        singleDiscountGoods: SingleDiscountGoods?,
        note: String?,
        cartCount: Int = 1
    ): ShoppingRecord {
        Timber.e("updateGoodsNum  localDingingStyle==> $localDingingStyle")
        val first = get(localDingingStyle)
        Timber.e("first=> ${first?.toJson()}")

        val nowGoodsRequest = GoodsRequest(
            num = cartCount,
            feedInfoList = feedList,
            goodsTagItems = goodsTagItemList,
            orderMealSetGoodList = orderMealSetGoodList,
            goods = goods,
            singleDiscountGoods = singleDiscountGoods,
            note = note,
        ).apply {
            finalSinglePrice = calculateSinglePrice()
        }
        // 预计算哈希值，避免重复计算
        val goodsHash = nowGoodsRequest.getHash()

        if (first == null || first.goodsVoStr == "[]") {
            //购物车还没有这道菜 This dish is not available in your shopping cart yet
            val goodsRequestList = arrayListOf(nowGoodsRequest)
            val values = calculateTotalPrice(goodsRequestList, localDingingStyle)
            val resultRecord = first?.apply {
                totalPrice = values[0]
                vatCharge = values[1]
                packPrice = values[2]
                serviceFeeCharge = values[3]
                storeId = goods.storeId
                diningStyle = localDingingStyle
                goodsVoStr = goodsRequestList.toJson()
            } ?: ShoppingRecord(
                totalPrice = values[0],
                vatCharge = values[1],
                packPrice = values[2],
                serviceFeeCharge = values[3],
                storeId = goods.storeId,
                diningStyle = localDingingStyle,
                goodsVoStr = goodsRequestList.toJson()
            )
            resultRecord.save()
            //由于一道菜可能由于feed和规格的不同，所以在购物车出现多种情况，所以给他们假定一个hash进行区分
            //Since a dish may appear in multiple situations in the shopping cart due to different feeds and specifications, a hash is assumed for them to distinguish them.
            val hashCacheKey = HashHelper.getHash(
                feedList,
                goodsTagItemList,
                orderMealSetGoodList,
                goods.id,
                singleDiscountGoods,
                goodsPriceKey = nowGoodsRequest.getGoodPriceKey(),
                note = note,
                uuid = goods.uuid
            )
            // 更新菜单数量
            GoodsHelper.update(goods, localDingingStyle, cartCount)
            Timber.e("购物车为空的时候添加商品")
            return resultRecord
        } else {
            val goodsVoRequestList = first.getGoodsVoList().toMutableList()
            // 使用any()优化存在性检查
            val exists = goodsVoRequestList.any { it.getHash() == goodsHash }
            if (exists) {
                // 更新已有商品数量
                goodsVoRequestList.forEach {
                    if (it.getHash() == goodsHash) {
                        it.num = cartCount
                        it.singleDiscountGoods = singleDiscountGoods
                    }
                }
            } else {
                // 添加新商品
                goodsVoRequestList.add(nowGoodsRequest)
            }
            // 计算总数并更新菜单
            val totalNum =
                goodsVoRequestList.filter { it.goods?.id == goods.id }.sumOf { it.num ?: 0 }
            GoodsHelper.update(goods, localDingingStyle, totalNum)

            val values = calculateTotalPrice(goodsVoRequestList, localDingingStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = goodsVoRequestList.toJson()
            first.save()
        }
        return first
    }


    fun updateGoodsRemark(
        localDingingStyle: Int,
        goodsRequest: GoodsRequest,
        note: String?,
    ): ShoppingRecord? {
        Timber.e("没有相同hashkey 修改商品备注")
        val first = get(localDingingStyle)
        Timber.e("first=> ${first?.toJson()}")
        first?.apply {
            val targetHash = goodsRequest.getHash()
            val goodsVoRequestList = first.getGoodsVoList()
            val index =
                first.getGoodsVoList().indexOfFirst { it.getHash() == targetHash } ?: -1
            if (index != -1) {
//                HashHelper.delete(
//                    goodsRequest.feedInfoList,
//                    goodsRequest.goodsTagItems,
//                    goodsRequest.orderMealSetGoodList,
//                    goodsRequest.goods?.id!!,
//                    goodsRequest.singleDiscountGoods,
//                    goodsPriceKey = goodsRequest.getGoodPriceKey(),
//                    note = goodsRequest.note,
//                    uuid = goodsRequest.goods?.uuid
//                )
                hashCache.remove(targetHash)

                goodsRequest.note = note
                goodsVoRequestList[index] = goodsRequest
                val newHashKey = goodsRequest.getHash()
                hashCache.getOrPut(newHashKey) {
                    newHashKey
                }
//                HashHelper.save(
//                    goodsRequest.feedInfoList,
//                    goodsRequest.goodsTagItems,
//                    goodsRequest.orderMealSetGoodList,
//                    goodsRequest.goods?.id!!,
//                    goodsRequest.singleDiscountGoods,
//                    goodsPriceKey = goodsRequest.getGoodPriceKey(),
//                    note = goodsRequest.note,
//                    uuid = goodsRequest.goods?.uuid
//                )
            }
            val values = calculateTotalPrice(goodsVoRequestList, localDingingStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = goodsVoRequestList.toJson()
            first.save()
        }

        return first
    }

    fun updateGoodsSingleDiscount(
        localDingingStyle: Int,
        goodsRequest: GoodsRequest,
        num: Int,
        singleDiscountGoods: SingleDiscountGoods?,
    ): ShoppingRecord? {
        Timber.e("没有相同hashkey 修改商品单品减免")
        val first = get(localDingingStyle)
        Timber.e("first=> ${first?.toJson()}")
        first?.apply {
            val targetHash = goodsRequest.getHash()
            val goodsVoRequestList = first.getGoodsVoList()
            val index =
                first.getGoodsVoList().indexOfFirst { it.getHash() == targetHash } ?: -1
            if (index != -1) {
//                HashHelper.delete(
//                    goodsRequest.feedInfoList,
//                    goodsRequest.goodsTagItems,
//                    goodsRequest.orderMealSetGoodList,
//                    goodsRequest.goods?.id!!,
//                    goodsRequest.singleDiscountGoods,
//                    goodsPriceKey = goodsRequest.getGoodPriceKey(),
//                    note = goodsRequest.note,
//                    uuid = goodsRequest.goods?.uuid
//                )
                hashCache.remove(targetHash)
                goodsRequest.num = num
                goodsRequest.singleDiscountGoods = singleDiscountGoods.apply { this?.num = num }
                goodsVoRequestList[index] = goodsRequest

                val newHashKey = goodsRequest.getHash()
                hashCache.getOrPut(newHashKey) {
                    newHashKey
                }
//                HashHelper.save(
//                    goodsRequest.feedInfoList,
//                    goodsRequest.goodsTagItems,
//                    goodsRequest.orderMealSetGoodList,
//                    goodsRequest.goods?.id!!,
//                    goodsRequest.singleDiscountGoods,
//                    goodsPriceKey = goodsRequest.getGoodPriceKey(),
//                    note = goodsRequest.note,
//                    uuid = goodsRequest.goods?.uuid
//                )
            }
            val values = calculateTotalPrice(goodsVoRequestList, localDingingStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = goodsVoRequestList.toJson()
            first.save()
        }

        return first
    }


    fun updateCustomer(localDingingStyle: Int?, it: ReserveTableRequest): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            name = it.name
            //兼容H5 Compatible with H5
            peopleDate = it.diningTime
            //兼容H5 Compatible with H5
            peopleNum = it.diningNumber
            diningNumber = it.diningNumber
            diningTime = it.diningTime
            mobile = it.mobile.replace(" ", "")
            areaCode = it.areaCode.replace(" ", "")
            Timber.e("更新购物车用户成功: ${localDingingStyle}")
            save()
        }
        return first
    }

    fun updateSelectTable(it: TableResponseItem?, localDingingStyle: Int?): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            tableUuid = it?.uuid
            tableLabel = it?.name
            tableType = it?.type
            save()
        }
        return first
    }


    fun updateOrderMore(
        isOrderMore: Boolean,
        orderID: String?,
        localDingingStyle: Int?
    ): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            this.isOrderMore = isOrderMore
            this.orderMoreID = orderID
            save()
        }
        return first
    }

    //更新优惠券信息
    fun updateCoupon(localDingingStyle: Int?, it: CouponModel?): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            couponModel = it?.toJson() ?: ""
            Timber.e("添加优惠券  ${it?.toJson() ?: ""}")
            save()
        }
        return first
    }

    //清掉优惠券信息
    fun clearCoupon(localDingingStyle: Int?): ShoppingRecord {
        Timber.e("清除优惠券")
        val first = getDefault(localDingingStyle!!)
        first.run {
            couponModel = ""
            save()
        }
        return first
    }


    fun updateSelectTable(
        tableUUID: String?,
        tableName: String?,
        tableType: Int?,
        localDingingStyle: Int?
    ): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            tableUuid = tableUUID
            tableLabel = tableName
            this.tableType = tableType
            save()
        }
        return first
    }

    fun updateNote(note: String?, localDingingStyle: Int?): ShoppingRecord {
        Timber.e("设置备注:${note}")
        val first = getDefault(localDingingStyle!!)
        first.run {
            this.note = note
            save()
        }
        return first
    }

//    fun clearDataVersion(localDingingStyle: Int?): ShoppingRecord {
//        Timber.e("清除本地版本号")
//        val first = getDefault(localDingingStyle!!)
//        first.run {
//            this.dataVersion = null
//            save()
//        }
//        return first
//    }

//    fun updateDataVersion(dataVersion: Long?, localDingingStyle: Int?): ShoppingRecord {
//        Timber.e("设置本地版本号:${dataVersion}")
//        val first = getDefault(localDingingStyle!!)
//        first.run {
//            this.dataVersion = dataVersion
//            save()
//        }
//        return first
//    }

    fun clearNote(localDingingStyle: Int?): ShoppingRecord {
        Timber.e("清除备注")
        val first = getDefault(localDingingStyle!!)
        first.run {
            this.note = ""
            save()
        }
        return first
    }


    fun updatePayStatus(
        payStatus: Int?,
        localDingingStyle: Int?
    ): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            this.payStatus = payStatus
            save()
        }
        return first
    }

    fun updateSelectTableAndCustomer(
        it: ReserveTableRequest,
        table: TableResponseItem,
        localDingingStyle: Int?
    ): ShoppingRecord {
        val first = getDefault(localDingingStyle!!)
        first.run {
            name = it.name
            //兼容H5 Compatible with H5
            peopleDate = it.diningTime
            //兼容H5 Compatible with H5
            peopleNum = it.diningNumber
            diningNumber = it.diningNumber
            diningTime = it.diningTime
            mobile = it.mobile.replace(" ", "")
            areaCode = it.areaCode.replace(" ", "")
            tableUuid = table.uuid
            tableLabel = table.name
            tableType = table.type
            save()
        }
        return first
    }

    /**
     * 存储外卖信息
     *
     * @param takeOutPlatformModel
     * @param dinStyle
     * @param takeOutOrderId
     * @return
     */
    fun updateTakeOutPlatform(
        takeOutPlatformModel: TakeOutPlatformModel, dinStyle: Int, takeOutOrderId: String
    ): ShoppingRecord {
        val first = getDefault(dinStyle)
        first.run {
            this.takeOutPlatformModel = takeOutPlatformModel.toJson()
            this.takeOutOrderId = takeOutOrderId
            save()
        }
        return first
    }

    /**
     * 修改购物车内时价菜金额
     *
     * @param goodsRequest
     * @param localDingingStyle
     * @return
     */
    fun updateTimeGoodsPrice(
        goodsRequest: GoodsRequest,
        localDingingStyle: Int,
    ): ShoppingRecord? {

        val start = System.nanoTime()
        val first = get(localDingingStyle)
        if (first != null) {
            val goodsList = first.getGoodsVoList()
            //修改购物车内时价菜的价格，如果是有单品折扣  则清掉单品折扣信息
            if (goodsList.isNotEmpty()) {
                val list = ArrayList<GoodsRequest>()
                goodsList.forEach { good ->
                    if (good.goods?.id == goodsRequest.goods?.id) {
                        //更新时价菜的价格
                        good.goods?.sellPrice = goodsRequest.goods?.sellPrice
                        good.goods?.discountPrice = goodsRequest.goods?.discountPrice
                        good.goods?.vipPrice = goodsRequest.goods?.vipPrice
                        good.goods?.pricingCompleted = goodsRequest.goods?.isHasCompletePricing()
                        good.goods?.weighingCompleted = goodsRequest.goods?.isHasCompleteWeight()
                        good.goods?.isProcessed = goodsRequest.goods?.isHasProcessed()
                        //修改时价菜 清掉单品折扣信息
                        if (good.singleDiscountGoods != null) {
                            good.singleDiscountGoods = null
//                            HashHelper.delete(
//                                good.feedInfoList,
//                                good.goodsTagItems,
//                                good.orderMealSetGoodList,
//                                good.goods!!.id,
//                                good.singleDiscountGoods,
//                                goodsPriceKey = good.getGoodPriceKey(),
//                                note = good.note,
//                                uuid = good.goods?.uuid
//                            )
                            hashCache.remove(good.getHash())
//                            GoodsHelper.update(good.goods!!, localDingingStyle, 0)
                        }
                        good.finalSinglePrice = good.calculateSinglePrice()
                        val targetHash = good.getHash()
                        val index = list.indexOfFirst { it.getHash() == targetHash }
                        if (index == -1) {
                            list.add(good)
                            hashCache.getOrPut(targetHash) { targetHash }

                        } else {
                            list[index].num = (list[index].num ?: 0) + (good.num ?: 0)
                        }
                    } else {
                        list.add(good)
                    }
                }
                Timber.e("list ${list.size}")

                val values = calculateTotalPrice(list, localDingingStyle)
                first.totalPrice = values[0]
                first.vatCharge = values[1]
                first.packPrice = values[2]
                first.serviceFeeCharge = values[3]
                first.goodsVoStr = list.toJson()
                first.save()
            }

        }
        val end = System.nanoTime()
        Timber.e("Time: ${(end - start) / 1_000_000} ms")
        return first
    }


    fun updateGoodsWeight(
        localDingingStyle: Int,
        goodsRequest: GoodsRequest,
        orderGoods: OrderMealSetGood?,
        weight: String?,
    ): ShoppingRecord? {
        val first = get(localDingingStyle)
        Timber.e("first=> ${first?.toJson()}")
        first?.apply {
            val goodsVoRequestList = first.getGoodsVoList()
            val index =
                first.getGoodsVoList().indexOfFirst { it.getHash() == goodsRequest.getHash() } ?: -1
            if (index != -1) {
                if (orderGoods == null) {
                    goodsRequest.goods?.weight = weight?.toDouble()
                    goodsRequest.goods?.weighingCompleted = true
                } else {
                    val goodsIndex = goodsRequest.orderMealSetGoodList?.indexOfFirst {
                        it.mealSetGroupId == orderGoods.mealSetGroupId
                                && it.mealSetGoodsId == orderGoods.mealSetGoodsId
                    } ?: -1
                    if (goodsIndex != -1) {
                        goodsRequest.orderMealSetGoodList?.get(goodsIndex)?.apply {
                            this.weight = orderGoods.weight
                            this.weighingCompleted = orderGoods.weighingCompleted
                        }
                    }
                }
                goodsRequest.singleDiscountGoods = null
                goodsVoRequestList[index] = goodsRequest
            }
            val values = calculateTotalPrice(goodsVoRequestList, localDingingStyle)
            first.totalPrice = values[0]
            first.vatCharge = values[1]
            first.packPrice = values[2]
            first.serviceFeeCharge = values[3]
            first.goodsVoStr = goodsVoRequestList.toJson()
            first.save()
        }

        return first
    }
}