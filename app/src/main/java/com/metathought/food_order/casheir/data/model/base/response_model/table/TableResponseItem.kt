package com.metathought.food_order.casheir.data.model.base.response_model.table

import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.response_model.order.Customer
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoodJson

data class TableResponseItem(
    val createTime: String?,
    val customerJson: String?,
    val expireTime: String?,
    val id: String?,
    val isAssemble: Boolean?,
    val location: String?,
    //最多坐几人
    val maxPeopleCount: Int?,
    val minPeopleCount: Any?,
    val name: String?,
    val note: Any?,
    val queueGroupId: Any?,
    val refId: String?,
    val reminder: Boolean?,
    val saasStoreId: String?,
    val shortUrl: String?,
    val status: Int?,
    val storeId: String?,
    val storeUserId: Any?,
    //1. 桌面二维码 2. 通用二维码  3 临时二维码
    val type: Int?,
    val updateTime: Any?,
    val url: Any?,
    val uuid: String?,
    val wechatAppQrPicParams: Any?,
    val totalPrice: Int?,
    val duration: Long?,

    val waitWeighMark: Boolean?,

    /**
     * 是否打印了预结单
     */
    val hasPrintPreSettlement: Boolean? = false,

    @Transient
    var select: Boolean = false
) {
    private var tableCustomerJson: CustomerInfoVo? = null
    fun getCustomerJson(): CustomerInfoVo? {

        if (tableCustomerJson == null && !customerJson.isNullOrEmpty()) {
            tableCustomerJson =
                MyApplication.globalGson.fromJson(customerJson, CustomerInfoVo::class.java)
        }
        return tableCustomerJson
    }

    fun isUniversalQr(): Boolean {
        return type == 2
    }
}