package com.metathought.food_order.casheir.ui.ordered.tablelist

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.databinding.DialogFilterTableListBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.FilterTableAdapter
import com.metathought.food_order.casheir.ui.adapter.FloorAdapter
import com.metathought.food_order.casheir.ui.ordered.OrderedViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FilterOrderTableListDialog : DialogFragment() {
    private var binding: DialogFilterTableListBinding? = null
    private var itemClickListener: ((ArrayList<OrderedTableListItem>) -> Unit)? = null
    private val orderedViewModel: OrderedViewModel by viewModels()
    private var filterTableAdapter: FilterTableAdapter? = null
    private var selectedArrayList: ArrayList<OrderedTableListItem>? = null
    private var floorAdapter: FloorAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogFilterTableListBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        initData()
        initListener()
        initObserver()
    }

    private fun showLoading() {
        binding?.run {
            progressBar.isVisible = true
        }
    }

    private fun hideLoading() {
        binding?.run {
            progressBar.isGone = true
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.6).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initObserver() {
        orderedViewModel.uiTableState.observe(viewLifecycleOwner) {
            it.response?.let {
                when (it) {
                    is ApiResponse.Loading -> {
                        showLoading()
                    }

                    is ApiResponse.Success -> {
                        hideLoading()
                        if (it.data.isEmpty()) {
                            binding?.apply {
                                btnConfirm.isEnabled = false
                            }
                        }
                        var floorArray: ArrayList<String> = arrayListOf()
                        floorArray.add(getString(R.string.all))
                        for (res in it.data) {
                            if (!floorArray.contains(res.location)) {
                                res.location?.let { it1 -> floorArray.add(it1) }
                            }

                        }
                        floorAdapter = context?.let { it1 ->
                            FloorAdapter(floorArray, it1, 0) { floor ->
                                orderedViewModel.filterFloor(
                                    if (floor == 0) "" else floorArray[floor],
                                )
                            }
                        }
                        binding?.recyclerViewPage?.adapter = floorAdapter
                    }

                    is ApiResponse.Error -> {
                        hideLoading()
                        Toast.makeText(context, "${it.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }

        }
        orderedViewModel.filteredTableResponse.observe(viewLifecycleOwner){

            filterTableAdapter?.updateItems(it)
        }
    }

    private fun initData() {
        binding?.apply {
            context?.let {
                orderedViewModel.getOrderedTableList()
                filterTableAdapter =
                    FilterTableAdapter(OrderedTableListResponse(), it, selectedArrayList ?: OrderedTableListResponse()) {
                        btnConfirm.text = getString(R.string.apply_filter).plus(" ${filterTableAdapter?.getSelectedArrayList()?.size}")
                    }
                recyclerViewTable.adapter = filterTableAdapter
            }
        }
    }

    private fun initListener() {
        binding?.apply {
//            btnClose.setOnClickListener() {
//                dismissAllowingStateLoss()
//            }
            btnCancel.setOnClickListener {
                filterTableAdapter?.resetSelect()
            }
            btnConfirm.setOnClickListener {
                filterTableAdapter?.getSelectedArrayList()?.let {
                    itemClickListener?.invoke(ArrayList(it))
                }
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        private const val AVAILABLE_TABLE_LIST = "AVAILABLE_TABLE_LIST"

        fun showDialog(
            fragmentManager: FragmentManager,
            selectedArray : ArrayList<OrderedTableListItem>,
            itemClickListener: ((ArrayList<OrderedTableListItem>?) -> Unit)) {
            var fragment = fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST)
            if (fragment != null) return
            fragment =
                newInstance(itemClickListener = itemClickListener, selectedArray = selectedArray )
            fragment.show(fragmentManager, AVAILABLE_TABLE_LIST)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST) as? FilterOrderTableListDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            itemClickListener: ((ArrayList<OrderedTableListItem>?) -> Unit), selectedArray : ArrayList<OrderedTableListItem>
        ): FilterOrderTableListDialog {
            val args = Bundle()
            val fragment = FilterOrderTableListDialog()
            fragment.itemClickListener = itemClickListener
            fragment.selectedArrayList = selectedArray
            fragment.arguments = args
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}
