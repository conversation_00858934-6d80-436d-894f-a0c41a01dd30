package com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist


import com.google.gson.annotations.SerializedName

data class BalanceListResponse(
    @SerializedName("countId")
    val countId: Any?,
    @SerializedName("current")
    val current: Int?,
    @SerializedName("maxLimit")
    val maxLimit: Any?,
    @SerializedName("optimizeCountSql")
    val optimizeCountSql: Boolean?,
    @SerializedName("orders")
    val orders: List<Any?>?,
    @SerializedName("pages")
    val pages: Int?,
    @SerializedName("records")
    val records: List<RecordBalance>?,
    @SerializedName("searchCount")
    val searchCount: Boolean?,
    @SerializedName("size")
    val size: Int?,
    @SerializedName("total")
    val total: Int?,
    @SerializedName("exactMatch")
    val exactMatch: Int?


)