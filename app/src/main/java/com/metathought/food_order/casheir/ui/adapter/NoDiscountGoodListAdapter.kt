package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description
 */
class NoDiscountGoodListAdapter(
    val list: List<GoodsRequest>,
    val isVip: Boolean? = false
) : RecyclerView.Adapter<NoDiscountGoodListAdapter.NoDiscountGoodViewHolder>() {


    inner class NoDiscountGoodViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodsRequest) {
            binding.apply {
                layoutBg.setPadding(0, 6, 0, 6)
                tvName.text = resource.goods?.name
                tvQTY.text = "x${resource.num}"
                tvSpecification.isVisible = resource.getGoodsTagStr().isNotEmpty()
                tvSpecification.text = resource.getGoodsTagStr()


                val isShowVipPrice = resource.goods?.isShowVipPrice()

                layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
                Timber.e("这里1111  ${resource.totalDiscountPrice()}  ${resource.goods?.weight}")
                layoutPrice.tvFoodPrice.text =
                    resource.totalDiscountPrice().priceFormatTwoDigitZero2()
                Timber.e("这里2222")
                layoutPrice.tvWeight.isVisible = false
                layoutPrice.tvTimePriceSign.isVisible = false

                if (isShowVipPrice == true) {
                    layoutPrice.tvVipPrice.text =
                        resource.totalVipPrice().priceFormatTwoDigitZero2()
                }
                if (resource.isToBeWeighed()) {
                    if (resource.goods?.isHasCompleteWeight() == true) {
                        layoutPrice.tvWeight.isVisible = true
                        layoutPrice.tvWeight.text = "(${resource.goods?.getWeightStr()})"
                        if (resource.goods?.isMealSet() == true) {
                            layoutPrice.tvWeight.isVisible = false
                        }
                    }
                }

//                if (resource.goods?.isHasCompleteWeight() == true) {
//                    layoutPrice.tvWeight.isVisible = true
//                    layoutPrice.tvWeight.text = "(${resource.goods?.getWeightStr()})"
//                }
                Timber.e("resource.goods?.isTimePriceGood()  ${resource.goods?.isTimePriceGood()}")
                if (resource.goods?.isTimePriceGood() == true) {
                    layoutPrice.tvTimePriceSign.isVisible = true
                }

            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = NoDiscountGoodViewHolder(
        SelectedMenuItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: NoDiscountGoodListAdapter.NoDiscountGoodViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}