package com.metathought.food_order.casheir.ui.ordered.merge

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.KIOSK
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderRequest

import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.ordered.OrderedViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/11/21 15:10
 * @description
 */
@HiltViewModel

class MergeOrderListViewModel @Inject

constructor(private val repository: Repository) : ViewModel() {

    val uiListState get() = _uiListState
    private val _uiListState = MutableLiveData<UIListModel>()


    val uiTableState get() = _uiTableState
    private val _uiTableState = MutableLiveData<UITableStateModel>()

    private val _tableResponse = MutableLiveData<List<OrderedTableListItem>>()

    private val _filteredTableResponse = MutableLiveData<List<OrderedTableListItem>>()
    val filteredTableResponse get() = _filteredTableResponse


    val uiMergeState get() = _uiMergeState
    private val _uiMergeState = MutableLiveData<ApiResponse<BaseBooleanResponse>>()

    fun getMergeList(
        orderRecord: OrderedRecord?,
        payStatus: List<Int>? = null,
        listTable: ArrayList<OrderedTableListItem>? = null,
    ) {
        viewModelScope.launch {
            try {

                val arrayTable = mutableListOf<String>()
                listTable?.let {
                    if (listTable.isNotEmpty()) {
                        for (table in listTable) {
                            table.uuid?.let { id ->

                                arrayTable.add(id)

                            }

                        }
                    }
                }
                val result =
                    repository.mergeAbleOrderList(
                        orderRecord?.orderNo,
                        payStatus = payStatus,
                        tableUuids = arrayTable
                    )

                withContext(Dispatchers.Main) {
                    if (result is ApiResponse.Success) {
                        val response = result.data
                        emitUIListState(
                            showSuccess = response,
                            showLoading = false,
                        )

                    } else if (result is ApiResponse.Error) {
                        emitUIListState(showError = result.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIListState(showError = e.message, showLoading = false)
            }
        }
    }

    fun isHasDiscount(selectList: MutableList<MergeOrderModel>): Boolean {
        val isDiscount =
            _uiListState.value?.showSuccess?.mergeOrderInfo?.isSettingDiscount == true || selectList.any { it.isSettingDiscount == true }
        return isDiscount
    }

    /**
     * 合并订单
     *
     */
    fun mergeOrder(orderId: String?, selectList: MutableList<MergeOrderModel>?) {
        viewModelScope.launch {
            try {
                val mergeOrderRequest = MergeOrderRequest().apply {
                    this.orderId = orderId
                    this.subOrderIds = selectList?.map {
                        it.orderId!!
                    }
                }
                val response = repository.mergeOrder(mergeOrderRequest)
                _uiMergeState.postValue(response)
            } catch (e: Exception) {

            }
        }
    }


    /**
     * 获取桌台列表
     */

    fun getOrderedTableList(tableUUID: String? = null) {
        viewModelScope.launch {
//            emitUITableState(response = ApiResponse.Loading)
            try {
                val orderedTable = repository.orderedTableList()
                var orderedTableItem: OrderedTableListItem? = null
                if (orderedTable is ApiResponse.Success) {
                    orderedTable.data.filter { it.name?.uppercase() != "DEFAULT" }
                        .sortedBy { it.id }
                        .let { list ->
                            val tmp = list.toMutableList()

                            _tableResponse.value = tmp
                            filteredTableResponse.value = tmp
                            orderedTableItem = tmp.firstOrNull { it.uuid == tableUUID }
                        }

                }
                emitUITableState(response = orderedTable, orderedTableItem = orderedTableItem)
            } catch (e: Exception) {
                emitUITableState(response = ApiResponse.Error(e.message))
            }

        }

    }


    fun filterFloor(query: String) {
        if (query.isNotEmpty()) {
            val filteredList = _tableResponse.value?.filter { table ->
                table.location?.equals(query, ignoreCase = true) == true
            }
            Timber.e("recyclerViewPage  filteredList  ${filteredList?.size}  $query")
            val orderedTableListResponse = OrderedTableListResponse()
            filteredList?.let { orderedTableListResponse.addAll(it) }

            _filteredTableResponse.value = orderedTableListResponse
        } else {
            _filteredTableResponse.value = _tableResponse.value
        }
    }


    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: MergeOrderListResponse? = null,
    ) {
        val uiModel = UIListModel(
            showLoading,
            showError,
            showSuccess
        )
        withContext(Dispatchers.Main) {
            _uiListState.value = uiModel
        }
    }

    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: MergeOrderListResponse?,
    )

    private suspend fun emitUITableState(
        response: ApiResponse<OrderedTableListResponse>? = null,
        orderedTableItem: OrderedTableListItem? = null
    ) {
        val uiModel = UITableStateModel(response, orderedTableItem)
        withContext(Dispatchers.Main) {
            _uiTableState.value = uiModel
        }
    }

    data class UITableStateModel(
        val response: ApiResponse<OrderedTableListResponse>? = null,
        val orderedTableItem: OrderedTableListItem?
    )


}