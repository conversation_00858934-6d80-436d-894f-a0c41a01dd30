package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.FloorItemBinding


class FloorAdapter(
    private var floorSize: ArrayList<String>,
    val context: Context,
    private var checkedIndex: Int,
    val onClickCallback: (Int) -> Unit
) : RecyclerView.Adapter<FloorAdapter.TableFloorViewHolder>() {

    inner class TableFloorViewHolder(val binding: FloorItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(showIndex: Int) {
            binding.apply {
                binding.tvValue.text = floorSize[showIndex]
                binding.root.strokeColor = ContextCompat.getColor(
                    context,
                    if (checkedIndex == showIndex) R.color.primaryColor else android.R.color.transparent
                )
                binding.root.setCardBackgroundColor(
                    ContextCompat.getColor(
                        context,
                        if (checkedIndex == showIndex) R.color.primaryColor else android.R.color.transparent
                    )
                )
                binding.tvValue.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (checkedIndex == showIndex) R.color.white else android.R.color.black
                    )
                )
                root.setOnClickListener {
                    setCheckedIndex(showIndex)
                    onClickCallback(showIndex)
                }
            }
        }
    }


    fun setCheckedIndex(position: Int) {
        checkedIndex = position
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableFloorViewHolder {
        return TableFloorViewHolder(
            FloorItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return floorSize.size
    }

    override fun onBindViewHolder(holder: TableFloorViewHolder, position: Int) {
        holder.bind(position)
    }

    fun getValueCheckedIndex():String{
        return floorSize[checkedIndex]
    }
    fun getCheckedIndex():Int{
        return checkedIndex
    }
}