package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.request_model.TimeSlotMode
import com.metathought.food_order.casheir.data.model.base.response_model.report.DailyReportData
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.databinding.ItemDailyReportDateBinding
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.formatDate5
import com.metathought.food_order.casheir.extension.formatDate6
import timber.log.Timber

/**
 * 每日报表日期列表适配器
 */
class DailyReportDateAdapter(
    val list: ArrayList<DailyReportData>,
) : RecyclerView.Adapter<DailyReportDateAdapter.ProduceReportItemViewHolder>() {

    private var timeSlotMode: TimeSlotMode = TimeSlotMode.Day

    inner class ProduceReportItemViewHolder(val binding: ItemDailyReportDateBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: DailyReportData, position: Int) {
            binding.apply {
                Timber.d("timeSlotMode:$timeSlotMode  date:${resource.date}  ${   resource.date?.formatDate6()}")
                tvDate.text = if (timeSlotMode == TimeSlotMode.Day) {
                    resource.date
                } else {
                    resource.date?.formatDate6()
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemDailyReportDateBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: DailyReportDateAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<DailyReportData>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    fun setupTimeSlotMode(timeSlotMode: TimeSlotMode) {
        this.timeSlotMode = timeSlotMode
    }

}