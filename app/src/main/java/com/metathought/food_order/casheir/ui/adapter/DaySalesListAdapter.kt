package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.DaySale
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.TopSaleRecord
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesOrdersDetailVo
import com.metathought.food_order.casheir.databinding.ItemDaySaleBinding
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleReportListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleTopListBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description 销售报表预览adapter
 */
class DaySalesListAdapter(
    val ctx: Context,
    val list: ArrayList<DaySale>,
) : RecyclerView.Adapter<DaySalesListAdapter.ProduceReportItemViewHolder>() {


    inner class ProduceReportItemViewHolder(val binding: ItemDaySaleBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: DaySale, position: Int) {
            binding.apply {

                day.text = "${resource.orderDate ?: ""}"
                tvPrice.text = resource.turnover.priceFormatTwoDigitZero2()
                tvPromoAmout.text = resource.basketPromo.priceFormatTwoDigitZero2()
                tvCancelum.text = resource.cancelledAmount.priceFormatTwoDigitZero2()
                tvFailAmout.text = resource.failedAmount.priceFormatTwoDigitZero2()
                tvOrderlum.text = "${resource.orderNum ?: ""}"
                tvPromoNum.text = "${resource.promoOrderNum ?: ""}"
                tvFailNum.text = "${resource.failedOrderNum ?: ""}"
                tvCancleNum.text = "${resource.cancelledOrderNum ?: ""}"
                vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemDaySaleBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: DaySalesListAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<DaySale>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}