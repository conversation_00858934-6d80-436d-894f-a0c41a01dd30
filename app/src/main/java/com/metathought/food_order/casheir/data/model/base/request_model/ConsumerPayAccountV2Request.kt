package com.metathought.food_order.casheir.data.model.base.request_model

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.AddToCartRequest

/**
 * <AUTHOR>
 * @date 2025/04/09 16:15
 * @description
 */
data class ConsumerPayAccountV2Request(
    //upay账号 or 昵称
    @SerializedName("upayAccount")
    val upayAccount: String? = null,

    //是否精确搜索账号，默认否
    @SerializedName("exactMatch")
    val exactMatch: Boolean? = false,

    @SerializedName("page")
    var page: Int? = 1,

    @SerializedName("pageSize")
    var pageSize: Int? = 20,

    )


