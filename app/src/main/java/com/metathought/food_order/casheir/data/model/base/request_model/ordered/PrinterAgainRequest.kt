package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName

data class PrinterAgainRequest(
    @SerializedName("cashierReceipt")
    val cashierReceipt: Boolean?,
    @SerializedName("kitchenReceipt")
    val kitchenReceipt: Boolean?,
    @SerializedName("orderId")
    val orderId: String?,
    @SerializedName("kitchenReceiptType")
    val kitchenReceiptType: Int?
)