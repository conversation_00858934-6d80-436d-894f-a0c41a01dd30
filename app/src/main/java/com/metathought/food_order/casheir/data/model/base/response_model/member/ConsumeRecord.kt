package com.metathought.food_order.casheir.data.model.base.response_model.member


import com.google.gson.annotations.SerializedName

data class ConsumeRecord(
    @SerializedName("accountId")
    val accountId: String?,
    @SerializedName("amount")
    val amount: Int?,
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("orderId")
    val orderId: String?,
    @SerializedName("saasStoreId")
    val saasStoreId: String?,
    @SerializedName("status")
    val status: Int?,
    @SerializedName("type")
    val type: Int?,
    @SerializedName("updateTime")
    val updateTime: String?
)