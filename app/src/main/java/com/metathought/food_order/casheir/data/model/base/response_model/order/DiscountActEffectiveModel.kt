package com.metathought.food_order.casheir.data.model.base.response_model.order


/**
 *<AUTHOR>
 *@time  2024/11/25
 *@desc 有效的优惠活动
 **/

data class DiscountActEffectiveModel(
    /**
     * 优惠活动
     */
    var activityLabel: ActivityLabel? = null,
    /**
     * 优惠活动 现价优惠掉的金额
     */
    var price: Long? = null,
    /**
     * 优惠活动 会员价优惠掉的金额
     */
    var vipPrice: Long? = null,

    /**
     * 优惠活动 是否含有待称重商品
     */
    var isUnWeight: Boolean? = null,
)