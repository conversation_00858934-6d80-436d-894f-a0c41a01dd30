package com.metathought.food_order.casheir.utils

import org.threeten.bp.LocalTime
import org.threeten.bp.ZoneId
import org.threeten.bp.format.DateTimeFormatter
import org.threeten.bp.format.DateTimeParseException


/**
 *<AUTHOR>
 *@time  2025/5/9
 *@desc
 **/

object BusinessHoursChecker {

    // 定义时间格式（24小时制）
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss")

    /**
     * 判断当前时间是否已到达今天的营业时间
     * @param startTime 营业开始时间（格式 "HH:mm"）
     * @param endTime 营业结束时间（格式 "HH:mm"）
     * @return 是否已到达或超过营业时间
     */
    fun isBusinessTimeReached(
        startTime: String,
        endTime: String
    ): Boolean {
        try {
            val start = LocalTime.parse(startTime, timeFormatter)
            val end = LocalTime.parse(endTime, timeFormatter)
            val now = LocalTime.now()

            return when {
                // 正常时间段（例如 09:00 ~ 18:00）
                start <= end -> now.isAfter(start) || now == start
                // 跨天时间段（例如 22:00 ~ 次日02:00）
                else -> now.isAfter(start) || now.isBefore(end)
            }
        } catch (e: DateTimeParseException) {
            throw IllegalArgumentException("Invalid time format. Use HH:mm", e)
        }
    }

    /**
     * 扩展函数版本（更简洁）
     */
    fun String.isBusinessTime(endTime: String): Boolean {
        return try {
            val start = LocalTime.parse(this, timeFormatter)
            val end = LocalTime.parse(endTime, timeFormatter)
            val now = LocalTime.now()

            when {
                start <= end -> now.isAfter(start) || now == start
                else -> now.isAfter(start) || now.isBefore(end)
            }
        } catch (e: DateTimeParseException) {
            false // 或抛出异常
        }
    }
}