package com.metathought.food_order.casheir.network

import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.annotation.RequiresApi
import com.facebook.stetho.okhttp3.StethoInterceptor
import com.google.gson.Gson
import com.metathought.food_order.casheir.BuildConfig
import com.metathought.food_order.casheir.MainActivity
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.extension.ShowSessionExpiredDialog
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.network.service.ApiService
import com.metathought.food_order.casheir.network.service.TableService
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.ResponseBody.Companion.toResponseBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ApiRetrofit {

    @Provides
    @Singleton
    fun provideRetrofit(): ApiService =
        Retrofit.Builder()
            .baseUrl(BuildConfig.BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .client(getHttpClient())
            .build()
            .create(ApiService::class.java)

    @Provides
    @Singleton
    fun provideTableRetrofit(): TableService =
        Retrofit.Builder()
            .baseUrl(BuildConfig.BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .client(getHttpClient(isNeedAuth = true))
            .build()
            .create(TableService::class.java)

    private fun getDeviceId(): String? {
        return Settings.System.getString(
            MyApplication.myAppInstance.contentResolver,
            Settings.Secure.ANDROID_ID
        )
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun getSN(): String? {
        Log.e("SerialNumber", Build.getSerial())
        return Build.getSerial()

    }

    private fun getHttpClient(isNeedAuth: Boolean? = false): OkHttpClient {
        val interceptor = HttpLoggingInterceptor()
        interceptor.level = HttpLoggingInterceptor.Level.HEADERS
        interceptor.level = HttpLoggingInterceptor.Level.BODY
        return OkHttpClient.Builder()
            .addNetworkInterceptor(StethoInterceptor())
            .addInterceptor { chain ->
                val ongoing = chain.request().newBuilder()
                ongoing.apply {
                    val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
                    addHeader("Accept-Language", lang)
                    MainDashboardFragment.CURRENT_USER?.token?.let {
                        addHeader("Authorization", "Bearer $it")
                    }
                }
                val response = chain.proceed(ongoing.build())
                var bodyString = response.body?.string()
                //added for remove response int when error for data
                try {
                    if (bodyString?.isNotBlank() == true) {
                        val res = Gson().fromJson(bodyString, BaseResponse::class.java)
                        if (res.code == SESSION_EXPIRE) {
                            if (!filterUrl(response.request.url.toString())) {
                                MainActivity.mainActivityInstance?.dismissProgress()
                                MainActivity.mainActivityInstance?.ShowSessionExpiredDialog(res.msg)
                            }
                            val newResponse = BaseResponse(res.code, "", null)
                            bodyString = newResponse.toJson()
                        } else if (!res.isSuccess()) {
                            val newResponse =
                                BaseResponse(res.code, res.msg, null, res.data)
                            bodyString = newResponse.toJson()
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                val clone = response.newBuilder()
                    .body(bodyString?.toResponseBody())
                    .build()
                clone
            }
            .apply {
                addInterceptor(interceptor)
            }
            .connectTimeout(TIME_OUT, TimeUnit.MILLISECONDS)
            .callTimeout(TIME_OUT, TimeUnit.MILLISECONDS)
            .readTimeout(TIME_OUT, TimeUnit.MILLISECONDS)
            .writeTimeout(TIME_OUT, TimeUnit.MILLISECONDS)
            .build()
    }

    private fun filterUrl(url: String): Boolean {
        if (url.contains("print/log")) {
            return true
        }
        return false
    }
}
