package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.OfflinePayMethodData
import com.metathought.food_order.casheir.databinding.PrintShiftHandoverOfflineRecordsItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverOfflineRecordsItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverRecordsItemBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

class PrintShiftHandoverOfflineRecordsAdapter(var list: ArrayList<OfflinePayMethodData> = ArrayList()) :
    RecyclerView.Adapter<PrintShiftHandoverOfflineRecordsAdapter.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            PrintShiftHandoverOfflineRecordsItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(position)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(list: List<OfflinePayMethodData>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addList(list: List<OfflinePayMethodData>) {
        this.list.addAll(list)
        notifyDataSetChanged()
    }


    inner class ViewHolder(val binding: PrintShiftHandoverOfflineRecordsItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(position: Int) {
            binding.apply {
                list[position].let { log ->
                    tvChannel.text = log.payMethod
                    tvAmount.text = "$${log.amount}"
                }

            }
        }

    }
}