package com.metathought.food_order.casheir.ui.ordered.time_price


import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.DialogEditTimePriceBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.filter.CashierInputFilter
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.order.change_num.EditGoodNumDialog
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.math.BigDecimal
import java.math.RoundingMode
import androidx.core.graphics.toColorInt
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper

/**
 *
 * @constructor 设置时价
 */

@AndroidEntryPoint
class EditTimePriceDialog : BaseDialogFragment() {

    private var binding: DialogEditTimePriceBinding? = null
    private var updateCartCallBackListener: ((GoodsRequest?) -> Unit)? = null
    private var updateOrderCallBackListener: ((salePrice: String?, vipPrice: String?) -> Unit)? =
        null

    private var orderInfo: OrderedInfoResponse? = null
    private var cartGood: GoodsRequest? = null
    private var orderedGood: OrderedGoods? = null

    /**
     * 是否加入购物车
     */
    private var addToCart: Boolean? = false

    /**
     * 数量
     */
    private var qty = 0

    /**
     * 剩余可加购数量
     */
    private var canAddNum = 999

    private var diningStyle = DiningStyleEnum.DINE_IN.id

    /**
     * 是否未称重
     */
    private var isUnWeight = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditTimePriceBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
        initView()
        initListener()
        initObserver()
    }

    private fun initObserver() {

    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        binding?.apply {
            llGoodInfo.isVisible = false

            if (addToCart == true) {
                //如果是加入购物车类型 显示加减
                imgMinus.isVisible = true
                imgPlus.isVisible = true
                tvQTY.setEnable(true)
            } else {
                tvQTY.setPadding(0, 0, 0, 0)
                tvQTY.setEnable(false)
            }

            if (orderedGood != null) {
                /**
                 * 订单菜品model
                 */
                llGoodInfo.isVisible = true
                orderedGood?.let {
                    qty = it.num ?: 0
                    updateNum()
                    tvName.text = it.name
                    if (!it.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                        tvName.addMealSetTag(requireContext())
                    }
                    val activityLabel = it.activityLabels?.firstOrNull()
                    if (activityLabel != null && orderInfo?.isTakeOut() != true) {
                        tvDiscountActivity.setStrokeAndColor(
                            color = activityLabel.color.toColorInt()
                        )
                        tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                        tvDiscountActivity.text = activityLabel.name
                        tvDiscountActivity.isVisible = true
                    } else {
                        tvDiscountActivity.isVisible = false
                    }
                    tvSpecification.isVisible = it.getGoodsTagStr().isNotEmpty()
                    tvSpecification.text = it.getGoodsTagStr()

                    val isShowVipPrice = it.isShowVipPrice()
                    tvVipPrice.isVisible = isShowVipPrice
                    if (isShowVipPrice) {
                        tvVipPrice.text =
                            it.totalVipPriceWithSingleDiscount().priceFormatTwoDigitZero2()
                    }

                    if (it.isToBeWeighed()) {
                        llWeight.isVisible = true
                        isUnWeight = it.isHasCompleteWeight() == false
                        if (it.isHasCompleteWeight()) {
                            tvWeight.text = it.getWeightStr()
                        } else {
                            tvWeight.text = getString(R.string.to_be_weighed)
                        }
                        tvWeightUnit.isVisible = true
                        tvWeightUnit.text = "/${it.getWeightUnit()}"
                        tvVipWeightUnit.isVisible = true
                        tvVipWeightUnit.text = "/${it.getWeightUnit()}"
                    }
                    Timber.e("sellPrice   ${it.sellPrice}")
                    if (!it.isHasCompletePricing()) {
                        tvPrice.text =
                            getString(R.string.time_price)
                        tvVipPrice.isVisible = false
                    } else {
//                        topBar.setTitle(getString(R.string.modify_time_price))
                        if (it.sellPrice != null) {
                            edtUsd.setText(
                                it.sellPrice.priceFormatTwoDigitZero3()
                            )
                            edtUsd.setSelection(edtUsd.length())
                        }
                        if (it.vipPrice != null) {
                            edtVipUsd.setText(it.vipPrice.priceFormatTwoDigitZero3())
                            edtVipUsd.setSelection(edtVipUsd.length())
                        }
                    }
                }
            } else if (cartGood != null) {
                if (addToCart == true) {
                    topBar.setTitle(getString(R.string.add_to_cart))
                    btnCancel.text = getString(R.string.set_later)
                }

                /**
                 * 当前购物车内已添加该商品的数量
                 */
                val record = cartGood?.goods?.id.let { GoodsHelper.get(it!!, diningStyle) }
                var maxNum = GOOD_MAX_NUM

                if (addToCart == true && cartGood?.isToBeWeighed() == true) {
                    maxNum = 1
                }
                /**
                 * 剩余可添加数量
                 */
                canAddNum = maxNum - (record?.num ?: 0)
                if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                    //预定数量限制
                    val restrictNum = (cartGood?.goods?.restrictNum
                        ?: 0)
                    //如果有预定数量限制
                    if (restrictNum > 0) {
                        canAddNum = restrictNum - (record?.num ?: 0)
                    }
                }

                llGoodInfo.isVisible = true
                qty = cartGood?.num ?: 0
                updateNum()
                tvName.text = cartGood?.goods?.name
                if (!cartGood?.orderMealSetGoodList.isNullOrEmpty()) {
                    tvName.addMealSetTag(requireContext())
                }
                val activityLabel = cartGood?.goods?.activityLabels?.firstOrNull()
                if (activityLabel != null) {
                    tvDiscountActivity.setStrokeAndColor(color = activityLabel.color.toColorInt())
                    tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                    tvDiscountActivity.text = activityLabel.name
                    tvDiscountActivity.isVisible = true
                } else {
                    tvDiscountActivity.isVisible = false
                }
                if (cartGood?.goods?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                    tvTmpSign.setStrokeAndColor(color = R.color.black60)
                    tvTmpSign.isVisible = true
                } else {
                    tvTmpSign.isVisible = false
                }
                tvSpecification.isVisible = !cartGood?.getGoodsTagStr().isNullOrEmpty()
                tvSpecification.text = cartGood?.getGoodsTagStr()
                Timber.e("isHasProcess: ${cartGood?.goods?.isHasProcessed()}")


                if (cartGood?.goods?.isToBeWeighed() == true) {
                    llWeight.isVisible = true
                    if (cartGood?.goods?.isHasCompleteWeight() == true) {
                        tvWeight.text = cartGood?.goods?.getWeightStr()
                    } else {
                        tvWeight.text = getString(R.string.to_be_weighed)
                    }

                    isUnWeight = cartGood?.goods?.isHasCompleteWeight() == false
                    tvWeightUnit.isVisible = true
                    tvWeightUnit.text = "/${cartGood?.goods?.getWeightUnit()}"
                    tvVipWeightUnit.isVisible = true
                    tvVipWeightUnit.text = "/${cartGood?.goods?.getWeightUnit()}"
                }

                if (cartGood?.goods?.isHasCompletePricing() == false) {
                    /**
                     * 未定价
                     */
                    tvPrice.text = "${cartGood?.goods?.getShowPrice(requireContext())}"
                    tvVipPrice.isVisible = false
                } else {
//                    topBar.setTitle(getString(R.string.modify_time_price))
                    val isShowVipPrice = cartGood?.goods?.isShowVipPrice()
                    tvVipPrice.isVisible = isShowVipPrice == true

                    if (cartGood?.goods?.sellPrice != null) {
                        edtUsd.setText(
                            "${
                                cartGood?.goods?.sellPrice?.priceFormatTwoDigitZero3()
                            }"
                        )
                        edtUsd.setSelection(edtUsd.length())
                    }
                    if (cartGood?.goods?.vipPrice != null) {
                        edtVipUsd.setText(
                            "${
                                cartGood?.goods?.vipPrice?.priceFormatTwoDigitZero3()
                            }"
                        )
                        edtVipUsd.setSelection(edtVipUsd.length())
                    }
                }
            }
            updatePriceView()
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            imgMinus.setOnClickListener {
                if (qty > 1) {
                    qty -= 1
                    updateNum()
                }
            }
            imgPlus.setOnClickListener {
                if (qty < canAddNum) {
                    qty += 1
                    updateNum()
                }
            }

            tvQTY.setOnClickListener {
                //称重菜不可以直接编辑数量
                if (cartGood == null || cartGood?.goods?.isToBeWeighed() == true) {
                    return@setOnClickListener
                }
                SingleClickUtils.isFastDoubleClick {
                    val num = if (tvQTY.text.isEmpty()) 0 else tvQTY.text.toString().toInt()
                    val record = GoodsHelper.get(cartGood?.goods?.id!!, diningStyle)
                    var maxNum = GOOD_MAX_NUM
                    if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                        //预定数量限制
                        if ((cartGood?.goods?.restrictNum ?: 0) > 0) {
                            maxNum = (cartGood?.goods?.restrictNum ?: 0)
                        }
                    }
                    val remainingNum = maxNum - (record?.num ?: 0)

                    EditGoodNumDialog(requireContext()).showDialog(
                        num = num,
                        remainingNum = remainingNum,
                        zeroEnable = false,
                        isSoldOut = cartGood?.goods?.isSoldOut(),
                        isPreOrder = diningStyle == DiningStyleEnum.PRE_ORDER.id
                    ) {
                        Timber.e("== $it")
                        qty = it.toInt()
                        updateNum()
                    }
                }
            }

            btnCancel.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (addToCart == true) {
                        if (cartGood != null) {
                            cartGood?.num = qty
                            //这个时价菜是否称重商品
                            if (cartGood?.goods?.isToBeWeighed() == true) {
                                cartGood?.goods?.setWeighingCompletedFlag(false)
                            } else {
                                cartGood?.goods?.setWeighingCompletedFlag(true)
                            }
                            cartGood?.goods?.setPriceCompletedFlag(false)
                            updateCartCallBackListener?.invoke(cartGood)
                        }
                    }
                    dismissCurrentDialog()
                }
            }

            btnYes.setOnClickListener {
                binding?.apply {
                    if (cartGood != null) {
                        cartGood?.num = qty
                        cartGood?.goods?.sellPrice =
                            (edtUsd.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO).times(
                                BigDecimal(100)
                            ).halfUp(0).toLong()
                        cartGood?.goods?.discountPrice = null

                        val vipPrice = edtVipUsd.text.toString().toBigDecimalOrNull()
                        if (vipPrice != null) {
                            cartGood?.goods?.vipPrice =
                                vipPrice.times(
                                    BigDecimal(100)
                                ).halfUp(0).toLong()
                        } else {
                            cartGood?.goods?.vipPrice = null
                        }
                        if (cartGood?.isToBeWeighed() == true) {
                            cartGood?.goods?.setPriceCompletedFlag(true)
                            //如果时价菜时称重的 ,那就判断是否称重了
                            if (cartGood?.goods?.isHasCompleteWeight() == true) {
                                cartGood?.goods?.setWeighingCompletedFlag(true)
                                cartGood?.goods?.isProcessed = true
                            } else {
                                cartGood?.goods?.setWeighingCompletedFlag(false)
                                cartGood?.goods?.isProcessed = false
                            }


                        } else {
                            cartGood?.goods?.isProcessed = true
                            cartGood?.goods?.setPriceCompletedFlag(true)
                        }

                        Timber.e("cartGood :${cartGood}")
                        updateCartCallBackListener?.invoke(cartGood)
                    }

                    if (orderInfo != null) {
                        val salePrice =
                            (edtUsd.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO).times(
                                BigDecimal(100)
                            ).halfUp(0).toLong()
                        var vipPrice = edtVipUsd.text.toString().toBigDecimalOrNull()
                        if (vipPrice != null) {
                            vipPrice =
                                vipPrice.times(
                                    BigDecimal(100)
                                ).halfUp(0)
                        }

                        updateOrderCallBackListener?.invoke(
                            salePrice.toString(),
                            if (vipPrice == null) null else vipPrice.toString()
                        )
                    }

                    dismissCurrentDialog()
                }
            }

            edtUsd.filters = arrayOf(CashierInputFilter(false, 1000000, false))
            edtUsd.addTextChangedListener {
                updatePriceView()

            }

            edtVipUsd.filters = arrayOf(CashierInputFilter(false, 1000000, false))
            edtVipUsd.addTextChangedListener {
                updatePriceView()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateNum() {
        binding?.apply {
            if (addToCart == true) {
                tvQTY.text = "$qty"
                imgPlus.setImageResource(R.drawable.ic_add)
                imgPlus.setEnable(true)
                imgMinus.isVisible = true
                if (qty <= 1) {
                    imgMinus.isVisible = false
                } else if (qty >= canAddNum) {
                    imgPlus.setImageResource(R.drawable.ic_add_disable)
                    imgPlus.setEnable(false)
                }
                if (cartGood?.isToBeWeighed() == true) {
                    Timber.e("称重时加菜")
                    imgPlus.setImageResource(R.drawable.ic_add_disable)
                    imgPlus.setEnable(false)
                    tvQTY.setEnable(false)
                }
            } else {
                tvQTY.text = "x${qty}"
            }
            updatePriceView()
        }
    }

    /**
     * 更新价格区域视图
     *
     */
    @SuppressLint("SetTextI18n")
    private fun updatePriceView() {
        binding?.apply {
            //商品规格小料加价的金额
            val targetPrice = if (cartGood != null) OrderHelper.calculateTagPrice(
                cartGood?.feedInfoList,
                cartGood?.goodsTagItems
            ) else if (orderedGood != null) OrderHelper.calculateTagPrice(
                ArrayList(orderedGood?.feeds ?: listOf()),
                ArrayList(orderedGood?.tagItems ?: listOf()),
            ) else 0L

            val isPackingFeeDisplay =
                if (cartGood != null) cartGood?.goods?.isPackingFeeDisplay() == true else orderedGood?.isPackingFeeDisplay() == true
            //打包费计算 是否计入商品。外卖。外带的时候计入
            val packageFee =
                if (isPackingFeeDisplay && (diningStyle == DiningStyleEnum.TAKE_OUT.id || diningStyle == DiningStyleEnum.TAKE_AWAY.id || diningStyle >= TakeOutPlatformToDiningHelper.BASE_INDEX)) {
                    if (cartGood != null) cartGood?.goods?.packingFee
                        ?: 0 else orderedGood?.packingFee
                        ?: 0
                } else {
                    0
                }


            tvVipPrice.isVisible = false

            if (edtVipUsd.text.isNullOrEmpty()) {
                tvVipPrice.isVisible = false
            } else {
                var price = (edtVipUsd.text.toString().toBigDecimalOrNull()
                    ?: BigDecimal.ZERO).times(BigDecimal.valueOf(100))

                if (orderedGood != null) {
                    //如果是订单里面菜品数据要计算上重量 舍去小数位
                    if (orderedGood?.isToBeWeighed() == true && orderedGood?.isHasCompleteWeight() == true) {
                        price = price.times(BigDecimal(orderedGood?.weight ?: 1.0))
                            .setScale(0, RoundingMode.DOWN);
                    }
                }

                price = price.plus(BigDecimal.valueOf(targetPrice))
                price = price.plus(BigDecimal.valueOf(packageFee.toLong()))
                price = price.times(BigDecimal.valueOf(qty.toLong()))
                tvVipPrice.text = "$${price.priceFormatTwoDigitZero3()}"

                val salePrice = edtUsd.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO
                val vipPrice = edtVipUsd.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO
                if (vipPrice < salePrice) {
                    tvVipPrice.isVisible = true
                }
            }

            if (edtUsd.text.isNullOrEmpty()) {
                tvPrice.isVisible = false
                tvPrice.text = getString(R.string.time_price)
            } else {
                tvPrice.isVisible = true
                var price = (edtUsd.text.toString().toBigDecimalOrNull()
                    ?: BigDecimal.ZERO).times(BigDecimal.valueOf(100))

                if (orderedGood != null) {
                    //如果是订单里面菜品数据要计算上重量 舍去小数位
                    if (orderedGood?.isToBeWeighed() == true && orderedGood?.isHasCompleteWeight() == true) {
                        //重量计算 舍去小数位
                        price = price.times(BigDecimal.valueOf(orderedGood?.weight ?: 1.0))
                            .setScale(0, RoundingMode.DOWN)
                    }
                }

                price = price.plus(BigDecimal.valueOf(targetPrice))
                price = price.plus(BigDecimal.valueOf(packageFee.toLong()))
                price = price.times(BigDecimal.valueOf(qty.toLong()))
                tvPrice.text = "$${price.priceFormatTwoDigitZero3()}"
            }


            if (edtUsd.text.isNullOrEmpty() || isUnWeight) {
                tvPrice.isVisible = true
                tvPrice.text = getString(R.string.time_price)
                tvVipPrice.isVisible = false
            }
        }
        checkBtnEnable()
    }


    private fun checkBtnEnable() {
        var isPrice = true

        binding?.apply {
            /**
             * 是否定价
             */
            if (edtUsd.text.isNullOrEmpty()) {
                isPrice = false
            } else {
                if (!edtVipUsd.text.isNullOrEmpty()) {
                    //如果有填vip价  vip价要小于现价
                    /** 会员价=销售价的话，总价那里是不展示会员价
                     */
                    val salePrice = edtUsd.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO
                    val vipPrice = edtVipUsd.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO
//                    tvVipPrice.isVisible = false
                    if (vipPrice > salePrice) {
                        isPrice = false
                    }
                }
            }

            btnYes.setEnableWithAlpha(isPrice)
        }
    }

    companion object {
        private const val TAG = "EditTimePriceDialog"

        /**
         * Show dialog
         *
         * @param fragmentManager
         * @param orderInfo 订单信息
         * @param cartGood 购物车商品信息
         * @param orderedGood 订单内商品信息
         * @param addToCart 是否加入购物车
         * @param diningStyle 订单类型
         * @param updateCartCallBackListener 购物车的回调
         * @param updateOrderCallBackListener 订单的回调
         */
        fun showDialog(
            fragmentManager: FragmentManager,
            orderInfo: OrderedInfoResponse? = null,
            cartGood: GoodsRequest? = null,
            orderedGood: OrderedGoods? = null,
            addToCart: Boolean? = false,
            diningStyle: Int,
            updateCartCallBackListener: ((GoodsRequest?) -> Unit)? = null,
            updateOrderCallBackListener: ((salePrice: String?, vipPrice: String?) -> Unit)? =
                null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                orderInfo = orderInfo,
                cartGood = cartGood,
                orderedGood = orderedGood,
                addToCart = addToCart,
                diningStyle = diningStyle,
                updateCartCallBackListener = updateCartCallBackListener,
                updateOrderCallBackListener = updateOrderCallBackListener
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? EditTimePriceDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            orderInfo: OrderedInfoResponse?,
            cartGood: GoodsRequest? = null,
            orderedGood: OrderedGoods? = null,
            addToCart: Boolean? = false,
            diningStyle: Int,
            updateCartCallBackListener: ((
                GoodsRequest?
            ) -> Unit)? = null,
            updateOrderCallBackListener: ((salePrice: String?, vipPrice: String?) -> Unit)? =
                null
        ): EditTimePriceDialog {
            val args = Bundle()

            val fragment = EditTimePriceDialog()
            fragment.arguments = args
            fragment.updateCartCallBackListener = updateCartCallBackListener
            fragment.updateOrderCallBackListener = updateOrderCallBackListener
            fragment.orderInfo = orderInfo
            fragment.addToCart = addToCart
            fragment.diningStyle = diningStyle
            fragment.cartGood = cartGood?.deepCopy()
            fragment.orderedGood = orderedGood
            return fragment
        }
    }


}
