package com.metathought.food_order.casheir.extension

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.text.InputFilter
import android.text.Spanned
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.widget.EditText
import android.widget.TextView
import androidx.core.content.ContextCompat
import java.util.regex.Pattern


fun EditText.showPass(show: Boolean = true) {
    val currentSelection = selectionStart to selectionEnd
    transformationMethod = when {
        show -> HideReturnsTransformationMethod.getInstance()
        else -> PasswordTransformationMethod.getInstance()
    }
    setSelection(currentSelection.first, currentSelection.second)
}

fun TextView.setTextViewDrawableColor(color: Int) {
    for (drawable in this.compoundDrawables) {
        if (drawable != null) {
            drawable.colorFilter =
                PorterDuffColorFilter(
                    ContextCompat.getColor(this.context, color),
                    PorterDuff.Mode.SRC_IN
                )
        }
    }
}

fun EditText.setMaxLength(maxLength: Int) {
    this.filters = arrayOf(InputFilter.LengthFilter(maxLength))
}

fun EditText.setDecimalFormat() {
    this.filters = arrayOf(NumberInputFilter(10, 2))
}

///**
// * 为TextView设置带粗细的中划线
// * @param text 显示的文本
// * @param width 中划线宽度（像素）
// * @param color 中划线颜色
// * @param isTextStrikethrough 是否同时启用原生中划线效果
// */
//fun TextView.setCustomStrikeThrough(
//    text: String,
//    width: Float = 2f,
//    @ColorInt color: Int = currentTextColor,
//    isTextStrikethrough: Boolean = false
//) {
//    val spannable = SpannableString(text)
//
//    // 设置原生中划线（可选）
//    if (isTextStrikethrough) {
//        paintFlags = paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
//    } else {
//        paintFlags = paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
//    }
//
//    // 添加自定义中划线Span
//    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//        val customStrikeSpan = object : StrikethroughSpan() {
//            override fun updateDrawState(ds: TextPaint) {
//                super.updateDrawState(ds)
//                ds.isStrikeThruText = false // 禁用原生中划线
//                ds.color = color
//                ds.strokeWidth = width
//                ds.style = Paint.Style.STROKE
//            }
//        }
//        spannable.setSpan(customStrikeSpan, 0, text.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//    } else {
//        // 兼容旧版本
//        val customStrikeSpan = object : CharacterStyle(), UpdateAppearance {
//            override fun updateDrawState(tp: TextPaint) {
//                tp.isStrikeThruText = false // 禁用原生中划线
//                tp.color = color
//                tp.strokeWidth = width
//                tp.style = Paint.Style.STROKE
//            }
//        }
//        spannable.setSpan(customStrikeSpan, 0, text.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//    }
//
//    this.text = spannable
//}


/**
 * 给只允许输入数字的EditText限制输入范围
 * Limit the input range of EditText to only allow numbers
 */
fun EditText.setNumberRange(min: Int, max: Int) {
    this.filters = arrayOf(NumberRangeFilter(min, max))
}


class NumberRangeFilter(private var min: Int, private var max: Int) : InputFilter {
    override fun filter(
        source: CharSequence,
        start: Int,
        end: Int,
        dest: Spanned,
        dstart: Int,
        dend: Int
    ): CharSequence? {
        try {
            // 拼接新的字符串 Concatenate new strings
            val newString = dest.subSequence(0, dstart).toString() + source + dest.subSequence(
                dend,
                dest.length
            )
            // 检查新的字符串是否以前导零开始 Checks if the new string starts with a leading zero
            if (newString.matches("^0+[1-9]+\\d*".toRegex())) {
                return "" // 拒绝输入
            }
            val input = newString.toInt()
            // 检查新的数值是否在 min 到 max 之间 Check if the new value is between min and max
            if (input in min..max) {
                return null // 接受输入 Accepting Input
            }
        } catch (e: NumberFormatException) {
            // 不做处理，这会阻止输入 No processing is done, this will prevent input
        }
        return "" // 拒绝输入 Reject Input
    }

}

class NumberInputFilter(precision: Int, scale: Int) : InputFilter {
    private val mPattern: Pattern

    init {
        val pattern =
            "^\\-?(\\d{0," + (precision - scale) + "}|\\d{0," + (precision - scale) + "}\\.\\d{0," + scale + "})$"
        mPattern = Pattern.compile(pattern)
    }

    override fun filter(
        source: CharSequence,
        start: Int,
        end: Int,
        destination: Spanned,
        destinationStart: Int,
        destinationEnd: Int
    ): CharSequence? {
        if (end > start) {

            val destinationString = destination.toString()
            val resultingTxt =
                destinationString.substring(0, destinationStart) + source.subSequence(
                    start,
                    end
                ) + destinationString.substring(destinationEnd)
            return if (resultingTxt.matches(mPattern.toString().toRegex())) null else ""
        }
        // removing: always accept
        return null
    }
}

/**
 * 限制 EditText 仅能输入数字和英文字母（大小写）
 */
fun EditText.filterDigitsAndLetters() {
    // 正则表达式：允许数字、大小写字母
    val pattern = Pattern.compile("[a-zA-Z0-9]")
    filters += InputFilter { source, start, end, dest, dstart, dend ->
        val sb = StringBuilder()
        // 遍历输入字符，仅保留匹配正则的字符
        for (i in start until end) {
            val char = source[i]
            if (pattern.matcher(char.toString()).matches()) {
                sb.append(char)
            }
        }
        // 返回过滤后的字符（若无需过滤则返回原字符）
        if (sb.length == end - start) source else sb.toString()
    }
}
