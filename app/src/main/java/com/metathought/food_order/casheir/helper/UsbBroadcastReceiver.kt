package com.metathought.food_order.casheir.helper

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import com.metathought.food_order.casheir.MyApplication
import timber.log.Timber


class UsbBroadcastReceiver : BroadcastReceiver() {

    companion object {
        @Volatile
        private var instance: UsbBroadcastReceiver? = null

        fun getInstance(): UsbBroadcastReceiver {
            return instance ?: synchronized(this) {
                instance ?: UsbBroadcastReceiver().also { instance = it }
            }
        }
    }

    private var callback: ((UsbDevice, Boolean) -> Unit)? = null

    init {
        regist()
    }

    fun setCallback(callback: ((UsbDevice, Boolean) -> Unit)?) {
        this.callback = callback
    }

    private fun regist() {
        val filter = IntentFilter()
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        MyApplication.myAppInstance.registerReceiver(this, filter) // 注销时调用unregisterReceiver
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action
        val device: UsbDevice = intent?.getParcelableExtra(UsbManager.EXTRA_DEVICE) ?: return

        // 识别目标设备：需过滤VendorID/ProductID
        val vendorId = device.vendorId
        val productId = device.productId

        when (action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                callback?.invoke(device, true)
                Timber.d("USB串口设备已连接")
            }


            UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                callback?.invoke(device, false)
                Timber.d("USB串口设备已断开")
            }
        }
    }
}