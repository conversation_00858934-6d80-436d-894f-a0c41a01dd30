package com.metathought.food_order.casheir.data.model.base.request_model.logout


import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class CashierLogoutRequest(
    @SerializedName("khrAmount")
    val khrAmount: BigDecimal? = null, //交接金额-瑞尔(单位:分)
    @SerializedName("usdAmount")
    val usdAmount: BigDecimal? = null,  //交接金额-美元(单位:分)
    @SerializedName("changeShiftRemark")
    val changeShiftRemark: String? = null, //交班备注
    @SerializedName("amountPaidKhr")
    val amountPaidKhr: BigDecimal? = null, //支出金额(KHR)(单位:分)
    @SerializedName("amountPaidUsd")
    val amountPaidUsd: BigDecimal? = null,  //支出金额(USD)(单位:分)

    @SerializedName("exit")
    val exit: Boolean? = null,  //交班后是否退出
)