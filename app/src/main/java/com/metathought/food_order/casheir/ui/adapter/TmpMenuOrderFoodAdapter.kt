package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.view.text.addTextTag
import com.view.text.config.TagConfig
import com.view.text.config.Type
import timber.log.Timber


class TmpMenuOrderFoodAdapter(
    val list: ArrayList<Goods>,
    val context: Context,
//    val onPlusCallback: (Goods) -> Unit,
//    val onSubCallback: (Goods) -> Unit,
//    val onNumCallback: (Goods) -> Unit,
    val onSelectCallback: (Goods) -> Unit,
) : RecyclerView.Adapter<TmpMenuOrderFoodAdapter.MenuOrderFoodItemViewHolder>() {
    var diningStyle: Int = 0

    private var selectGoodsRequest: Goods? = null

    inner class MenuOrderFoodItemViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.run {
                root.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        SingleClickUtils.isFastDoubleClick {
                            selectGoodsRequest = list[bindingAdapterPosition]
                            onSelectCallback?.invoke(selectGoodsRequest!!)
                            notifyDataSetChanged()
                        }
//                            onNumCallback.invoke(list[bindingAdapterPosition])
                    }
//                    }
                }
            }
        }

        fun bind(resource: Goods?, position: Int?) {
            resource?.let { _ ->
                binding.apply {
                    if (selectGoodsRequest != null) {
                        layoutBg.isSelected = HashHelper.getHash(
                            resource.feeds,
                            null,
                            goodsId = resource.id,
                            singleItemDiscount = null,
                            goodsPriceKey = "",
                            note = "",
                            uuid = null
                        ) == HashHelper.getHash(
                            null,
                            null,
                            goodsId = selectGoodsRequest!!.id,
                            singleItemDiscount = null,
                            goodsPriceKey = "",
                            note = "",
                            uuid = null
                        )
                    } else {
                        layoutBg.isSelected = false
                    }

                    tvName.text = resource.name
                    layoutPrice.tvWeight.isVisible = false
                    layoutPrice.tvVipPrice.isVisible = false
//                    layoutPrice.tvOriginalPrice.isVisible = false

                    tvQTY.text = "x${resource.totalCount}"
                    layoutPrice.tvFoodPrice.text =
                        ((resource.sellPrice ?: 0L) * (resource.totalCount
                            ?: 0))?.priceFormatTwoDigitZero2()


                    if (resource?.isSoldOut() == true) {
                        tvName.setTextColor(context.getColor(R.color.black))
                        tvSpecification.setTextColor(context.getColor(R.color.black))
                        tvQTY.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvFoodPrice.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvWeight.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvVipPrice.setTextColor(context.getColor(R.color.black))
//                        layoutPrice.tvOriginalPrice.setTextColor(context.getColor(R.color.black))
                        layoutBg.alpha = 0.4f
                    } else {
                        tvName.setTextColor(context.getColor(R.color.black))
                        tvSpecification.setTextColor(context.getColor(R.color.black60))
                        tvQTY.setTextColor(context.getColor(R.color.black60))
                        layoutPrice.tvFoodPrice.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvWeight.setTextColor(context.getColor(R.color.black))
                        layoutPrice.tvVipPrice.setTextColor(context.getColor(R.color.member_price_color))
//                        layoutPrice.tvOriginalPrice.setTextColor(context.getColor(R.color.black60))
                        layoutBg.alpha = 1f
                    }
                }
            }
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuOrderFoodItemViewHolder {
        val itemView = SelectedMenuItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )

        return MenuOrderFoodItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: MenuOrderFoodItemViewHolder, position: Int) {

        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun updateItems(newItems: List<Goods>) {
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateDiningStyle(style: Int) {
        diningStyle = style
    }

    fun clearSelect() {
        Timber.e("清空选择")
        selectGoodsRequest = null
        notifyDataSetChanged()
    }

    fun getSelectItem(): Goods? {
        return selectGoodsRequest
    }

    fun updateSelectItem(item: Goods?) {
        selectGoodsRequest = item
    }

}