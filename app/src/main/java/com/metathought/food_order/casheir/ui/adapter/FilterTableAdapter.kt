package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding


class FilterTableAdapter(
    val list: OrderedTableListResponse,
    val context: Context,
    val selected: List<OrderedTableListItem>,
    val onClickCallback: (OrderedTableListItem) -> Unit,
) : RecyclerView.Adapter<FilterTableAdapter.TableItemViewHolder>() {

    init {
        selected.isNotEmpty().let {
            for (tableID in selected) {
                list.firstOrNull {
                    it.id == tableID.id
                }?.ischeck = true
            }
        }
    }

    inner class TableItemViewHolder(val binding: FilterTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: OrderedTableListItem?, index: Int) {
            resource?.let { _ ->
                binding.apply {
                    tvTableID.text = resource.name
                    tvTableID.setTextColor(ContextCompat.getColor(
                        context,
                        if (resource.ischeck) R.color.primaryColor else android.R.color.black
                    ))
                    root.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            if (resource.ischeck) R.color.filter_table_background else android.R.color.transparent
                        )
                    )
                    root.setOnClickListener {
                        resource.ischeck = resource.ischeck != true
                        notifyItemChanged(index)
                        onClickCallback.invoke(resource)
                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableItemViewHolder {
        var itemView = FilterTableItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
        return TableItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: TableItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun getSelectedArrayList(): List<OrderedTableListItem> {
        return list.filter { it.ischeck }
    }

    fun resetSelect() {
        val selectedTable = list.filter { it.ischeck }
        for (table in selectedTable) {
            table.ischeck = false
        }
        notifyDataSetChanged()
    }

    fun updateItems(newItems: List<OrderedTableListItem>) {
        list.clear()
        list.addAll(newItems)
        selected.isNotEmpty().let {
            for (tableID in selected) {
                list.firstOrNull {
                    it.id == tableID.id
                }?.ischeck = true
            }
        }
        notifyDataSetChanged()
    }

    fun updateItemCheck(selectedTableItem:ArrayList<OrderedTableListItem>){
        list.forEach {
            it.ischeck=false
        }
        selectedTableItem.forEach {
            val indexOf = list.indexOf(it)
            if(indexOf!=-1){
                list[indexOf].ischeck=true
            }
        }
        notifyDataSetChanged()
    }

}