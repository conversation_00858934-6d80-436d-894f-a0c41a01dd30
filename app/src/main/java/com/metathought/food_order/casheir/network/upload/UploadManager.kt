package com.metathought.food_order.casheir.network.upload

import android.R.attr.data
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import okhttp3.Call
import okhttp3.Callback
import okhttp3.FormBody
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.IOException
import java.util.concurrent.atomic.AtomicReference


/**
 *<AUTHOR>
 *@time  2024/10/28
 *@desc
 **/

class UploadManager {
    companion object {
        private const val TAG = "UploadManager"
        private val INSTANCE = AtomicReference<UploadManager>()
        fun getInstance(): UploadManager {
            while (true) {
                var current: UploadManager? = INSTANCE.get()
                if (current != null) {
                    return current
                }
                current = UploadManager()
                if (INSTANCE.compareAndSet(null, current)) {
                    return current
                }
            }
        }
    }


    fun upload(
        serverUrl: String,
        imagePath: String,
        success: ((String?) -> Unit)? = null,
        fail: ((String?) -> Unit)? = null
    ) {

        val request = Request.Builder()
            .url(serverUrl)
            .header("Content-Type", "text/plain")
            .put(RequestBody.create(null, File(imagePath)))
            .build()

        val client = OkHttpClient()
        // 执行异步请求
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                fail?.invoke(e.message ?: "")
                Timber.e("上传失败 ${e.message}")
            }

            override fun onResponse(call: Call, response: okhttp3.Response) {
                Timber.e("上传完成 ${response.code}")
                if (response.code == 200) {
                    success?.invoke(serverUrl)
                } else {
                    fail?.invoke(response.message)
                }
            }

        })
    }
}