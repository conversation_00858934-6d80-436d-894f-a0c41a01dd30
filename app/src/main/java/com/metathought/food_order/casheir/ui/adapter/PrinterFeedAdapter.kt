package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterFeedBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.utils.DisplayUtils
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2024/5/2716:42
 * @description
 */
class PrinterFeedAdapter(
    val list: List<Feed>,
    val isKitchen: Boolean,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机
    val isShowPrice: Boolean?,
) : RecyclerView.Adapter<PrinterFeedAdapter.PrinterFeedViewHolder>() {


    inner class PrinterFeedViewHolder(val binding: ItemPrinterFeedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: Feed) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
                var nameString = ""
                localeList.forEachIndexed { index, locale ->
                    val name = resource.getNameByLocale(locale)

                    if (isEightyWidth == true) {
                        if (index == 0) {
                            tvFeedNameEn.text = "$name"
                            tvFeedNameEn.isVisible = !name.isNullOrEmpty()
                        } else if (index == 1) {
                            tvFeedNameKm.text = "$name"
                            tvFeedNameKm.isGone =
                                (tvFeedNameKm.text == tvFeedNameEn.text) || name.isNullOrEmpty()
                        }
                        llItemIndex.isInvisible = true
                    } else {
                        if (nameString.isEmpty()) {
                            nameString = "$name"
                        } else {
                            if (nameString != name) {
                                nameString = "$nameString $name"
                            }
                        }
                    }
                }

                if (isEightyWidth == true) {
                    tvFeedName.isVisible = false
                    llFeedName.isVisible = true
                    llItemIndex.isInvisible = true
//                    llDiscountPrice.isInvisible = true

                    (llItemIndex.layoutParams as LinearLayout.LayoutParams).weight = 1f
                    (llFeedName.layoutParams as LinearLayout.LayoutParams).weight = 2.5f
                    (llFeedFoodCount.layoutParams as LinearLayout.LayoutParams).weight = 1f
                    (llFeedFoodPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
//                    (llDiscountPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                    (llFeedFoodTotal.layoutParams as LinearLayout.LayoutParams).weight =
                        1.5f

                    tvFeedNameEn.text = "+ ${tvFeedNameEn.text}"
                    tvFeedNameKm.setPadding(80, 0, 0, 0)
                } else {
                    tvFeedName.text = "+ $nameString"
                    tvFeedName.setPadding(20, 0, 0, 0)
                }

                tvFeedFoodCount.text = "x${resource.alreadyNum}"
                if (isShowPrice == true) {
                    tvFeedFoodPrice.text = "$${resource.sum?.priceDecimalFormatTwoDigitZero()}"
                    tvFeedFoodPrice.isVisible = (resource.sum ?: 0.0) > 0.0
                }
            }
        }


    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterFeedBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterFeedAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}