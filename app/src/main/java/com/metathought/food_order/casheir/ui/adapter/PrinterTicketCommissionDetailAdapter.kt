package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemTicketCommissionDetailBinding
import com.metathought.food_order.casheir.databinding.ItemTicketOrderMenuBinding
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber
import java.math.BigDecimal
import java.util.Locale

/**
 * 外卖小票佣金详情列表
 * Dine-in/Take-away/Booking to print receipt information
 * <AUTHOR>
 * @date 2024/5/1014:15
 * @description
 */
class PrinterTicketCommissionDetailAdapter(
    // 修改为接收佣金分组信息
    val commissionGroupedList: List<Pair<BigDecimal?, Long>>,
    val currentOrderedInfo: OrderedInfoResponse?,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机
    val langList: MutableList<Locale>
) :
    RecyclerView.Adapter<PrinterTicketCommissionDetailAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemTicketCommissionDetailBinding) :
        RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(commissionInfo: Pair<BigDecimal?, Long>, position: Int) {
            itemView.context.run {
                langList.forEachIndexed { index, locale ->
                    (binding.llCommissionTitle[index] as TextView).isVisible = true
                    (binding.llCommissionTitle[index] as TextView).text =
                        itemView.context.getStringByLocale(
                            R.string.commission, locale
                        )
                }

                if (isEightyWidth == true) {
                    if (binding.tvCommission1.isVisible) {
                        binding.tvCommission1.text =
                            "${binding.tvCommission1.text}(${commissionInfo.first ?: 0}%)"
                    } else {
                        binding.tvCommission0.text =
                            "${binding.tvCommission0.text}(${commissionInfo.first ?: 0}%)"
                    }
                    binding.llCommissionTitle.orientation = LinearLayout.HORIZONTAL
                } else {
                    binding.tvCommission0.text =
                        "${binding.tvCommission0.text}(${commissionInfo.first ?: 0}%)"
                }

                binding.tvCommissionPrice.text =
                    "-${commissionInfo.second.priceFormatTwoDigitZero2()}"
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        PrinterTicketMenuViewHolder(
            ItemTicketCommissionDetailBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    override fun getItemCount() = commissionGroupedList.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(commissionGroupedList[position], position)
    }

    fun replaceData(
        newData: List<Pair<BigDecimal?, Long>>
    ) {
        if (newData.isNotEmpty()) {
            commissionGroupedList.toMutableList().clear()
            commissionGroupedList.toMutableList().addAll(newData)
            notifyDataSetChanged()
        }
    }
}