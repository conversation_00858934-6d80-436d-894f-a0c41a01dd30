package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecordVo
import com.metathought.food_order.casheir.databinding.ItemCreditRecordsBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4
import com.metathought.food_order.casheir.ui.member.dialog.RechargeDetailDialog
import kotlin.math.min


class CreditRecordsAdapter :
    RecyclerView.Adapter<CreditRecordsAdapter.CreditRecordsViewHolder>() {

    var maxItemCount = -1
    private val list = mutableListOf<CreditRecordVo>()
    var onDetailClick: ((CreditRecordVo, Int) -> Unit)? = null

    inner class CreditRecordsViewHolder(val binding: ItemCreditRecordsBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.tvDetail.setOnClickListener {
                if (bindingAdapterPosition != -1) {
                    list[bindingAdapterPosition].let {
                        onDetailClick?.invoke(it, bindingAdapterPosition)
                    }
                }
            }
        }

        fun bind(resource: CreditRecordVo) {
            binding.apply {
                tvOrderID.text = resource.orderId
                tvCreditAmount.text = resource.amount?.priceFormatTwoDigitZero4()
                tvTime.text = resource.creditDate?.formatDate()

                when (resource.payStatus) {
                    OrderedStatusEnum.CREDIT_UNPAID.id -> {
                        tvStatus.setText(R.string.credit_unpaid)
                        tvStatus.setTextColor(tvStatus.context.getColor(R.color.color_ff3141))
                    }

                    OrderedStatusEnum.CREDIT_PAID.id -> {
                        tvStatus.setText(R.string.credit_paid)
                        tvStatus.setTextColor(tvStatus.context.getColor(R.color.black))
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CreditRecordsViewHolder(
        ItemCreditRecordsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() =
        if (maxItemCount == -1) list.size else min(maxItemCount, list.size)

    fun getItemSize() = list.size

    override fun onBindViewHolder(
        holder: CreditRecordsAdapter.CreditRecordsViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<CreditRecordVo>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addData(response: List<CreditRecordVo>) {
        this.list.addAll(response)
        notifyDataSetChanged()
    }

}