package com.metathought.food_order.casheir.ui.pending_order

import android.annotation.SuppressLint
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.pending.PendingOrderListRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingRecord
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.websocket.ApiWebSocket
import com.tinder.scarlet.Lifecycle
import com.tinder.scarlet.WebSocket
import com.tinder.scarlet.lifecycle.LifecycleRegistry
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PendingOrderViewModel @Inject
constructor(private val repository: Repository) : ViewModel() {
    private var pagNo = 1
    private val pageSize = 20
    private val _uiListState = MutableLiveData<UIListModel>()
    val uiListState get() = _uiListState

    //    private val _uiDeleteState = MutableLiveData<ApiResponse<BaseBooleanResponse>>()
    private val _uiDeleteState = MutableLiveData<UIDeleteState>()
    val uiDeleteState get() = _uiDeleteState

    private val _uiGetRecordState = MutableLiveData<UIGetState>()
    val uiGetRecordState get() = _uiGetRecordState

    private val _uiSaveRecordState = MutableLiveData<UISaveRecordStateModel>()
    val uiSaveRecordState get() = _uiSaveRecordState
    fun getPendingList(
        isRefresh: Boolean? = null,
        keyword: String? = null,
        diningStyleList: ArrayList<Int>? = null
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIListState(showLoading = true)
                }
            }
            val pendingListRequest = PendingOrderListRequest(
                page = pagNo,
                pageSize = pageSize,
                keyword = keyword,
                diningStyleList = diningStyleList ?: arrayListOf(0, 1, 2)
            )
            try {
                //_pendingOrderResponse.value = repository.getPendingList(pendingListRequest)
                val response = repository.getPendingList(pendingListRequest)
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data
                        if (response.records.isNullOrEmpty()) {
                            emitUIListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false,
                                keyword = keyword
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false,
                            keyword = keyword
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUIListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                //_pendingOrderResponse.value = ApiResponse.Error(e.message)
                emitUIListState(showError = e.message, showLoading = false)
            }
        }
    }

    fun deletePendingByOrderNo(orderNO: String, isResumeOrder: Boolean? = null) {
        viewModelScope.launch {
            emitUiDeleteState(response = ApiResponse.Loading)
            try {
                val response = repository.deletedPendingOrder(orderNO)
                emitUiDeleteState(response = response, isResumeOrder = isResumeOrder)
            } catch (e: Exception) {
                emitUiDeleteState(response = ApiResponse.Error(e.message))
            }
        }
    }

    fun getPendingOrderByNo(orderNO: String, record: PendingRecord? = null) {
        viewModelScope.launch {
            emitUiGetState(pendingRecord = ApiResponse.Loading)
            try {
                val response = repository.getPendingOrder(orderNO)

                emitUiGetState(pendingRecord = response, record = record)
            } catch (e: Exception) {
                emitUiGetState(pendingRecord = ApiResponse.Error(e.message))
            }
        }
    }


    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: PendingListResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
        keyword: String? = null
    ) {
        val uiModel =
            UIListModel(showLoading, showError, showSuccess, showEnd, isRefresh, keyword)
        withContext(Dispatchers.Main) {
            _uiListState.value = uiModel
        }
    }

    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: PendingListResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
        val keyword: String? = null
    )

    fun updateShoppingRecord(
        it: ReserveTableRequest,
        diningStyleEnum: Int,
        goods: List<OrderedGoods>,
        note: String? = null
    ) {
        viewModelScope.launch {
            emitUiSaveRecordState(showLoading = true)
            ShoppingHelper.clearNote(diningStyleEnum)
            ShoppingHelper.clearCoupon(diningStyleEnum)
            ShoppingHelper.delAndCustomerAndTable(diningStyleEnum)
            goods.forEach { orderedGood ->
                val good = orderedGood.orderedGoodsConvertToGoods()
                var arrayFeed = ArrayList<Feed>()
                var arrayTag = ArrayList<GoodsTagItem>()
                orderedGood.tagItems?.let {
                    arrayTag = ArrayList(orderedGood.tagItems)
                }
                orderedGood.feeds?.let {
                    arrayFeed = ArrayList(orderedGood.feeds)
                }
                ShoppingHelper.plusAndAdd(
                    good,
                    localDingingStyle = diningStyleEnum,
                    feedList = arrayFeed,
                    goodsTagItemList = arrayTag,
                    orderMealSetGoodList = orderedGood.orderMealSetGoodsDTOList,
                    cartCount = orderedGood.num ?: 0,
                    singleDiscountGoods = null,
                    note = orderedGood.note
                )
            }
            ShoppingHelper.updateNote(note ?: "", diningStyleEnum)
            val shoppingRecord = ShoppingHelper.updateCustomer(diningStyleEnum, it)

            withContext(Dispatchers.Main) {
                emitUiSaveRecordState(showSuccess = true, shoppingRecord = shoppingRecord)
            }
        }
    }

    private suspend fun emitUiSaveRecordState(
        showLoading: Boolean? = null,
        showSuccess: Boolean? = null,
        shoppingRecord: ShoppingRecord? = null
    ) {
        val uiModel =
            UISaveRecordStateModel(showLoading, showSuccess, shoppingRecord)
        withContext(Dispatchers.Main) {
            _uiSaveRecordState.value = uiModel
        }
    }

    data class UISaveRecordStateModel(
        val showLoading: Boolean?,
        val showSuccess: Boolean?,
        val shoppingRecord: ShoppingRecord?
    )

    private suspend fun emitUiDeleteState(
        isResumeOrder: Boolean? = null,
        response: ApiResponse<BaseBooleanResponse>? = null,
    ) {
        val uiModel =
            UIDeleteState(isResumeOrder, response)
        withContext(Dispatchers.Main) {
            _uiDeleteState.value = uiModel
        }
    }

    data class UIDeleteState(
        val isResumeOrder: Boolean?,
        val response: ApiResponse<BaseBooleanResponse>?,
    )


    private suspend fun emitUiGetState(
        pendingRecord: ApiResponse<PendingRecord>? = null,
        record: PendingRecord? = null
    ) {
        val uiModel =
            UIGetState(pendingRecord, record)
        withContext(Dispatchers.Main) {
            _uiGetRecordState.value = uiModel
        }
    }

    data class UIGetState(
        val pendingRecord: ApiResponse<PendingRecord>?,
        var record: PendingRecord? = null
    )


    //Socket
//    private val _liveDATA = MutableLiveData<WebSocket.Event>()
//    val liveDataRespose get() = _liveDATA
//    val lifecycleRegistry = LifecycleRegistry()
//    val apiWebSocketService = ApiWebSocket.provideSocketApi(lifecycleRegistry)
//
//    @SuppressLint("CheckResult")
//    fun connectWebsocket() {
//        apiWebSocketService.observeConnection().observeOn(Schedulers.io()).subscribe({ response ->
//            Timber.e("WebSocket  ${response.toString()}")
//            viewModelScope.launch {
//                _liveDATA.value = response
//            }
//        }, { error ->
//            Timber.e("WebSocket ${error.message.orEmpty()}")
//        })
//    }
//
//    fun testingWebsocketSendMessage() {
//        apiWebSocketService.sendMessage("pong")
//        Log.d("WebSocket", "send back pong")
//    }
//
//
//    fun destroylifeCycle() {
//        lifecycleRegistry.onNext(Lifecycle.State.Destroyed)
//    }
//
//    fun startLifeCycle() {
//        lifecycleRegistry.onNext(Lifecycle.State.Started)
//    }


}