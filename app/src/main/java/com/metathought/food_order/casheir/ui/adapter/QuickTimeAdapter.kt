package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.QuickTimeType
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding
import com.metathought.food_order.casheir.model.QuickTimeItem

class QuickTimeAdapter(
    val list: List<QuickTimeItem>,
    val selected: QuickTimeItem?,
    val context: Context
) : RecyclerView.Adapter<QuickTimeAdapter.QuickTimeViewHolder>() {

    var onItemClickCallback: ((QuickTimeItem) -> Unit)? = null

    inner class QuickTimeViewHolder(val binding: FilterTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: QuickTimeItem) {
            binding.apply {
                tvTableID.text = item.displayName
                tvTableID.gravity = Gravity.CENTER
                tvTableID.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (selected?.type == item.type) R.color.primaryColor else android.R.color.black
                    )
                )
                root.setCardBackgroundColor(
                    ContextCompat.getColor(
                        context,
                        if (selected?.type == item.type) R.color.filter_table_background else android.R.color.transparent
                    )
                )
                root.setOnClickListener {
                    onItemClickCallback?.invoke(item)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): QuickTimeViewHolder {
        val binding =
            FilterTableItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return QuickTimeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: QuickTimeViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size
}
