package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.offline.PaymentMethodModel
import com.metathought.food_order.casheir.databinding.ItemPaymentMethodBinding
import com.metathought.food_order.casheir.extension.getColor

/**
 * <AUTHOR>
 * @date 2024/5/2711:24
 * @description
 */
class DropPaymentMethodAdapter(
    var list: List<PaymentMethodModel>,
    val onSelectItem: (PaymentMethodModel) -> Unit,
) : RecyclerView.Adapter<DropPaymentMethodAdapter.ItemPaymentMethodViewHolder>() {

    inner class ItemPaymentMethodViewHolder(val binding: ItemPaymentMethodBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.run {
                itemView.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        list.forEach {
                            it.isSelectPayment = false
                        }
                        list[bindingAdapterPosition].isSelectPayment = true
                        onSelectItem.invoke(list[bindingAdapterPosition])
                        notifyDataSetChanged()
                    }
                }
            }
        }

        fun bind(resource: PaymentMethodModel) {
            itemView.context?.let { context ->
                resource.let {
                    binding.apply {
                        tvItemView.text = it.getPayMethod(context)
                        if (it.isSelectPayment) {
                            tvItemView.setBackgroundResource(R.drawable.background_language_selected)
                            tvItemView.setTextColor(
                                ContextCompat.getColor(
                                    itemView.context,
                                    R.color.primaryColor
                                )
                            )
                        } else {
                            tvItemView.setBackgroundColor(R.color.white.getColor(itemView.context))
                            tvItemView.setTextColor(R.color.black.getColor(itemView.context))
                        }

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = ItemPaymentMethodViewHolder(
        ItemPaymentMethodBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: ItemPaymentMethodViewHolder, position: Int) {
        holder.bind(list[position])
    }


}