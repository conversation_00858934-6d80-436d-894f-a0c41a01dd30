package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.databinding.ItemManagerPrinterBinding
import com.metathought.food_order.casheir.helper.NewPrinterUsbDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.ui.app_dashbord.selectPrinter
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber
import java.util.concurrent.ExecutionException


/**
 * <AUTHOR>
 * @date 2024/08/16 11:45
 * @description
 */
class PrinterManagerListAdapter(
    val list: MutableList<PrinterConfigInfo?>,
    val isBiggerWidth: Boolean,
    val onSelectType: (View, PrinterConfigInfo?) -> Unit,
    val onItemClickListener: (PrinterConfigInfo?) -> Unit,
) : RecyclerView.Adapter<PrinterManagerListAdapter.PrinterManagerListViewHolder>() {


    inner class PrinterManagerListViewHolder(val binding: ItemManagerPrinterBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: PrinterConfigInfo?, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {

                        if (isBiggerWidth) {
//                            Timber.e("修改 比例")
                            (tvPrinterName.layoutParams as LinearLayout.LayoutParams).weight = 2f
                            (tvPrinterType.layoutParams as LinearLayout.LayoutParams).weight = 2f
                            (tvPrinterDevice.layoutParams as LinearLayout.LayoutParams).weight = 2f
                            (tvBindingTime.layoutParams as LinearLayout.LayoutParams).weight = 1.6f
                            (tvReceiptSize.layoutParams as LinearLayout.LayoutParams).weight = 1.2f
                            (tvTemplateConfigured.layoutParams as LinearLayout.LayoutParams).weight =
                                2f
                            (llStatus.layoutParams as LinearLayout.LayoutParams).weight =
                                1.2f
//                            (llOperate.layoutParams as LinearLayout.LayoutParams).weight = 1f
                        }

                        btnConnect.setOnClickListener {
                            SingleClickUtils.isFastDoubleClick {
                                onItemClickListener.invoke(resource)
                            }
                        }

                        tvPrinterName.text = resource?.name

                        tvPrinterDevice.text = resource?.getPrinterType(context)
                        tvReceiptSize.text = resource?.model

                        tvBindingTime.text = "${resource?.createTime}"

                        tvTemplateConfigured.text = resource?.getPrinterTempDesc(context)

                        tvPrinterType.setOnClickListener {
                            if (resource?.type == PrinterTypeEnum.USB.type) {
                                onSelectType?.invoke(tvPrinterType, resource)
                            }
                        }

                        when (resource?.type) {
                            PrinterTypeEnum.WIFI.type -> {
                                val connection =
                                    PrinterDeviceHelper.getWifiConnection(resource.ipAddress)
                                var isConnect = PrinterDeviceHelper.getWifiConnectState(resource)

                                btnConnect.isVisible = !isConnect

                                tvPrinterType.text = context.getString(R.string.ticket)

                                tvConnectionStatus.text = if (isConnect) {
                                    context.getString(R.string.success)
                                } else {
                                    context.getString(R.string.fail)
                                }
                                tvConnectionStatus.setTextColor(
                                    if (isConnect) {
                                        context.getColor(R.color.primaryColor)
                                    } else {
                                        context.getColor(R.color.printer_remark_color)
                                    }
                                )

                            }

                            PrinterTypeEnum.USB.type -> {
                                if (it?.machineType == LocalPrinterEnum.TICKET_PRINTER.id) {
                                    tvPrinterType.text = context.getString(R.string.ticket)
                                } else if (it?.machineType == LocalPrinterEnum.LABEL_PRINTER.id) {
                                    tvPrinterType.text = context.getString(R.string.label)
                                }
                                Timber.e("deviceName: ${it?.usbDevice?.deviceName}")
                                if (Printer.isXPrinter()) {
                                    val connectUSD =
                                        NewPrinterUsbDeviceHelper.isConnectUSB(it?.usbDevice?.deviceName)
                                    connectUSD.addListener(object :
                                        ListenableFuture.Listener<Boolean> {
                                        override fun onSuccess(result: Boolean) {
                                            Timber.e("connectUSD  onSuccess deviceName: ${it?.usbDevice?.deviceName}   ${result}")
                                            tvConnectionStatus.text = if (result) {
                                                context.getString(R.string.success)
                                            } else {
                                                context.getString(R.string.fail)
                                            }
                                            tvConnectionStatus.setTextColor(
                                                if (result) {
                                                    context.getColor(R.color.primaryColor)
                                                } else {
                                                    context.getColor(R.color.printer_remark_color)
                                                }
                                            )
                                        }

                                        override fun onFailure(e: ExecutionException) {
                                            Timber.e("connectUSD onFailure ${e}")
                                            tvConnectionStatus.text =
                                                context.getString(R.string.fail)

                                            tvConnectionStatus.setTextColor(
                                                context.getColor(R.color.printer_remark_color)
                                            )
                                        }
                                    })
                                } else {
                                    val sunmiIsConnect =
                                        Printer.sunmiIsConnect() && !it!!.isLabelPrinter()
                                    tvConnectionStatus.text = if (sunmiIsConnect) {
                                        context.getString(R.string.success)
                                    } else {
                                        context.getString(R.string.fail)
                                    }
                                    tvConnectionStatus.setTextColor(
                                        if (sunmiIsConnect) {
                                            context.getColor(R.color.primaryColor)
                                        } else {
                                            context.getColor(R.color.printer_remark_color)
                                        }
                                    )

                                }

                                btnConnect.isVisible = false
                            }

                            else -> {
                                btnConnect.isVisible = false
                            }
                        }

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PrinterManagerListViewHolder {
        val itemView =
            ItemManagerPrinterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PrinterManagerListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: PrinterManagerListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    fun replaceData(list: List<PrinterConfigInfo?>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

}