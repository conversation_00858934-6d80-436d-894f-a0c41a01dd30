package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.google.gson.annotations.SerializedName


/**
 *<AUTHOR>
 *@time  2024/5/11
 *@desc
 **/

data class DeleteGoodsNumRequest(
    @SerializedName("isPosPrint")
    val isPosPrint: Boolean? = false,
    @SerializedName("note")
    val note: String? = null,
    @SerializedName("orderNo")
    val orderNo: String? = null,
    @SerializedName("removeGoods")
    val removeGoods: MutableList<RemoveGoodsBo?>? = null,
    @SerializedName("reasonType")
    val reasonType: Int? = null,
    @SerializedName("reason")
    val reason: String? = null,

    @SerializedName("autoInStock")
    val autoInStock: Boolean? = null
) {

}


data class RemoveGoodsBo(
    var cartsId: String? = null,//收银端不用这个
    var num: Int?,  //剩余数量
    var hashKey: String?
)