package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ProcessedReportData
import com.metathought.food_order.casheir.data.model.base.response_model.Header
import com.metathought.food_order.casheir.databinding.ItemComprehensiveReportContentBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.TableScrollController
import com.metathought.food_order.casheir.utils.ColumnWidthCalculator
import androidx.core.graphics.toColorInt
import com.metathought.food_order.casheir.ui.dialog.store_report.ComprehensiveReportDialog

/**
 * 处理后的综合报表内容适配器 - 按行展示数据，每行显示一个字段的所有日期数据
 */
class ProcessedReportContentAdapter(
    private val context: Context
) : RecyclerView.Adapter<ProcessedReportContentAdapter.ViewHolder>() {

    private var processedData: ProcessedReportData? = null
    private var tableColW = 110f // 列宽度，会根据数据量动态调整
    private var headers: List<Header> = emptyList() // 表头信息，用于高亮判断
    private var preCalculatedColumnWidth: Float? = null // 预计算的列宽

    // 水平滚动同步回调（保留用于兼容性，但不再使用）
    var onHorizontalScrollListener: ((dx: Int, fromPosition: Int) -> Unit)? = null

    // 存储当前绑定的ViewHolder的引用，用于同步滚动
    private val activeViewHolders = mutableMapOf<Int, ViewHolder>()

    // 当前的水平滚动位置（用于新ViewHolder的初始化）
    private var currentScrollX = 0

    // RecyclerView 引用，用于直接访问可见的 ViewHolder
    private var recyclerView: RecyclerView? = null

    // 表格滚动控制器
    private var scrollController: TableScrollController? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    /**
     * 设置滚动控制器
     */
    fun setScrollController(controller: TableScrollController) {
        scrollController = controller
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemComprehensiveReportContentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        processedData?.let { data ->
            // 将ViewHolder添加到活跃列表中
            activeViewHolders[position] = holder

            // 获取对应的 header 信息
            val header = if (position < headers.size) headers[position] else null
            holder.bind(data, position, header)

            // 注册到滚动控制器（这会设置滚动监听器）
            scrollController?.registerScrollView(holder.getHorizontalScrollView())

            // 不再设置额外的监听器，避免覆盖 TableScrollController 的监听器
            // 如果需要调试信息，可以在 TableScrollController 中添加
        }
    }

    override fun getItemCount(): Int = processedData?.headers?.size ?: 0

    fun updateData(newProcessedData: ProcessedReportData) {
        processedData = newProcessedData
        // 重置滚动位置
        currentScrollX = 0
        // 动态计算列宽
        calculateColumnWidth(newProcessedData)
        notifyDataSetChanged()
    }

    /**
     * 设置预计算的列宽
     */
    fun setPreCalculatedColumnWidth(width: Float) {
        preCalculatedColumnWidth = width
        println("ProcessedReportContentAdapter 设置预计算列宽: ${width}dp")
    }

    /**
     * 使用预计算的列宽更新数据，避免重复计算和闪烁
     */
    fun updateDataWithPreCalculatedWidth(newProcessedData: ProcessedReportData) {
        processedData = newProcessedData
        // 重置滚动位置
        currentScrollX = 0

        // 使用预计算的列宽
        preCalculatedColumnWidth?.let { width ->
            tableColW = width
            println("ProcessedReportContentAdapter 使用预计算列宽: ${width}dp，跳过动态计算")
        } ?: run {
            // 如果没有预计算的列宽，则进行动态计算
            println("ProcessedReportContentAdapter 没有预计算列宽，进行动态计算")
            calculateColumnWidth(newProcessedData)
        }

        notifyDataSetChanged()
    }

    /**
     * 设置表头信息，用于高亮判断
     */
    fun setHeaders(headerList: List<Header>) {
        headers = headerList
    }

    /**
     * 动态计算列宽
     * 根据当前数据区域的宽度平分给各个item，最小宽度110dp
     * 注意：这个方法会导致二次刷新，建议使用预计算方式
     */
    private fun calculateColumnWidth(data: ProcessedReportData) {
        // 如果已有预计算的列宽，直接使用，避免重复计算
        preCalculatedColumnWidth?.let { width ->
            if (tableColW != width) {
                tableColW = width
                println("ProcessedReportContentAdapter 使用预计算列宽: ${width}dp")
                // 不需要再次刷新，因为调用方会处理刷新
            }
            return
        }

        val dataCount = data.dateLabels.size
        if (dataCount > 0) {
            // 获取当前数据区域的实际宽度
            recyclerView?.let { rv ->
                rv.post {
                    val contentAreaWidth = rv.width
                    if (contentAreaWidth > 0) {
                        // 使用统一的计算工具，最小宽度调整为110dp
                        val calculatedWidth = ColumnWidthCalculator.calculateColumnWidth(
                            context, contentAreaWidth, dataCount, 110f
                        )

                        if (tableColW != calculatedWidth) {
                            tableColW = calculatedWidth

                            // 打印计算过程
                            ColumnWidthCalculator.logCalculation(
                                "ProcessedReportContentAdapter(动态计算)",
                                dataCount,
                                contentAreaWidth,
                                ColumnWidthCalculator.calculateColumnWidth(
                                    context,
                                    contentAreaWidth,
                                    dataCount,
                                    0f
                                ),
                                tableColW
                            )

                            // 重新刷新数据以应用新的列宽
                            println("ProcessedReportContentAdapter 动态计算完成，进行二次刷新")
                            notifyDataSetChanged()
                        }
                    }
                }
            }
        }
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        // 从活跃列表中移除回收的ViewHolder
        activeViewHolders.entries.removeAll { it.value == holder }
        // 从滚动控制器注销
        scrollController?.unregisterScrollView(holder.getHorizontalScrollView())
        // 清理ViewHolder的监听器
        holder.clearScrollListener()
        println("ViewHolder回收，清理监听器")
    }

    override fun onViewDetachedFromWindow(holder: ViewHolder) {
        super.onViewDetachedFromWindow(holder)
        // 当ViewHolder从窗口分离时也移除
        activeViewHolders.entries.removeAll { it.value == holder }
    }

//    /**
//     * 同步所有行的水平滚动（除了触发滚动的那一行）
//     * 现在由 TableScrollController 处理，这里保留用于兼容性
//     */
//    fun syncAllRowsHorizontalScroll(dx: Int, excludePosition: Int) {
//        println("syncAllRowsHorizontalScroll 被调用，但现在由 TableScrollController 处理")
//    }
//
//    /**
//     * 从外部同步所有行的水平滚动
//     * 现在由 TableScrollController 处理，这里保留用于兼容性
//     */
//    fun syncAllRowsFromExternal(dx: Int) {
//        println("syncAllRowsFromExternal 被调用，但现在由 TableScrollController 处理")
//    }
//
//    /**
//     * 同步所有行到指定的滚动位置
//     * 现在由 TableScrollController 处理，这里保留用于兼容性
//     */
//    fun syncAllRowsToPosition(scrollX: Int, excludePosition: Int) {
//        println("syncAllRowsToPosition 被调用，但现在由 TableScrollController 处理")
//        currentScrollX = scrollX // 更新当前滚动位置
//    }
//
//    /**
//     * 强制同步所有当前可见的ViewHolder到指定位置
//     * 用于解决ViewHolder复用时的位置不同步问题
//     */
//    fun forceSync() {
//        println("适配器 forceSync 被调用，委托给 TableScrollController")
//        scrollController?.forceSync()
//    }
//
//    /**
//     * 获取当前计算的列宽
//     */
//    fun getCurrentColumnWidth(): Float = tableColW

    inner class ViewHolder(private val binding: ItemComprehensiveReportContentBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private var horizontalScrollListener: ((dx: Int) -> Unit)? = null
        private var isScrollingFromExternal = false

        fun bind(data: ProcessedReportData, fieldIndex: Int, header: Header?) {
            // 清空之前的动态视图
            binding.llDynamicContent.removeAllViews()

            // 获取当前字段的数据
            if (fieldIndex < data.columnDataList.size) {

                val columnData = data.columnDataList[fieldIndex]
                val isHighlight = header?.isHighlight ?: false

                // 为该字段的每个日期值创建TextView
                columnData.values.forEach { value ->
                    val textView = TextView(context).apply {
                        layoutParams = LinearLayout.LayoutParams(
                            DisplayUtils.dp2px(context, tableColW),
                            LinearLayout.LayoutParams.MATCH_PARENT
                        )
                        // 去掉 margin，增加5dp的左右内边距
                        setPadding(
                            DisplayUtils.dp2px(context, 5f), // left
                            0, // top
                            DisplayUtils.dp2px(context, 5f), // right
                            0  // bottom
                        )

                        gravity = android.view.Gravity.CENTER
                        setTextColor(ContextCompat.getColor(context, R.color.black))
                        textSize = 14f
                        setTextAppearance(R.style.FontLocalization)

                        // 设置高亮背景
                        if (isHighlight) {
                            setBackgroundColor(context.getColor(R.color.color_efefef))
                        }

                        // 根据 header 的 key 判断是否为用餐人数或订单数量，是则按原值展示
                        val key = header?.key
                        text = if (key in listOf(
                                ComprehensiveReportDialog.TOTAL_PAX,
                                ComprehensiveReportDialog.ORDER_COUNT
                            ) || (key ?: "").lowercase().endsWith("count")
                        ) {
                            // 处理整数转换：支持 Double/Int/字符串类型的数值转换为整数
                            when (value) {
                                is Number -> value.toDouble().toInt().toString() // 处理 5.0 → 5
                                is String -> value.toDoubleOrNull()?.toInt()?.toString()
                                    ?: value // 处理 "5.0" → 5
                                else -> value.toString() // 其他类型保留原值
                            }
                        } else {
                            // 原格式化逻辑
                            when (value) {
                                is Double -> value.priceFormatTwoDigitZero2("$")
                                is String -> value
                                is Number -> value.toString()
                                else -> "$0.00"
                            }
                        }
                    }
                    binding.llDynamicContent.addView(textView)
                }
            }

            // 设置HorizontalScrollView的滚动监听器
            setupHorizontalScrollListener()
        }

        fun setHorizontalScrollListener(listener: ((dx: Int) -> Unit)?) {
            horizontalScrollListener = listener
            setupHorizontalScrollListener()
        }

        private fun setupHorizontalScrollListener() {
            horizontalScrollListener?.let { listener ->
                binding.mainScrollView.setOnScrollChangeListener { _, scrollX, _, oldScrollX, _ ->
                    val dx = scrollX - oldScrollX

                    if (dx != 0 && !isScrollingFromExternal) {
                        println("ViewHolder滚动触发: dx=$dx, position=${adapterPosition}, scrollX=$scrollX")
                        listener(dx)
                    }
                }
            }
        }

        /**
         * 从外部同步滚动（避免循环调用）
         */
        fun scrollByExternal(dx: Int) {
            isScrollingFromExternal = true
            val oldScrollX = binding.mainScrollView.scrollX
            binding.mainScrollView.scrollBy(dx, 0)
            val newScrollX = binding.mainScrollView.scrollX
            println("    ViewHolder[${adapterPosition}] scrollByExternal: dx=$dx, $oldScrollX -> $newScrollX")
            isScrollingFromExternal = false
        }

        /**
         * 同步到指定的滚动位置
         */
        fun scrollToPosition(scrollX: Int) {
            isScrollingFromExternal = true
            val oldScrollX = binding.mainScrollView.scrollX
            binding.mainScrollView.scrollTo(scrollX, 0)
            val newScrollX = binding.mainScrollView.scrollX
            println("    ViewHolder[${adapterPosition}] scrollToPosition: $oldScrollX -> $newScrollX (目标: $scrollX)")
            isScrollingFromExternal = false
        }

//        /**
//         * 获取当前滚动位置
//         */
//        fun getCurrentScrollX(): Int = binding.mainScrollView.scrollX

        /**
         * 清理滚动监听器
         */
        fun clearScrollListener() {
            horizontalScrollListener = null
            binding.mainScrollView.setOnScrollChangeListener(null)
        }

        /**
         * 获取HorizontalScrollView引用
         */
        fun getHorizontalScrollView() = binding.mainScrollView


    }
}
