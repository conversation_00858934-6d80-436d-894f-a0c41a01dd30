package com.metathought.food_order.casheir.helper

import com.lxj.xpopup.core.BasePopupView
import timber.log.Timber

object XpopHelper {
    private val xPopList = mutableMapOf<String, BasePopupView>()

    fun addToMap(popView: BasePopupView) {
        if (!xPopList.contains(popView.hashCode().toString())) {
            Timber.e("添加 弹窗 ${popView.hashCode().toString()}")
            xPopList[popView.hashCode().toString()] = popView
        }
    }

    fun removeToMap(popView: BasePopupView?) {
        if (popView != null) {
            if (xPopList.contains(popView.hashCode().toString())) {
                Timber.e("移出 弹窗 ${popView.hashCode().toString()}")
                xPopList.remove(popView.hashCode().toString())
            }
        }
    }

    fun removeAllXpop() {
        xPopList.forEach { s, basePopupView ->
            basePopupView.dismiss()
        }
    }
}