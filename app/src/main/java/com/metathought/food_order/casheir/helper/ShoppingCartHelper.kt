package com.metathought.food_order.casheir.helper

import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CartGoodEditRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.AddToCartRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.BatchAddCart
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.TIME_OUT
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.function.Consumer
import com.metathought.food_order.casheir.data.model.base.response_model.cart.Goods as CartGoods


/**
 *<AUTHOR>
 *@time  2025/4/1
 *@desc
 **/

object ShoppingCartHelper {
    fun getGoodsBoList(shareRecord: ShoppingRecord?): ArrayList<GoodsBo> {
        val goodsVoList = shareRecord?.getGoodsVoList()
        val goodsBoList = ArrayList<GoodsBo>()
        goodsVoList?.forEach {
            goodsBoList.add(
                goodsRequestToGoodsBo(it)
            )
        }
        return goodsBoList
    }

    fun goodsRequestToGoodsBo(request: GoodsRequest?): GoodsBo {
        val bo = GoodsBo(
            id = request?.goods?.id,
            num = request?.num,
            tagItemId = request?.getTagItemId(),
            feeds = request?.getFeedBo(),
            pricingMethod = request?.goods?.pricingMethod,
            weight = request?.goods?.weight,
            uuid = request?.goods?.uuid,
            discountPrice = request?.goods?.discountPrice,
            goodsType = request?.goods?.goodsType,
            singleDiscountGoods = request?.singleDiscountGoods?.apply {
                if (request?.num != null) {
                    num = request.num
                }
                Timber.e("singleDiscountGoods:${goodsHashKey}")
            },
            mealSetGoodsDTOList = OrderHelper.getSelectMealSetGood(request?.orderMealSetGoodList),
            singleItemDiscount = request?.singleDiscountGoods?.coverToSingleDiscountRequest()
                ?.apply {
                    if (request?.num != null) {
                        goodsNum = request.num
                    }
                    goodsHashKey = request.getHash()
                    Timber.e("singleItemDiscount:${goodsHashKey}")
                },
            goodsHashKey = request?.getHash(),
            note = request?.note,
            isProcessed = request?.isProcessed(),
            name = request?.goods?.name,
        )

        //如果是时价菜 要走这段逻辑
        if (request?.goods?.isTimePriceGood() == true) {
            if (request.goods?.isHasCompletePricing() == true) {
                bo.pricingCompleted = true
                bo.sellPrice = request.goods?.sellPrice
                bo.vipPrice = request.goods?.vipPrice
            } else {
                bo.pricingCompleted = false
            }
        }

        return bo
    }

    /**
     * 生成添加购物车请求
     *
     * @param goodRequest
     * @param localDingingStyle
     * @return
     */
    fun getAddToCartRequest(goodRequest: GoodsRequest, localDingingStyle: Int?): AddToCartRequest {
        val request = AddToCartRequest(
            diningStyle = localDingingStyle,
            feedInfoList = goodRequest.getFeedBo(),
            tagItemIds = goodRequest.getTagItemId(),
            tableUuid = ShoppingHelper.get(localDingingStyle!!)?.tableUuid ?: "",
            num = goodRequest.num,
            goodsId = goodRequest.goods?.id ?: "",
            goodsType = goodRequest.goods?.goodsType,
            mealSetGoodsInfoList = OrderHelper.getSelectMealSetGood(goodRequest.orderMealSetGoodList),
            note = goodRequest.note,
            uuid = goodRequest.goods?.uuid,
            weight = goodRequest.goods?.weight,
        )
        //如果是时价菜 要走这段逻辑
        if (goodRequest.goods?.isTimePriceGood() == true) {
            if (goodRequest.goods?.isHasCompletePricing() == true) {
                request.pricingCompleted = true
                request.sellPrice = goodRequest.goods?.sellPrice
                request.vipPrice = goodRequest.goods?.vipPrice
            } else {
                request.pricingCompleted = false
            }
        }

        return request
    }

    /**
     * 生成共享 修改备注请求
     *
     * @param goodRequest
     * @param localDingingStyle
     * @param note
     * @return
     */
    fun createCartGoodEditRequest(
        goodRequest: GoodsRequest,
        localDingingStyle: Int?,
        note: String?
    ): CartGoodEditRequest {
        val request = CartGoodEditRequest(
            cartId = goodRequest.goods?.cartsId,
            diningStyle = localDingingStyle,
            goodsId = goodRequest.goods?.id ?: "",
            note = note,
            num = goodRequest.num,
            tableUuid = ShoppingHelper.get(localDingingStyle!!)?.tableUuid ?: "",
            goodsType = goodRequest.goods?.goodsType,
            feedInfoList = goodRequest.getFeedBo(),
            tagItemIds = goodRequest.getTagItemId(),
            mealSetGoodsInfoList = OrderHelper.getSelectMealSetGood(goodRequest.orderMealSetGoodList),
            groupId = goodRequest.goods?.groupID,
        )

        return request
    }


    /**
     * cartGood 转成order路径下的Goods
     *
     * @param orderedGood
     * @return
     */
    fun cartGoodsToGoods(orderedGood: CartGoods?): Goods {
        Timber.e(orderedGood?.toJson())
        return Goods(
            id = orderedGood?.id ?: "",
            sellPrice = orderedGood?.sellPrice ?: 0L,
            name = orderedGood?.name,
            cartsId = orderedGood?.cartsId,
            vipPrice = orderedGood?.vipPrice,
            finalVipPrice = orderedGood?.finalVipPrice,
            finalVipVatPrice = orderedGood?.finalVipVatPrice,
            packingFee = orderedGood?.packingFee,
            totalCount = orderedGood?.num,
            feedStr = orderedGood?.getGoodsTagStr(),
            pricingMethod = orderedGood?.pricingMethod,
            weight = orderedGood?.weight,
            uuid = orderedGood?.uuid,
            discountPrice = orderedGood?.discountPrice,
            vatWhitelisting = orderedGood?.vatWhitelisting,
            vatPercentage = orderedGood?.vatPercentage
                ?: MainDashboardFragment.CURRENT_USER?.vatPercentage,
            serviceChargeWhitelisting = orderedGood?.serviceChargeWhitelisting,
            totalServiceCharge = orderedGood?.getTotalDiscountService(),
            totalVipServiceCharge = orderedGood?.getTotalVipService(),
            totalDiscountServiceCharge = orderedGood?.getTotalDiscountService(),
            activityLabels = orderedGood?.activityLabels,
            goodsType = orderedGood?.goodsType,
            type = orderedGood?.type,
            orderMealSetGoodsDTOList = orderedGood?.orderMealSetGoodsDTOList,
            singleItemDiscount = orderedGood?.singleItemDiscount,
            commissionPercentage = orderedGood?.commissionPercentage,
//            totalCommission = orderedGood?.getTotalCommissionPrice(),
            note = orderedGood?.note,
            isProcessed = orderedGood?.isHasProcessed(),
            weighingCompleted = orderedGood?.isHasCompleteWeight(),
            pricingCompleted = orderedGood?.isHasCompletePricing(),
            currentPrice = orderedGood?.currentPrice,
            packingFeeDisplay = orderedGood?.packingFeeDisplay,
        )
    }

    fun orderGoodsToGoodsRequest(orderedGood: OrderedGoods?): GoodsRequest {
        return GoodsRequest(
            num = orderedGood?.num,
            feedInfoList = orderedGood?.feeds?.toMutableList()?.let { ArrayList(it) },
            goodsTagItems = orderedGood?.tagItems?.toMutableList()?.let { ArrayList(it) },
            orderMealSetGoodList = orderedGood?.orderMealSetGoodsDTOList,
            goods = orderedGood?.orderedGoodsConvertToGoods(),
            note = orderedGood?.note,
            singleDiscountGoods = orderedGood?.singleItemDiscount?.toCartSingleDiscountGood()
        )
    }


    fun cartGoodsToGoodsRequest(orderedGood: CartGoods?): GoodsRequest {
        return GoodsRequest(
            num = orderedGood?.num,
            feedInfoList = orderedGood?.feeds?.toMutableList()?.let { ArrayList(it) },
            goodsTagItems = orderedGood?.tagItems?.toMutableList()?.let { ArrayList(it) },
            orderMealSetGoodList = orderedGood?.orderMealSetGoodsDTOList,
            goods = cartGoodsToGoods(orderedGood),
            note = orderedGood?.note,
            singleDiscountGoods = orderedGood?.singleItemDiscount?.toCartSingleDiscountGood()
        )
    }


    fun toBatchAddCartModel(goodRequest: GoodsRequest): BatchAddCart {
        val bo = BatchAddCart(
            goodsId = goodRequest.goods?.id,
            num = goodRequest.num,
            tagItemIds = goodRequest.getTagItemId(),
            feedInfoList = goodRequest.getFeedBo(),
            goodsType = goodRequest.goods?.goodsType,
            cartMealSetGoodsDTOList = OrderHelper.getSelectMealSetGood(goodRequest.orderMealSetGoodList),
            note = goodRequest.note
        )
        //如果是时价菜 要走这段逻辑
        if (goodRequest.goods?.isTimePriceGood() == true) {
            if (goodRequest.goods?.isHasCompletePricing() == true) {
                bo.pricingCompleted = true
                bo.sellPrice = goodRequest.goods?.sellPrice
                bo.vipPrice = goodRequest.goods?.vipPrice
            } else {
                bo.pricingCompleted = false
            }
        }
        return bo
    }

    /**
     * 计算订单中每个商品的佣金和ordersPrice中的总佣金
     *
     * @param dinningStyle
     * @param goodsList
     * @param discountCalculationType
     * @param orderPrice
     */
    fun calculateCommissionAmount(
        dinningStyle: Int, goodsList: List<GoodsRequest>,
        discountCalculationType: Int? //, orderPrice: OrderPrice
    ): Pair<BigDecimal, Map<String, BigDecimal>>? {
        // 如果是外卖订单:订单中的菜品需要佣金金额、OrdersPrice表中佣金总金额
        if (dinningStyle >= TakeOutPlatformToDiningHelper.BASE_INDEX) {
            //总佣金
            var totalCommissionAmount = BigDecimal.ZERO
            // 1-可减免金额
//            val beforeDiscountItemAmount: BigDecimal =
//                if (discountCalculationType == WholeDiscountCalculationTypeEnum.WITHOUT_VAT.id
//                    && orderPrice.getTotalWholeDiscountAmount().compareTo(BigDecimal.ZERO) > 0
//                ) {
//                    // 不含vat可减免金额: (商品金额 + 打包费 + 服务费)
//                    orderPrice.getSubTotal()
//                        .add(orderPrice.getTotalPackingAmount())
//                        .max(BigDecimal.ZERO)
//                } else {
//                    // 含vat可减免金额: (商品金额 + 打包费 + 服务费 + vat)
//                    orderPrice.getSubTotal()
//                        .add(orderPrice.getTotalPackingAmount())
//                        .add(orderPrice.getBeforeWholeDiscountVat())
//                        .max(BigDecimal.ZERO)
//                }
//            //2 - 折扣后金额
//            val afterDiscountItemAmount: BigDecimal =
//                beforeDiscountItemAmount.subtract(orderPrice.getTotalWholeDiscountAmount())
//            // 3-折扣率 = (参与折扣的商品税前金额)
//            val finalBeforeDiscountItemAmount: BigDecimal =
//                if (beforeDiscountItemAmount.compareTo(BigDecimal.ZERO) === 0) BigDecimal.ONE else beforeDiscountItemAmount
//            val discountRate: BigDecimal = afterDiscountItemAmount.divide(
//                finalBeforeDiscountItemAmount,
//                10,
//                RoundingMode.HALF_UP
//            )
            val commissionMap = mutableMapOf<String, BigDecimal>()
            val discountRate: BigDecimal = BigDecimal.valueOf(1.0)
            goodsList.forEach(Consumer<GoodsRequest> { orderGoodsDTO: GoodsRequest ->
                val goods = orderGoodsDTO.goods!!
                // 折扣前商品金额0 || 佣金比例 <= 0, 那么佣金金额设置为0
//                if (beforeDiscountItemAmount.compareTo(BigDecimal.ZERO) === 0 || orderGoodsDTO.getCommissionPercentage() == null || orderGoodsDTO.getCommissionPercentage()
//                        .compareTo(BigDecimal.ZERO) <= 0
//                ) {
//                    orderGoodsDTO.setFinalSingleGoodsCommissionAmount(0)
//                    return@forEach
//                }
                val realFinalPrice = BigDecimal.valueOf(orderGoodsDTO.singleDiscountPrice() ?: 0)
                    .divide(BigDecimal.valueOf(100))
                Timber.e("realFinalPrice=> ${realFinalPrice}")
                // 单个商品实收金额：商品实际金额*折扣率
                val finalItemPrice =
                    realFinalPrice.multiply(discountRate).setScale(2, RoundingMode.HALF_UP)
                // 单个商品实收金额VAT：单个商品实收金额*VAT比例
                val finalItemVatPrice =
                    finalItemPrice.multiply(
                        BigDecimal(
                            MainDashboardFragment.CURRENT_USER?.vatPercentage ?: 0
                        ).divide(BigDecimal.valueOf(100.0))
                    )
                        .setScale(2, RoundingMode.HALF_UP)
                Timber.e("finalItemVatPrice=> ${finalItemVatPrice}")
                var finalItemPackingFeePrice: BigDecimal = BigDecimal.ZERO
                var finalItemPackingFeeVatPrice: BigDecimal = BigDecimal.ZERO
                if (goods.getCalculatePackingFee() > 0 && !goods.isPackingFeeDisplay()) {
                    Timber.e("packerPrice ${goods.getCalculatePackingFee()}")
                    //单个商品的实收打包费(打包费>0 && 是独立打包费)：商品实际打包费*折扣率
                    val realFinalPackingFee =
                        BigDecimal.valueOf(goods.getCalculatePackingFee().toLong())
                            .divide(BigDecimal.valueOf(100))
                    Timber.e("realFinalPackingFee=> $realFinalPackingFee")
                    finalItemPackingFeePrice =
                        realFinalPackingFee.multiply(discountRate)
                            .setScale(2, RoundingMode.HALF_UP)
                    //单个商品的实收打包费VAT: 单个商品的实收打包费*VAT比例
                    finalItemPackingFeeVatPrice =
                        finalItemPackingFeePrice.multiply(
                            BigDecimal(
                                MainDashboardFragment.CURRENT_USER?.vatPercentage ?: 0
                            ).divide(BigDecimal.valueOf(100.0))
                        )
                            .setScale(2, RoundingMode.HALF_UP)

                    Timber.e("finalItemPackingFeeVatPrice=> ${finalItemPackingFeeVatPrice}")
                }
                //单商品佣金计算：(单个商品实收金额 + 单个商品实收金额VAT + 单个商品实收打包费 + 单个商品实收打包费VAT) * 佣金比例 * 数量，最后除100 获取最终价格

                Timber.e("finalItemPrice:${finalItemPrice}.  finalItemVatPrice:${finalItemVatPrice}.  finalItemPackingFeePrice:${finalItemPackingFeePrice}.  finalItemPackingFeeVatPrice:${finalItemPackingFeeVatPrice}")
                val finalSingleGoodsCommissionAmount =
                    (finalItemPrice.add(finalItemVatPrice)
                        .add(finalItemPackingFeePrice)
                        .add(finalItemPackingFeeVatPrice))
                        .multiply(goods.getCommissionPercent())
                        .multiply(BigDecimal.valueOf((orderGoodsDTO.num ?: 0).toLong()))
                        .divide(
                            BigDecimal.valueOf(100.0)
                        )
                        .setScale(2, RoundingMode.HALF_UP)

                // 以上算出来是dollor

                Timber.e("${goods.name} 佣金-》 $finalSingleGoodsCommissionAmount")
                commissionMap[orderGoodsDTO.getHash()] =
                    finalSingleGoodsCommissionAmount.multiply(BigDecimal.valueOf(100))
//                orderGoodsDTO.setFinalSingleGoodsCommissionAmount(
//                    CurrencyUtils.convertDollarToCents(
//                        finalSingleGoodsCommissionAmount
//                    )
//                )
                totalCommissionAmount = totalCommissionAmount.plus(
                    commissionMap[orderGoodsDTO.getHash()] ?: BigDecimal.ZERO
                )
                //总佣金累加
//                totalCommissionAmount.updateAndGet(UnaryOperator<BigDecimal> { v: BigDecimal ->
//                    v.add(
//                        finalSingleGoodsCommissionAmount
//                    )
//                })
            })

            //orderPrice保存总佣金
//            orderPrice.setTotalCommissionAmount(totalCommissionAmount.get())
            return Pair(totalCommissionAmount, commissionMap)
        }
        return null
    }


    //订单菜品列表转成购物车菜品列表  Order/Goods
    fun marginOrderGoodsToGoodsList(
        goodsList: ArrayList<OrderedGoods>?, percentage: Int? = null
    ): List<Goods> {
        Timber.e("marginOrderGoodsToGoodsList percentage: $percentage")

        val goodsRequests = goodsList?.map {
            val data = orderGoodsToGoodsRequest(it)
            data.goods?.serviceChargePercentage =
                percentage ?: MainDashboardFragment.STORE_INFO?.getNowServiceChargePercentage()
            data
        } ?: listOf()
        // 按 hashkey 分组并合并相同 hashkey 的商品
        val mergedGoodsRequests = goodsRequests.groupBy { it.getHash() }
            .mapValues { (_, group) ->
                group.reduce { acc, request ->
                    // 累加数量
                    acc.num = (acc.num ?: 0) + (request.num ?: 0)
                    acc
                }
            }.values.toList()

        val discountActivityList = DiscountActivityHelper.calculateAllDiscountAct(
            mergedGoodsRequests,
        )
        for (goodsRequest in mergedGoodsRequests) {
            // 新增：匹配优惠参与数量
            val goodsHash = goodsRequest?.getHash()
            val discountActivity = discountActivityList
                .flatMap { it.getNormalGoodsDiscountInfo() }  // 展开所有优惠商品信息
                .firstOrNull { discountInfo -> discountInfo.hashKey == goodsHash }
            val discountVipActivity = discountActivityList
                .flatMap { it.getVipGoodsDiscountInfo() }  // 展开所有优惠商品信息
                .firstOrNull { discountInfo -> discountInfo.hashKey == goodsHash }
            Timber.e("discountActivityList. ${discountActivityList.toJson()}")
            if (discountActivity != null) {
                goodsRequest.goods?.discountActivityPair =
                    Pair(
                        discountActivity.num,
                        discountActivity.discountAmount.times(BigDecimal(100))
                            .toLong()
                    )
                // 扣减优惠活动参与数量，确保不小于0
                discountActivity.num =
                    (discountActivity.num - (goodsRequest.num
                        ?: 0)).coerceAtLeast(0)
            }
            if (discountVipActivity != null) {
                goodsRequest.goods?.discountActivityVipPair =
                    Pair(
                        discountVipActivity.num,
                        discountVipActivity.discountAmount.times(BigDecimal(100))
                            .toLong()
                    )
                // 扣减会员优惠活动参与数量，确保不小于0
                discountVipActivity.num =
                    (discountVipActivity.num - (goodsRequest.num
                        ?: 0)).coerceAtLeast(0)
            }
            goodsRequest.goods?.totalCount = goodsRequest.num
            goodsRequest.goods?.feedStr = goodsRequest.getGoodsTagStr()
            goodsRequest.goods?.totalServiceCharge =
                goodsRequest.totalDiscountServiceChargePrice()
            goodsRequest.goods?.totalVipServiceCharge =
                goodsRequest.totalVipServiceChargePrice()
            goodsRequest.goods?.totalDiscountServiceCharge =
                goodsRequest.totalDiscountServiceChargePrice()
            goodsRequest.goods?.goodHashCode = goodsRequest.getHash()
            goodsRequest.goods?.orderMealSetGoodsDTOList =
                goodsRequest.orderMealSetGoodList
            goodsRequest.goods?.goodNote = goodsRequest.note
        }

        return mergedGoodsRequests.map { it.goods!! }
    }

}


