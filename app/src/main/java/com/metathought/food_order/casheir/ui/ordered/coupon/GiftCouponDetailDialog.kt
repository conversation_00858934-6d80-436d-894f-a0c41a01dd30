package com.metathought.food_order.casheir.ui.ordered.coupon

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeTier
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeTierCouponTemplate
import com.metathought.food_order.casheir.databinding.DialogGiftCouponDetailBinding
import com.metathought.food_order.casheir.ui.adapter.GiftCouponDetailListAdapter
import dagger.hilt.android.AndroidEntryPoint


/**
 * 赠送优惠券详情
 */
@AndroidEntryPoint
class GiftCouponDetailDialog : DialogFragment() {
    companion object {
        private const val TAG = "CouponDetailDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            rechargeTierCouponTemplateList: List<RechargeTierCouponTemplate>? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(rechargeTierCouponTemplateList)
            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            rechargeTierCouponTemplateList: List<RechargeTierCouponTemplate>? = null,
        ): GiftCouponDetailDialog {
            val fragment = GiftCouponDetailDialog()
            fragment.rechargeTierCouponTemplateList = rechargeTierCouponTemplateList
            return fragment
        }
    }

    private var rechargeTierCouponTemplateList: List<RechargeTierCouponTemplate>? = null
    private var binding: DialogGiftCouponDetailBinding? = null


    private val adapter: GiftCouponDetailListAdapter by lazy {
        GiftCouponDetailListAdapter()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogGiftCouponDetailBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initListener()
    }

    private fun initView() {

    }


    private fun initListener() {
        binding?.apply {
            rvCouponList.adapter = adapter
            adapter.replaceData(rechargeTierCouponTemplateList)
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.7).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}