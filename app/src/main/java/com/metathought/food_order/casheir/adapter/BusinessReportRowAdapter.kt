package com.metathought.food_order.casheir.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.model.BusinessReportItem
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

class BusinessReportRowAdapter : RecyclerView.Adapter<BusinessReportRowAdapter.ViewHolder>() {

    private var dataList: List<BusinessReportItem> = emptyList()
    private var onItemClickListener: ((Int) -> Unit)? = null

    fun setNewInstance(list: List<BusinessReportItem>) {
        dataList = list
        notifyDataSetChanged()
    }

    fun setOnItemClickListener(listener: (Int) -> Unit) {
        onItemClickListener = listener
    }

    fun getItem(position: Int): BusinessReportItem? {
        return if (position in 0 until dataList.size) {
            dataList[position]
        } else {
            null
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_business_report_row, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.bind(item, position)
    }

    override fun getItemCount(): Int = dataList.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvDate: TextView = itemView.findViewById(R.id.tvDate)
        private val tvBalance: TextView = itemView.findViewById(R.id.tvBalance)
        private val tvIncome: TextView = itemView.findViewById(R.id.tvIncome)
        private val tvReserve: TextView = itemView.findViewById(R.id.tvReserve)
        private val tvExpense: TextView = itemView.findViewById(R.id.tvExpense)
        private val tvCreditCustomers: TextView = itemView.findViewById(R.id.tvCreditCustomers)
        private val btnDetail: TextView = itemView.findViewById(R.id.btnDetail)

        fun bind(item: BusinessReportItem, position: Int) {
            tvDate.text = item.businessDate
            tvBalance.text = item.operatingIncome?.priceFormatTwoDigitZero2("$")
            tvIncome.text = item.revenueAmount?.priceFormatTwoDigitZero2()
            var openingCashUSD = item.openingCashUSD?.priceFormatTwoDigitZero2()
            if ((item.openingCashKHR ?: BigDecimal.ZERO) > BigDecimal.ZERO) {
                openingCashUSD = "${
                    openingCashUSD
                } + ៛${item.openingCashKHR?.decimalFormatZeroDigit()}"
            }
            tvReserve.text = openingCashUSD
            var amountPaid = item.amountPaidUSD?.priceFormatTwoDigitZero2()
            if ((item.amountPaidKHR ?: BigDecimal.ZERO) > BigDecimal.ZERO) {
                amountPaid = "${
                    amountPaid
                } + ៛${item.amountPaidKHR?.decimalFormatZeroDigit()}"
            }
            tvExpense.text = amountPaid
            tvCreditCustomers.text = item.creditAmount?.priceFormatTwoDigitZero2()

            btnDetail.setOnClickListener {
                onItemClickListener?.invoke(position)
            }
        }
    }
}