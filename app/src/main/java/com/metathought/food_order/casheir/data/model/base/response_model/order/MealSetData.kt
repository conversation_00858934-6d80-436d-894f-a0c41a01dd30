package com.metathought.food_order.casheir.data.model.base.response_model.order

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PricingMethodEnum
import com.metathought.food_order.casheir.constant.SpecificationTypeEnum
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.helper.FoundationHelper
import timber.log.Timber
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2025/1/6
 *@desc 套餐model
 **/

data class MealSetData(
    //是否展示商品图片
    @SerializedName("showGoodsPic")
    val showGoodsPic: Boolean? = null,

    //子商品下架不影响套餐售卖  如果是false就是受影响
    @SerializedName("removalDoesNotAffectSale")
    val removalDoesNotAffectSale: Boolean? = false,

    //套餐搭配分组
    @SerializedName("mealSetGroupList")
    val mealSetGroupList: List<MealSetGroup>? = null,

    /**
     * 厨房独立打印时，套餐商品合并打印 true是合并 false是分开
     */
    @SerializedName("kitchenMergePrint")
    var kitchenMergePrint: Boolean? = true,
)


data class MealSetGroup(
    /**
     * 套餐分组id
     */
    @SerializedName("id")
    val id: String? = null,

    /**
     *分组名称
     */
    @SerializedName("name")
    val name: String? = null,

//    /**
//     *是否设置必选
//     */
//    @SerializedName("requiredSelect")
//    val requiredSelect: Boolean? = null,

    /**
     *是否可以重复选择
     */
    @SerializedName("repeatSelect")
    val repeatSelect: Boolean? = null,

    /**
     *可选数量类型: 0-按固定值， 1-按区间
     */
    @SerializedName("repeatType")
    val repeatType: Int? = null,

    /**
     *固定值，有值表示是按选择固定值
     */
    @SerializedName("fixedValue")
    val fixedValue: Long? = null,

    /**
     *区间值，从什么值
     */
    @SerializedName("fromValue")
    val fromValue: Long? = null,

    /**
     *区间值，到什么值
     */
    @SerializedName("toValue")
    val toValue: Long? = null,

    /**
     *
     */
    @SerializedName("mealSetGoodsList")
    val mealSetGoodsList: List<MealSetGoods>? = null,

    ) {

    fun isToBeWeighed(): Boolean {
        val isToBeWeighed = mealSetGoodsList?.firstOrNull { it.isToBeWeighed() }
        return isToBeWeighed != null
    }

    /**
     * 这个分组选中的商品是否完成称重
     *
     * @return
     */
    fun isHasCompleteWeight(): Boolean {
        //不是必选的分组
//        if (!isRequiredSelect()) return true
        //没有称重的子商品
        if (!isToBeWeighed()) return true
        //还没有达到最小可选数量
//        if (!isReachMinNum()) return false
        //选中且需要称重的商品
        val weightGoods =
            mealSetGoodsList?.filter { it.selectItems.size > 0 && it.isToBeWeighed() }
        if (weightGoods.isNullOrEmpty()) {
            //选中的都没有需要称重的
            return true
        }
        //选中需要称重的商品中有没有为称重的
        return weightGoods.firstOrNull { !it.isHasCompleteWeight() } == null
    }

    /**
     * 是否必选
     *
     */
    fun isRequiredSelect(): Boolean {
        //固定套餐 必选
        if (repeatType == 0) {
            return true
        } else {
            //区间套餐 如果 开始0表示可以不选
            if (fromValue == 0L) {
                return false
            } else {
                return true
            }
        }
    }


    /**
     * 获取已选描述
     *
     */
    fun getHasSelectStr(context: Context): String {
        var range = ""
        val selectNum = getSelectNum()
        if (repeatType == 0) {
            range = "${fixedValue ?: 0L}"
            return context.getString(
                R.string.meal_set_selected_content_str,
                "${mealSetGoodsList?.size ?: 0}",
                "$range", "$selectNum"
            )
        } else if (repeatType == 1) {
            range = "${fromValue}-${toValue}"
            return context.getString(
                R.string.meal_set_selected_content_str2,
                "$range", "$selectNum"
            )
        }

        return ""


    }

    fun getSelectNum(): Long {
        val selectNum = (mealSetGoodsList ?: listOf()).fold(0L) { acc, element ->
            acc + element.getSelectNum()
        }
        return selectNum
    }

    /**
     * 判断是否选到最大数量
     *
     */
    fun isMaxNum(): Boolean {
        val num = getSelectNum()
        Timber.e("num: $num   fixedValue:${fixedValue}")
        return num >= enableSelectMaxNum()
    }

    /**
     * 最大可选数量
     *
     * @return
     */
    fun enableSelectMaxNum(): Long {
        if (repeatType == 0) {
            return (fixedValue ?: 0)
        }
        if (repeatType == 1) {
            return (toValue ?: 0)
        }
        return 0
    }

    /**
     * 最小可选数量
     *
     * @return
     */
    fun enableSelectMinNum(): Long {
        if (repeatType == 0) {
            return (fixedValue ?: 0)
        }
        if (repeatType == 1) {
            return (fromValue ?: 0)
        }
        return 0
    }

    /**
     * 是否达到最小可选数字
     *
     */
    fun isReachMinNum(): Boolean {
        val num = getSelectNum()
        Timber.e("num: $num")
        return num >= enableSelectMinNum()
    }


}


data class MealSetGoods(

    /**
     *商品id
     */
    @SerializedName("goodsId")
    val goodsId: String? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("picUrl")
    val picUrl: String? = null,

    /**
     *加价:单位分，在原价格基础上加价
     */
    @SerializedName("priceMarkup")
    val priceMarkup: Long? = null,

    /**
     *数量-套餐内某个商品设置的数量，例如用户选择这个商品，可得到汉堡X2
     */
    @SerializedName("num")
    var num: Int? = null,

    /**
     *是否用户自选规格， 开启：用户自选，关闭：商户配置选
     */
    @SerializedName("optionalSpec")
    var optionalSpec: Boolean? = null,

    /**
     *规格ids, 逗号隔开(商户配置选才有这个值)
     */
    @SerializedName("tagItemIds")
    var tagItemIds: String? = null,

    @SerializedName("tags")
    var tags: List<GoodsTag>? = null,

//    //单品详情用这个字段返的
//    @SerializedName("tagItemDetailList")
//    var tagItemDetailList: List<GoodsTag>? = null,

    @SerializedName("soldOut")
    var soldOut: Boolean? = null,

    //1下架
    @SerializedName("status")
    var status: Int? = null,

    //这个商品 不同规格以及数量 本地的
    @SerializedName("selectItems")
    var selectItems: MutableList<MealSetChooseItem> = mutableListOf(),

    /**
     * 计价方式
     * WHOLE_UNIT(0, "整份计费", ""),
     * PER_KILOGRAM(1, "每公斤", "KG"),
     * PER_POUND(2, "每磅", "LB"),
     * PER_LITER(3, "每升", "L"),
     * PER_OUNCE(4, "每盎司", "OZ"),
     * PER_GALLON(5, "每加仑", "GAL"),
     * PER_GRAM(6, "每克", "G");
     */
    @SerializedName("pricingMethod")
    val pricingMethod: Int? = null,


    /**
     * 重量
     */
    var weight: Double? = null,

    /**
     * 是否称重完成true是false否
     */
    var weighingCompleted: Boolean? = false,
) {

    /**
     * 是否称重菜
     *
     * @return
     */
    fun isToBeWeighed(): Boolean {
        return (pricingMethod ?: 0) > PricingMethodEnum.WHOLE_UNIT.id
    }

    /**
     * 是否已经设置重量
     *
     * @return
     */
    fun isHasCompleteWeight(): Boolean {
        if (weighingCompleted == null) {
            return ((weight ?: 0.0) > 0.0)
        }
        return weighingCompleted ?: false

    }

    fun getWeightUnit(): String {
        return PricingMethodEnum.entries.find {
            it.id == (pricingMethod ?: 0)
        }?.unit ?: ""
    }

    //获取称重后 重量加单位  描述
    fun getWeightStr(): String {
        if (weight?.isInt() == true) {
            return "${weight?.toInt()}${getWeightUnit()}"
        }
        return "${weight ?: 0}${getWeightUnit()}"
    }

    fun isSoldOutOrTakeDown(removalDoesNotAffectSal: Boolean?): Boolean {
        //如果是外卖平台 只要判断是否售罄
        if (FoundationHelper.isTakeOut) {
            return soldOut == true
        }
        //子商品下架了是否影响套餐售卖  true 不影响
        if (removalDoesNotAffectSal == true) {
            return soldOut == true
        }
        return status == 1 || soldOut == true
    }

    fun clearCheck() {
        tags?.forEach { tag ->
            tag.goodsTagItems?.forEach { goodsTagItem ->
                goodsTagItem.isCheck = false
            }
        }
    }


    /**
     * 过滤掉 数量未0的 规格
     *
     */
    fun filterEmptyNum() {
        val list = selectItems.filter { it.selectNum != 0 }
        clearSelect()
        selectItems.addAll(list)
    }

    fun clearSelect() {
        tags?.forEach { it.goodsTagItems?.forEach { it.isCheck = false } }
        selectItems.clear()
        weight = null
        weighingCompleted = false
    }

    /**
     * 获取规格描述
     *
     * @return
     */
    fun getGoodsTagStr(): String {
        val sbf = StringBuffer()
        if (optionalSpec == false) {
            //商户固定规格
            val tagIds = (tagItemIds ?: "").split(",")
            tags?.forEach { tag ->
                tag.goodsTagItems?.forEach { goodsTagItem ->
                    if (tagIds.contains(goodsTagItem.id)) {
                        var str = ""
                        when (tag.type) {
                            SpecificationTypeEnum.FEATURE.id -> {
                                str = goodsTagItem.name ?: ""
                            }

                            SpecificationTypeEnum.SPECIFICATION.id,
                            SpecificationTypeEnum.INGREDIENT.id -> {
                                if ((goodsTagItem.price ?: 0.0) > 0) {
//                                    str =
//                                        "${goodsTagItem.name ?: ""} +$${goodsTagItem.price?.priceDecimalFormatTwoDigitZero()}"
                                    if (FoundationHelper.isKrh) {
                                        str =
                                            "${goodsTagItem.name ?: ""} +${
                                                FoundationHelper.getPriceStrByUnit(
                                                    FoundationHelper.useConversionRatio,
                                                    BigDecimal(goodsTagItem.price ?: 0.0).toLong(),
                                                    FoundationHelper.isKrh
                                                )
                                            }"
                                    } else {
                                        str =
                                            "${goodsTagItem.name ?: ""} +$${goodsTagItem.price?.priceDecimalFormatTwoDigitZero()}"
                                    }
                                } else {
                                    str = goodsTagItem.name ?: ""
                                }
                            }
                        }
                        if (sbf.isEmpty()) {
                            sbf.append(str)
                        } else {
                            sbf.append(",${str}")
                        }
                    }
                }
            }
        } else {
            //用户自选规格
            var str = ""
            selectItems.forEach {
                it.selectTag.forEach { goodsTagItem ->
                    when (goodsTagItem.type) {
                        SpecificationTypeEnum.FEATURE.id -> {
                            str = goodsTagItem.name ?: ""
                        }

                        SpecificationTypeEnum.SPECIFICATION.id,
                        SpecificationTypeEnum.INGREDIENT.id -> {
                            if ((goodsTagItem.price ?: 0.0) > 0) {
//                                str =
//                                    "${goodsTagItem.name ?: ""} +$${goodsTagItem.price?.priceDecimalFormatTwoDigitZero()}"
                                if (FoundationHelper.isKrh) {
                                    str =
                                        "${goodsTagItem.name ?: ""} +${
                                            FoundationHelper.getPriceStrByUnit(
                                                FoundationHelper.useConversionRatio,
                                                BigDecimal(goodsTagItem.price ?: 0.0).toLong(),
                                                FoundationHelper.isKrh
                                            )
                                        }"
                                } else {
                                    str =
                                        "${goodsTagItem.name ?: ""} +$${goodsTagItem.price?.priceDecimalFormatTwoDigitZero()}"
                                }
                            } else {
                                str = goodsTagItem.name ?: ""
                            }

                        }
                    }
                    if (sbf.isEmpty()) {
                        sbf.append(str)
                    } else {
                        sbf.append(",${str}")
                    }
                }
            }
        }
        return sbf.toString()
    }

    /**
     * 点击商家固定tag
     *
     */
    fun selectFixedTag(calculate: String, goodsNum: Int? = 1) {
        val tagIds = (tagItemIds ?: "").split(",")
        val selectTag = mutableListOf<GoodsTagItem>()
        tags?.forEach { tag ->
            tag.goodsTagItems?.forEach { goodsTagItem ->
                if (tagIds.contains(goodsTagItem.id)) {
                    goodsTagItem.type = tag.type
                    selectTag.add(goodsTagItem)
                }
            }
        }

        val hash =
            HashHelper.getHash(null, ArrayList(selectTag), null, goodsId!!, null, "", "", null)
        val index = selectItems.indexOfFirst {
            hash == HashHelper.getHash(
                null,
                ArrayList(it.selectTag),
                null,
                goodsId,
                null,
                "",
                "",
                null
            )
        }
        if (index != -1) {
            if (calculate == "reduce") {
                //减
                selectItems[index].selectNum -= 1
                if (selectItems[index].selectNum <= 0) {
                    selectItems.removeAt(index)
                }
            } else if (calculate == "modify") {
                selectItems[index].selectNum = (goodsNum ?: 1)
                if (goodsNum == 0) {
                    selectItems.removeAt(index)
                }
            } else if (calculate == "add") {
                selectItems[index].selectNum += (goodsNum ?: 1)
            }

        } else {
            if (calculate == "reduce") {

            } else if (calculate == "modify" || calculate == "add") {
                selectItems.add(MealSetChooseItem(selectNum = goodsNum ?: 1, selectTag = selectTag))
            }
        }
    }

    /**
     * 选择用户
     *
     * @param isCanRepeat 是否可重复选
     */
    fun selectCustomTag(
        calculate: String,
        selectTag: MutableList<GoodsTagItem>,
        goodsNum: Int? = 1,
    ) {

        val hash =
            HashHelper.getHash(null, ArrayList(selectTag), null, goodsId!!, null, "", "", null)
        val index = selectItems.indexOfFirst {
            hash == HashHelper.getHash(
                null,
                ArrayList(it.selectTag),
                null,
                goodsId,
                null,
                "",
                "",
                null
            )
        }

        if (index != -1) {
            if (calculate == "reduce") {
                //减
                selectItems[index].selectNum -= 1
                if (selectItems[index].selectNum <= 0) {
                    selectItems.removeAt(index)
                }
            } else if (calculate == "modify") {
                selectItems[index].selectNum = (goodsNum ?: 1)
                if (goodsNum == 0) {
                    selectItems.removeAt(index)
                }
            } else if (calculate == "add") {
                selectItems[index].selectNum += (goodsNum ?: 1)
            }
        } else {
            if (calculate == "reduce") {

            } else if (calculate == "modify" || calculate == "add") {
                selectItems.add(MealSetChooseItem(selectNum = goodsNum ?: 1, selectTag = selectTag))
            }
        }
    }


    fun isSelectItem(): Boolean {
        var count = getSelectNum()
        return count > 0
    }

    fun getSelectNum(): Int {
        var count = 0
        selectItems.forEach {
            count += it.selectNum
        }
        return count

    }
}


/**
 * 套餐内 每个菜品选择的规格  和数量
 *
 * @property selectNum
 * @property selectTag
 * @constructor Create empty Meal set choose item
 */
data class MealSetChooseItem(
    @SerializedName("selectNum")
    var selectNum: Int = 0,
    @SerializedName("selectTag")
    var selectTag: MutableList<GoodsTagItem> = mutableListOf(),
) {
    /**
     * 获取规格描述
     *
     * @return
     */
    fun getGoodsTagStr(): String {
        val sbf = StringBuffer()
        selectTag.forEach { goodsTagItem ->
            var str = ""
            when (goodsTagItem.type) {
                SpecificationTypeEnum.FEATURE.id -> {
                    str = goodsTagItem.name ?: ""
                }

                SpecificationTypeEnum.SPECIFICATION.id,
                SpecificationTypeEnum.INGREDIENT.id -> {
                    if ((goodsTagItem.price ?: 0.0) > 0) {
                        if (FoundationHelper.isKrh) {
                            str =
                                "${goodsTagItem.name ?: ""} +${
                                    FoundationHelper.getPriceStrByUnit(
                                        FoundationHelper.useConversionRatio,
                                        BigDecimal(goodsTagItem.price ?: 0.0).toLong(),
                                        FoundationHelper.isKrh
                                    )
                                }"
                        } else {
                            str =
                                "${goodsTagItem.name ?: ""} +$${goodsTagItem.price?.priceDecimalFormatTwoDigitZero()}"
                        }
                    } else {
                        str = goodsTagItem.name ?: ""
                    }
                }
            }
            if (sbf.isEmpty()) {
                sbf.append(str)
            } else {
                sbf.append(",${str}")
            }

        }
        return sbf.toString()
    }

}