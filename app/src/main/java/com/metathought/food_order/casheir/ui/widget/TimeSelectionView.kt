package com.metathought.food_order.casheir.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.fragment.app.FragmentManager

import com.wdullaer.materialdatetimepicker.date.DatePickerDialog
import com.wdullaer.materialdatetimepicker.time.TimePickerDialog
import com.kunzisoft.switchdatetime.SwitchDateTimeDialogFragment
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.databinding.DialogFilterTimeSlotBinding
import com.metathought.food_order.casheir.model.QuickTimeItem
import com.metathought.food_order.casheir.databinding.LayoutTimeSelectionBinding
import com.metathought.food_order.casheir.ui.adapter.QuickTimeAdapter
import com.metathought.food_order.casheir.utils.SingleClickUtils
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 时间选择组件
 * 包含快捷时间选择和日历时间选择功能
 */
class TimeSelectionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val binding: LayoutTimeSelectionBinding

    // 弹窗相关
    private var quickTimePopupWindow: PopupWindow? = null

    // 选择状态
    private var selectedQuickTime: QuickTimeItem? = null
    private var startDate: Date? = null
    private var endDate: Date? = null

    private var defaultQuickTime: QuickTimeItem? = null

    // 快捷时间数据源
    private var quickTimeList: List<QuickTimeItem> = emptyList()

    /**
     * 快捷时间选择监听器
     */
    private var onQuickTimeSelectedListener: ((Int) -> Unit)? = null

    /**
     * 日期选择监听器
     */
    private var onDateSelectedListener: ((Date?, Date?) -> Unit)? = null

    /**
     * Toast 显示监听器
     */
    private var onShowToastListener: ((String) -> Unit)? = null

    /**
     * FragmentManager 用于显示日期选择器
     */
    private var fragmentManager: FragmentManager? = null

    /**
     * 日期范围校验器
     */
    private var dateRangeValidator: ((Date, Date) -> String?)? = null

    /**
     * 日期选择器是否正在显示
     */
    private var isDatePickerShowing = false

    /**
     * 时间选择模式：true为包含时间，false为仅日期
     */
    private var includeTime = true

    init {
        binding = LayoutTimeSelectionBinding.inflate(LayoutInflater.from(context), this, true)
        initListener()
    }

    private fun initListener() {
        binding.apply {
            // 快捷时间选择点击事件
            dropdownQuickTime.setOnClickListener {
                showQuickTimePopupWindow(it)
            }

            // 日历时间选择点击事件
            tvCalendar.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    datePickerDialog()
                }
            }


        }
    }



    /**
     * 设置快捷时间选择监听器
     */
    fun setOnQuickTimeSelectedListener(listener: (Int) -> Unit) {
        this.onQuickTimeSelectedListener = listener
    }

    /**
     * 设置日期选择监听器
     */
    fun setOnDateSelectedListener(listener: (Date?, Date?) -> Unit) {
        this.onDateSelectedListener = listener
    }

    /**
     * 设置 Toast 显示监听器
     */
    fun setOnShowToastListener(listener: (String) -> Unit) {
        this.onShowToastListener = listener
    }

    /**
     * 设置 FragmentManager
     */
    fun setFragmentManager(fragmentManager: FragmentManager) {
        this.fragmentManager = fragmentManager
    }

    /**
     * 设置快捷时间数据源
     */
    fun setQuickTimeList(quickTimeList: List<QuickTimeItem>, defaultId: Int? = null) {
        this.quickTimeList = quickTimeList
        if (defaultId != null) {
            defaultQuickTime = quickTimeList.firstOrNull { it.type == defaultId }
        } else {
            defaultQuickTime = quickTimeList.firstOrNull()
        }
        selectedQuickTime = defaultQuickTime
        binding.tvQuickTime.text = selectedQuickTime?.displayName
    }


    /**
     * 设置日期范围校验器
     * @param validator 校验函数，返回错误信息，null表示校验通过
     */
    fun setDateRangeValidator(validator: (Date, Date) -> String?) {
        this.dateRangeValidator = validator
    }

    /**
     * 获取选中的快捷时间
     */
    fun getSelectedQuickTime(): QuickTimeItem? {
        return selectedQuickTime
    }

    /**
     * 获取开始日期
     */
    fun getStartDate(): Date? {
        return startDate
    }

    /**
     * 获取结束日期
     */
    fun getEndDate(): Date? {
        return endDate
    }

    /**
     * 设置快捷时间文本
     */
    fun setQuickTimeText(text: String) {
        binding.tvQuickTime.text = text
    }

    /**
     * 获取快捷时间文本
     */
    fun getQuickTimeText(): String {
        return binding.tvQuickTime.text.toString()
    }

    /**
     * 设置日历时间文本
     */
    fun setCalendarTimeText(text: String) {
        binding.tvCalendar.text = text
    }

    /**
     * 获取日历时间文本
     */
    fun getCalendarTimeText(): String {
        return binding.tvCalendar.text.toString()
    }

    /**
     * 设置时间选择模式
     * @param includeTime true为包含时间，false为仅日期
     */
    fun setIncludeTimeMode(includeTime: Boolean) {
        this.includeTime = includeTime
    }

    /**
     * 获取当前时间选择模式
     * @return true为包含时间，false为仅日期
     */
    fun isIncludeTimeMode(): Boolean {
        return includeTime
    }

    /**
     * 重置时间选择
     */
    fun resetTimeSelection() {
        clearQuickTimeSelection()
        clearDateSelection()

        selectedQuickTime = defaultQuickTime
        binding.tvQuickTime.text =
            selectedQuickTime?.displayName
    }

    /**
     * 清除快捷时间选择
     */
    private fun clearQuickTimeSelection() {
        selectedQuickTime = null
        binding.tvQuickTime.text = context.getString(R.string.customize)
    }

    /**
     * 清除日期选择
     */
    private fun clearDateSelection() {
        binding.tvCalendar.text = ""
        startDate = null
        endDate = null
        binding.tvCalendar.updateCalendarColor()
    }

    /**
     * 显示快捷时间选择弹窗
     */
    private fun showQuickTimePopupWindow(anchorView: View) {
        val binding = DialogFilterTimeSlotBinding.inflate(LayoutInflater.from(context))
        val quickTimeAdapter = QuickTimeAdapter(
            quickTimeList,
            selectedQuickTime,
            context
        )
        quickTimeAdapter.onItemClickCallback = { quickTimeItem ->
            selectedQuickTime = quickTimeItem
            this.binding.tvQuickTime.text = quickTimeItem.displayName
            // 选择快捷时间时清除日期选择
            clearDateSelection()
            onQuickTimeSelectedListener?.invoke(quickTimeItem.type)
            quickTimePopupWindow?.dismiss()
        }
        binding.recyclerViewTable.adapter = quickTimeAdapter

        quickTimePopupWindow = PopupWindow(
            binding.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        // 设置弹窗样式
        quickTimePopupWindow?.elevation = 20f
        quickTimePopupWindow?.animationStyle = R.style.PopupAnimation

        // 显示弹窗时设置背景
        anchorView.setBackgroundResource(R.drawable.background_spinner_top)
        quickTimePopupWindow?.showAsDropDown(anchorView)

        // 设置弹窗关闭监听器
        quickTimePopupWindow?.setOnDismissListener {
            // 弹窗关闭时恢复原背景
            anchorView.setBackgroundResource(R.drawable.background_white_border_black12_radius_100)
        }
    }


    /**
     * 显示日期时间选择对话框
     */
    @SuppressLint("SetTextI18n")
    private fun datePickerDialog() {
        // 防止重复打开
        if (isDatePickerShowing) {
            return
        }

        val fragmentManager = this.fragmentManager
        if (fragmentManager == null) {
            onShowToastListener?.invoke("FragmentManager 未设置")
            return
        }

        isDatePickerShowing = true

        // 根据时间模式选择不同的选择器
        if (includeTime) {
            // 显示开始时间选择（包含时间）
            showStartDateTimeSelection(fragmentManager)
        } else {
            // 显示开始日期选择（仅日期）
            showDateOnlyStartSelection(fragmentManager)
        }
    }

    /**
     * 显示开始时间选择
     */
    private fun showStartDateTimeSelection(fragmentManager: FragmentManager) {
        val startDateTimeDialog = SwitchDateTimeDialogFragment.newInstance(
            "选择开始时间",
            "确定",
            "取消"
        )
        startDateTimeDialog.setMinimumDateTime(null)
        startDateTimeDialog.setMaximumDateTime(Date())

        // 设置24小时制
        startDateTimeDialog.set24HoursMode(true)
        
        // 设置更大的对话框尺寸
        startDateTimeDialog.setMinimumDateTime(null)
        startDateTimeDialog.setMaximumDateTime(Date())
        
        // 设置日期时间选择器样式，使其更大
        startDateTimeDialog.setTimeZone(null)

        // 如果有已选择的开始时间，设置为默认值
        startDate?.let {
            startDateTimeDialog.setDefaultDateTime(it)
        }

        startDateTimeDialog.setOnButtonClickListener(object : SwitchDateTimeDialogFragment.OnButtonClickListener {
            override fun onPositiveButtonClick(date: Date?) {
                date?.let { selectedStartDate ->
                    // 临时保存开始时间
                    val tempStartDate = selectedStartDate

                    // 显示结束时间选择
                    showEndDateTimeSelection(fragmentManager, tempStartDate)
                }
                isDatePickerShowing = false
            }

            override fun onNegativeButtonClick(date: Date?) {
                isDatePickerShowing = false
            }
        })

        startDateTimeDialog.show(fragmentManager, "start_datetime_picker")
    }

    /**
     * 显示结束时间选择
     */
    private fun showEndDateTimeSelection(fragmentManager: FragmentManager, selectedStartDate: Date) {
        val endDateTimeDialog = SwitchDateTimeDialogFragment.newInstance(
            "选择结束时间",
            "确定",
            "取消"
        )

        // 设置24小时制
        endDateTimeDialog.set24HoursMode(true)
        
        // 设置时间范围
        endDateTimeDialog.setMinimumDateTime(selectedStartDate)
        endDateTimeDialog.setMaximumDateTime(Date())
        
        // 设置日期时间选择器样式，使其更大
        endDateTimeDialog.setTimeZone(null)

        // 如果有已选择的结束时间，设置为默认值，否则设置为开始时间
        val defaultEndDate = endDate ?: selectedStartDate
        endDateTimeDialog.setDefaultDateTime(defaultEndDate)

        endDateTimeDialog.setOnButtonClickListener(object : SwitchDateTimeDialogFragment.OnButtonClickListener {
            override fun onPositiveButtonClick(date: Date?) {
                date?.let { selectedEndDate ->
                    // 执行日期范围校验
                    val validationError = dateRangeValidator?.invoke(selectedStartDate, selectedEndDate)
                    if (validationError != null) {
                        onShowToastListener?.invoke(validationError)
                        return
                    }

                    // 保存选择的时间范围
                    startDate = selectedStartDate
                    endDate = selectedEndDate

                    // 清除快捷选择
                    clearQuickTimeSelection()

                    // 格式化显示时间
                    val dateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                    val startDateStr = startDate?.let { dateTimeFormat.format(it) } ?: ""
                    val endDateStr = endDate?.let { dateTimeFormat.format(it) } ?: ""

                    binding.tvCalendar.text = "$startDateStr - $endDateStr"
                    binding.tvCalendar.updateCalendarColor()
                    onDateSelectedListener?.invoke(startDate, endDate)
                }
            }

            override fun onNegativeButtonClick(date: Date?) {
                // 用户取消了结束时间选择，重新显示开始时间选择
                showStartDateTimeSelection(fragmentManager)
            }
        })

        endDateTimeDialog.show(fragmentManager, "end_datetime_picker")
    }

    /**
     * 显示仅日期范围选择（不包含时间）
     */
    fun showDateOnlyRangePicker() {
        val fragmentManager = this.fragmentManager
        if (fragmentManager == null) {
            onShowToastListener?.invoke("FragmentManager 未设置")
            return
        }

        showDateOnlyStartSelection(fragmentManager)
    }

    /**
     * 显示开始日期选择（仅日期）
     */
    private fun showDateOnlyStartSelection(fragmentManager: FragmentManager) {
        val calendar = java.util.Calendar.getInstance()

        val startDatePicker = DatePickerDialog.newInstance(
            { _, year, monthOfYear, dayOfMonth ->
                val selectedStartCalendar = java.util.Calendar.getInstance()
                selectedStartCalendar.set(year, monthOfYear, dayOfMonth, 0, 0, 0)
                selectedStartCalendar.set(java.util.Calendar.MILLISECOND, 0)

                // 显示结束日期选择
                showDateOnlyEndSelection(fragmentManager, selectedStartCalendar.time)
            },
            calendar.get(java.util.Calendar.YEAR),
            calendar.get(java.util.Calendar.MONTH),
            calendar.get(java.util.Calendar.DAY_OF_MONTH)
        )

        // 设置最大日期为今天
        startDatePicker.maxDate = calendar
        
        // 设置更大的日期选择器
        startDatePicker.setThemeDark(false)
        startDatePicker.vibrate(true)
        startDatePicker.dismissOnPause(true)
        startDatePicker.showYearPickerFirst(false)
//        startDatePicker.title = "选择开始日期"

        startDatePicker.show(fragmentManager, "start_date_picker")
    }

    /**
     * 显示结束日期选择（仅日期）
     */
    private fun showDateOnlyEndSelection(fragmentManager: FragmentManager, selectedStartDate: Date) {
        val calendar = java.util.Calendar.getInstance()
        val startCalendar = java.util.Calendar.getInstance()
        startCalendar.time = selectedStartDate

        val endDatePicker = DatePickerDialog.newInstance(
            { _, year, monthOfYear, dayOfMonth ->
                val selectedEndCalendar = java.util.Calendar.getInstance()
                selectedEndCalendar.set(year, monthOfYear, dayOfMonth, 23, 59, 59)
                selectedEndCalendar.set(java.util.Calendar.MILLISECOND, 999)

                val selectedEndDate = selectedEndCalendar.time

                // 执行日期范围校验
                val validationError = dateRangeValidator?.invoke(selectedStartDate, selectedEndDate)
                if (validationError != null) {
                    onShowToastListener?.invoke(validationError)
                    return@newInstance
                }

                // 保存选择的日期范围
                startDate = selectedStartDate
                endDate = selectedEndDate

                // 清除快捷选择
                clearQuickTimeSelection()

                // 格式化显示日期
                val dateFormat = SimpleDateFormat(FORMAT_DATE, Locale.getDefault())
                val startDateStr = startDate?.let { dateFormat.format(it) } ?: ""
                val endDateStr = endDate?.let { dateFormat.format(it) } ?: ""

                binding.tvCalendar.text = "$startDateStr - $endDateStr"
                binding.tvCalendar.updateCalendarColor()
                onDateSelectedListener?.invoke(startDate, endDate)
            },
            calendar.get(java.util.Calendar.YEAR),
            calendar.get(java.util.Calendar.MONTH),
            calendar.get(java.util.Calendar.DAY_OF_MONTH)
        )

        // 设置最小日期为开始日期
        endDatePicker.minDate = startCalendar
        // 设置最大日期为今天
        endDatePicker.maxDate = calendar
        
        // 设置更大的日期选择器
        endDatePicker.setThemeDark(false)
        endDatePicker.vibrate(true)
        endDatePicker.dismissOnPause(true)
        endDatePicker.showYearPickerFirst(false)
//        endDatePicker.title = "选择结束日期"

        endDatePicker.show(fragmentManager, "end_date_picker")
    }

}