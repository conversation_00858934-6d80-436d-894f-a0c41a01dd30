package com.metathought.food_order.casheir.data.model.base.response_model.ordered


import com.google.gson.annotations.SerializedName

data class OrderedTableListItem(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("location")
    val location: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("storeId")
    val storeId: String? = null,
    @SerializedName("type")
    val type: Int? = null,
    @SerializedName("uuid")
    val uuid: String? = null,
    @Transient
    var ischeck: Boolean = false
)