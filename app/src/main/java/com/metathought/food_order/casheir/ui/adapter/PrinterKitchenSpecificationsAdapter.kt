package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterFeedBinding
import com.metathought.food_order.casheir.databinding.ItemPrinterKitchenFeedBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2024/08/21 14:31
 * @description  厨打小票按80 样式打
 */
class PrinterKitchenSpecificationsAdapter(
    val list: List<GoodsTagItem>,
    val orderedGoods: OrderedGoods?,
    val templateItem: PrintTamplateResponseItem
) : RecyclerView.Adapter<PrinterKitchenSpecificationsAdapter.PrinterFeedViewHolder>() {


    inner class PrinterFeedViewHolder(val binding: ItemPrinterKitchenFeedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodsTagItem) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
                localeList.forEachIndexed { index, locale ->
                    val name = orderedGoods?.getLocaleSingleGoodsTagStr(locale, resource) ?: ""
                    if (index == 0) {
                        tvFeedNameEn.text = "$name"
                        tvFeedNameEn.isVisible = !name.isNullOrEmpty()
                    } else if (index == 1) {
                        tvFeedNameKm.text = "$name"
                        tvFeedNameKm.isGone =
                            tvFeedNameKm.text == tvFeedNameEn.text || name.isNullOrEmpty()
                    }
                }

                tvFeedNameKm.isVisible =
                    !tvFeedNameKm.text.isNullOrEmpty() && tvFeedNameEn.text != tvFeedNameKm.text

//                tvFeedFoodCount.text = ""
                llFeedFoodCount.isVisible = false

                if (templateItem.informationShow != null) {
                    tvFeedNameEn.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                    tvFeedNameKm.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                    tvFeedFoodCount.textSize =
                        templateItem.informationShow.getProductNumFontRealSize(itemView.context, isEightyWidth = true)
                }
            }
        }


    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterKitchenFeedBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterKitchenSpecificationsAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}