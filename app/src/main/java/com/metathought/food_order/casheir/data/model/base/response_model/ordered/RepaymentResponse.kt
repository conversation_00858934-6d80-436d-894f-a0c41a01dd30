package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.PayInfo
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.doubleStrToIntStr
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4
import com.metathought.food_order.casheir.helper.FoundationHelper
import java.math.BigDecimal
import java.util.Locale


data class WsRepaymentResponse(
    @SerializedName("repaymentResult")
    val repaymentResult: RepaymentResponse? = null
)

data class RepaymentResponse(
    @SerializedName("consumerId")
    val consumerId: String?,
    @SerializedName("repaymentDate")
    var repaymentDate: String? = null,    // 还款时间
    @SerializedName("nickName")
    val nickName: String?,
    @SerializedName("telephone")
    var telephone: String?, //账户(电话)
    @SerializedName("amount")
    val amount: BigDecimal?,
    @SerializedName("balancePayAmount")
    val balancePayAmount: BigDecimal?,  // 余额支付金额
    @SerializedName("offlinePayAmount")
    val offlinePayAmount: BigDecimal?,   //组合支付 线下支付金额
    @SerializedName("payType")
    val payType: Int,
    @SerializedName("offlinePaymentChannelsId")
    val offlinePaymentChannelsId: String?, //线下支付渠道ID
    @SerializedName("offlinePaymentChannelsName")
    var offlinePaymentChannelsName: String? = null,    // 线下支付渠道名称
    @SerializedName("orderNum")
    val orderNum: Int?,
    @SerializedName("orderDetailList")
    var orderDetailList: List<CreditOrderDetail>? = null,
    @SerializedName("khqrCode")
    var khqrCode: KhqrCode? = null,
    @SerializedName("gatewayMap")
    var gatewayMap: GatewayMap? = null,

    @SerializedName("conversionRatio")
    val conversionRatio: Long? = null,
    @SerializedName("collectCash")
    val collectCash: BigDecimal? = null,  //收取现金 现金支付时使用,瑞尔
    @SerializedName("changeAmount")
    val changeAmount: BigDecimal? = null, //找零金额 现金支付时使用，瑞尔
    @SerializedName("collectCashDollar")
    val collectCashDollar: BigDecimal? = null,  //收取现金 现金支付时使用,美元
    @SerializedName("changeAmountDollar")
    val changeAmountDollar: BigDecimal? = null,  //找零金额 现金支付时使用，美元
    @SerializedName("surplusStoreBalance")
    var surplusStoreBalance: BigDecimal? = null,

    //混合支付
    @SerializedName("combinedPayInfoList")
    var combinedPayInfoList: List<PayInfo>? = null,
) {


    /**
     * 获取现金收款的金额
     *
     * @return
     */
    fun getReceiveAmountToTicket(): String {
        return if (collectCashDollar != null && collectCashDollar > BigDecimal.ZERO
            && collectCash != null && collectCash > BigDecimal.ZERO
        ) {
            "$${collectCashDollar.priceFormatTwoDigitZero3()} + ៛${collectCash.decimalFormatZeroDigit()}"
        } else if (collectCashDollar != null && collectCashDollar > BigDecimal.ZERO) {
            "$${collectCashDollar.priceFormatTwoDigitZero3()}"
        } else if (collectCash != null && collectCash > BigDecimal.ZERO) {
            "៛${collectCash.decimalFormatZeroDigit()}"
        } else {
            "$0.00"
        }
    }

    /**
     * 获取找零的金额
     *
     * @return
     */
    fun getChangeAmountToTicket(): String {
        if (!combinedPayInfoList.isNullOrEmpty()) {
            //如果是组合支付
            combinedPayInfoList?.forEach { payInfo ->
                if (payInfo.isCash()) {
                    if (payInfo.offlinePayment?.changeAmountDollar?.isZero() == true) {
                        return "$0.00"
                    }
                    val result =
                        "${payInfo.offlinePayment?.changeAmountDollar?.priceFormatTwoDigitZero4()} = ${
                            getChangeTotalKhrToTicket(
                                (payInfo.offlinePayment?.changeAmountDollar ?: BigDecimal.ZERO).toLong()
                            )
                        }"
                    return result
                }
            }
            return "$0.00"
        } else {
            var result = ""
            if (changeAmountDollar == BigDecimal.ZERO) {
                return "$0.00"
            }
            result =
                "$${changeAmountDollar?.priceFormatTwoDigitZero3()} = ${getChangeTotalKhrToTicket((changeAmountDollar ?: BigDecimal.ZERO).toLong())}"
            return result
        }
    }


    //找零美元转成khr 总计
    private fun getChangeTotalKhrToTicket(changeAmountDollar: Long): String {
        return "៛${
            FoundationHelper.usdConverToKhr(
                (conversionRatio ?: 0),
                (changeAmountDollar ?: 0)
            ).decimalFormatZeroDigit()
        }"
    }

    fun getPaymentMethod(context: Context, locale: Locale): String {
        return when (payType) {
            PayTypeEnum.CREDIT.id -> {
                context.getStringByLocale(R.string.credit_payment, locale)
            }

            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getStringByLocale(R.string.online_payment, locale)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                getOfflinePayMethodByLocal(context, locale)
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getStringByLocale(R.string.pay_by_balance, locale)
            }

            PayTypeEnum.PAY_OTHER.id -> {
                context.getStringByLocale(R.string.pay_by_other, locale)
            }

            else -> ""
        }
    }

    fun getOfflinePayMethodByLocal(context: Context, locale: Locale): String {
        return "${context.getStringByLocale(R.string.offline, locale)}-${
            if (isCash()) {
                context.getStringByLocale(R.string.cash, locale)
            } else if (isAccountsReceivable()) {
                context.getStringByLocale(R.string.accounts_receivable, locale)
            } else {
                //服务端有做翻译处理
//                if (locale.language.startsWith("zh")) {
//                    offlinePaymentChannelsName ?: ""
//                } else if (locale.language.startsWith("en")) {
//                    offlinePaymentChannelsNameEn ?: ""
//                } else if (locale.language.startsWith("km")) {
//                    offlinePaymentChannelsNameKm ?: ""
//                }
                offlinePaymentChannelsName ?: ""
            }
        }"
    }


    //是否挂账
    private fun isAccountsReceivable(): Boolean {
        val channelId = try {
            offlinePaymentChannelsId?.doubleStrToIntStr()
        } catch (e: Exception) {
            offlinePaymentChannelsId
        }
        return channelId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true
    }


    //是否现金支付
    fun isCash(): Boolean {
        val channelId = try {
            offlinePaymentChannelsId?.doubleStrToIntStr()
        } catch (e: Exception) {
            offlinePaymentChannelsId
        }
        return channelId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true
    }

    fun getOfflinePayMethod(context: Context): String {
        if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
//            return context.getString(R.string.cash)
        } else if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        } else {
            return "${context.getString(R.string.offline_payments)} - $offlinePaymentChannelsName"
        }
    }


}

data class CreditOrderDetail(
    @SerializedName("orderId")
    val orderId: String?,
    @SerializedName("amount")
    val amount: BigDecimal?
)

data class KhqrCode(
    @SerializedName("outTradeNo")
    val outTradeNo: String?,
    @SerializedName("qrcode")
    val qrcode: String?,
    @SerializedName("deeplink")
    val deeplink: String?
)

data class GatewayMap(
    val creditConsumerTelephone: String?,//挂账客户账号
    val creditConsumerNickname: String?,//挂账客户昵称
)