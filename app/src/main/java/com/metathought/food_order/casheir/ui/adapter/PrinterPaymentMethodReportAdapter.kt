package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesPayMethodReportDetail
import com.metathought.food_order.casheir.databinding.ItemPaymentMethodBinding
import com.metathought.food_order.casheir.databinding.ItemPaymentMethodReportBinding
import com.metathought.food_order.casheir.databinding.ItemProductReportBinding


/**

 */
class PrinterPaymentMethodReportAdapter(
    val list: ArrayList<SalesPayMethodReportDetail>
) :
    RecyclerView.Adapter<PrinterPaymentMethodReportAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemPaymentMethodReportBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: SalesPayMethodReportDetail, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvItem.text = resource.payMethod
                        tvPrice.text = "$ ${resource.amount}"

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        PrinterTicketMenuViewHolder(
            ItemPaymentMethodReportBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: List<SalesPayMethodReportDetail>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}