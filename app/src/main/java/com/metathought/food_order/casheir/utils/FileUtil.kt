package com.metathought.food_order.casheir.utils

import android.content.Context
import android.media.MediaScannerConnection
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 *<AUTHOR>
 *@time  2024/10/29
 *@desc
 **/

object FileUtil {
    fun getFileExtension(file: File?): String {
        if (file != null) {
            val name = file.name
            val lastIndex = name.lastIndexOf('.')
            if (lastIndex > 0 && lastIndex < name.length - 1) {
                return name.substring(lastIndex + 1)
            }
        }
        return ""
    }

    fun isFileSizeGreaterThan2MB(filePath: String?): Boolean {
        val file = File(filePath)
        if (file.exists() && file.isFile) {
            val fileSize = file.length()
            return fileSize >= 2 * 1024 * 1024 // 2MB in bytes
        }
        return false
    }

    fun isImageFile(file: File): Boolean {
        val fileName = file.name
        if (fileName.lastIndexOf(".") != -1 &&
            (fileName.lowercase(Locale.getDefault()).endsWith(".png") ||
                    fileName.lowercase(Locale.getDefault()).endsWith(".jpeg") ||
                    fileName.lowercase(Locale.getDefault()).endsWith(".jpg"))
        ) {
            return true
        }
        return false
    }

    private fun copyFile(sourceFile: File, destinationFile: File) {
        try {
            val inputStream = FileInputStream(sourceFile)
            val outputStream = FileOutputStream(destinationFile)
            val buffer = ByteArray(1024)
            var length: Int
            while (inputStream.read(buffer).also { length = it } > 0) {
                outputStream.write(buffer, 0, length)
            }
            inputStream.close()
            outputStream.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    fun getFileNameInUrl(url: String): String {
        val lastIndex = url.lastIndexOf('/')
        if (lastIndex != -1) {
            val filename = url.substring(lastIndex + 1)
            return filename
        }
        return ""
    }

    fun scanFile(context: Context, file: File) {
        MediaScannerConnection.scanFile(
            context,
            arrayOf(file.absolutePath),
            null
        ) { path, uri ->
            // 扫描完成后的回调
        }
    }

    /**
     * 生成报表文件名称
     * @param url 文件下载地址
     * @param reportName 报表名称（如"综合报表"）
     * @return 格式化后的文件名（格式：yyyy-M-d HH_mm_ss-报表名称.扩展名）
     */
    fun generateReportFileName(url: String, reportName: String): String {
        // 从URL获取原始文件名
        val originalFileName = getFileNameInUrl(url)
        // 提取扩展名（含.）
        val extension = if (originalFileName.contains('.')) {
            "." + originalFileName.substringAfterLast('.')
        } else {
            "" // 无扩展名时为空
        }
        // 生成当前日期时间字符串
        val currentTime = SimpleDateFormat("yyyy-M-d HH_mm_ss", Locale.getDefault()).format(Date())
        // 组合最终文件名
        return "${currentTime}-${reportName}${extension}"
    }
}