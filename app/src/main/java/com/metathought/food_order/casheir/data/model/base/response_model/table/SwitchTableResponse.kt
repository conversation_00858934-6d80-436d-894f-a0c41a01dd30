package com.metathought.food_order.casheir.data.model.base.response_model.table


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.cart.Goods

data class SwitchTableResponse(
    @SerializedName("confirmOrderExists")
    val confirmOrderExists: Boolean?,
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("discountsAvailable")
    val discountsAvailable: Boolean?,
//    @SerializedName("goodsList")
//    val goodsList: List<Goods?>?,
    @SerializedName("goodsList")
    val goodsList: List<Goods?>?,
    @SerializedName("goodsTotalNum")
    val goodsTotalNum: Int?,
    @SerializedName("tableUuid")
    val tableUuid: String?,
    @SerializedName("totalPackingFee")
    val totalPackingFee: Int?,
    @SerializedName("totalPrice")
    val totalPrice: Int?,
    @SerializedName("totalVatPrice")
    val totalVatPrice: Int?,
    @SerializedName("totalVipPrice")
    val totalVipPrice: Int?,
    @SerializedName("totalVipVatPrice")
    val totalVipVatPrice: Int?,
//    @SerializedName("withPackingFee")
//    val withPackingFee: Boolean?
)