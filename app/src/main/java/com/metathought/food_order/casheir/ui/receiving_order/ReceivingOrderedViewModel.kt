package com.metathought.food_order.casheir.ui.receiving_order

import android.content.Context
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.KIOSK
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.AcceptOrderListRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.ShoppingCartHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ReceivingOrderedViewModel @Inject
constructor(private val repository: Repository) : ViewModel() {

    val uiInfoRequestState get() = _uiInfoRequestState
    private val _uiInfoRequestState = MutableLiveData<UIInfoRequestModel>()

    val uiListState get() = _uiListState
    private val _uiListState = MutableLiveData<UIListModel>()

    val uiInfoState get() = _uiInfoState
    private val _uiInfoState = MutableLiveData<UIInfoModel>()

    val uiSocketOrderedState get() = _uiSocketOrderedState
    private val _uiSocketOrderedState = MutableLiveData<OrderedRecord>()


    val uiTableState get() = _uiTableState
    private val _uiTableState = MutableLiveData<UITableStateModel>()

    private val _tableResponse = MutableLiveData<List<OrderedTableListItem>>()

    private val _filteredTableResponse = MutableLiveData<List<OrderedTableListItem>>()
    val filteredTableResponse get() = _filteredTableResponse


    private val _removeOrderState = MutableLiveData<UIRemoveOrderModel>()
    val removeOrderState get() = _removeOrderState

    private val _acceptOrderState = MutableLiveData<UIAcceptOrderModel>()
    val acceptOrderState get() = _acceptOrderState


    private var pagNo = 1
    private val pageSize = 50
    var currentOrderedInfo: OrderedInfoResponse? = null

    private var orderRecord: OrderedRecord? = null


    fun getOrderedList(
        isRefresh: Boolean? = null,
        acceptStatus: Int? = null,
        keyword: String? = null,
        listTable: ArrayList<OrderedTableListItem>? = null,
        isGrab:Boolean?=null
    ) {

        viewModelScope.launch {

            if (isRefresh != false) {
                pagNo = 1
                if (uiListState.value?.showLoading == true) {
                    //防止刷新的请求太多次
                    return@launch
                }
                if (isRefresh == null) {
                    emitUIListState(showLoading = true)
                }
            }


            try {

                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))

                val acceptOrderListRequest = AcceptOrderListRequest(
                    page = pagNo,
                    pageSize = pageSize,
                    keyword = keyword,
                    acceptStatus = acceptStatus,
                    isGrab = isGrab
                )

                listTable?.let {
                    if (listTable.isNotEmpty()) {
                        val arrayTable = mutableListOf<String>()
                        var isHasKiosk = false
                        for (table in listTable) {
                            table.uuid?.let { id ->
                                if (table.uuid != KIOSK) {
                                    arrayTable.add(id)
                                } else {
                                    isHasKiosk = true
                                }
                            }

                        }
                        acceptOrderListRequest.tableUuids = arrayTable
                    }
                }

                val result = repository.acceptOrderList(
                    acceptOrderListRequest
                )
                withContext(Dispatchers.Main) {
                    if (result is ApiResponse.Success) {
                        val response = result.data
                        if (response.records.isNullOrEmpty()) {
                            emitUIListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false,
                        )

                    } else if (result is ApiResponse.Error) {
                        emitUIListState(showError = result.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIListState(showError = e.message, showLoading = false)
            }
        }
    }

    /**
     * 获取接单详情
     *
     * @param record
     * @param isRead
     */
    fun getOrderedInfo(
        record: OrderedRecord,
        isRead: Boolean? = false,
    ) {
        viewModelScope.launch {
            emitUIInfoState(orderedInfoResponse = ApiResponse.Loading)
            try {
                orderRecord = record
                val orderedInfo = repository.getAcceptOrderInfo(record.id, isReadOrder = isRead)
                currentOrderedInfo = if (orderedInfo is ApiResponse.Success) {
                    orderedInfo.data
                } else {
                    null
                }

                if (orderedInfo is ApiResponse.Error) {
                    emitUIInfoState(orderedInfoResponse = orderedInfo, record = null)
                }

                if (isRead == true && record.isRead == false) {
                    //未读的时候调用下未读数接口
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
                }


                var item: OrderedRecord? = null
                //判断一下请求回来的是否当前列表选择的
                if (orderRecord?.id == currentOrderedInfo?.id) {
                    record.payStatus = currentOrderedInfo?.payStatus
                    if(orderRecord?.isGrab==true){
                        record.realPrice=currentOrderedInfo?.grabOrdersPriceDto?.total;
                    }else {
                        record.realPrice = currentOrderedInfo?.price?.getTotalToLong()
                    }
                    record.expireCountDown = currentOrderedInfo?.expireCountDown
                    record.tableUuid = currentOrderedInfo?.tableUuid
                    record.tableName = currentOrderedInfo?.tableName
                    record.tableType = currentOrderedInfo?.tableType
                    if (isRead == true) {
                        record.isRead = true
                    }
                    if (currentOrderedInfo?.needResetUnReadAndPrintStatus() == true) {
                        record.isPrinted = true
                        record.isRead = true
                    }

                    record.isOrdersWeightMark = currentOrderedInfo?.isHasNeedProcess()
                    record.goodsTotalNum = currentOrderedInfo?.getTotalGoodsNum()

                    item = record

                    emitUIInfoState(orderedInfoResponse = orderedInfo, record = item)
                }
            } catch (e: Exception) {

                emitUIInfoState(orderedInfoResponse = ApiResponse.Error(e.message))
                e.printStackTrace()
            } finally {

            }

        }
    }


    /**
     * 通过ws 更新订单列表信息
     *
     * @param orderRecord
     */
    fun websocketRefreshOrder(orderRecord: OrderedRecord) {
        if (currentOrderedInfo?.id == orderRecord.id) {
            getOrderedInfo(orderRecord)
        } else {
            viewModelScope.launch {
                try {
                    val orderedInfo = repository.getAcceptOrderInfo(id = orderRecord.id)
                    if (orderedInfo is ApiResponse.Success) {
                        if (orderedInfo?.data != null) {
                            orderRecord.payStatus = orderedInfo.data.payStatus
                            orderRecord.realPrice =
                                orderedInfo.data.price?.getTotalToLong()

                            orderRecord.tableUuid = orderedInfo.data.tableUuid
                            orderRecord.tableName = orderedInfo.data.tableName
                            orderRecord.tableType = orderedInfo.data.tableType


                            orderRecord.isOrdersWeightMark = orderedInfo.data.isHasNeedProcess()
                            orderRecord.goodsTotalNum = orderedInfo.data.getTotalGoodsNum()

                            if (orderedInfo.data.needResetUnReadAndPrintStatus()) {
                                orderRecord.isPrinted = true
                                orderRecord.isRead = true
                            }


                            _uiSocketOrderedState.postValue(orderRecord)
                        }
                    }

                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    /**
     * 获取接单详情
     *
     * @param id
     */
    fun getOrderedInfo(id: String) {
        viewModelScope.launch {
            emitUIInfoState(orderedInfoResponse = ApiResponse.Loading)
            try {
                val response = repository.getAcceptOrderInfo(id = id)
                var item: OrderedRecord? = null
                if (response is ApiResponse.Success) {
                    //如果列表选择的是请求的订单
                    if (orderRecord?.id == id) {
                        Timber.e("还是当前选中的")
                        currentOrderedInfo = response.data
                        if (orderRecord != null) {
                            orderRecord?.payStatus = currentOrderedInfo?.payStatus
                            orderRecord?.realPrice =
                                currentOrderedInfo?.price?.getTotalToLong()

                            orderRecord?.tableUuid = currentOrderedInfo?.tableUuid
                            orderRecord?.tableName = currentOrderedInfo?.tableName
                            orderRecord?.tableType = currentOrderedInfo?.tableType

                            orderRecord?.isOrdersWeightMark = currentOrderedInfo?.isHasNeedProcess()
                            orderRecord?.goodsTotalNum = currentOrderedInfo?.getTotalGoodsNum()

                            if (currentOrderedInfo?.needResetUnReadAndPrintStatus() == true) {
                                orderRecord?.isPrinted = true
                                orderRecord?.isRead = true
                            }

                            item = orderRecord

                        }
                    } else {
                        val index =
                            uiListState.value?.showSuccess?.records?.indexOfFirst { it.id == id }
                                ?: -1
                        Timber.e("查询完后不是当前列表选中的了 $index")
                        if (index != -1) {
                            item = uiListState.value?.showSuccess?.records?.get(index)
                            item?.payStatus = response.data?.payStatus
                            item?.realPrice = response.data?.price?.getTotalToLong()

                            item?.tableUuid = response.data?.tableUuid
                            item?.tableName = response.data?.tableName
                            item?.tableType = response.data?.tableType

                            item?.isOrdersWeightMark = response.data?.isHasNeedProcess()
                            item?.goodsTotalNum = response.data?.getTotalGoodsNum()

                            if (response.data?.needResetUnReadAndPrintStatus() == true) {
                                item?.isPrinted = true
                                item?.isRead = true
                            }
                        }
                    }
                    emitUIInfoState(orderedInfoResponse = response, record = item)
                } else if (response is ApiResponse.Error) {
                    currentOrderedInfo = null
                    emitUIInfoState(orderedInfoResponse = ApiResponse.Error(response.message))
                }

            } catch (e: Exception) {
                emitUIInfoState(orderedInfoResponse = ApiResponse.Error(e.message))
                e.printStackTrace()
            }
        }
    }

    /**
     * 取消接单
     *
     * @param id
     * @param cancelReason
     */
    fun cancelAcceptOrder(id: String, reasonType: Int, cancelReason: String?) {
        viewModelScope.launch {
            emitUIInfoRequestState(ApiResponse.Loading)
            try {
                val response = repository.cancelAcceptOrder(id, cancelReason, reasonType)

                if (response is ApiResponse.Success) {

                    emitUIRemoveOrderState(id)
                    emitUIInfoRequestState(response)
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))

                } else if (response is ApiResponse.Error) {
                    emitUIInfoRequestState(ApiResponse.Error(response.message))
                }
            } catch (e: Exception) {
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }


    fun cancelGrabAcceptOrder(id: String) {
        viewModelScope.launch {
            emitUIInfoRequestState(ApiResponse.Loading)
            try {
                val response = repository.cancelAcceptGrabOrder(id)

                if (response is ApiResponse.Success) {

                    Timber.e("cancelGrabAcceptOrder ok")

                    emitUIRemoveOrderState(id)
                    emitUIInfoRequestState(response)
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))

                } else if (response is ApiResponse.Error) {
                    Timber.e("  Timber.e(cancelGrabAcceptOrder fail")
                    emitUIInfoRequestState(ApiResponse.Error(response.message))
                }
            } catch (e: Exception) {
                Timber.e("  Timber.e(cancelGrabAcceptOrder error")
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }



    /**
     * 接单
     *
     * @param context
     */
    fun receivingOrder(context: Context) {
        viewModelScope.launch {
            if (_uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                return@launch
            }
            emitUIInfoRequestState(ApiResponse.Loading)

            try {
                val id = currentOrderedInfo?.id

                var response:ApiResponse<BaseBooleanResponse>?=null;

                if(currentOrderedInfo?.grabOrdersPriceDto!=null){
                    response = repository.acceptGrabOrder(id)
                }else{
                    response = repository.acceptOrder(id)
                }
                if (response is ApiResponse.Success) {
                    emitUIInfoRequestState(ApiResponse.Success(response))
                    if (orderRecord != null && orderRecord?.id == id) {
                        val goodsVoList = currentOrderedInfo?.goods
                        val goodsBoList = ArrayList<GoodsBo>()
                        //TODO 2.16.10 需要注意接单页面重量处理
                        goodsVoList?.forEach {
                            val bo = GoodsBo(
                                id = it.id,
                                num = it.num,
                                tagItemId = it.getTagItemId(),
                                feeds = it.getFeedBo(),
                                pricingMethod = it.pricingMethod,
                                weight = it.weight,
                                discountPrice = it.discountPrice,
                                goodsType = it.goodsType,
                                mealSetGoodsDTOList = OrderHelper.getSelectMealSetGood(it.orderMealSetGoodsDTOList),
                                goodsHashKey = it.getHash(),
                                note = it.note,
                                uuid = it.uuid,
                            )
                            if (it.isTimePriceGood()) {
                                bo.sellPrice = it.sellPrice
                                bo.vipPrice = it.vipPrice
                                bo.pricingCompleted = it.pricingCompleted
                            }
                            goodsBoList.add(
                                bo
                            )
                        }
                        emitAcceptState(response, goodsBoList)
                    }

                } else if (response is ApiResponse.Error) {
                    if (id != null) {
                        getOrderedInfo(id)
                    }
                    emitUIInfoRequestState(ApiResponse.Error(response.message))
                }

            } catch (e: Exception) {
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }


    fun getOrderedTableList(tableUUID: String? = null) {
        viewModelScope.launch {
            emitUITableState(response = ApiResponse.Loading)
            try {
                val orderedTable = repository.orderedTableList()
                var orderedTableItem: OrderedTableListItem? = null
                if (orderedTable is ApiResponse.Success) {
                    orderedTable.data.sortedBy { it.id }.let { list ->
                        val tmp = list.toMutableList()
                        _tableResponse.value = tmp
                        filteredTableResponse.value = tmp
                        orderedTableItem = tmp.firstOrNull { it.uuid == tableUUID }
                    }
                }
                emitUITableState(response = orderedTable, orderedTableItem = orderedTableItem)
//                }
            } catch (e: Exception) {
                emitUITableState(response = ApiResponse.Error(e.message))
            }
        }

    }


    fun filterFloor(query: String) {
        if (query.isNotEmpty()) {
            val filteredList = _tableResponse.value?.filter { table ->
                table.location?.equals(query, ignoreCase = true) == true
            }
            Timber.e("recyclerViewPage  filteredList  ${filteredList?.size}  $query")
            val orderedTableListResponse = OrderedTableListResponse()
            filteredList?.let { orderedTableListResponse.addAll(it) }

            _filteredTableResponse.value = orderedTableListResponse
        } else {
            _filteredTableResponse.value = _tableResponse.value
        }
    }


    private suspend fun emitUIInfoState(
        orderedInfoResponse: ApiResponse<OrderedInfoResponse>? = null,
        record: OrderedRecord? = null
    ) {
        val uiModel = UIInfoModel(orderedInfoResponse, record)
        withContext(Dispatchers.Main) {
            _uiInfoState.value = uiModel
        }
    }

    data class UIInfoModel(
        val orderedInfoResponse: ApiResponse<OrderedInfoResponse>?,
        val record: OrderedRecord?
    )

    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: OrderedTotalResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel = UIListModel(
            showLoading,
            showError,
            showSuccess,
            showEnd,
            isRefresh,
        )
        withContext(Dispatchers.Main) {
            _uiListState.value = uiModel
        }
    }


    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: OrderedTotalResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )


    private suspend fun emitUITableState(
        response: ApiResponse<OrderedTableListResponse>? = null,
        orderedTableItem: OrderedTableListItem? = null
    ) {
        val uiModel = UITableStateModel(response, orderedTableItem)
        withContext(Dispatchers.Main) {
            _uiTableState.value = uiModel
        }
    }

    data class UITableStateModel(
        val response: ApiResponse<OrderedTableListResponse>?,
        val orderedTableItem: OrderedTableListItem?
    )


    private suspend fun emitUIInfoRequestState(
        baseBooleanResponse: ApiResponse<Any>? = null,
    ) {
        val uiInfoRequestModel = UIInfoRequestModel(baseBooleanResponse)
        withContext(Dispatchers.Main) {
            _uiInfoRequestState.value = uiInfoRequestModel
        }
    }

    data class UIInfoRequestModel(
        val baseBooleanResponse: ApiResponse<Any>?,
    )


    private suspend fun emitUIRemoveOrderState(
        id: String
    ) {
        val uiModel = UIRemoveOrderModel(id)
        withContext(Dispatchers.Main) {
            _removeOrderState.value = uiModel
        }
    }

    data class UIRemoveOrderModel(
        var id: String
    )

    private suspend fun emitAcceptState(
        response: ApiResponse<BaseBooleanResponse>,
        goodList: ArrayList<GoodsBo>
    ) {
        val uiModel = UIAcceptOrderModel(response, goodList)
        withContext(Dispatchers.Main) {
            _acceptOrderState.value = uiModel
        }
    }

    data class UIAcceptOrderModel(
        var response: ApiResponse<BaseBooleanResponse>,
        var goodList: ArrayList<GoodsBo>
    )

}