package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import android.os.Parcelable
import com.metathought.food_order.casheir.constant.WholeDiscountType
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/7/10
 *@desc
 **/
@Parcelize
data class ReduceDiscountDetailModel(
    //美元转瑞尔汇率
    var conversionRatio: Long? = null,

    //	减免折扣率价格
    var reduceAmount: BigDecimal? = null,
    //	销售价：减免金额（美元）
    var reduceDollar: BigDecimal? = null,
    //销售价：减免金额（瑞尔）
    var reduceKhr: BigDecimal? = null,
    //销售价：减免百分比
    var reduceRate: Double? = null,
    //减免后实际金额
    var reduceRealPrice: Long? = null,
    //减免后实际金额(瑞尔)
    var reduceRealPriceKhr: Long? = null,

    //会员价：减免折扣率价格
    var reduceVipAmount: BigDecimal? = null,
    //会员价：减免金额（美元）
    var reduceVipDollar: BigDecimal? = null,
    //会员价：减免金额（瑞尔）
    var reduceVipKhr: BigDecimal? = null,
    //会员价：减免百分比
    var reduceVipRate: Double? = null,
    //会员价：减免后实际金额
    var reduceVipRealPrice: Long? = null,
    //会员价：减免后实际金额(瑞尔)
    var reduceVipRealPriceKhr: Long? = null,

    //销售价：订单实付总金额
    var totalAmount: Long? = null,
    //销售价：订单实付总金额（瑞尔）
    var totalAmountKhr: Long? = null,
    //会员价：订单实付总金额，如果有值则需展示会员价tab
    var totalVipAmount: Long? = null,
    //会员价：订单实付总金额（瑞尔）
    var totalVipAmountKhr: Long? = null,

    //是否有会员价tab
    var vipTabFlag: Boolean? = null,
    //当前设置的类型 类型:最后一次保存销售价或会员价 1-销售价 2-会员价
    var type: Int? = null,


    //优惠活动减免总金额
    var couponActivityAmount: BigDecimal? = null,
    //优惠活动vip减免总金额
    var couponActivityVipAmount: BigDecimal? = null,
    //整单减免原因
    var reduceReason: String? = null,
    //减免类型：0：discount(正常折扣) 1:void(菜品有问题时原因必填)
    var discountType: Int? = null,
    //折扣减免活动Id
    var discountReduceActivityId: String? = null,

    //整单折扣计算方式: 1-基于总计（含VAT）折扣 2-基于小计（不含VAT）折扣")
    var wholeDiscountCalculationType: Int? = 1,

    //本地的后台配置折扣信息
    var discountReduceInfo: DiscountReduceInfo? = null,

    //本地总的vat
    var totalVatPrice: Long? = null,
    //本地总的vipVat
    var totalVipVatPrice: Long? = null,

    ) : Parcelable

@Parcelize
data class DiscountReduceInfo(
    val id: String? = null,
    //折扣活动名称
    val name: String? = null,
    //优惠活动类型，2:固定金额减免  1:百分比折扣减免，
    val type: Int? = null,
    //减免百分比或固定减免金额
    val reduceRateAmount: BigDecimal? = null,

    //门槛金额（美元），null：无门槛
    var thresholdAmount: BigDecimal? = null,
    //减免金额上限（美元）null：无上限
    var reduceAmountLimit: BigDecimal? = null,


    //折扣减免金额(符合门槛金额)-销售价
    var discountReduceAmount: BigDecimal? = null,
    //折扣减免金额(符合门槛金额)-vip价
    var discountReduceVipAmount: BigDecimal? = null,

    //前端展示使用：门槛金额（美元），null：无门槛
    var showThresholdAmount: String? = null,

    //前端展示使用：减免金额上限（美元）null：无上限
    var showReduceAmountLimit: String? = null,
    //前端展示使用：减免百分比或固定减免金额
    val showReduceRateAmount: String? = null,

    ) : Parcelable {

    fun getReduceAmountLimitAfterAmplify(): Long? {
        if (reduceAmountLimit == null) {
            return null
        }
        return reduceAmountLimit!!.times(BigDecimal(100)).toLong()
    }

    fun getThresholdAmountAfterAmplify(): Long? {
        if (thresholdAmount == null) {
            return null
        }
        return thresholdAmount!!.times(BigDecimal(100)).toLong()
    }

    fun isPercentDiscount(): Boolean {
        return type == WholeDiscountType.PERCENTAGE.id
    }

    fun isFixedAmount(): Boolean {
        return type == WholeDiscountType.FIXED_AMOUNT.id
    }

//    /**
//     * 获取这个折扣优惠 现价优惠多少钱
//     *
//     * @return
//     */
//    fun getAmount(): BigDecimal {
//        return discountReduceAmount ?: BigDecimal.ZERO
//    }

    /**
     * 获取这个折扣优惠 vip优惠多少钱
     *
     * @return
     */
//    fun getVipAmount(): BigDecimal {
//        return discountReduceVipAmount ?: BigDecimal.ZERO
//    }
}