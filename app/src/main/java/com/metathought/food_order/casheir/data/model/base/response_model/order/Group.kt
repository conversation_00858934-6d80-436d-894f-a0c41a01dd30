package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName

data class Group(
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("goods")
    val goods: Any?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("mainStoreId")
    val mainStoreId: Any?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("picUrl")
    val picUrl: String?,
    @SerializedName("saasStoreId")
    val saasStoreId: String?,
    @SerializedName("sort")
    val sort: String?,
    @SerializedName("status")
    val status: Int?,
    @SerializedName("storeId")
    val storeId: String?,
    @SerializedName("updateTime")
    val updateTime: String?,
    @Transient
    var checked: Boolean? = false,
    /**
     * 分组可选最大数量
     */
    @SerializedName("maxGoodsNum")
    var maxGoodsNum: Int? = null,
    /**
     * 分组可选最小数量
     */
    @SerializedName("minGoodsNum")
    var minGoodsNum: Int? = null,

    )