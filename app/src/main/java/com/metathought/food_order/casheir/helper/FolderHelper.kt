package com.metathought.food_order.casheir.helper


import android.os.Environment
import android.util.Log
import com.metathought.food_order.casheir.extension.dir
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.wecloud.im.common.context.AppContextWrapper
import timber.log.Timber
import java.io.File

object FolderHelper {
    private const val TAG = "FolderHelper"
    const val APP_NAME = "MPOS_Cashier"

    fun getTempFileDir(): File {
        return File(getBaseDir(), "Temp").dir()
    }

    fun getBackupDir(): File {
        return File(getBaseDir(), ".Backup").dir()
    }

    fun getThemeDir(): File {
        return File(getBaseDir(), ".ThemeResource").dir()
    }

    fun getThemeResourceDir(code: String): File {
        return File(getThemeDir(), code).dir()
    }

    fun getCollectFileDir(key: String?): File {
        val collectFolder = File(getMediaFileDir(), "Collect").dir()

        return if (key == null) collectFolder else File(collectFolder, key).dir()
    }

    fun getAvatarFileDir(): File {//头像
        return File(getMediaFileDir(), ".avatar").dir()
    }

    fun getEditFaceFileDir(): File {
        return File(getMediaFileDir(), "faceImage")
    }

    fun getEmojiFileDir(): File {
        return File(getMediaFileDir(), "emoji").dir()
    }

    fun getGroupCodeFileDir(): File {
        return File(getMediaFileDir(), "barCode")
    }

    fun getGlideFileDir(): File {
        return File(getMediaFileDir(), ".image_glide_cache").dir()
    }

    fun getEditImageFileDir(): File {
        return File(getMediaFileDir(), "imageEdit").dir()
    }

    fun getThumbImageDir(): File {
        return File(getMediaFileDir(), ".ThumbImg").dir()
    }

    fun getImagesDir(): File {
        return File(getMediaFileDir(), ".Photo").dir()
    }

    fun getFilesDir(): File {
        return File(getMediaFileDir(), ".Files").dir()
    }

    fun getVideoDir(): File {
        return File(getMediaFileDir(), ".Video").dir()
    }

    fun getVideoCacheDir(): File {
        return File(getVideoDir(), "cache").dir()
    }

    fun getAudioDir(): File {
        return File(getMediaFileDir(), ".Audio").dir()
    }

    fun getOtherDir(): File {
        return File(getMediaFileDir(), ".Other").dir()
    }

    //--------------收藏---------------------------
    fun getCollectThumbImageDir(): File {
        return File(getMediaFileDir(), ".collectThumbImg").dir()
    }

    fun getCollectImagesDir(): File {
        return File(getMediaFileDir(), ".collectPhoto").dir()
    }

    fun getCollectFilesDir(): File {
        return File(getMediaFileDir(), ".collectFiles").dir()
    }

    fun getCollectVideoDir(): File {
        return File(getMediaFileDir(), ".collectVideo").dir()
    }

    fun getCollectAudioDir(): File {
        return File(getMediaFileDir(), ".collectAUDIO").dir()
    }

    private fun getMediaFileDir(): File {
        return File(getBaseDir(), "Media").dir()
    }

    fun getLogFileDir(): File {
        return File(getBaseDir(), "Log").dir()
    }

    private fun getBaseDir(): File? {
        Timber.e("storeId  ${MainDashboardFragment.CURRENT_USER?.storeId}  ${AppContextWrapper.getBaseContext()}")
        val baseDir =
            AppContextWrapper.getBaseContext().getExternalFilesDir(MainDashboardFragment.CURRENT_USER?.storeId)
                ?: return null
        return File(baseDir, APP_NAME).dir()
    }

//    private fun getFilesDirFixed(): File {
//        for (a in 0..9) {
//            val path = AppContextWrapper.getApplicationContext().filesDir
//            if (path != null) {
//                return path
//            }
//        }
//        try {
//            val info = AppContextWrapper.getApplicationContext().applicationInfo
//            val path = File(info.dataDir, "files")
//            return path.dir()
//        } catch (e: Exception) {
//            Timber.d(e)
//        }
//        return File("/data/data/com.highlander.mosapp/files")
//    }
//
//    fun getLanguageDir(): File {
//        return File(getFilesDirFixed(), "Language").dir()
//    }


    /**
     * 获取缓存路径
     */
    fun getCacheDir(): File {
        var state: String? = null
        try {
            state = Environment.getExternalStorageState()
        } catch (e: Exception) {
            Log.e(TAG, "getCacheDir: " + e.message)
        }
        if (state == null || state.startsWith(Environment.MEDIA_MOUNTED)) {
            try {
                val file: File?
                val dirs: Array<File?> = AppContextWrapper.getBaseContext().externalCacheDirs
                file = dirs[0]
                if (file != null) {
                    return file.dir()
                }
            } catch (e: java.lang.Exception) {
                Log.e(TAG, "getCacheDir: " + e.message)
            }
        }
        try {
            val file = AppContextWrapper.getBaseContext().cacheDir
            if (file != null) {
                return file.dir()
            }
        } catch (e: java.lang.Exception) {
            Log.e(TAG, "getCacheDir: " + e.message)
        }
        return File("")
    }

     fun getDownloadFolderPath(): String {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath
    }

     fun getDocumentsFolderPath(): String {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).absolutePath
    }

}