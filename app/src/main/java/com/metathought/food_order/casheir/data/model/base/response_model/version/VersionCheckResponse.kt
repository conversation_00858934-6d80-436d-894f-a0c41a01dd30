package com.metathought.food_order.casheir.data.model.base.response_model.version

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTranslateVo
import com.metathought.food_order.casheir.helper.LocaleHelper
import kotlinx.parcelize.Parcelize


/**
 *<AUTHOR>
 *@time  2024/7/2
 *@desc
 **/
@Parcelize
data class VersionCheckResponse(
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("downloadUrl")
    val downloadUrl: String?,
    @SerializedName("changelogZh")
    val changelogZh: String?,
    @SerializedName("changelogEn")
    val changelogEn: String?,
    @SerializedName("changelogKm")
    val changelogKm: String?,
    /**
     * @ApiModelProperty("更新类型:0-无需更新; 1-可选更新; 2-强制更新")
     * **/
    @SerializedName("type")
    val type: Int?,
): Parcelable{


    fun getUpdateDesc():String{
        val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
        return if (lang.startsWith("zh")) {
            changelogZh?:""
        } else if (lang.startsWith("km")) {
            changelogKm?:""
        } else {
            changelogEn?:""
        }
    }
}