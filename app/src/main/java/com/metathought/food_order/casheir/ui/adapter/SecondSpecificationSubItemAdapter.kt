package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.SpecificationTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.databinding.SecondSpecificationItemBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero


class SecondSpecificationSubItemAdapter(
    private var tagType: Int,
    private var goodsTags: List<GoodsTagItem>,
) : RecyclerView.Adapter<SecondSpecificationSubItemAdapter.SpecificationViewHolder>() {

    inner class SpecificationViewHolder(val binding: SecondSpecificationItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            binding.apply {
                goodsTags[position].apply {
                    when (tagType) {

                        SpecificationTypeEnum.FEATURE.id -> {
                            tvDishedNameAndPrice.text = name
                        }

                        SpecificationTypeEnum.SPECIFICATION.id,
                        SpecificationTypeEnum.INGREDIENT.id -> {
                            if((price ?:0.0) >0) {
                                tvDishedNameAndPrice.text =
                                    "$name +$${price?.priceDecimalFormatTwoDigitZero()}"
                            }else{
                                tvDishedNameAndPrice.text = name
                            }
                        }
                    }


                }
                tvDishedNameAndPrice.setTextColor(
                    ContextCompat.getColor(
                        itemView.context,
                        if (goodsTags[position].isCheck == true) R.color.primaryColor else R.color.black
                    )
                )
                layoutMain.isSelected=goodsTags[position].isCheck == true
//                layoutMain.setOnClickListener {
//                    when (type) {
//                        SpecificationTypeEnum.SPECIFICATION.id,
//                        SpecificationTypeEnum.FEATURE.id -> {
//                            for (i in goodsTags.indices) {
//                                if (goodsTags[i].isCheck == true) {
//                                    goodsTags[i].isCheck = false
//                                    notifyItemChanged(i)
//                                    break
//                                }
//                            }
//                            goodsTags[position].isCheck = true
//                            notifyItemChanged(position)
//                        }
//
//                        SpecificationTypeEnum.INGREDIENT.id -> {
//                            goodsTags[position].isCheck = goodsTags[position].isCheck != true
//                            notifyItemChanged(position)
//                        }
//                    }
//                    onClickCallback.invoke(position)
//                }
            }
        }
    }



    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SpecificationViewHolder {
        return SpecificationViewHolder(
            SecondSpecificationItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return goodsTags.size
    }

    override fun onBindViewHolder(holder: SpecificationViewHolder, position: Int) {
        holder.bind(position)
    }

}