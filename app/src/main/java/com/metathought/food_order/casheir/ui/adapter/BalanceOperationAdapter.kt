package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.constant.BalanceStatusEnum
import com.metathought.food_order.casheir.constant.BalanceTypeEnum
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.RecordBalance
import com.metathought.food_order.casheir.databinding.BalanceOperationItemBinding
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment

/**
 * 余额界面操作按钮适配器
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class BalanceOperationAdapter(
    val list: ArrayList<RecordBalance>
) : RecyclerView.Adapter<BalanceOperationAdapter.OperationViewHolder>() {

    inner class OperationViewHolder(val binding: BalanceOperationItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: RecordBalance, position: Int) {
            binding.apply {
//                btnDetail.setOnClickListener {
//                    mOnOperationListener?.onDetailClick(resource)
//                }
//
//                // 根据状态显示/隐藏取消按钮
//                btnCancel.isVisible = resource.status == "PENDING" || resource.status == "PROCESSING"
//                btnCancel.setOnClickListener {
//                    mOnOperationListener?.onCancelClick(resource)
//                }

                btnCancel.setVisibleGone(
                    resource.type == BalanceTypeEnum.TOP_UP.id
                            && resource.status == BalanceStatusEnum.SUCCESS.id
                            &&  MainDashboardFragment.CURRENT_USER?.getPermissionList()
                        ?.contains(PermissionEnum.RECHARGE.type) == true
                )
                btnCancel.setOnClickListener {
                    mOnOperationListener?.onCancelClick(resource)
                }

                btnDetail.setOnClickListener {
                    mOnOperationListener?.onDetailClick(resource)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OperationViewHolder {
        val itemView = BalanceOperationItemBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return OperationViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: OperationViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }

    fun replaceData(newList: ArrayList<RecordBalance>?) {
        if (newList != null) {
            this.list.clear()
            this.list.addAll(newList)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<RecordBalance>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }

    private var mOnOperationListener: OnOperationListener? = null

    fun setOnOperationListener(listener: OnOperationListener) {
        this.mOnOperationListener = listener
    }

    interface OnOperationListener {
        fun onDetailClick(record: RecordBalance)
        fun onCancelClick(record: RecordBalance)
    }
}
