package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.report.DailyReportData
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.databinding.ItemDailyReportOperBinding

/**
 * 每日报表操作列表适配器
 */
class DailyReportOperAdapter(
    val list: ArrayList<DailyReportData>,
) : RecyclerView.Adapter<DailyReportOperAdapter.ProduceReportItemViewHolder>() {

    var onItemClickListener: ((DailyReportData) -> Unit)? = null

    inner class ProduceReportItemViewHolder(val binding: ItemDailyReportOperBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: DailyReportData, position: Int) {
            binding.apply {
                root.setOnClickListener {
                    onItemClickListener?.invoke(resource)
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemDailyReportOperBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: DailyReportOperAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<DailyReportData>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}