package com.metathought.food_order.casheir.helper

import android.widget.PopupWindow


/**
 *<AUTHOR>
 *@time  2024/11/6
 *@desc
 **/

object PopupWindowHelper {
    private val popupWindows: MutableList<PopupWindow> = ArrayList()

    fun addPopupWindow(popupWindow: PopupWindow) {
        popupWindows.add(popupWindow)
    }

    fun closeAllPopupWindows() {
        for (popupWindow in popupWindows) {
            if (popupWindow != null && popupWindow.isShowing) {
                popupWindow.dismiss()
            }
        }
        popupWindows.clear()
    }

    /**
     * 删除对应popwindow 记录
     *
     * @param popupWindow
     */
    fun deletePopupWindow(popupWindow: PopupWindow) {
        popupWindows.removeIf { popupWindow.hashCode() == it.hashCode() }
    }

}