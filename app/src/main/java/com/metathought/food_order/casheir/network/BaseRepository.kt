package com.metathought.food_order.casheir.network

import com.metathought.food_order.casheir.MainActivity
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.coroutineScope
import javax.net.ssl.SSLHandshakeException

/**
 * <AUTHOR>
 * @date 2024/3/1817:01
 * @description
 */
open class BaseRepository {


    suspend fun <T : Any> safeApiCall(
        call: suspend () -> ApiResponse<T>,
        errorMessage: String
    ): ApiResponse<T> {
        if (NetworkHelper.hasInternet(MyApplication.myAppInstance)) {
            return try {
                call()
            } catch (e: SSLHandshakeException) {
                ApiResponse.Error("", 1)
            } catch (e: Exception) {
                e.printStackTrace()
//                CrashReport.postCatchedException(e)
                // An exception was thrown when calling the API so we're converting this to an IOException
                if (errorMessage.isNotEmpty()) {
                    if (errorMessage.contains("hostname")) {
                        ApiResponse.Error(
                            MainActivity.mainActivityInstance?.getString(R.string.network_dns_resolution_failed),
                            500
                        )
                    } else {
                        ApiResponse.Error(errorMessage, 500)
                    }
                } else {
//                    ApiResponse.Error(e.message.toString(), 500)
//                    ApiResponse.Error(
//                        MainActivity.mainActivityInstance?.getString(R.string.error_title_service_unavailable),
//                        500
//                    )
                    ApiResponse.Error(
                        "",
                        500
                    )
                }
            }
        } else
            return ApiResponse.Error(
                MainActivity.mainActivityInstance?.getString(R.string.no_internet_connection),
                500
            )
    }


    suspend fun <T : Any> executeResponse(
        response: BaseResponse<T>, successBlock: (suspend CoroutineScope.() -> Unit)? = null,
        errorBlock: (suspend CoroutineScope.() -> Unit)? = null
    ): ApiResponse<T> {
        return coroutineScope {
            if (response.isSuccess()) {
                successBlock?.let { it() }
                ApiResponse.Success(response.data)
            } else {
                errorBlock?.let { it() }
                ApiResponse.Error(
                    response.msg,
                    response.code,
                    errorCode = if (response.errorCode is Double) {
                        response.errorCode.toInt()
                    } else {
                        null
                    }
                )
            }
        }
    }
}