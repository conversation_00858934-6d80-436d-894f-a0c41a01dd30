package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo


/**
 *<AUTHOR>
 *@time  2024/8/26
 *@desc
 **/

data class CouponCodeRecongnitionRequest(
    @SerializedName("diningStyle")
    val diningStyle: Int? = null,
    @SerializedName("goodsList")
    val goodsList: List<GoodsBo>? = null,
    @SerializedName("isPreOrder")
    val isPreOrder: Boolean? = null,
    @SerializedName("tableUuid")
    val tableUuid: String? = null,
    @SerializedName("couponCode")
    val couponCode: String? = null,
    @SerializedName("orderNo")
    val orderNo: String? = null
)