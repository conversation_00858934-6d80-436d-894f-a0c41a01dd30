package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.google.gson.annotations.SerializedName

data class DiscountSingleParamRequest(
    @SerializedName("orderNo")
    val orderNo: String? = null,
    //收银端废弃
    @SerializedName("singleReduceReason")
    val singleReduceReason: String? = null,
    @SerializedName("singleItemDiscountList")
    var singleItemDiscountList: List<SingleDiscountRequest>?
)
