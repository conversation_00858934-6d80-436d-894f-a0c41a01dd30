package com.metathought.food_order.casheir.ui.second_display.other

import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.databinding.SecondOtherLayoutBinding

/**
 * 其他情况 副屏
 * Other cases: second-screen
 * <AUTHOR>
 * @date 2024/5/1311:34
 * @description
 */
class OtherScreenUI(private var mContext: Context, display: Display) :
    Presentation(mContext, display) {

    private lateinit var binding: SecondOtherLayoutBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = SecondOtherLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.run {
            businessSlogan.text = mContext.getString(R.string.your_best_business_partner)
            welcome.text = mContext.getString(R.string.welcome)
        }
    }

    fun updateUI(user: UserLoginResponse?) {
        binding.run {
            Glide.with(mContext).load(user?.url).circleCrop()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgLogo)
            tvStoreName.text = user?.getStoreNameByLan()
        }
    }



}