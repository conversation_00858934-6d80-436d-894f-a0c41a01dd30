package com.metathought.food_order.casheir.ui.store_dashbord

import android.content.Context
import android.icu.util.Calendar
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.ChartTimeType
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.constant.OrderType
import com.metathought.food_order.casheir.databinding.FragmentDashboardBinding
import com.metathought.food_order.casheir.databinding.PopupDateTypeBinding
import com.metathought.food_order.casheir.extension.getDateTypeEnum
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.network.NetworkHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.store_dashbord.ordered.StoreOrderListDialog
import com.metathought.food_order.casheir.ui.store_dashbord.store.DashboardStoreFragment
import com.metathought.food_order.casheir.ui.store_dashbord.takeout.DashboardTakeoutFragment
import com.tinder.scarlet.WebSocket
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


@AndroidEntryPoint
class DashboardFragment : BaseFragment() {

    companion object {
        fun newInstance() = DashboardFragment()
    }

    private var _binding: FragmentDashboardBinding? = null
    private val binding get() = _binding
    private var startDateString: String = ""
    private var endDateString: String = ""
    private var type: String = "1"
    private var orderType: OrderType = OrderType.STRORE;

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentDashboardBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
        initObserver()
    }

    private fun initObserver() {
    }

    private fun initView() {
        binding?.apply {
            var isShowRadioGroupFilter: Boolean = MainDashboardFragment.STORE_INFO?.isGrab == true;
            radioGroupFilter.setVisibleGone(isShowRadioGroupFilter)
            context?.let {
                replaceFragment(DashboardStoreFragment(), buildBundle())
            }
        }
    }

    override fun onLoad() {
        super.onLoad()
        context?.let {
            getStoreData()
        }
    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        val constraintsBuilder = CalendarConstraints.Builder()
        // Set the minimum year
        constraintsBuilder.setStart(Calendar.getInstance().apply {
            set(Calendar.YEAR, 2024)
            set(Calendar.MONTH, Calendar.JANUARY)
        }.timeInMillis)
        val constraints = constraintsBuilder.build()
        builder.setCalendarConstraints(constraints)
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            type = ""
            binding?.run {
                tvType.text = getString(R.string.not_selected)
            }
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate))
            endDateString = sdf.format(Date(endDate))
            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
            getStoreData()
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun initListener() {

        binding?.apply {
            radioGroupFilter.setOnCheckedChangeListener { group, checkedId ->
                when (checkedId) {
                    R.id.radioStore -> {
                        orderType = OrderType.STRORE;
                        onSelectDate(isReset = true)
                    }

                    R.id.radioTakeOut -> {
                        orderType = OrderType.CRAB;
                        onSelectDate(isReset = true)
                    }
                }
            }
            tvCalendar.setOnClickListener {
                datePickerDialog()
            }
            layoutViewOrderDetail.setOnClickListener {
                var isGrab = false;
                if (orderType == OrderType.CRAB)
                    isGrab = true
                StoreOrderListDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    type = type,
                    isGrab = isGrab,
                    startDate = startDateString,
                    endDate = endDateString
                ) {

                }
            }
            tvClearFilter.setOnClickListener {
                onSelectDate(isReset = true)
            }
            dropdownFilter.setOnClickListener {
                arrow.animate().rotation(180f).setDuration(200)
                dropdownFilter.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupWindowDayType(dropdownFilter)
            }
        }
    }

    private fun buildBundle(): Bundle {
        var bundle = Bundle();
        bundle.putString("startDateString", startDateString);
        bundle.putString("endDateString", endDateString)
        bundle.putString("type", type);
        bundle.putBoolean("isRefresh", true);
        return bundle;
    }

    fun replaceFragment(fragment: BaseFragment, bundle: Bundle? = null) {
        fragment.arguments = bundle
        childFragmentManager.beginTransaction()
            .replace(R.id.fragmentOrder, fragment).commit()
    }


    private fun getStoreData(isRefresh: Boolean? = null) {
        if (orderType == OrderType.STRORE) {
            replaceFragment(DashboardStoreFragment(), buildBundle())
        } else if (orderType == OrderType.CRAB) {
            replaceFragment(DashboardTakeoutFragment(), buildBundle())
        }
    }

    private fun showPopupWindowDayType(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupDateTypeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }
        popupView.tvToday.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.today)) {
                binding?.tvType?.text = getString(R.string.today)
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        if (orderType == OrderType.CRAB) {
            popupView.tvWeek.text = getString(R.string.yesterday)
            popupView.tvMonth.text = getString(R.string.last_7_days)
            popupView.tvQuarter.text = getString(R.string.last_30_days)
        }
        popupView.tvWeek.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_week)) {
                binding?.tvType?.text = popupView.tvWeek.text
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        popupView.tvMonth.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_month)) {
                binding?.tvType?.text = popupView.tvMonth.text
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        popupView.tvQuarter.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_quarter)) {
                binding?.tvType?.text = popupView.tvQuarter.text
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        popupView.tvYear.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_year)) {
                binding?.tvType?.text = getString(R.string.this_year)
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        context?.let {
            val type = context?.let { binding?.tvType?.text.toString().getDateTypeEnum(it) }
            when (type) {
                ChartTimeType.TODAY.type -> setSelectedDayType(popupView.tvToday, it)
                ChartTimeType.WEEK.type -> setSelectedDayType(popupView.tvWeek, it)
                ChartTimeType.MONTH.type -> setSelectedDayType(popupView.tvMonth, it)
                ChartTimeType.QUARTER.type -> setSelectedDayType(popupView.tvQuarter, it)
                ChartTimeType.YEAR.type -> setSelectedDayType(popupView.tvYear, it)
                else -> {
//                    setSelectedLanguages(popupView.tvToday, it)
                }
            }
        }
    }

    private fun setSelectedDayType(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }

    private fun onSelectDate(isReset: Boolean? = false) {
        if (isReset == true) {
            binding?.tvType?.text = getString(R.string.today)
        }
        binding?.tvCalendar?.text = ""
        binding?.tvCalendar?.updateCalendarColor()
        type = context?.let { binding?.tvType?.text.toString().getDateTypeEnum(it) } ?: ""
        startDateString = ""
        endDateString = ""
        getStoreData()
    }

    //Socket
    override fun onResume() {
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        super.onResume()
    }

    override fun onPause() {
//        viewModel.destroylifeCycle()
        super.onPause()
    }

    private fun wsHandel(event: WebSocket.Event) {

    }

}