package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.databinding.ItemCouponGoodBinding
import com.metathought.food_order.casheir.databinding.ItemNoticeBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description
 */
class NoticeListAdapter(
    val list: ArrayList<NoticeResponse>,
    val onItemClickListener: ((NoticeResponse) -> Unit)? = null
) : RecyclerView.Adapter<NoticeListAdapter.CouponGiftGoodViewHolder>() {


    inner class CouponGiftGoodViewHolder(val binding: ItemNoticeBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick {
                        if (it != -1) {
                            list[it].isRead = true
                            onItemClickListener?.invoke(list[it])
                            notifyItemChanged(it)
                        }
                    }

                }

            }
        }

        fun bind(resource: NoticeResponse) {
            binding.apply {
                tvTitle.text = resource.title
//                tvContent.text = resource.content
//                tvContent.isVisible = !resource.content.isNullOrEmpty()
                tvTime.text = resource.createTime
                tvNew.isVisible = resource.isRead == false
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CouponGiftGoodViewHolder(
        ItemNoticeBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: NoticeListAdapter.CouponGiftGoodViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }


    fun replaceData(list: ArrayList<NoticeResponse>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<NoticeResponse>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


}