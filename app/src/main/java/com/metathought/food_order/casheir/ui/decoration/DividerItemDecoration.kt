package com.metathought.food_order.casheir.ui.decoration

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class DividerItemDecoration(context: Context) : RecyclerView.ItemDecoration() {
    
    private val paint = Paint().apply {
        color = 0x0F000000 // 黑色透明度6%
        style = Paint.Style.FILL
    }
    
    private val dividerHeight = 1 // 1dp
    
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        
        // 为每个item底部添加分割线空间
        val position = parent.getChildAdapterPosition(view)
        if (position < (parent.adapter?.itemCount ?: 0) - 1) {
            outRect.bottom = dividerHeight
        }
    }
    
    override fun onDraw(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(canvas, parent, state)
        
        val left = parent.paddingLeft
        val right = parent.width - parent.paddingRight
        
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            val position = parent.getChildAdapterPosition(child)
            
            // 不为最后一个item绘制分割线
            if (position < (parent.adapter?.itemCount ?: 0) - 1) {
                val top = child.bottom.toFloat()
                val bottom = top + dividerHeight
                
                canvas.drawRect(left.toFloat(), top, right.toFloat(), bottom, paint)
            }
        }
    }
}