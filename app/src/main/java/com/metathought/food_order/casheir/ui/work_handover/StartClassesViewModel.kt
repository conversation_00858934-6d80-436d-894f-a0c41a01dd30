package com.metathought.food_order.casheir.ui.work_handover

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.SaveOrUpdateOpeningCashRequest
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class StartClassesViewModel @Inject constructor(val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<ApiResponse<BaseBooleanResponse>>()
    val uiState get() = _uiState


    private val _shiftLogtState = MutableLiveData<ApiResponse<BaseResponse<CashRegisterHandoverLogVo>>>()
    val shiftLogtState get() = _shiftLogtState

    fun getShiftLog(){
        viewModelScope.launch {
            _shiftLogtState.value = ApiResponse.Loading
            val response = repository.getShiftLog()
            if (response is ApiResponse.Success) {
                PreferenceDataStoreHelper.getInstance().apply {
                    val model = Gson().fromJson(
                        getFirstPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            ""
                        ), UserLoginResponse::class.java
                    )
                    model.shiftLog = response.data.data
                    this.putPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        model.toJson()
                    )
                    MainDashboardFragment.CURRENT_USER = model
                }
            }
            _shiftLogtState.value = response

        }
    }

    fun saveOrUpdateOpeningCash(
        cashUsd: String?, //备用金(USD)(单位:分)
        cashKhr: String?, //备用金(KHR)(单位:分)
        startShiftRemark: String?, //开班备注
    ) {
        viewModelScope.launch {
            _uiState.value = ApiResponse.Loading
            val openingCashUsd =
                if (cashUsd.isNullOrEmpty()) null else cashUsd.toBigDecimalOrNull()
            val openingCashKhr =
                if (cashKhr.isNullOrEmpty()) null else cashKhr.toBigDecimalOrNull()
            val response = repository.saveOrUpdateOpeningCash(
                SaveOrUpdateOpeningCashRequest(
                    openingCashUsd,
                    openingCashKhr,
                    startShiftRemark
                )
            )
            if (response is ApiResponse.Success) {
                val shiftLog = repository.getShiftLog()
                if (shiftLog is ApiResponse.Success) {
                    PreferenceDataStoreHelper.getInstance().apply {
                        val model = Gson().fromJson(
                            getFirstPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                ""
                            ), UserLoginResponse::class.java
                        )
                        model.shiftLog = shiftLog.data.data
                        this.putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            model.toJson()
                        )
                        MainDashboardFragment.CURRENT_USER = model
                    }

                }
            }
            _uiState.value = response
        }
    }

    fun startClasses(
        cashUsd: String?, //备用金(USD)(单位:分)
        cashKhr: String?, //备用金(KHR)(单位:分)
        startShiftRemark: String?, //开班备注
    ) {
        viewModelScope.launch {
            _uiState.value = ApiResponse.Loading

            val response = repository.startClasses(
                SaveOrUpdateOpeningCashRequest(
                    if (cashUsd.isNullOrEmpty()) null else cashUsd.toBigDecimalOrNull(),
                    if (cashKhr.isNullOrEmpty()) null else cashKhr.toBigDecimalOrNull(),
                    startShiftRemark
                )
            )
            Timber.d("startClasses response: $response")
            if (response is ApiResponse.Success) {
                PreferenceDataStoreHelper.getInstance().apply {
                    val model = Gson().fromJson(
                        getFirstPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            ""
                        ), UserLoginResponse::class.java
                    )
                    //是否需要开班
                    model.isNeedStartShift = false
                    //是否是开班人员
                    model.isShiftEmployee = true
                    model.isNeedChangeShift = true
                    model.openShiftEmployee = model.name
                    this.putPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        model.toJson()
                    )
                    Timber.d("startClasses model: $model")
                    MainDashboardFragment.CURRENT_USER = model
                }
            }
            _uiState.value = response
        }
    }

    data class UIModel(
        val uiState: ApiResponse<BaseBooleanResponse>,
        val reqType: String
    )
}