package com.metathought.food_order.casheir.ui.printer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PrinterManagerViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {

    fun getPrinterInfoList() {
        viewModelScope.launch {
            try {
                PrinterDeviceHelper.getPrinterInfoListFromNet(repository)
            } catch (e: Exception) {

            }
        }
    }
}