package com.metathought.food_order.casheir.database.dao

import com.metathought.food_order.casheir.database.TakeOutPlatformToDiningRecord
import org.litepal.LitePal
import org.litepal.LitePal.count
import org.litepal.extension.findFirst


/**
 *<AUTHOR>
 *@time  2025/3/4
 *@desc
 **/

object TakeOutPlatformToDiningHelper {
    //外卖平台起始索引
    const val BASE_INDEX = 5000
    fun get(takeoutId: String): TakeOutPlatformToDiningRecord? {
        return LitePal.where(
            "takeoutId = ?",
            takeoutId,
        ).findFirst<TakeOutPlatformToDiningRecord>()
    }

    fun getTakeOutPlatformDingStyle(takeoutId: String): Int {
        val model = get(takeoutId)
        var diningStyle: Int? = null
        if (model == null) {
            val totalCount: Int = count(TakeOutPlatformToDiningRecord::class.java)
            val startIndex = BASE_INDEX
            diningStyle = startIndex + totalCount
            TakeOutPlatformToDiningRecord(
                takeoutId = takeoutId,
                diningStyle = diningStyle.toLong(),
            ).save()
        } else {
            diningStyle = model.diningStyle!!.toInt()
        }
        return diningStyle
    }
}