package com.metathought.food_order.casheir.data.model.base.response_model.table


import com.google.gson.annotations.SerializedName

/**
 * 创建临时桌码响应
 * Create a temporary table code response
 */
data class CreateTempTableCodeResponse(
    @SerializedName("expireTime")
    val expireTime: String?,
    @SerializedName("id")
    val id: Long?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("qrCode")
    val qrCode: String?,
    @SerializedName("welcomeSlogans")
    val welcomeSlogans: String?,
    //"临时桌码语言设置: zh，en，km"
    @SerializedName("tempTableCodeLang")
    val tempTableCodeLang: String?,
)