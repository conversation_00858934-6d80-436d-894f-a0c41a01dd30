package com.metathought.food_order.casheir.ui.ordered.anti_settlement

import android.content.Context
import android.widget.Toast
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogAntiSettlementBinding
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.XpopHelper
import com.metathought.food_order.casheir.ui.dialog.BaseCenterDialog
import com.metathought.food_order.casheir.ui.widget.ReasonItem
import com.metathought.food_order.casheir.ui.widget.ReasonSelectionView
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class AntiSettlementDialog
    (private val act: Context?) : BaseCenterDialog(act!!) {

    private var binding: DialogAntiSettlementBinding? = null
    private var onConfirmClickListener: ((OrderedInfoResponse?, Int, String?) -> Unit)? = null

    private var orderInfo: OrderedInfoResponse? = null

    fun showDialog(
        orderInfo: OrderedInfoResponse?,
        onConfirmClickListener: ((OrderedInfoResponse?, Int, String?) -> Unit)? = null,
    ): AntiSettlementDialog {
        this.orderInfo = orderInfo
        this.onConfirmClickListener = onConfirmClickListener

        XPopup.Builder(act)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }

                override fun onDismiss(popupView: BasePopupView?) {
                    XpopHelper.removeToMap(popupView)
                    super.onDismiss(popupView)
                }
            })

            .asCustom(this)
            .show()
        XpopHelper.addToMap(this)
        return this
    }


    // 返回自定义弹窗的布局
    override fun getImplLayoutId(): Int {
        return R.layout.dialog_anti_settlement
    }


    override fun onCreate() {
        super.onCreate()
        binding = DialogAntiSettlementBinding.bind(popupImplView)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {

    }

    private fun initData() {
        orderInfo?.let {
            binding?.apply {
                reasonSelectionView.setSpanCount(2)
                reasonSelectionView.setReasonItems(
                    listOf(
                        ReasonItem(
                            0,
                            context.getString(R.string.anti_settlement_reason_change_good),
                            false
                        ),
                        ReasonItem(
                            1,
                            context.getString(R.string.anti_settlement_reason_cancel_good_after_pay),
                            false
                        ),
                        ReasonItem(
                            2,
                            context.getString(R.string.anti_settlement_reason_chang_payment),
                            false
                        ),
                        ReasonItem(
                            3,
                            context.getString(R.string.anti_settlement_reason_other),
                            true
                        )
                    )
                )

                reasonSelectionView.setOnReasonSelectedListener { reasonType, reason ->
                    btnConfirm.setEnableWithAlpha(reasonSelectionView.isSelectionValid())
                }

                reasonSelectionView.setDefaultSelection(0)

            }
        }
    }

    private fun initListener() {
        binding?.apply {
            // 关闭按钮监听
            topBar.getCloseBtn()?.setOnClickListener {
                // 使用自定义View的隐藏键盘方法
                findViewById<ReasonSelectionView>(R.id.reasonSelectionView)?.hideKeyboard()
                dismiss()
            }


            // 确认按钮监听
            btnConfirm.setOnClickListener {
                val reasonSelectionView =
                    findViewById<ReasonSelectionView>(R.id.reasonSelectionView)

                if (reasonSelectionView?.isSelectionValid() == true) {
                    val selectedReason = reasonSelectionView.getSelectedReason()
                    selectedReason?.let { (reasonType, reason) ->
                        onConfirmClickListener?.invoke(orderInfo, reasonType, reason)
                        reasonSelectionView.hideKeyboard()
                        dismiss()
                    }
                } else {
                    // 显示验证失败提示
//                    val validationMessage = reasonSelectionView?.getValidationMessage()
//                    validationMessage?.let { message ->
//                        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
//                    }
                }
            }
        }
    }


    // 设置最大宽度，看需要而定，
    override fun getMaxWidth(): Int {
        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val screenWidth = (displayMetrics.widthPixels * 0.3).toInt()
                return screenWidth
            }
        }

        return super.getMaxWidth()
    }


}

