//package com.metathought.food_order.casheir.ui.adapter
//
//import android.content.Context
//import android.util.Log
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import android.widget.Toast
//import androidx.core.content.ContextCompat
//import androidx.core.view.isVisible
//import androidx.recyclerview.widget.RecyclerView
//import com.bumptech.glide.Glide
//import com.bumptech.glide.load.engine.DiskCacheStrategy
//import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
//import com.metathought.food_order.casheir.R
//import com.metathought.food_order.casheir.constant.DiningStyleEnum
//import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
//import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
//import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderGoods
//import com.metathought.food_order.casheir.data.model.base.response_model.order.OrderInfoTypeModel
//import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
//import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
//import com.metathought.food_order.casheir.database.dao.GoodsHelper
//import com.metathought.food_order.casheir.databinding.ItemOrderInfoTypeBinding
//import com.metathought.food_order.casheir.databinding.ItemReceiveOrderInfoTypeBinding
//import com.metathought.food_order.casheir.databinding.MenuItemBinding
//import com.metathought.food_order.casheir.databinding.StickerHeaderItemBinding
//import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
//import com.metathought.food_order.casheir.extension.setVisibleInvisible
//import com.metathought.food_order.casheir.ui.ordered.weight.EditWeightDialog
//import timber.log.Timber
//
//
//class OrderInfoTypeMenuAdapter(
//    val list: ArrayList<OrderInfoTypeModel>,
//    val context: Context,
//    val onEditWeightClick: (OrderedGoods) -> Unit,
//) : RecyclerView.Adapter<OrderInfoTypeMenuAdapter.Companion.BaseViewHolder<*>>() {
//
//    private lateinit var orderInfoBinding: ItemOrderInfoTypeBinding
//    private lateinit var receivingInfoBinding: ItemReceiveOrderInfoTypeBinding
//
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<*> {
//        return when (viewType) {
//            ViewType.ORDER_INFO.ordinal -> {
//                orderInfoBinding = ItemOrderInfoTypeBinding.inflate(
//                    LayoutInflater.from(parent.context),
//                    parent,
//                    false
//                )
//                OrderInfoViewHolder(orderInfoBinding)
//            }
//
//            ViewType.RECEIVE_ORDER_INFO.ordinal -> {
//                receivingInfoBinding =
//                    ItemReceiveOrderInfoTypeBinding.inflate(
//                        LayoutInflater.from(parent.context),
//                        parent,
//                        false
//                    )
//                ReceivingInfoViewHolder(receivingInfoBinding)
//            }
//
//            else -> throw IllegalArgumentException("Not a layout")
//        }
//    }
//
//    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
//        when (holder) {
//            is OrderInfoViewHolder -> {
//                val data = list[position]
//                holder.binding.apply {
//                    val adapter = OrderedInfoAdapter(arrayListOf()) { index, item ->
//                        onEditWeightClick.invoke(item)
//                    }
//                    orderedInfoRecyclerView.adapter = adapter
//                    adapter.replaceData(
//                        data.orderInfo.goods,
//                        data.orderInfo.refundGoodsJson,
//                        data.orderInfo
//                    )
//                }
//            }
//
//            is ReceivingInfoViewHolder -> {
//
//
//            }
//        }
//    }
//
//
//    override fun onBindViewHolder(
//        holder: BaseViewHolder<*>,
//        position: Int,
//        payloads: MutableList<Any>
//    ) {
//        if (payloads.isEmpty()) {
//            super.onBindViewHolder(holder, position, payloads)
//        } else {
//            when (holder) {
//                is OrderInfoViewHolder -> {
//
//
//                }
//
//                is ReceivingInfoViewHolder -> {
//
//
//                }
//            }
//        }
//
//    }
//
//
//    override fun getItemViewType(position: Int): Int {
//        return when (list[position].isOrderType) {
//            true -> ViewType.ORDER_INFO.ordinal
//            false -> ViewType.RECEIVE_ORDER_INFO.ordinal
//            else -> {
//                ViewType.OTHER.ordinal
//            }
//        }
//    }
//
//    override fun getItemCount(): Int {
//        return list.size
//    }
//
//    fun updateItems(newItems: ArrayList<OrderInfoTypeModel>) {
//        list.clear()
//        list.addAll(newItems)
//        notifyDataSetChanged()
//    }
//
//    fun replaceData(
//        newData: ArrayList<OrderInfoTypeModel>?,
//    ) {
//        if (newData != null) {
//            this.list.clear()
//            this.list.addAll(newData)
//            notifyDataSetChanged()
//        }
//    }
//
//    inner class OrderInfoViewHolder(val binding: ItemOrderInfoTypeBinding) :
//        BaseViewHolder<ItemOrderInfoTypeBinding>(binding.root) {
//
//    }
//
//    inner class ReceivingInfoViewHolder(val binding: ItemReceiveOrderInfoTypeBinding) :
//        BaseViewHolder<ItemReceiveOrderInfoTypeBinding>(binding.root) {
//
////        fun bind(resource: Goods?, indexBind: Int) {
////
////        }
//    }
//
//
//    companion object {
//        abstract class BaseViewHolder<T>(view: View) : RecyclerView.ViewHolder(view)
//    }
//
//    enum class ViewType {
//        ORDER_INFO,
//        RECEIVE_ORDER_INFO,
//        OTHER
//    }
//}