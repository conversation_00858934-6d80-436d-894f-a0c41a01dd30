package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.WholeDiscountType
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.databinding.ItemPreconfigurationDiscountListBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber

/**
 * Preconfiguration discount list adapter
 *
 * @property list
 * @property isShowVip
 * @property orderInfo
 * @constructor 预配置
 */

class PreconfigurationDiscountListAdapter(
    var list: List<DiscountReduceInfo>,
    var onClickListener: ((DiscountReduceInfo) -> Unit)? = null
) : RecyclerView.Adapter<PreconfigurationDiscountListAdapter.CouponGiftGoodViewHolder>() {

    private var selectItem: DiscountReduceInfo? = null

    inner class CouponGiftGoodViewHolder(val binding: ItemPreconfigurationDiscountListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        if (it != -1) {
                            onClickListener?.invoke(list[it])
                        }
                    }
                }

            }
        }

        fun bind(resource: DiscountReduceInfo) {
            binding.apply {

                checkbox.isSelected = selectItem?.id == resource.id

                tvName.text = resource.name
                tvMaxDiscount.isVisible = resource.reduceAmountLimit != null
                tvMaxDiscount.text =
                    "${itemView.context.getString(R.string.limit_max)}${
                        resource.reduceAmountLimit?.setScale(
                            2
                        )?.priceFormatTwoDigitZero2()
                    }"

                tvLimitDesc.isVisible = resource.thresholdAmount != null
                tvLimitDesc.text =
                    "${
                        itemView.context.getString(
                            R.string.full_price_available,
                            "${resource.thresholdAmount?.setScale(2)?.priceFormatTwoDigitZero2()}"
                        )
                    }"

                if (resource.type == WholeDiscountType.FIXED_AMOUNT.id) {
                    tvAmount.text =
                        "${resource.reduceRateAmount?.setScale(2)?.decimalFormatTwoDigitZero()}"
                    tvUnit.isVisible = true
                    tvPercent.isVisible = false
                } else if (resource.type == WholeDiscountType.PERCENTAGE.id) {
                    tvAmount.text =
                        "${resource.reduceRateAmount?.setScale(2)?.decimalFormatTwoDigitZero()}"

                    tvPercent.isVisible = true
                    tvUnit.isVisible = false
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CouponGiftGoodViewHolder(
        ItemPreconfigurationDiscountListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PreconfigurationDiscountListAdapter.CouponGiftGoodViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

    fun updateData(datas: List<DiscountReduceInfo>) {
        list = datas
        notifyDataSetChanged()
    }

    fun setSelectItem(data: DiscountReduceInfo?) {
        if (selectItem?.id == data?.id) {
            selectItem = null
        } else {
            selectItem = data
        }
        notifyDataSetChanged()
    }
}