package com.metathought.food_order.casheir.ui.ordered.offline

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/5/913:44
 * @description
 */
@HiltViewModel

class OfflineChannelsViewModel @Inject

constructor(private val repository: Repository) : ViewModel() {


    val uiModeState get() = _uiModeState
    private val _uiModeState = MutableLiveData<UIModel>()

    fun getChannels() {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val result = repository.getOfflineChannels()
                emitUIState(result = result)
            } catch (e: Exception) {
                emitUIState(result = ApiResponse.Error(e.message))
            }
        }
    }

    private suspend fun emitUIState(
        result: ApiResponse<OfflineChannelTotalModel>? = null
    ) {
        withContext(Dispatchers.Main) {
            _uiModeState.value = UIModel(result = result)
        }
    }

    data class UIModel(
        val result: ApiResponse<OfflineChannelTotalModel>?,
    )


}