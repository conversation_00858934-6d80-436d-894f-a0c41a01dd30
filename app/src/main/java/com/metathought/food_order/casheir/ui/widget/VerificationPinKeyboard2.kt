package com.metathought.food_order.casheir.ui.widget

import android.content.Context

import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.core.view.isGone
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.ui.widget.keyboard.Keyboard
import com.metathought.food_order.casheir.ui.widget.keyboard.KeyboardView


class VerificationPinKeyboard2 @kotlin.jvm.JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        initialize()
    }

    private var listener: OnKeyPressListener? = null

    private var keyboardView: KeyboardView? = null
//    private var progressBar: ProgressBar? = null


    private fun initialize() {
        inflate(context, R.layout.verification_pin_keyboard_view2, this)
        keyboardView = findViewById(R.id.keyboard_view)
//        progressBar = findViewById(R.id.progressBar)
        keyboardView?.run {
            isPreviewEnabled = false
            keyboard = Keyboard(context, R.xml.pin_keyboard2)

            setOnKeyboardActionListener(object : KeyboardView.OnKeyboardActionListener {
                override fun onPress(primaryCode: Int) {
//                    if (primaryCode == -1000)
//                        return
                    listener?.onKeyPress(primaryCode)
                }

                override fun onRelease(primaryCode: Int) {
                }

                override fun onKey(primaryCode: Int, keyCodes: IntArray?) {
                }

                override fun onText(text: CharSequence?) {
                }

                override fun swipeLeft() {
                }

                override fun swipeRight() {
                }

                override fun swipeDown() {
                }

                override fun swipeUp() {
                }

            })

        }
        displayKeyboard()
    }

    fun setShowKeyBg(showBg:Boolean){
        keyboardView?.isShowBg = showBg
    }

    fun displayKeyboard() {
        keyboardView?.isGone = false
//        progressBar?.isGone = true
    }

    fun displayProgress() {
        keyboardView?.visibility = View.INVISIBLE
//        progressBar?.visibility = View.VISIBLE
    }

    fun setOnKeyPressListener(listener: OnKeyPressListener?) {
        this.listener = listener
    }


    interface OnKeyPressListener {
        fun onKeyPress(keyCode: Int)
    }
}