package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.member.TopUpCanUseCouponRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeTierCouponTemplate
import com.metathought.food_order.casheir.databinding.ItemDialogGiftCouponDetailListBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import timber.log.Timber
import kotlin.math.min


class GiftCouponDetailListAdapter :
    RecyclerView.Adapter<GiftCouponDetailListAdapter.MemberCouponListViewHolder>() {


    private val list: MutableList<RechargeTierCouponTemplate> = mutableListOf()

    inner class MemberCouponListViewHolder(val binding: ItemDialogGiftCouponDetailListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: RechargeTierCouponTemplate?, position: Int) {
            itemView.context.let { context ->
                val couponTemplate = resource?.couponTemplate
                val num = resource?.num ?: 0
                binding.apply {
                    tvCouponNum.text = "x$num"
                    tvAmount.text = couponTemplate?.getDiscountDesc(context)

                    tvLimitAmount.isVisible = couponTemplate?.isThresholdCoupon() == true
                    tvLimitAmount.text = couponTemplate?.getThresholdDesc(context)

                    tvCouponName.text = couponTemplate?.name

                    tvEffectiveTime.text =couponTemplate?.rule?.expiration?.getExpirationDate(context)
                    tvEffectiveTime.isVisible = tvEffectiveTime.text.isNotEmpty()


                    if (couponTemplate?.isGiftGoodCoupon() == true) {
                        tvGiftGoodNum.isVisible = true
                        if (couponTemplate.rule?.discount?.giveGoods.isNullOrEmpty()) {
                            //没有赠送商品的时候
                            tvGiftGoodNum.setEnable(false)
                            tvGiftGoodNum.text =
                                context.getString(R.string.free_item_removed_by_merchant)
                            tvGiftGoodNum.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                null,
                                null
                            )
                        } else {
                            tvGiftGoodNum.setEnable(true)
                            tvGiftGoodNum.text = context.getString(
                                R.string.gift_products_num,
                                "${couponTemplate.rule?.discount?.giveGoods?.size ?: 0}"
                            )
                            //赠送商品
                            var endDrawable = ContextCompat.getDrawable(
                                context,
                                R.drawable.icon_arrow_down
                            )
                            if (resource.isGiftGoodsExpand == true) {
                                endDrawable = ContextCompat.getDrawable(
                                    context,
                                    R.drawable.icon_arrow_up
                                )
                                rvGiftGoods.isVisible = true
                                rvGiftGoods.adapter =
                                    CouponGoodListAdapter(
                                        couponTemplate.rule?.discount?.giveGoods
                                            ?: listOf()
                                    )
                            } else {
                                rvGiftGoods.isVisible = false

                            }
                            tvGiftGoodNum.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                endDrawable,
                                null
                            )
                        }
                    } else {
                        tvGiftGoodNum.isVisible = false
                        rvGiftGoods.isVisible = false
                    }

                    tvGiftGoodNum.setOnClickListener {
                        list[position].isGiftGoodsExpand =
                            !(list[position].isGiftGoodsExpand ?: false)
                        notifyItemChanged(position)
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberCouponListViewHolder {
        val itemView =
            ItemDialogGiftCouponDetailListBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return MemberCouponListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: MemberCouponListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<RechargeTierCouponTemplate>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

}
