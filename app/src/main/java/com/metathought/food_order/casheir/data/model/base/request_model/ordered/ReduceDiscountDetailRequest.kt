package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/7/10
 *@desc
 **/

data class ReduceDiscountDetailRequest(
    val orderNo: String? = null,
    //减免百分比
    val reduceRate: Double? = null,
    //减免金额（美元）
    val reduceDollar: BigDecimal? = null,
    //减免vip金额（美元）
    val reduceVipDollar: BigDecimal? = null,
    //减免金额（khr）
    val reduceKhr: BigDecimal? = null,
    //减免vip金额（khr）
    val reduceVipKhr: BigDecimal? = null,

    //减免类型：0：discount(正常折扣) 1:void(菜品有问题时原因必填写)
    val discountType: Int? = null,
    //原因
    var reduceReason: String? = null,
    //类型@Schema(description = "类型 0:默认 1.销售价 2.会员 3:销售价+会员价 4.配置折扣活动 5.USD自定义整单减免 6.KHR自定义整单减免")
    val reduceType: Int? = null,

    //折扣id
    val discountReduceActivityId: String? = null,

    //优惠券id
    val couponId: Long? = null,

    )