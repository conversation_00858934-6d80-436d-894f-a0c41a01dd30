package com.metathought.food_order.casheir.data.model.base.response_model.pending


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Customer
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoodJson

data class PendingRecord(
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("customerJson")
    val customerJson: String?,
    @SerializedName("goodsJson")
    val goodsJson: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("note")
    val note: String?,
    @SerializedName("saasStoreId")
    val saasStoreId: String?,
    @SerializedName("serialNumber")
    val serialNumber: String?,
    @SerializedName("storeId")
    val storeId: String?,
    @SerializedName("totalNum")
    val totalNum: Int?,
    @SerializedName("totalPrice")
    val totalPrice: Long?,
    @SerializedName("type")
    val type: Int?,
    @SerializedName("updateTime")
    val updateTime: String?,
    @SerializedName("isPreOrder")
    val isPreOrder: Boolean?,
    @Transient
    var isSelect: Boolean?,

    /**
     *  有效优惠活动列表
     */
    @SerializedName("couponActivityList")
    var couponActivityList: List<CouponActivityModel>?,

    /**
     *  有效优惠活动 优惠现价金额
     */
    @SerializedName("couponActivityAmount")
    var couponActivityAmount: Long?,

    /**
     *  有效优惠活动 优惠会员金额
     */
    @SerializedName("couponActivityVipAmount")
    var couponActivityVipAmount: Long?,

    /**
     * 订单备注
     */
    @SerializedName("orderNote")
    val orderNote: String?,

) {
    private var pendingGoodJson: OrderedGoodJson? = null
    fun getPendingGoodJson(): OrderedGoodJson? {
        if (pendingGoodJson == null && !goodsJson.isNullOrEmpty()) {
            pendingGoodJson =
                MyApplication.globalGson.fromJson(goodsJson, OrderedGoodJson::class.java)
        }
        return pendingGoodJson
    }

    private var pendingCustomerJson: Customer? = null
    fun getCustomerJson(): Customer? {
        if (pendingCustomerJson == null && !customerJson.isNullOrEmpty()) {
            pendingCustomerJson =
                MyApplication.globalGson.fromJson(customerJson, Customer::class.java)
        }
        return pendingCustomerJson
    }

    /**
     * 是否有待定价商品
     *
     * @return
     */
    fun isHasNeedProcess(): Boolean {
        var hasUnProcess = false
        //判断是否含有 待定价商品
        getPendingGoodJson()?.goodsList?.forEach {
            if (!it.isHasProcessed()) {
                hasUnProcess = true
            }
        }
        return hasUnProcess
    }

    /**
     * 获取优惠活动vip 优惠金额
     *
     * @return
     */
     fun getTotalVipCouponActivityAmount(): Long {
        var price = 0L
        couponActivityList?.forEach {
            price += (it.activityVipCouponAmount ?: 0L)
        }
        return price
    }

    /**
     * 获取优惠活动现价 优惠金额
     *
     * @return
     */
     fun getTotalCouponActivityAmount(): Long {
        var price = 0L
        couponActivityList?.forEach {
            price += (it.activityCouponAmount ?: 0L)
        }
        return price
    }

}