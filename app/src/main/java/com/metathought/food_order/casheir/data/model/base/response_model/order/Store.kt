package com.metathought.food_order.casheir.data.model.base.response_model.order

data class Store(
    val address: String,
    val area: String,
    val city: String,
    val configType: Int,
    val createTime: String,
    val diningStyle: Int,
    val disableStatus: Int,
    val endTime: String,
    val id: String,
    val isPaymentInAdvance: Boolean,
    val isTableService: Boolean,
    val isTempTableCode: Boolean,
    val kioskId: Any,
    val lat: String,
    val latlng: String,
    val lng: String,
    val mainStoreId: String,
    val name: String,
    val note: String,
    val pageViewId: String,
    val province: String,
    val saasStoreId: String,
    val vatPercentage: Int,
    val serviceChargePercentage: Int,
    val startTime: String,
    val status: Int,
    val storeBalancePassword: String,
    val storeBalancePercentage: String,
    val telephons: String,
    val tempTableCodeExpireDays: Int,
    val type: Int,
    val updateTime: String,
    val url: String
)