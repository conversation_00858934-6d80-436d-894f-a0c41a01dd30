package com.metathought.food_order.casheir.utils

import android.content.Context

/**
 * 列宽计算工具类
 * 确保所有适配器使用相同的计算逻辑
 */
object ColumnWidthCalculator {
    
    /**
     * 计算动态列宽
     * @param context 上下文
     * @param areaWidth 区域宽度（像素）
     * @param dataCount 数据数量
     * @param minWidth 最小宽度（dp）
     * @return 计算后的列宽（dp）
     */
    fun calculateColumnWidth(
        context: Context,
        areaWidth: Int,
        dataCount: Int,
        minWidth: Float = 110f
    ): Float {
        if (dataCount <= 0 || areaWidth <= 0) {
            return minWidth
        }
        
        // 由于去掉了 margin，现在没有间距，直接平分宽度
        val columnWidthPx = areaWidth / dataCount
        val calculatedWidth = DisplayUtils.px2dp(context, columnWidthPx.toFloat()).toFloat()
        
        // 设置最小宽度
        return maxOf(calculatedWidth, minWidth)
    }
    
    /**
     * 打印计算过程（用于调试）
     */
    fun logCalculation(
        tag: String,
        dataCount: Int,
        areaWidth: Int,
        calculatedWidth: Float,
        finalWidth: Float
    ) {
        println("$tag 列宽计算: 数据数量=$dataCount, 区域宽度=${areaWidth}px, 计算宽度=${calculatedWidth}dp, 最终宽度=${finalWidth}dp")
    }
}
