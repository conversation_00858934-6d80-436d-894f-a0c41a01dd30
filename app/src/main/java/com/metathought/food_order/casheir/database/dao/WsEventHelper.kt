package com.metathought.food_order.casheir.database.dao

import com.metathought.food_order.casheir.database.WsEventRecord
import org.litepal.LitePal
import org.litepal.extension.findFirst
import timber.log.Timber

object WsEventHelper {
    fun get(eventId: String): WsEventRecord? {
        return LitePal.where("eventId = ?", eventId).findFirst<WsEventRecord>()
    }

    /**
     * 删除过期的记录
     * @param expireDay 过期天数
     */
    fun delExpireRecords(expireDay: Int) {
        LitePal.deleteAll(
            WsEventRecord::class.java,
            "updateTime < ?",
            (System.currentTimeMillis() - 1000 * 60 * 60 * 24 * expireDay).toString()
        )
    }

    fun getWsEventRecordIds(map: List<String?>): List<String>? {
        val sql = String.format("eventid in (%s)", map.joinToString(","))
        Timber.d("sql: %s", sql)
        return LitePal.select("eventid").where(sql)
            .find(WsEventRecord::class.java)?.mapNotNull { it.eventId }
    }
}