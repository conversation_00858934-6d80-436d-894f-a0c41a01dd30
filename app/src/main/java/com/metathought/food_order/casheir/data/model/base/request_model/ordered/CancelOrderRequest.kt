package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName

data class CancelOrderRequest(
    @SerializedName("orderId")
    val orderId: String? = null,
    @SerializedName("reasonType")
    val reasonType: Int? = null,
    @SerializedName("cancelReason")
    val cancelReason: String?,
    @SerializedName("autoInStock")
    val autoInStock: Boolean?,
)