package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName

data class AcceptOrderListRequest(
    /**
     * 索会员手机号或者是顾客手机号
     */
    @SerializedName("keyword")
    val keyword: String? = null,
    @SerializedName("page")
    val page: Int,
    @SerializedName("pageSize")
    val pageSize: Int = 50,
    /**
     * 选择的桌子ids
     */
    @SerializedName("tableUuids")
    var tableUuids: List<String>? = null,
    /**
     * 0待接单1已接单2已拒绝
     */
    @SerializedName("acceptStatus")
    var acceptStatus: Int? = null,
    @SerializedName("isGrab")
    var isGrab: Boolean? = null,
)