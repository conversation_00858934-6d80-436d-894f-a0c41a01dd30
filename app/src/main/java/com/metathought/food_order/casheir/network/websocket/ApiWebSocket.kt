package com.metathought.food_order.casheir.network.websocket

import android.os.Build
import com.google.gson.Gson
import com.metathought.food_order.casheir.BuildConfig
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.tinder.scarlet.Lifecycle
import com.tinder.scarlet.Scarlet
import com.tinder.scarlet.lifecycle.android.AndroidLifecycle
import com.tinder.scarlet.retry.LinearBackoffStrategy
import com.tinder.scarlet.streamadapter.rxjava2.RxJava2StreamAdapterFactory
import com.tinder.scarlet.websocket.okhttp.newWebSocketFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import timber.log.Timber
import java.time.Duration
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ApiWebSocket {
    @Singleton
    @Provides
    fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .pingInterval(30, TimeUnit.SECONDS)
//            .addInterceptor(HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BASIC))
            .build()
    }

    @Singleton
    @Provides
    fun provideSocketApi(lifecycle: Lifecycle): WebSocketApi {
        val token = MainDashboardFragment.CURRENT_USER?.token ?: ""
        Timber.e("2222222  ${MainDashboardFragment.CURRENT_USER?.token}")
        val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
        val deviceSn = Build.SERIAL
        var url =
            "${BuildConfig.BASE_WS}?token=${token}&lang=${lang}&clientType=7&version=${BuildConfig.VERSION_NAME}"
        if (!deviceSn.isNullOrEmpty()) {
            url = "${url}&deviceSn=${deviceSn}"
        } else {
            url = "${url}&deviceSn=unknown"
        }
        Timber.d("ApiWebSocket 连接的Url${url}")
        return Scarlet.Builder()
            .backoffStrategy(LinearBackoffStrategy(1000))
            .lifecycle(lifecycle = lifecycle)
            .webSocketFactory(
                provideOkHttpClient().newWebSocketFactory(
                    url
                )
            )
            .addStreamAdapterFactory(provideStreamAdapterFactory())
            .build()
            .create<WebSocketApi>()
    }

    @Singleton
    @Provides
    fun provideGsonInstance(): Gson {
        return Gson()
    }

    @Singleton
    @Provides
    fun provideStreamAdapterFactory(): RxJava2StreamAdapterFactory {
        return RxJava2StreamAdapterFactory()
    }

    @Singleton
    @Provides
    fun provideStreamAdapterLifeCycle(): Lifecycle {
        return AndroidLifecycle.ofApplicationForeground(MyApplication.myAppInstance)
    }
}