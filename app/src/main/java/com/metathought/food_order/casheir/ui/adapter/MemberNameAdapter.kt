package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.MemberNameItemBinding

/**
 * 会员昵称适配器 - 固定列
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class MemberNameAdapter(
    val list: ArrayList<Record>
) : RecyclerView.Adapter<MemberNameAdapter.NameViewHolder>() {

    inner class NameViewHolder(val binding: MemberNameItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: Record, position: Int) {
            itemView.context.let { context ->
                binding.apply {
                    tvAccountName.text = resource.nickName
                    tvAccountName.text =
                        if (!resource.nickName.isNullOrEmpty()) resource.nickName else context.getString(
                            R.string.n_a
                        )
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NameViewHolder {
        val itemView = MemberNameItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return NameViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: NameViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }

    fun replaceData(newList: ArrayList<Record>?) {
        if (newList != null) {
            this.list.clear()
            this.list.addAll(newList)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<Record>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }
}
