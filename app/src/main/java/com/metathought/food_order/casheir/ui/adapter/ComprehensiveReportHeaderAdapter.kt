package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.Header

/**
 * 综合报表字段名称适配器 - 垂直滑动显示字段名称列表
 */
class ComprehensiveReportHeaderAdapter(
    private var headerList: ArrayList<Header>,
    private val context: Context,
    private val onItemClickListener: ((ImageView, String) -> Unit)? = null  // item点击回调，传递infoButton和describe
) : RecyclerView.Adapter<ComprehensiveReportHeaderAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_comprehensive_report_header, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(headerList[position], onItemClickListener)
    }

    override fun getItemCount(): Int = headerList.size

    fun replaceData(newHeaders: ArrayList<Header>) {
        headerList.clear()
        headerList.addAll(newHeaders)
        notifyDataSetChanged()
    }

    class ViewHolder(
        itemView: android.view.View
    ) : RecyclerView.ViewHolder(itemView) {
        
        private val container: ConstraintLayout = itemView as ConstraintLayout
        private val textView: TextView = itemView.findViewById(R.id.tv_header_text)
        private val infoButton: ImageView = itemView.findViewById(R.id.iv_info_button)
        
        fun bind(header: Header, onItemClickListener: ((ImageView, String) -> Unit)?) {
            textView.text = header.value
            container.setBackgroundResource(if (header.isHighlight) R.color.color_efefef else 0)
            
            // 如果 describe 有值，显示感叹号按钮并设置整个item可点击
            if (!header.describe.isNullOrEmpty()) {
                infoButton.visibility = android.view.View.VISIBLE
                
                // 设置整个item的点击事件，但不添加视觉效果
                container.setOnClickListener {
                    onItemClickListener?.invoke(infoButton, header.describe!!)
                }
                
            } else {
                infoButton.visibility = android.view.View.GONE
                container.setOnClickListener(null)
            }
        }
    }
}
