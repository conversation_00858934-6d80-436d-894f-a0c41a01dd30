package com.metathought.food_order.casheir.data.model.base.request_model


import com.google.gson.annotations.SerializedName

data class AddOrderMoreGoodRequest(
    @SerializedName("goodsList")
    val goodsList: List<GoodsBo?>?,
    @SerializedName("isPosPrint")
    val isPosPrint: Boolean?,
    @SerializedName("note")
    val note: String?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("isValid")
    val isValid: Boolean?
)