package com.metathought.food_order.casheir.ui.second_display.payment

import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import androidx.core.view.isVisible
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.SecondPaymentLayoutBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.order.payment.PaymentQrDialog
import kh.org.nbc.bakong_khqr.BakongKHQR
import java.nio.charset.Charset

/**
 * KHQR付款 副屏
 * KHQR Payment: second-screen
 * <AUTHOR>
 * @date 2024/5/1314:31
 * @description
 */
class PaymentScreenUI(private var mContext: Context, display: Display) : Presentation(mContext, display) {

    private lateinit var binding: SecondPaymentLayoutBinding
    var paymentResponse: PaymentResponse? = null
    var orderedInfo: OrderedInfoResponse? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = SecondPaymentLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.apply {
            if (paymentResponse != null) {
                clPaymentLayout.isVisible = true
                clPaymentResultLayout.isVisible = false
                val decode = BakongKHQR.decode(paymentResponse?.qrcode ?: "")
                val amountStr = "$${decode.data.transactionAmount}"
                imgQR.setImageDrawable(
                    QrCodeDrawable(
                        QrData.Url(paymentResponse?.qrcode ?: ""),
                        PaymentQrDialog.getQROption(R.mipmap.icon_qr_bakong, mContext),
                        Charset.forName("UTF-8")
                    )
                )
                tvScanQRAmount.text = amountStr
                tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
            }
            orderedInfo?.let { orderedInfo ->
                clPaymentLayout.isVisible = false
                clPaymentResultLayout.isVisible = true
                tvTableName.text = orderedInfo.tableName
                tvFoodCount.text = "${orderedInfo.goods?.size}"
                tvSubtotal.text = orderedInfo.getSubTotal().priceFormatTwoDigitZero2()
                tvVat.text = "$${orderedInfo?.getRealVatPrice()?.priceFormatTwoDigitZero()}"
                tvDiscountReduction.text = "$0"
                tvReceivable.text = orderedInfo.totalPrice?.priceFormatTwoDigitZero2()
                tvTotalPrice.text = orderedInfo.totalPrice?.priceFormatTwoDigitZero()
            }
        }
        initResource()
    }

    private fun initResource(){
        binding.run {
            tvInfo.text=mContext.getString(R.string.plz_scan_qr_pay_before_expires)
            tableName.text=mContext.getString(R.string.table_name)
            numberProducts.text=mContext.getString(R.string.number_products)
            subtotal.text=mContext.getString(R.string.subtotal)
            vat.text=mContext.getString(R.string.vat)
            discount.text=mContext.getString(R.string.discount_reduction)
            receivable.text=mContext.getString(R.string.receivable)
            paymentResult.text=mContext.getString(R.string.payment_successfully)
        }
    }

    fun updateTime(time: String) {
        binding.tvDuration.text = time
    }



}