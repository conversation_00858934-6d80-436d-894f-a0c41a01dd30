package com.metathought.food_order.casheir.helper

import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Handler
import android.text.InputType
import android.text.TextUtils
import android.text.method.DigitsKeyListener
import android.view.*
import android.widget.EditText
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.metathought.food_order.casheir.R
import java.io.File

object DialogHelper {

    //    fun showSimpleDialog(
//        context: Context,
//        message: CharSequence,
//        onPositiveClickListener: DialogInterface.OnClickListener?,
//    ): AlertDialog {
//        return showSimpleDialog(
//            context,
//            message,
//            context.getString(R.string.confirm2),
//            onPositiveClickListener,
//            context.getString(
//                R.string.cancel
//            ),
//            null,
//            false,
//            true
//        )
//    }
//
    fun showSimpleDialog(
        context: Context,
        message: CharSequence,
        onPositiveClickListener: DialogInterface.OnClickListener?,
        isCancel: Boolean = true
    ): AlertDialog {
        return showSimpleDialog(
            context,
            message,
            context.getString(R.string.confirm2),
            onPositiveClickListener,
            context.getString(R.string.cancel),
            null,
            false,
            isCancel
        )
    }

//    fun showSimpleDialog(
//        context: Context,
//        message: CharSequence,
//        confirmText: CharSequence,
//        onPositiveClickListener: DialogInterface.OnClickListener?
//    ): AlertDialog {
//        return showSimpleDialog(
//            context,
//            message,
//            confirmText,
//            onPositiveClickListener,
//            null,
//            null,
//            false,
//            false
//        )
}

fun showSimpleDialog(
    context: Context,
    message: CharSequence,
    confirmText: CharSequence,
    onPositiveClickListener: DialogInterface.OnClickListener?,
    cancelText: CharSequence?,
    onNegativeClickListener: DialogInterface.OnClickListener?,
    isOver: Boolean? = false,
    isCancel: Boolean = true
): AlertDialog {
    return showSimpleDialog(
        context, null, null, message, confirmText,
        onPositiveClickListener, cancelText,
        Color.LTGRAY, onNegativeClickListener, isOver, isCancel
    )
}

//    fun showSimpleDialog(
//        context: Context,
//        title: CharSequence?,
//        icon: Drawable?,
//        message: CharSequence?,
//        confirmText: CharSequence,
//        onPositiveClickListener: DialogInterface.OnClickListener?,
//        cancelText: CharSequence?,
//        onNegativeClickListener: DialogInterface.OnClickListener?,
//        isCancel: Boolean = true
//    ): AlertDialog {
//        return showSimpleDialog(
//            context, title, icon, message, confirmText,
//            onPositiveClickListener, cancelText,
//            Color.LTGRAY, onNegativeClickListener, false, isCancel
//        )
//    }

fun showSimpleDialog(
    context: Context,
    title: CharSequence?,
    icon: Drawable?,
    message: CharSequence?,
    confirmText: CharSequence,
    onPositiveClickListener: DialogInterface.OnClickListener?,
    cancelText: CharSequence?,
    cancelColor: Int,
    onNegativeClickListener: DialogInterface.OnClickListener?,
    isOver: Boolean? = false,
    isCancel: Boolean = true
): AlertDialog {

    val builder = AlertDialog.Builder(context, R.style.MyDialogTheme)
    if (!TextUtils.isEmpty(title)) {
        builder.setTitle(title)
    }

    if (icon != null) {
        builder.setIcon(icon)
    }

    builder.setMessage(message)
        .setPositiveButton(confirmText, onPositiveClickListener)
        .setCancelable(false)

    if (isCancel) {
        if (cancelText != null) {
            builder.setNegativeButton(cancelText, onNegativeClickListener)
        }
    } else {
        builder.setNegativeButton(null, null)
    }
    val dialog = builder.create()
    if (isOver == true) {
        if (Build.VERSION.SDK_INT >= 26) {//8.0新特性
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
        } else {
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
        }
    }

    dialog.show()
    try {
        dialog.getButton(DialogInterface.BUTTON_NEGATIVE).isAllCaps = false;
        dialog.getButton(DialogInterface.BUTTON_POSITIVE).isAllCaps = false;
    } catch (e: Exception) {

    }

    setDialogDefaultColor(dialog)
    return dialog
}


fun showDoubleChooseDialog(
    context: Context, title: String, message: String,
    firstChoose: String, secondChoose: String, cancel: String,
    firstChooseClickListener: DialogInterface.OnClickListener,
    secondChooseClickListener: DialogInterface.OnClickListener,
    cancelClickListener: DialogInterface.OnClickListener
): AlertDialog {
    val dialog = AlertDialog.Builder(context)
        .setTitle(title)
        .setMessage(message)
        .setPositiveButton(firstChoose, firstChooseClickListener)
        .setNegativeButton(secondChoose, secondChooseClickListener)
        .setNeutralButton(cancel, cancelClickListener)
        .create()

    dialog.show()

    setDialogButtonColor(dialog, AlertDialog.BUTTON_POSITIVE, Color.DKGRAY)
    setDialogButtonColor(dialog, AlertDialog.BUTTON_NEGATIVE, Color.DKGRAY)
    setDialogButtonColor(dialog, AlertDialog.BUTTON_NEUTRAL, Color.LTGRAY)

    return dialog
}


fun showItemsChooseDialog(
    context: Context,
    title: String?,
    items: Array<String>,
    onChooseClickListener: DialogInterface.OnClickListener
): AlertDialog {
    return AlertDialog.Builder(context)
        .setTitle(title)
        .setItems(items, onChooseClickListener)
        .create()
}

fun showItemsChooseDialog(
    context: Context,
    items: Array<String>,
    onChooseClickListener: DialogInterface.OnClickListener
): AlertDialog {
    return AlertDialog.Builder(context)
        .setItems(items, onChooseClickListener)
        .create()
}

//    fun showEditTextDialog(
//        context: Context,
//        title: CharSequence?,
//        positiveClickListener: OnEditTextDialogPositiveClickListener?
//    ) {
//        showEditTextDialog(
//            context,
//            title,
//            null,
//            context.getString(R.string.GeneralBottomPopupConfirm),
//            context.getString(R.string.GeneralBottomPopupCancel),
//            null,
//            null,
//            InputType.TYPE_CLASS_TEXT,
//            null,
//            positiveClickListener,
//            null
//        )
//    }
//
//    fun showEditTextDialog(
//        context: Context,
//        title: CharSequence?,
//        message: CharSequence?,
//        positiveText: String,
//        negativeText: String,
//        content: String? = null,
//        hint: String? = null,
//        inputType: Int = InputType.TYPE_CLASS_TEXT,
//        digits: CharSequence? = null,
//        positiveClickListener: OnEditTextDialogPositiveClickListener?,
//        negativeClickListener: DialogInterface.OnClickListener?
//    ): AlertDialog {
//        var negativeClickListener = negativeClickListener
//        val dialog: AlertDialog
//        val builder = AlertDialog.Builder(context)
//        builder.setCancelable(false)
//        val editText =
//            LayoutInflater.from(context).inflate(R.layout.widget_edit_text, null, false) as EditText
//        editText.isFocusable = true
//        editText.isFocusableInTouchMode = true
//
//        editText.setText(content)
//        editText.hint = hint
//        editText.textSize = 14f
//        editText.setSelectAllOnFocus(true)
////        editText.inputType = inputType
//        editText.layoutParams = ViewGroup.LayoutParams(
//            ViewGroup.LayoutParams.MATCH_PARENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        if (digits != null) {
//            editText.keyListener = DigitsKeyListener.getInstance(digits.toString())
//        }
//        val layout = LinearLayout(context)
//        layout.layoutParams = LinearLayout.LayoutParams(
//            ViewGroup.LayoutParams.MATCH_PARENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//
//        layout.setPadding(80, 20, 80, 0)
//        layout.addView(editText)
//
//        if (!TextUtils.isEmpty(title)) {
//            builder.setTitle(title)
//        }
//
//        if (!TextUtils.isEmpty(message)) {
//            builder.setMessage(message)
//        }
//
//        if (negativeClickListener == null) {
//            negativeClickListener = DialogInterface.OnClickListener { dialog, _ ->
//                SoftInputHelper.closeSoftKeyboard(editText)
//                dialog.dismiss()
//            }
//        }
//
//        builder.setView(layout)
//            .setPositiveButton(positiveText, null)
//            .setNegativeButton(negativeText, null)
//
//        dialog = builder.create()
//        dialog.show()
//
//        dialog.getButton(AlertDialog.BUTTON_POSITIVE).apply {
//            setOnClickListener {
//                SoftInputHelper.closeSoftKeyboard(editText)
//                positiveClickListener?.onClick(dialog, editText.text.toString())
//            }
//
//        }
//        dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setOnClickListener {
//            negativeClickListener.onClick(dialog, DialogInterface.BUTTON_NEGATIVE)
//        }
//
//        setDialogDefaultColor(dialog)
//        Handler().postDelayed({
//            SoftInputHelper.openSoftKeyboard(editText)
//        }, 100)
//        return dialog
//    }

//    fun showLongEditTextDialog(
//        context: Context,
//        message: CharSequence?,
//        listener: OnEditTextDialogPositiveClickListener
//    ): AlertDialog {
//        val etReason = ClearEditText(context)
//        val layout = LinearLayout(context)
//        layout.layoutParams = ViewGroup.LayoutParams(
//            DisplayUtils.dp2px(context, 180F),
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        etReason.layoutParams = ViewGroup.LayoutParams(
//            ViewGroup.LayoutParams.MATCH_PARENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        etReason.textSize = 14F
//        val padding = DisplayUtils.dp2px(context, 16F)
//        val paddingTop = DisplayUtils.dp2px(context, 10F)
//        layout.setPadding(padding * 2, paddingTop, padding * 2, paddingTop)
//        etReason.setPadding(padding, paddingTop, padding, paddingTop)
//        etReason.gravity = Gravity.TOP
//        etReason.background = ContextCompat.getDrawable(context, R.drawable.bg_white_bound_gray_4dp)
//        etReason.maxLines = 3
//        etReason.minLines = 3
//        layout.addView(etReason)
//        val dialog = AlertDialog.Builder(context)
//            .setMessage(message)
//            .setPositiveButton(R.string.GeneralBottomPopupConfirm) { dialogInterface, _ ->
//                listener.onClick(dialogInterface, etReason.text.toString())
//            }
//            .setNegativeButton(R.string.GeneralBottomPopupCancel, null)
//            .setView(layout)
//            .create()
//        dialog.show()
//        return dialog
//    }


private fun setDialogButtonColor(dialog: AlertDialog, button: Int?, color: Int): AlertDialog {
    dialog.getButton(button ?: return dialog).setTextColor(color)
    return dialog
}

private fun setDialogDefaultColor(dialog: AlertDialog) {
    setDialogButtonColor(
        dialog,
        AlertDialog.BUTTON_POSITIVE,
        dialog.context.getColor(R.color.black)
    )
    setDialogButtonColor(dialog, AlertDialog.BUTTON_NEGATIVE, Color.LTGRAY)
}
//
//    fun setPositiveButtonColor(dialog: AlertDialog, color: Int): AlertDialog {
//        return setDialogButtonColor(dialog, AlertDialog.BUTTON_POSITIVE, color)
//    }
//
//    fun setNegativeButtonColor(dialog: AlertDialog, color: Int): AlertDialog {
//        return setDialogButtonColor(dialog, AlertDialog.BUTTON_NEGATIVE, color)
//    }
//
//    interface OnEditTextDialogPositiveClickListener {
//        fun onClick(dialog: DialogInterface, text: String)
//    }

