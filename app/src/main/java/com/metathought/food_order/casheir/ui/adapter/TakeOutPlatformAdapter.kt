package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.SingleDiscountItemBinding
import com.metathought.food_order.casheir.databinding.TakeOutPlatformItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber


class TakeOutPlatformAdapter(
    val onItemClickListener: ((TakeOutPlatformModel) -> Unit)? = null
) : RecyclerView.Adapter<TakeOutPlatformAdapter.TakeOutPlatformHolder>() {

    private val list = mutableListOf<TakeOutPlatformModel>()

    private var selectItem: TakeOutPlatformModel? = null

    inner class TakeOutPlatformHolder(val binding: TakeOutPlatformItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    if (it != -1) {
                        setSelectItem(list[it])
                        onItemClickListener?.invoke(list[it])
                    }
                }
            }
        }

        fun bind(resource: TakeOutPlatformModel) {
            Timber.d("Binding ${resource.toJson()}")
            binding?.apply {
                tvName.text = resource.name
                tvName.isSelected = selectItem?.id == resource.id
            }
        }
    }


    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = TakeOutPlatformHolder(
        TakeOutPlatformItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size


    override fun onBindViewHolder(
        holder: TakeOutPlatformAdapter.TakeOutPlatformHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<TakeOutPlatformModel>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun setSelectItem(data: TakeOutPlatformModel?) {
        selectItem = data
        notifyDataSetChanged()
    }

    fun getSelectItem(): TakeOutPlatformModel? {
        return selectItem
    }
}