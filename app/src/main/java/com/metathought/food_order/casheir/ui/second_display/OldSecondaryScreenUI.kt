package com.metathought.food_order.casheir.ui.second_display

import android.annotation.SuppressLint
import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import androidx.core.view.isVisible
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.databinding.SecondaryLayoutBinding
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.order.payment.PaymentQrDialog
import kh.org.nbc.bakong_khqr.BakongKHQR
import java.nio.charset.Charset

@Deprecated(message = "旧版副屏方案")
class OldSecondaryScreenUI(context: Context, display: Display) : Presentation(context, display) {
    private lateinit var binding: SecondaryLayoutBinding
    var paymentResponse : PaymentResponse? = null
    var isShowQR = false
    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = SecondaryLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
            binding.apply {
                val decode = BakongKHQR.decode(paymentResponse?.qrcode ?: "")
                val amountStr = "$${decode.data.transactionAmount}"
                imgQR.setImageDrawable(
                    QrCodeDrawable(
                        QrData.Url(paymentResponse?.qrcode ?: ""),
                        PaymentQrDialog.getQROption(R.drawable.ic_khqr_usd_logo, context),
                        Charset.forName("UTF-8")
                    )
                )
                tvScanQRAmount.text = amountStr
                tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
            }
        binding.layoutAd.isVisible = !isShowQR
    }

    fun updateTime(time : String){
        binding.tvDuration.text = time
    }
    fun showAd(){
        isShowQR = false
        binding.layoutAd.isVisible = !isShowQR
    }

}