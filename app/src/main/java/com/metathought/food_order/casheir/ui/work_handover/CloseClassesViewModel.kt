package com.metathought.food_order.casheir.ui.work_handover

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.data.model.base.request_model.logout.CashierLogoutRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CloseClassesViewModel @Inject constructor(val repository: Repository) : ViewModel() {
    private val _logoutState = MutableLiveData<ApiResponse<ShiftReportPrint>>()
    val logoutState get() = _logoutState

    private val _shiftLogtState =
        MutableLiveData<ApiResponse<BaseResponse<CashRegisterHandoverLogVo>>>()
    val shiftLogtState get() = _shiftLogtState

    var isExit = false

    fun getShiftLog() {
        viewModelScope.launch {
            _shiftLogtState.value = ApiResponse.Loading
            val response = repository.getShiftLog()
            if (response is ApiResponse.Success) {
                PreferenceDataStoreHelper.getInstance().apply {
                    val model = Gson().fromJson(
                        getFirstPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            ""
                        ), UserLoginResponse::class.java
                    )
                    model.shiftLog = response.data.data
                    this.putPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        model.toJson()
                    )
                    MainDashboardFragment.CURRENT_USER = model
                }
            }
            _shiftLogtState.value = response

        }
    }


    fun logout(
        khrAmount: String, //交接金额-瑞尔(单位:分)
        usdAmount: String,  //交接金额-美元(单位:分)
        changeShiftRemark: String? = null, //交班备注
        amountPaidKhr: String? = null, //支出金额(KHR)(单位:分)
        amountPaidUsd: String? = null,  //支出金额(USD)(单位:分)
        exit: Boolean? = null
    ) {
        val logoutRequest = CashierLogoutRequest(
            khrAmount = khrAmount.toBigDecimal(),
            usdAmount = usdAmount.toBigDecimal(),
            changeShiftRemark = changeShiftRemark,
            amountPaidKhr = if (amountPaidKhr.isNullOrEmpty()) null else amountPaidKhr.toBigDecimal(),
            amountPaidUsd = if (amountPaidUsd.isNullOrEmpty()) null else amountPaidUsd.toBigDecimal(),
            exit = exit
        )
        viewModelScope.launch {
            _logoutState.value = ApiResponse.Loading
            val response = repository.putLogout(logoutRequest)
            isExit = exit ?: true
            _logoutState.value = response
        }
    }

}