package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.google.gson.annotations.SerializedName

/**
 *
 *
 */
data class AbNormalOrderV2Request(
    @SerializedName("tableUuid")
    val tableUuid: String?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("payStatusList")
    val payStatusList: List<Int>?,
)
