package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate


/**
 *<AUTHOR>
 *@time  2024/8/20
 *@desc
 **/

enum class WsPrinterActionEnum(val type: String) {
    DEL("del"),  //删除
    CREATE("create"),  //添加
    UPDATE("update"),  //更新
    UPDATE_TEMPLATE("update_template"),  //模板更新
}


data class WsPrinterActionResponse(
    //操作
    val type: String? = null,

    //打印配置
    val data: PrinterConfigInfo? = null,
)