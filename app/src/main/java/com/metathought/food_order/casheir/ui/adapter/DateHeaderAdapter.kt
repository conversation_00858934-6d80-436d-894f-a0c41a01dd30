package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.ColumnWidthCalculator
import timber.log.Timber

/**
 * 日期标题适配器 - 水平滑动显示日期标题
 */
class DateHeaderAdapter(
    private var dateLabels: ArrayList<String>,
    private val context: Context
) : RecyclerView.Adapter<DateHeaderAdapter.ViewHolder>() {

    private var columnWidth = 110f // 动态列宽
    private var recyclerView: RecyclerView? = null
    private var contentRecyclerView: RecyclerView? = null // 用于获取内容区域宽度
    private var preCalculatedColumnWidth: Float? = null // 预计算的列宽

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_date_header, parent, false)

        // 创建 ViewHolder，宽度会在 onBindViewHolder 中设置

        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        // 确保每次绑定时都设置正确的宽度
        holder.updateWidth(columnWidth)
        holder.bind(dateLabels[position])
    }

    override fun getItemCount(): Int = dateLabels.size

    fun replaceData(newDateLabels: ArrayList<String>) {
        dateLabels.clear()
        dateLabels.addAll(newDateLabels)
        // 计算动态列宽
        calculateColumnWidth()
        notifyDataSetChanged()
    }

    /**
     * 设置预计算的列宽
     */
    fun setPreCalculatedColumnWidth(width: Float) {
        preCalculatedColumnWidth = width
        println("DateHeaderAdapter 设置预计算列宽: ${width}dp")
    }

    /**
     * 使用预计算的列宽替换数据，避免重复计算和闪烁
     */
    fun replaceDataWithPreCalculatedWidth(newDateLabels: ArrayList<String>) {
        dateLabels.clear()
        dateLabels.addAll(newDateLabels)

        // 使用预计算的列宽
        preCalculatedColumnWidth?.let { width ->
            columnWidth = width
            println("DateHeaderAdapter 使用预计算列宽: ${width}dp，跳过动态计算")
        } ?: run {
            // 如果没有预计算的列宽，则进行动态计算
            println("DateHeaderAdapter 没有预计算列宽，进行动态计算")
            calculateColumnWidth()
        }

        notifyDataSetChanged()
    }

    /**
     * 更新列宽
     */
    fun updateColumnWidth(width: Float) {
        if (columnWidth != width) {
            columnWidth = width
            println("DateHeaderAdapter 更新列宽: ${width}dp")
            // 使用 notifyItemRangeChanged 而不是 notifyDataSetChanged，更高效
            notifyItemRangeChanged(0, itemCount)
        }
    }

    /**
     * 设置内容区域RecyclerView引用，用于获取一致的宽度
     */
    fun setContentRecyclerView(contentRv: RecyclerView) {
        contentRecyclerView = contentRv
    }

    /**
     * 动态计算列宽
     * 使用内容区域的宽度来确保与数据内容完全一致
     * 注意：这个方法会导致二次刷新，建议使用预计算方式
     */
    private fun calculateColumnWidth() {
        // 如果已有预计算的列宽，直接使用，避免重复计算
        preCalculatedColumnWidth?.let { width ->
            if (columnWidth != width) {
                columnWidth = width
                println("DateHeaderAdapter 使用预计算列宽: ${width}dp")
                // 不需要再次刷新，因为调用方会处理刷新
            }
            return
        }

        val dataCount = dateLabels.size
        if (dataCount > 0) {
            // 使用内容区域的宽度，确保与 ProcessedReportContentAdapter 一致
            val targetRecyclerView = contentRecyclerView ?: recyclerView
            targetRecyclerView?.let { rv ->
                rv.post {
                    val areaWidth = rv.width
                    if (areaWidth > 0) {
                        // 使用统一的计算工具，最小宽度调整为110dp
                        val newColumnWidth = ColumnWidthCalculator.calculateColumnWidth(
                            context, areaWidth, dataCount, 110f
                        )

                        if (columnWidth != newColumnWidth) {
                            columnWidth = newColumnWidth
                            val sourceArea = if (contentRecyclerView != null) "内容区域" else "日期区域"

                            // 打印计算过程
                            ColumnWidthCalculator.logCalculation(
                                "DateHeaderAdapter($sourceArea-动态计算)",
                                dataCount,
                                areaWidth,
                                ColumnWidthCalculator.calculateColumnWidth(context, areaWidth, dataCount, 0f),
                                columnWidth
                            )

                            // 重新刷新数据以应用新的列宽
                            println("DateHeaderAdapter 动态计算完成，进行二次刷新")
                            notifyItemRangeChanged(0, itemCount)
                        }
                    }
                }
            }
        }
    }

    class ViewHolder(itemView: android.view.View) : RecyclerView.ViewHolder(itemView) {
        private val tvDateText: TextView = if (itemView is TextView) {
            itemView
        } else {
            itemView.findViewById(R.id.tvDateText)
        }

        fun bind(dateLabel: String) {
            tvDateText.text = dateLabel
        }

        /**
         * 更新宽度，确保复用时宽度正确
         */
        fun updateWidth(width: Float) {
            val context = itemView.context
            val widthPx = DisplayUtils.dp2px(context, width)

            // 获取当前宽度用于比较
            val currentWidth = tvDateText.layoutParams?.width ?: 0

            if (currentWidth != widthPx) {
                // 更新 TextView 的宽度
                val layoutParams = tvDateText.layoutParams
                if (layoutParams != null) {
                    layoutParams.width = widthPx
                    tvDateText.layoutParams = layoutParams
                    println("DateHeaderAdapter ViewHolder 更新宽度: ${DisplayUtils.px2dp(context, currentWidth.toFloat())}dp -> ${width}dp")
                } else {
                    // 如果没有 layoutParams，创建新的
                    val newLayoutParams = LinearLayout.LayoutParams(
                        widthPx,
                        LinearLayout.LayoutParams.MATCH_PARENT
                    )
                    tvDateText.layoutParams = newLayoutParams
                    println("DateHeaderAdapter ViewHolder 创建新的 layoutParams，宽度: ${width}dp")
                }

                // 设置5dp的左右内边距
                tvDateText.setPadding(
                    DisplayUtils.dp2px(context, 5f), // left
                    tvDateText.paddingTop, // top (保持原有)
                    DisplayUtils.dp2px(context, 5f), // right
                    tvDateText.paddingBottom // bottom (保持原有)
                )
            }
        }
    }
}
