package com.metathought.food_order.casheir.helper

import aclasdriver.AclasScale
import android.annotation.SuppressLint
import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable.isActive
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import java.io.File
import java.util.Collections
import kotlin.coroutines.cancellation.CancellationException

// WeightScaleManager.kt
class WeightScaleManager private constructor() {
    private var scale: AclasScale? = null
    private var newScale: AclasScale? = null
    private var listener: AclasScale.AclasScaleListener? = null
    private var currentWeight: Float = 0f
    private var isConnected: Boolean = false
    private var connectionCallback: ConnectionCallback? = null
    private var onWeightUpdate: ((Float) -> Unit)? = null
    private var connectJob: Job? = null  // 用于存储连接任务的Job引用

    private var isAllConnectedFail: Boolean? = null

    // 定义连接回调接口
    interface ConnectionCallback {
        fun onConnected(portName: String)
        fun onConnectionFailed(error: String)
    }

    // 使用伴生对象实现单例模式
    companion object {
        @Volatile
        private var instance: WeightScaleManager? = null
        private const val PREF_NAME = "weight_scale_prefs"
        private const val KEY_LAST_PORT = "last_successful_port"

        fun getInstance(): WeightScaleManager {
            return instance ?: synchronized(this) {
                instance ?: WeightScaleManager().also { instance = it }
            }
        }
    }

    fun startRead(onWeightUpdate: (Float) -> Unit) {
        Timber.d("startRead")
        this.onWeightUpdate = onWeightUpdate
        scale?.StartRead()
    }

    fun stopRead() {
        Timber.d("stopRead")
        this.onWeightUpdate = null
        scale?.StopRead()
    }

    // 初始化并自动连接电子秤，增加回调参数
    fun initialize(context: Context) {
        listener = object : AclasScale.AclasScaleListener {
            override fun OnError(code: Int) {
                when (code) {
                    AclasScale.CODENEEDUPDATE -> {
                        val errorMsg = "固件需要更新"
                        Timber.e(errorMsg)
                        disconnectScale()
                        connectionCallback?.onConnectionFailed(errorMsg)
                    }

                    AclasScale.NODATARECEIVE -> {
                        val errorMsg = "没有接收到数据"
                        Timber.e(errorMsg)
                        disconnectScale()
                        connectionCallback?.onConnectionFailed(errorMsg)
                    }

                    AclasScale.STREAMERROR -> {
                        val errorMsg = "串口通道错误"
                        Timber.e(errorMsg)
                        disconnectScale()
                        connectionCallback?.onConnectionFailed(errorMsg)
                    }

                    AclasScale.DISCONNECT -> {
                        val errorMsg = "电子秤断开连接"
                        Timber.e(errorMsg)
                        disconnectScale()
                        connectionCallback?.onConnectionFailed(errorMsg)
                    }
                }
            }

            @SuppressLint("DefaultLocale")
            override fun OnDataReceive(data: AclasScale.St_Data) {
                if (data.m_iStatus == -1) {
                    Timber.e("数据错误")
                    return
                }

                currentWeight = data.m_fWeight
                if (currentWeight < 0f) {
                    currentWeight = 0f
                }
                onWeightUpdate?.invoke(currentWeight)

                Timber.d("重量数据: ${String.format("%.3f", data.m_fWeight)} ${data.m_strUnit}")
            }

            override fun OnReadTare(fVal: Float, bFlag: Boolean) {
                Timber.d("读取皮重: $fVal, 状态: $bFlag")
            }
        }

//        UsbBroadcastReceiver.getInstance().setCallback { usbDevice, isConn ->
//            if (isConnected) {
//                Timber.d("称重设备已经连接，不用处理")
//                return@setCallback
//            }
//            autoConnectScale(context)
//        }
        // 自动获取可用串口并尝试连接，优先尝试上次成功的端口
        autoConnectScale(context)
    }

    // 设置连接回调（用于后续更改回调）
    fun setConnectionCallback(callback: ConnectionCallback?) {
        this.connectionCallback = callback
    }

    fun isSupportScale(): Boolean {
        val availablePorts = AclasScale.getAvailableUartList()
        return availablePorts.isNotEmpty()
    }

    // 自动连接电子秤，异步校验连接状态
    private fun autoConnectScale(context: Context) {
        // 取消之前的连接任务（如果有）
        cancelAutoConnect()

        val availablePorts = AclasScale.getAvailableUartList()
        if (availablePorts.isEmpty()) {
            val errorMsg = "没有找到可用的串口设备"
            Timber.e(errorMsg)
            isConnected = false
            connectionCallback?.onConnectionFailed(errorMsg)
            return
        }

        // 获取上次成功连接的端口
        val lastPort = getLastSuccessfulPort(context)

        // 重新排序端口列表，将上次成功的端口放在最前面
        val orderedPorts = if (lastPort != null && availablePorts.contains(lastPort)) {
            listOf(lastPort) + availablePorts.filter { it != lastPort }
        } else {
            availablePorts
        }

        // 使用协程异步尝试连接
        connectJob = CoroutineScope(Dispatchers.IO).launch {
            tryConnectPorts(orderedPorts, context)
        }
    }

    // 取消自动连接
    fun cancelAutoConnect() {
        connectJob?.let {
            if (it.isActive) {
                Timber.d("取消正在进行的连接任务")
                it.cancel()
            }
            connectJob = null
        }
        // 确保在取消连接任务时也清除临时监听器
        try {
            Timber.e("清除电子秤监听器")
            scale?.apply {
                setAclasScaleListener(null)  // 移除监听器
                StopRead()  // 停止读取
            }
            newScale?.setAclasScaleListener(null)  // 先移除监听器
            newScale?.StopRead()
            newScale?.close()
            newScale = null
        } catch (e: Exception) {
            Timber.e(e, "清除电子秤监听器失败")
        }
    }

    // 异步尝试连接所有可用端口
    private suspend fun tryConnectPorts(availablePorts: List<String>, context: Context) {
        for (port in availablePorts) {
            try {
                // 检查任务是否被取消
                if (!isActive) {
                    Timber.d("连接任务已被取消")
                    return
                }

                Timber.d("尝试连接串口: $port")

                // 断开之前的连接
                disconnectScale()

                // 尝试新连接
                val connected = withContext(Dispatchers.IO) {
                    connectAndVerify(port)
                }

                if (connected) {
                    Timber.d("成功连接到串口: $port")
                    isConnected = true

                    // 保存成功连接的端口
                    saveLastSuccessfulPort(context, port)

                    // 通知连接成功
                    withContext(Dispatchers.Main) {
                        if (onWeightUpdate == null) {
                            stopRead()
                        }
                        connectionCallback?.onConnected(port)
                    }
                    return
                } else {
                    Timber.d("串口 $port 连接失败或设备不响应")
                    disconnectScale() // 断开无效连接
                }
            } catch (e: Exception) {
                if (e is CancellationException) {
                    Timber.d("连接任务被取消")
                    return
                }
                Timber.e(e, "连接串口 $port 失败")
                disconnectScale() // 确保资源被释放
            }
        }

        // 所有端口都尝试失败
        val errorMsg = "所有串口连接尝试均失败"
        Timber.e(errorMsg)
        isConnected = false
        isAllConnectedFail = true
        // 通知连接失败
        withContext(Dispatchers.Main) {
            connectionCallback?.onConnectionFailed(errorMsg)
        }
    }

    /**
     *  是否所有连接都失败
     */
    fun isAllConnectedFail(): Boolean? {
        return isAllConnectedFail
    }

    // 连接并异步验证
    private suspend fun connectAndVerify(portPath: String): Boolean {
        return try {
            // 检查任务是否被取消
            if (!isActive) {
                return false
            }

            // 创建新的电子秤连接
            newScale = AclasScale(File(portPath), 0, null).apply {
                bLogFlag = true
                open()
            }

            // 使用协程超时机制进行验证
            val result = withTimeoutOrNull(500) { // 2秒超时
                var verified = false

                // 创建临时监听器
                val tempListener = object : AclasScale.AclasScaleListener {
                    override fun OnError(code: Int) {
                        // 出错不影响验证过程，继续等待
                    }

                    override fun OnDataReceive(data: AclasScale.St_Data) {
                        verified = true
                    }

                    override fun OnReadTare(fVal: Float, bFlag: Boolean) {
                        verified = true
                    }
                }

                // 设置临时监听器并开始读取
                newScale?.setAclasScaleListener(tempListener)
                newScale?.StartRead()

                // 等待验证成功或超时
                while (!verified) {
                    // 检查任务是否被取消
                    if (!isActive) {
                        break
                    }
                    delay(100) // 短暂延迟，避免CPU占用过高
                }

                verified
            }

            // 处理验证结果
            if (result == true) {
                // 验证成功，设置为当前使用的scale
                scale = newScale
                scale?.setAclasScaleListener(listener)
                true
            } else {
                // 超时或验证失败，关闭连接
                try {
                    newScale?.setAclasScaleListener(null)  // 先移除监听器
                    newScale?.StopRead()
                    newScale?.close()
                } catch (e: Exception) {
                    Timber.e(e, "关闭失败的连接时出错")
                }
                false
            }
        } catch (e: Exception) {
            if (e is CancellationException) {
                Timber.d("连接验证过程被取消")
                // 确保在取消时关闭newScale
                try {
                    newScale?.setAclasScaleListener(null)  // 先移除监听器
                    newScale?.StopRead()
                    newScale?.close()
                } catch (cleanupEx: Exception) {
                    Timber.e(cleanupEx, "取消时关闭newScale失败")
                }
                throw e // 重新抛出取消异常，以便上层函数能够正确处理
            }
            Timber.e(e, "连接或验证过程中出错")
            // 确保在其他异常时也关闭newScale
            try {
                newScale?.setAclasScaleListener(null)  // 先移除监听器
                newScale?.StopRead()
                newScale?.close()
            } catch (cleanupEx: Exception) {
                Timber.e(cleanupEx, "异常时关闭newScale失败")
            }
            false
        }
    }

    // 保存上次成功连接的端口
    private fun saveLastSuccessfulPort(context: Context, port: String) {
        try {
            val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            prefs.edit().putString(KEY_LAST_PORT, port).apply()
            Timber.d("已保存成功连接的端口: $port")
        } catch (e: Exception) {
            Timber.e(e, "保存端口信息失败")
        }
    }

    // 获取上次成功连接的端口
    private fun getLastSuccessfulPort(context: Context): String? {
        return try {
            val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            val port = prefs.getString(KEY_LAST_PORT, null)
            Timber.d("获取上次成功连接的端口: $port")
            port
        } catch (e: Exception) {
            Timber.e(e, "获取端口信息失败")
            null
        }
    }

    // 断开连接
    fun disconnectScale() {
        try {
//            // 先取消自动连接任务
//            cancelAutoConnect()

            scale?.apply {
                StopRead()
                close()
            }
            scale = null
            isConnected = false
        } catch (e: Exception) {
            Timber.e(e, "断开电子秤连接失败")
            scale = null
            isConnected = false
        }
    }

    // 获取当前重量
    fun getCurrentWeight(): Float = currentWeight

    // 检查是否已连接
    fun isConnected(): Boolean = isConnected
}