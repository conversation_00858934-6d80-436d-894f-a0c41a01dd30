package com.metathought.food_order.casheir.utils

import android.content.Context
import android.content.res.Resources
import android.graphics.PointF
import android.text.TextPaint
import android.view.View
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup

object PopupUtils {

    /**
     * 显示智能提示弹窗，根据可用空间自动选择显示位置
     * - 左右空间都足够时，显示在中间
     * - 左侧空间不足时，显示在右侧
     * - 右侧空间不足时，显示在左侧
     *
     * @param context 上下文
     * @param anchorView 锚点视图，弹窗将显示在此视图附近
     * @param message 要显示的提示信息
     * @param duration 显示时长，默认5000毫秒
     */
    fun showSmartTipPopup(
        context: Context,
        anchorView: View,
        message: String,
        duration: Long = 5000,
        position: PopupPosition? = null
    ) {
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val anchorX = location[0]
        val anchorY = location[1]
        val anchorWidth = anchorView.width

        // 获取屏幕宽度
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenWidth = displayMetrics.widthPixels

        // 估算消息宽度 (使用TextPaint计算实际文本宽度)
        val textPaint = TextPaint()
        textPaint.textSize =
            context.resources.getDimension(R.dimen._12ssp) // 使用与CustomBubbleAttachPopup相同的文本大小
        val estimatedMessageWidth = textPaint.measureText(message).toInt() +
                context.resources.getDimensionPixelSize(R.dimen.fragment_horizontal_margin) * 2 // 添加左右内边距
        val halfMessageWidth = estimatedMessageWidth / 2

        // 计算锚点中心位置
        val anchorCenterX = anchorX + (anchorWidth / 2)

        // 判断左右空间是否足够
        val hasEnoughSpaceOnLeft = anchorCenterX > halfMessageWidth
        val hasEnoughSpaceOnRight = (screenWidth - anchorCenterX) > halfMessageWidth

        // 创建XPopup构建器
        val popupBuilder = XPopup.Builder(context)
            .hasShadowBg(false)
            .isTouchThrough(false)
            .isDestroyOnDismiss(true)
//        if (position != null) {
//            popupBuilder.popupPosition(position)
//        } else {
        // 根据空间决定显示位置
        if (hasEnoughSpaceOnLeft && hasEnoughSpaceOnRight) {
            // 左右空间都足够，显示在中间
            popupBuilder.popupPosition(PopupPosition.Top) // 使用Top位置，但实际上是居中的
            // 设置居中显示
            popupBuilder.isCenterHorizontal(true)
        } else if (hasEnoughSpaceOnRight) {
            // 右侧空间足够，显示在右侧
            popupBuilder.popupPosition(PopupPosition.Right)
        } else {
            // 默认显示在左侧
            popupBuilder.popupPosition(PopupPosition.Left)
        }
//        }
//        popupBuilder.popupPosition(PopupPosition.Left)
        // 显示弹窗
        popupBuilder
            .atView(anchorView)
            .atPoint(PointF(anchorCenterX.toFloat(), anchorY.toFloat()))
            .asCustom(CustomBubbleAttachPopup(context, message, duration))
            .show()
    }
}