package com.metathought.food_order.casheir.ui.ordered.tablelist

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager

import com.metathought.food_order.casheir.databinding.DialogSwitchTableByOrderBinding
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.order.table_available.AvailableTableListDialog
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/5/17
 *@desc
 **/

class SwitchTableByOrderDialog : DialogFragment() {
    private var binding: DialogSwitchTableByOrderBinding? = null

    private var leftButtonListener: (() -> Unit)? = null
    private var rightButtonListener: (() -> Unit)? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSwitchTableByOrderBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun initData() {
        val content = arguments?.getString(CONTENT)
        val leftBtnTitle = arguments?.getString(LEFT_BUTTON_TITLE)
        val rightBtnTitle = arguments?.getString(RIGHT_BUTTON_TITLE)

        binding?.apply {
            tvContent.text = content
            btnLeft.text = leftBtnTitle

            btnRight.text = rightBtnTitle
            btnRight.isVisible = !rightBtnTitle.isNullOrEmpty()
        }
    }


    fun updateUi(
        content: String? = null,
        leftBtnTitle: String? = null,
        rightBtnTitle: String? = null,
        leftBtnListener: (() -> Unit)? = null,
        rightBtnListener: (() -> Unit)? = null,
    ) {
        binding?.apply {
            tvContent.text = content
            btnLeft.text = leftBtnTitle

            btnRight.text = rightBtnTitle
            btnRight.isVisible = !rightBtnTitle.isNullOrEmpty()
        }
        this.leftButtonListener = leftBtnListener
        this.rightButtonListener = rightBtnListener
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                AvailableTableListDialog.showDialog(parentFragmentManager)
                dismissAllowingStateLoss()
            }
            btnLeft.setOnClickListener {

                if (leftButtonListener != null) {
                    leftButtonListener?.invoke()
                } else {
                    AvailableTableListDialog.showDialog(parentFragmentManager)
                    dismissAllowingStateLoss()
                }

//                dismissAllowingStateLoss()
            }
            btnRight.setOnClickListener {
                rightButtonListener?.invoke()
//                dismissAllowingStateLoss()

            }
        }
    }

    companion object {
        const val SWITCH_TABLE_BY_ORDER = "SWITCH_TABLE_BY_ORDER"
        private const val CONTENT = "CONTENT"

        private const val LEFT_BUTTON_TITLE = "LEFT_BUTTON_TITLE"
        private const val RIGHT_BUTTON_TITLE = "RIGHT_BUTTON_TITLE"

        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            leftBtnTitle: String? = null,
            rightBtnTitle: String? = null,
            leftBtnListener: (() -> Unit)? = null,
            rightBtnListener: (() -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(SWITCH_TABLE_BY_ORDER)
            if (fragment != null) {
                Timber.e("重复利用 SwitchTableByOrderDialog")
                (fragment as? SwitchTableByOrderDialog)?.updateUi(
                    content = content,
                    leftBtnTitle = leftBtnTitle,
                    rightBtnTitle = rightBtnTitle,
                    leftBtnListener = leftBtnListener,
                    rightBtnListener = rightBtnListener
                )
                return
            }
            Timber.e("创建新的 SwitchTableByOrderDialog")
            fragment = newInstance(
                content = content,
                leftBtnTitle = leftBtnTitle,
                rightBtnTitle = rightBtnTitle,
                leftBtnListener = leftBtnListener,
                rightBtnListener = rightBtnListener
            )
            fragment.show(fragmentManager, SWITCH_TABLE_BY_ORDER)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(SWITCH_TABLE_BY_ORDER) as? SwitchTableByOrderDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            content: String? = null,
            leftBtnTitle: String? = null,
            rightBtnTitle: String? = null,
            leftBtnListener: (() -> Unit)? = null,
            rightBtnListener: (() -> Unit)? = null,
        ): SwitchTableByOrderDialog {
            val args = Bundle()
            val fragment = SwitchTableByOrderDialog()
            args.putString(LEFT_BUTTON_TITLE, leftBtnTitle)
            args.putString(RIGHT_BUTTON_TITLE, rightBtnTitle)
            args.putString(CONTENT, content)
            fragment.leftButtonListener = leftBtnListener
            fragment.rightButtonListener = rightBtnListener
            fragment.arguments = args
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}