package com.metathought.food_order.casheir.network.download

import android.util.Log
import com.metathought.food_order.casheir.MainActivity
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.extension.formatDateStr
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.net.ConnectException
import java.util.*
import java.util.concurrent.atomic.AtomicReference

class DownloadManager {

    private var mClient: OkHttpClient = OkHttpClient.Builder().build()
    private var taskMap = HashMap<String, DownloadTask>() //用来存放各个下载的请求

    class Progress(val progress: Long, val max: Long)

    companion object {
        private const val TAG = "DownloadManager"
        private val INSTANCE = AtomicReference<DownloadManager>()
        fun getInstance(): DownloadManager {
            while (true) {
                var current: DownloadManager? = INSTANCE.get()
                if (current != null) {
                    return current
                }
                current = DownloadManager()
                if (INSTANCE.compareAndSet(null, current)) {
                    return current
                }
            }
        }
    }


    /**
     * 开始下载(先下载到临时文件夹，完成后在复制到目标目录。防止下载失败产生失效文件)
     * @param url 下载请求的网址
     * @param dirPath 本地保存文件夹目录
     */
    @Synchronized
    fun downloadV2(
        sourceTag: String?,
        url: String,
        fileName: String,
        dirPath: String,
        downloadListener: DownloadListener?,
        isNewFileName: Boolean = true,
    ): Disposable {
        val task = taskMap[url] ?: DownloadTask()
        downloadListener?.let { task.register(sourceTag, it) }
        if (task.disposable != null) return task.disposable!!
        taskMap[url] = task

        //先下载到临时文件，完成在复制到目标目录。防止下载一半文件损坏

        val tempDownloadDir = MainActivity.mainActivityInstance?.cacheDir.toString()
        val tempLocalFile =
            if (isNewFileName) File(getLocalPath(tempDownloadDir, fileName)) else File(
                tempDownloadDir,
                fileName
            )

        if (tempLocalFile.exists()) {
            Log.e(TAG, "tempLocalFile 文件存在删除")
            tempLocalFile.delete()
        }

        var disposable: Disposable? = null
        disposable = Observable.create<Progress> {
            Log.e(TAG, "开始下载: $fileName")

            val request = Request.Builder().url(url).build()
            val response = mClient.newCall(request).execute()
            if (!response.isSuccessful) {
                throw ConnectException()
            }
            val contentLength = response.body?.contentLength() ?: 0
            if (contentLength == 0L) {
                throw Exception(MainActivity.mainActivityInstance?.getString(R.string.invalid_file))
            }

            var inputStream: InputStream? = null
            var fileOutputStream: FileOutputStream? = null
            try {
                inputStream = response.body?.byteStream()
                fileOutputStream = FileOutputStream(tempLocalFile, true)
                val buffer = ByteArray(2048)//缓冲数组2kB

                var downloadLength = 0L
                var len: Int = inputStream!!.read(buffer)
                while (len != -1) {
                    fileOutputStream.write(buffer, 0, len)
                    downloadLength += len.toLong()
                    if (it.isDisposed) {
                        return@create
                    }
                    it.onNext(Progress(downloadLength, contentLength))
//                    Log.e(TAG, "download: " + downloadLength)
//                    try {
//                        Thread.sleep(100)
//                    } catch (e: Exception) {
//
//                    }
                    len = inputStream.read(buffer)
                }
                fileOutputStream.flush()
                task.localFilePath = tempLocalFile.path

                if (it.isDisposed) {
                    return@create
                }
                it.onComplete()
            } finally {
                //关闭IO流
                inputStream?.close()
                fileOutputStream?.close()
            }
        }
            .doOnDispose {
                Log.e(TAG, "任务取消: $fileName")
                task.loadFailure("任务取消")
                cancelDownload(url)
            }
//            .compose(SchedulerUtils.ioToMain())
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.trampoline())
            .subscribe({
                task.progress(it.progress, it.max)
            }, {
                Log.e(TAG, "下载失败: $fileName  ${it.message}")
                task.loadFailure(MainActivity.mainActivityInstance!!.getString(R.string.download_failed))
                cancelDownload(url)
            }, {
                Log.e(TAG, "Disposable: $fileName loadSuccess")
                val tempF = File(task.localFilePath ?: "")
                if (!tempF.exists()) {
                    task.loadFailure(MainActivity.mainActivityInstance!!.getString(R.string.download_failed))
                    cancelDownload(url)
                    return@subscribe
                }

                val newFile =
                    if (isNewFileName) File(getLocalPath(dirPath, fileName))
                    else File(dirPath, fileName)
                val file = tempF.copyTo(newFile, true)
                if (!file.exists()) {
                    task.loadFailure(MainActivity.mainActivityInstance!!.getString(R.string.download_failed))
                    cancelDownload(url)
                    return@subscribe
                }
                tempF.delete()
                task.localFilePath = file.absolutePath
                task.loadSuccess()
                cancelDownload(url)
            })
        task.disposable = disposable
        return disposable
    }

    /**
     * 开始下载
     * @param url 下载请求的网址
     * @param dirPath 本地保存文件夹目录
     */
    @Synchronized
    fun download(
        sourceTag: String?,
        url: String,
        fileName: String,
        dirPath: String,
        downloadListener: DownloadListener?,
        isNewFileName: Boolean = true,
    ): Disposable {
        val task = taskMap[url] ?: DownloadTask()
        downloadListener?.let { task.register(sourceTag, it) }
        if (task.disposable != null) return task.disposable!!
        taskMap[url] = task
        var disposable: Disposable? = null
        disposable = Observable.create<Progress> {
            val request = Request.Builder().url(url).build()
            val response = mClient.newCall(request).execute()
            if (!response.isSuccessful) {
                throw ConnectException()
            }
            val contentLength = response.body?.contentLength() ?: 0
            if (contentLength == 0L) {
                throw Exception(MainActivity.mainActivityInstance!!.getString(R.string.invalid_file))
            }
            val localFile = if (isNewFileName) File(getLocalPath(dirPath, fileName)) else File(
                dirPath,
                fileName
            )

            var inputStream: InputStream? = null
            var fileOutputStream: FileOutputStream? = null
            try {
                inputStream = response.body?.byteStream()
                fileOutputStream = FileOutputStream(localFile, true)
                val buffer = ByteArray(2048)//缓冲数组2kB

                var downloadLength = 0L
                var len: Int = inputStream!!.read(buffer)
                while (len != -1) {
                    fileOutputStream.write(buffer, 0, len)
                    downloadLength += len.toLong()
                    if (it.isDisposed) {
                        return@create
                    }
                    it.onNext(Progress(downloadLength, contentLength))
//                    Log.e(TAG, "download: " + downloadLength)
//                    try {
//                        Thread.sleep(100)
//                    } catch (e: Exception) {
//
//                    }
                    len = inputStream.read(buffer)
                }
                fileOutputStream.flush()
                task.localFilePath = localFile.path

                if (it.isDisposed) {
                    return@create
                }
                it.onComplete()
            } finally {
                //关闭IO流
                inputStream?.close()
                fileOutputStream?.close()
            }
        }
            .doOnDispose {
                Log.e(TAG, "任务取消: $fileName")
                task.loadFailure("任务取消")
                cancelDownload(url)
            }
//            .compose(SchedulerUtils.ioToMain())
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.trampoline())
            .subscribe({
                task.progress(it.progress, it.max)
            }, {
                try {
                    Log.e(TAG, "下载失败: $fileName  ${it.message}")
                    task.loadFailure(MainActivity.mainActivityInstance!!.getString(R.string.download_failed))
                    cancelDownload(url)
                } catch (e: Exception) {

                }
            }, {
                Log.e(TAG, "Disposable: $fileName loadSuccess")
                task.loadSuccess()
                cancelDownload(url)
            })
        task.disposable = disposable
        return disposable
    }

    fun downloadFile(
        url: String,
        fileName: String,
        dirPath: String,
        processCB: ((Long) -> Boolean)? = null
    ): String {
        val request = Request.Builder().url(url).build()
        val response = mClient.newCall(request).execute()
        if (!response.isSuccessful) {
            throw ConnectException()
        }
        val contentLength = response.body?.contentLength() ?: 0
        if (contentLength == 0L) {
            throw Exception(MainActivity.mainActivityInstance!!.getString(R.string.invalid_file))
        }
        val localFile = File(getLocalPath(dirPath, fileName))
        var inputStream: InputStream? = null
        var fileOutputStream: FileOutputStream? = null
        try {
            inputStream = response.body?.byteStream()
            fileOutputStream = FileOutputStream(localFile, true)
            val buffer = ByteArray(2048)//缓冲数组2kB

            var downloadLength = 0L
            var len: Int = inputStream!!.read(buffer)
            while (len != -1) {
                fileOutputStream.write(buffer, 0, len)
                downloadLength += len.toLong()
                val isContinue = processCB?.invoke(downloadLength) ?: true
                if (!isContinue) {
                    return ""
                }
                len = inputStream.read(buffer)
            }
            fileOutputStream.flush()
            return localFile.path
        } finally {
            //关闭IO流
            inputStream?.close()
            fileOutputStream?.close()
        }
    }


    /**
     * 取消下载 删除本地文件
     * @param url 下载地址
     */
    @Synchronized
    fun cancelDownload(url: String) {
        val task = taskMap.remove(url)
        task?.cancel()
    }

    @Synchronized
    fun isDownload(url: String): Boolean {
        return taskMap.containsKey(url)
    }

    /**
     * 文件名前增加时间戳，如果文件名重复增加索引
     * @param dir 要保存到本地的文件目录
     * @param url 后台的下载文件路径
     * @return
     */
    private fun getLocalPath(dir: String, downFileName: String): String {
        //@TODO 后台返回的下载url不一定能拿到正确的文件名，到时候需要自行处理
        val date = Calendar.getInstance().time
        val timeStr = date.formatDateStr("yyyy-MM-dd-HHmmss")
        //从下载url截取到的文件名称
        //var fileName = url.substring(url.lastIndexOf("/"))
        var fileName = timeStr
        //保存到本地的文件目录名称
        var file = File("$dir${File.separator}$fileName${downFileName}")
        fileName = fileName.substringBeforeLast(".")
        //这里有坑，后台返回的下载链接不一定有后缀
        val ext = file.extension
        var i = 1
        while (file.exists()) {
            file = File("$dir${File.separator}$fileName($i).${ext}")
            i++
        }
        return file.path
    }

//    fun downloadFileSync(
//        url: String,
//        fileName: String,
//        dirPath: String,
//        isNewFileName: Boolean = true
//    ): String {
//        val request = Request.Builder().url(url).build()
//        val response = mClient.newCall(request).execute()
//        if (!response.isSuccessful) {
//            throw ConnectException()
//        }
//        val contentLength = response.body?.contentLength() ?: 0
//        if (contentLength == 0L) {
//            throw Exception( MainActivity.mainActivityInstance!!.getString(R.string.invalid_file))
//        }
//        val localFile =
//            if (isNewFileName) File(getLocalPath(dirPath, fileName)) else File(dirPath, fileName)
//        var inputStream: InputStream? = null
//        var fileOutputStream: FileOutputStream? = null
//        try {
//            inputStream = response.body?.byteStream()
//            fileOutputStream = FileOutputStream(localFile, true)
//            val buffer = ByteArray(2048)//缓冲数组2kB
//
//            var downloadLength = 0L
//            var len: Int = inputStream!!.read(buffer)
//            while (len != -1) {
//                fileOutputStream.write(buffer, 0, len)
//                downloadLength += len.toLong()
//                len = inputStream.read(buffer)
//            }
//            fileOutputStream.flush()
//            return localFile.path
//        } finally {
//            //关闭IO流
//            inputStream?.close()
//            fileOutputStream?.close()
//        }
//    }
//
//
//    fun downloadFileSyncHadKey(
//        key: String,
//        url: String,
//        fileName: String,
//        dirPath: String,
//        isNewFileName: Boolean = true,
//        success: (key: String, path: String) -> Unit, fail: (key: String, msg: String) -> Unit
//    ) {
//        val request = Request.Builder().url(url).build()
//        val response = mClient.newCall(request).execute()
//        if (!response.isSuccessful) {
//            throw ConnectException()
//        }
//        val contentLength = response.body?.contentLength() ?: 0
//        if (contentLength == 0L) {
//            throw Exception( MainActivity.mainActivityInstance!!.getString(R.string.invalid_file))
//        }
//        val localFile =
//            if (isNewFileName) File(getLocalPath(dirPath, fileName)) else File(dirPath, fileName)
//        var inputStream: InputStream? = null
//        var fileOutputStream: FileOutputStream? = null
//        try {
//            inputStream = response.body?.byteStream()
//            fileOutputStream = FileOutputStream(localFile, true)
//            val buffer = ByteArray(2048)//缓冲数组2kB
//
//            var downloadLength = 0L
//            var len: Int = inputStream!!.read(buffer)
//            while (len != -1) {
//                fileOutputStream.write(buffer, 0, len)
//                downloadLength += len.toLong()
//                len = inputStream.read(buffer)
//            }
//            fileOutputStream.flush()
//            success(key, localFile.path)
//        } catch (e: Exception) {
//            fail(key, "下载失败")
//        } catch (e: IOException) {
//            fail(key, "下载失败")
//        } finally {
//            //关闭IO流
//            inputStream?.close()
//            fileOutputStream?.close()
//        }
//    }
}