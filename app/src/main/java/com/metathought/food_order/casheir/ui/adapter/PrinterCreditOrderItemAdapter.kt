package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.CreditOrderDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesPayMethodReportDetail
import com.metathought.food_order.casheir.databinding.ItemCreditRecordMenuBinding
import com.metathought.food_order.casheir.databinding.ItemPaymentMethodBinding
import com.metathought.food_order.casheir.databinding.ItemPaymentMethodReportBinding
import com.metathought.food_order.casheir.databinding.ItemProductReportBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4


/**

 */
class PrinterCreditOrderItemAdapter(
    val list: List<CreditOrderDetail>
) :
    RecyclerView.Adapter<PrinterCreditOrderItemAdapter.PrinterCreditOrderItemViewHolder>() {

    inner class PrinterCreditOrderItemViewHolder(val binding: ItemCreditRecordMenuBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: CreditOrderDetail, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvOrderId.text = resource.orderId
                        tvOrderAmount.text =resource.amount?.priceFormatTwoDigitZero4()
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        PrinterCreditOrderItemViewHolder(
            ItemCreditRecordMenuBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterCreditOrderItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


}