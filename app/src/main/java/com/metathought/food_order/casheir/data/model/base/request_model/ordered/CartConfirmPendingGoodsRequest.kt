package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.FeedBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood

/**
 * <AUTHOR>
 * @date 2025/04/09 16:15
 * @description
 */
data class CartConfirmPendingGoodsRequest(
    //购物车内商品id
    @SerializedName("cartsId")
    val cartsId: String? = null,
    @SerializedName("sellPrice")
    val sellPrice: Long? = null,
    @SerializedName("vipPrice")
    val vipPrice: Long? = null,
    @SerializedName("type")
    val type: Int? = 1,
    @SerializedName("weight")
    val weight: Double? = null,
    @SerializedName("mealSetWeighingGoodsKey")
    val mealSetWeighingGoodsKey: String?, //套餐内称重商品标识：格式为 套餐分组ID_套餐商品ID_UUID前8位
)


