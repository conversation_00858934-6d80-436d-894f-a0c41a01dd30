package com.metathought.food_order.casheir.data.model.base.response_model.report


/**
 *<AUTHOR>
 *@time  2024/12/4
 *@desc
 **/

data class PaymentMethodReportResponse(
    val storeName: String?,
    val startTime: String?,
    val endTime: String?,
    val printTime: String?,
    val data: PaymentMethodReport?
)

data class PaymentMethodReport(
    val startTime: String?,
    val endTime: String?,
    val exportTime: String?,
    val totalOnlineAmount: String?,
    var totalOfflineAmount: String?,
    var totalBalanceAmount: String?,
    var totalOrderAmount: String?,
    var totalCashAmount: String?,
    var totalCreditAmount: String?,
    var totalOrderNum: Int?,
    var totalOtherAmount: String?,
    val salesPayMethodReportDetails: List<SalesPayMethodReportDetail>?
)

data class SalesPayMethodReportDetail(
    val payMethod: String?,
    val payChannel: Int?,
    val payMethodImgUrl: String?,
    val amount: String?,
    val orderNum: Int?,
    val amountRatio: String?
)