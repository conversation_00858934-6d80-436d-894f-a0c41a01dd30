package com.metathought.food_order.casheir.helper

import android.app.Application
import android.content.Context
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.WifiPrinterDevice
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.doubleStrToIntStr
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.widget.printer.WifiPrintQueueManager
import net.posprinter.IDeviceConnection
import net.posprinter.POSConnect
import net.posprinter.POSPrinter
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import android.util.Log
import android.widget.Toast
import com.metathought.food_order.casheir.BuildConfig
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.utils.PrinterLogUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *<AUTHOR>
 *@time  2024/7/22
 *@desc
 **/

// 扩展函数：将异步回调转换为挂起函数

suspend fun POSPrinter.getPrinterStatus(): Int {
    return suspendCoroutine { continuation ->
        printerStatus { status -> continuation.resume(status) }
    }
}

suspend fun POSPrinter.getConnect(): Boolean {
    return suspendCoroutine { continuation ->
        isConnect { status -> continuation.resume(status == 1) }
    }
}

object PrinterDeviceHelper {


    private var timer: Timer? = null

    //挂账记录打印
    private var creditRecordPrinterMap = mutableMapOf<String, String>()

    //存二维码已付款打印过的订单
    private var orderPrinterMap = mutableMapOf<String, String>()

    private var acceptOrderPrinterMap = mutableMapOf<String, String>()

    //当前USB打印配置
//    private var currentUsbPrinterInfo: PrinterConfigInfo? = null

    /**
     *  网络请求回来的打印列表
     */
    private var printerInfoList = mutableListOf<PrinterConfigInfo>()

//    private var curConnect: IDeviceConnection? = null
//    private var printer: POSPrinter? = null
//

//    var noPrintList = mutableListOf<NoPrintModel>()


    //wifi 打印机
    private var wifiPrinterDevice: ConcurrentHashMap<String, WifiPrinterDevice> =
        ConcurrentHashMap()

    //    private var wifiConnectionsMap: MutableMap<String, IDeviceConnection> = mutableMapOf()
//    private var wifiConnectionsStateMap: MutableMap<String, Boolean> = mutableMapOf()
//    var wifiPOSPrinter: POSPrinter? = null

    private var logoBase64: String? = null

    fun getLogo(): String? {
        return logoBase64
    }

    fun initPrinter(context: Context) {
        // 1. 计算线程池大小（I/O密集型）
        val cpuCores = Runtime.getRuntime().availableProcessors()
        val coreThreads = cpuCores * 3  // 核心线程数（长期保留）
        val maxThreads = cpuCores * 6   // 最大线程数（应对突发任务）
        // 2. 自定义线程工厂（带命名和异常处理）
        var threadSeq = 0 // 线程序号计数器
        val threadFactory = ThreadFactory { runnable ->
            Thread(
                runnable,
                "Printer-BgThread-${System.currentTimeMillis()}-${threadSeq++}"
            ).apply {
                isDaemon = true  // 守护线程避免阻塞应用退出
//                priority = Thread.NORM_PRIORITY  // 普通优先级
                priority = Thread.NORM_PRIORITY + 1  // 略高于普通优先级（确保打印任务优先）
                uncaughtExceptionHandler = Thread.UncaughtExceptionHandler { _, e ->
                    Timber.e("打印机后台线程异常: ${e.message}", e)
                }
            }
        }
        // 3. 配置线程池（关键优化）
        val threadPool = ThreadPoolExecutor(
            coreThreads,
            maxThreads,
            60,
            TimeUnit.SECONDS,
            LinkedBlockingQueue(36),  // 队列容量增加至9台×4倍（36）
            threadFactory,
            ThreadPoolExecutor.DiscardOldestPolicy()
        )
        // 添加线程池状态监控（每5秒输出一次）
//        Timer().scheduleAtFixedRate(object : TimerTask() {
//            override fun run() {
//                Log.e(
//                    "打印线程日志", """
//                打印机线程池状态：
//                核心线程数: ${threadPool.corePoolSize}
//                最大线程数: ${threadPool.maximumPoolSize}
//                活跃线程数: ${threadPool.activeCount}
//                已完成任务数: ${threadPool.completedTaskCount}
//                队列大小: ${threadPool.queue.size}
//                拒绝策略: ${threadPool.rejectedExecutionHandler.javaClass.simpleName}
//            """.trimIndent()
//                )
//            }
//        }, 0, 5000) // 初始延迟0秒，每5秒输出一次

        // 4. 替换SDK的后台线程池（在init前）
        POSConnect.backgroundThreadExecutor = threadPool
        POSConnect.init(context)
    }

//    fun resetPrinter() {
//        curConnect?.close()
//        curConnect = null
//        printer = null
//        EventBus.getDefault().post(
//            SimpleEvent(
//                SimpleEventType.UPDATE_PRINTER_MANAGER_LIST,
//                null
//            )
//        )
//    }


    fun connectWifiPrinter(ipAddress: String?) {
        // 1. 空值防御：提前处理无效IP
        val safeIp = ipAddress?.trim()
        if (safeIp.isNullOrEmpty()) {
            Timber.e("connectWifiPrinter失败：IP地址为空或无效")
            return
        }

        // 2. 使用computeIfAbsent原子操作（替代putIfAbsent）
        val isNewDevice = !wifiPrinterDevice.containsKey(safeIp) // 标记是否是新设备
        val device = wifiPrinterDevice.computeIfAbsent(safeIp) {
            Timber.e("新设备加入: $safeIp")
            WifiPrinterDevice().apply {
            } // 初始化时不标记连接中（避免提前阻塞）
        }
        // 4. 新设备直接触发连接逻辑
        if (isNewDevice) {
            Timber.e("新设备触发连接: $safeIp")
            connectWifiPrinterByIp(safeIp) // 关键修改：新设备直接调用连接方法
            return
        }


        // 3. 检查是否已存在有效连接或正在连接
        if (device.isConnect.get()) {
            Timber.e("设备[$safeIp]已连接，无需重复操作")
            return
        }

        if (device.isConnecting.get()) {
            Timber.e("设备[$safeIp]正在连接中，跳过重复请求")
            return
        }

        // 4. 记录连接开始日志（集成PrinterLogUtils）
        PrinterLogUtils.writePrintLog(
            printerIp = safeIp,
            taskInfo = "{}",  // 无具体任务时传空对象
            status = PrinterLogUtils.PrintStatus.CONNECT_START,
            message = "开始尝试连接WiFi打印机"
        )


        // 5. 标记为连接中并发起连接
        device.isConnecting.set(true)
        device.iDeviceConnection.get()?.isConnect(byteArrayOf(0)) { status ->
            // 回调执行时先校验设备是否仍存在（防止被提前移除）
            val currentDevice = wifiPrinterDevice[safeIp] ?: run {
                Timber.e("连接状态回调时设备[$safeIp]已被移除")
                device.isConnecting.set(false)
                return@isConnect
            }

            if (status != 1) {
                currentDevice.isConnecting.set(false)
                Timber.e("设备[$safeIp]连接状态异常（状态码：$status），尝试重连")
                // 记录连接失败日志
                PrinterLogUtils.writePrintLog(
                    printerIp = safeIp,
                    taskInfo = "{}",
                    status = PrinterLogUtils.PrintStatus.CONNECT_FAIL,
                    message = "连接状态检查失败，状态码：$status"
                )
                // 执行重连（需先关闭旧连接）
                closeWifiConnection(safeIp, true)  // 重连不删除设备记录
                connectWifiPrinterByIp(safeIp)      // 重新发起连接
            } else {
                Timber.e("设备[$safeIp]连接状态正常")
                currentDevice.isConnect.set(true)
                // 无论结果如何，重置连接中标记
                currentDevice.isConnecting.set(false)
                // 记录连接成功日志
                PrinterLogUtils.writePrintLog(
                    printerIp = safeIp,
                    taskInfo = "{}",
                    status = PrinterLogUtils.PrintStatus.CONNECT_SUCCESS,
                    message = "连接状态检查成功"
                )

            }

        }
    }


    fun connectWifiPrinterByIp(ipAddress: String?) {
        Timber.e("connectWifiPrinterByIp")
        val safeIp = ipAddress?.trim()
        if (safeIp.isNullOrEmpty()) {
            Timber.e("connectWifiPrinterByIp失败：IP地址为空或无效")
            return
        }

        // 使用computeIfAbsent原子操作确保多线程下设备实例唯一
        val currentDevice = wifiPrinterDevice.computeIfAbsent(safeIp) {
            Timber.e("创建新WiFi设备实例: $safeIp")
            WifiPrinterDevice()
        }
        // 保存当前设备实例的局部引用（避免回调中通过map获取时可能被移除）
        currentDevice.isConnecting.set(true)
        currentDevice.apply {
            iDeviceConnection.set(POSConnect.createDevice(POSConnect.DEVICE_TYPE_ETHERNET))
            iDeviceConnection.get()?.connectSync(safeIp) { code, connInfo, msg ->
                Timber.e("连接回调: code=$code, msg=$msg, ip=$safeIp")
                // 使用局部引用访问设备属性（替代通过map二次获取）
                Timber.e("wifi hashcode ${iDeviceConnection.get()?.hashCode()}")
                currentDevice.isConnecting.set(false)
                connectListener(safeIp, code, connInfo, msg)
            }
        }

    }


    private fun connectListener(ipAddress: String?, code: Int, connInfo: String?, msg: String) {
        when (code) {
            POSConnect.CONNECT_SUCCESS -> {
                Timber.e("链接上了 ${ipAddress} :${wifiPrinterDevice[ipAddress]?.iDeviceConnection?.get()}")
                if (wifiPrinterDevice[ipAddress]?.iDeviceConnection?.get() != null) {
                    Timber.e("设置pOSPrinter ${ipAddress}")
                    wifiPrinterDevice[ipAddress]?.pOSPrinter?.set(
                        POSPrinter(wifiPrinterDevice[ipAddress]?.iDeviceConnection?.get())
                    )
                }
                val printerIp = ipAddress ?: "未知IP"
                PrinterLogUtils.writePrintLog(
                    printerIp = printerIp,
                    taskInfo = "{${msg}}",
                    status = PrinterLogUtils.PrintStatus.PRINTER_CONNECT,
                    message = "打印机连接成功（状态码：${POSConnect.CONNECT_SUCCESS}）"
                )
                setWifiConnectState(ipAddress, true)
                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))

            }

            POSConnect.CONNECT_INTERRUPT -> {
                Timber.e("链接中断:${msg}")
                /**
                 * 这里不做处理了 轮询查状态失败那边做重连
                 */
                val printerIp = ipAddress ?: "未知IP"
                PrinterLogUtils.writePrintLog(
                    printerIp = printerIp,
                    taskInfo = "{${msg}}",
                    status = PrinterLogUtils.PrintStatus.PRINTER_DISCONNECTED,
                    message = "打印机连接中断（状态码：${POSConnect.CONNECT_INTERRUPT}"
                )
                setWifiConnectState(ipAddress, false)
//                closeWifiConnection(ipAddress)
//                connectWifiPrinter(ipAddress)
            }

            POSConnect.CONNECT_FAIL -> {
                Timber.e("链接失败:${msg}")
                /**
                 * 这里不做处理了 轮询查状态失败那边做重连
                 */
                // 新增：记录连接失败日志
                val printerIp = ipAddress ?: "未知IP"
                PrinterLogUtils.writePrintLog(
                    printerIp = printerIp,
                    taskInfo = "{${msg}}",
                    status = PrinterLogUtils.PrintStatus.PRINTER_DISCONNECTED,
                    message = "打印机连接失败（状态码：${POSConnect.CONNECT_FAIL}）"
                )
//                closeWifiConnection(ipAddress)
                setWifiConnectState(ipAddress, false)
            }

            POSConnect.SEND_FAIL -> {
                val printerIp = ipAddress ?: "未知IP"
                PrinterLogUtils.writePrintLog(
                    printerIp = printerIp,
                    taskInfo = "{connInfo:${connInfo}, msg:${msg}}",
                    status = PrinterLogUtils.PrintStatus.PRINT_FAILURE,
                    message = "打印机发送失败（状态码：${POSConnect.SEND_FAIL}）"
                )
            }

        }
        EventBus.getDefault()
            .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
    }


    private fun setWifiConnectState(ipAddress: String?, isConnect: Boolean) {
        if (ipAddress.isNullOrEmpty()) {
            Timber.e("setWifiConnectState失败：ipAddress为空")
            return
        }
        // 使用ConcurrentHashMap的线程安全特性获取设备
        val device = wifiPrinterDevice[ipAddress]
        if (device == null) {
            Timber.e("setWifiConnectState失败：未找到IP为[$ipAddress]的打印机设备")
            return
        }
        // 明确记录状态变更（包含IP和新旧状态）
        Timber.e("设置打印机[$ipAddress]连接状态：旧状态=${device.isConnect} → 新状态=$isConnect")
        // 直接更新设备状态（假设WifiPrinterDevice的isConnect为线程安全类型，如AtomicBoolean）
        // 使用AtomicBoolean的原子方法更新状态（替代synchronized）
        if (!isConnect) {
            // 使用 AtomicReference 的 set 方法设置 pOSPrinter 为 null
            device.pOSPrinter.set(null)
        }
        val oldState = device.isConnect.getAndSet(isConnect)
        // 发送事件通知列表更新（保持原有逻辑）
        EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
    }

    fun getWifiConnectState(printerConfigInfo: PrinterConfigInfo?): Boolean {
        // 1. 明确处理printerConfigInfo为null的情况
        if (printerConfigInfo == null) {
            Timber.e("获取连接状态失败：printerConfigInfo为null")
            return false
        }

        // 2. 严格校验ipAddress有效性（空字符串/空白字符）
        val ipAddress = printerConfigInfo.ipAddress?.trim()
        if (ipAddress.isNullOrEmpty()) {
            Timber.e("获取连接状态失败：printerConfigInfo的ipAddress为空或无效")
            return false
        }

        // 3. 线程安全获取设备实例（ConcurrentHashMap的get操作是线程安全的）
        val device = wifiPrinterDevice[ipAddress]
        if (device == null) {
            Timber.e("获取连接状态失败：未找到IP为[$ipAddress]的WiFi打印机设备")
            return false
        }

        // 4. 直接读取线程安全的连接状态（假设isConnect为AtomicBoolean）
        return device.isConnect.get()
    }


    fun getPrinterList(): List<PrinterConfigInfo> {
        return printerInfoList
    }

    //删除打印机
    fun delPrinter(printerConfigInfo: PrinterConfigInfo?) {
        val index = printerInfoList.indexOfFirst {
            Timber.e("it.id ${it.id}   ${printerConfigInfo?.id?.doubleStrToIntStr()}")
            it.id == printerConfigInfo?.id?.doubleStrToIntStr()
        }
        Timber.e("del index-> $index")
        if (index != -1) {
            printerInfoList.removeAt(index)
            if (printerConfigInfo?.type == PrinterTypeEnum.WIFI.type) {
                /**
                 * 如果是wifi 要断开对应的链接
                 * 判断一下wifi打印机列表里是否还有相同ip地址
                 */
                val list = printerInfoList.filter {
                    it.ipAddress == printerConfigInfo.ipAddress
                }
                if (list.isNullOrEmpty()) {
                    closeWifiConnection(printerConfigInfo?.ipAddress)
                } else {
                    Timber.e("还存在 相同ipAddress")
                }
            } else if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                dealUsbPrinterList()
            }
            EventBus.getDefault()
                .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
        }
    }

    //更新打印机配置
    fun updatePrinter(printerConfigInfo: PrinterConfigInfo?) {
        val index = printerInfoList.indexOfFirst {
            Timber.e("it.id ${it.id}   ${printerConfigInfo?.id?.doubleStrToIntStr()}")
            it.id == printerConfigInfo?.id?.doubleStrToIntStr()
        }
        Timber.e("update index-> $index")
        if (index != -1) {
            //新设置的ip 和原来的不一样
            val isDiffIp = printerInfoList[index].ipAddress != printerConfigInfo!!.ipAddress
            if (printerConfigInfo.type == PrinterTypeEnum.WIFI.type && isDiffIp) {
                //如果是wifi 要断开对应的链接   不一样才去断开重连
                //判断一下wifi打印机列表里是否还有其他和要断开连接的相同ip地址
                val list = printerInfoList.filter {
                    it.ipAddress == printerInfoList[index].ipAddress && it.id != printerInfoList[index].id
                }
                if (list.isEmpty()) {
                    Timber.e("不存在 相同ipAddress")
                    closeWifiConnection(printerInfoList[index].ipAddress)
                } else {
                    Timber.e("还存在 相同ipAddress")
                }
                connectWifiPrinter(printerConfigInfo.ipAddress)
            }

            printerInfoList[index] = printerConfigInfo
            printerInfoList[index].id = printerConfigInfo.id?.doubleStrToIntStr()
            if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                dealUsbPrinterList()
            }
            EventBus.getDefault()
                .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
        }
    }

    fun getWifiConnection(ipAddress: String?): IDeviceConnection? {
        if (wifiPrinterDevice.containsKey(ipAddress)) {
            // 使用 AtomicReference 的 get 方法获取 iDeviceConnection
            return wifiPrinterDevice[ipAddress]?.iDeviceConnection?.get()
        }
        Timber.e("iDeviceConnection 不存在  ${ipAddress}")
        return null
    }

    fun getWifiPosPrinter(ipAddress: String?): POSPrinter? {
        if (wifiPrinterDevice.containsKey(ipAddress)) {
            // 使用 AtomicReference 的 get 方法获取 pOSPrinter
            return wifiPrinterDevice[ipAddress]?.pOSPrinter?.get()
        }
        Timber.e("pOSPrinter 不存在")
        return null
    }

    fun closeWifiConnection(ipAddress: String?, isReConnection: Boolean? = false) {
        if (ipAddress.isNullOrEmpty()) return
        try {
            val connect = getWifiConnection(ipAddress)
            Timber.e("关闭连接: $ipAddress (重连标记: $isReConnection)")
            connect?.closeSync()
            // 非重连场景下移除设备记录
            if (isReConnection == false) {
                //做重连的时候删除
                wifiPrinterDevice.remove(ipAddress)
            }
            setWifiConnectState(ipAddress, false)
        } catch (e: Exception) {
            Timber.e("关闭连接wifi连接error $ipAddress")
            e.printStackTrace()
        }
    }


    fun getUsbPrinterInfo(): PrinterConfigInfo? {
//        if (currentUsbPrinterInfo != null) {
//            return currentUsbPrinterInfo
//        }
        val printerInfo = printerInfoList.firstOrNull { it.type == PrinterTypeEnum.USB.type }
        return printerInfo
    }

    /**
     * 指定USB地址断开重连的方法
     * @param usbDeviceName USB设备名称/地址
     * @return 是否成功执行重连操作
     */
    fun reconnectUsbPrinterByAddress(usbDeviceName: String?): Boolean {
        try {
            Timber.e("开始重连")

            if (BuildConfig.FLAVOR == "itest") {
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    Toast.makeText(MyApplication.myAppInstance, "开始重连", Toast.LENGTH_SHORT)
                        .show()
                }
            }

            PrinterLogUtils.writeUsbPrintLog(
                "[PrinterDeviceHelper] 开始USB设备重连: $usbDeviceName",
                "",
                PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                ""
            )

            if (usbDeviceName.isNullOrEmpty()) {
                PrinterLogUtils.writeUsbPrintLog(
                    "[PrinterDeviceHelper] USB设备重连失败: 设备名称为空",
                    "",
                    PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                    ""
                )
                return false
            }

            // 查找指定USB设备名称对应的打印机配置
            val targetPrinterConfig = printerInfoList.find { printerConfig ->
                printerConfig.type == PrinterTypeEnum.USB.type &&
                        printerConfig.usbDevice?.deviceName == usbDeviceName
            }

            if (targetPrinterConfig == null) {
                PrinterLogUtils.writeUsbPrintLog(
                    "[PrinterDeviceHelper] 未找到USB设备对应的打印机配置: $usbDeviceName",
                    "",
                    PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                    ""
                )
                return false
            }

            PrinterLogUtils.writeUsbPrintLog(
                "[PrinterDeviceHelper] 找到目标打印机配置: ${targetPrinterConfig.name}",
                "",
                PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                ""
            )

            // 释放指定USB设备的连接
            val releaseResult = NewPrinterUsbDeviceHelper.releaseSingleConnection(usbDeviceName)
            if (!releaseResult) {
                PrinterLogUtils.writeUsbPrintLog(
                    "[PrinterDeviceHelper] USB设备连接释放失败: $usbDeviceName",
                    "",
                    PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                    ""
                )
                return false
            }

            PrinterLogUtils.writeUsbPrintLog(
                "[PrinterDeviceHelper] USB设备连接释放成功: $usbDeviceName",
                "",
                PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                ""
            )

            // 清空该配置的USB设备引用
//            targetPrinterConfig.usbDevice = null

            // 重新处理USB打印机列表，重新分配连接
//            dealUsbPrinterList()
            val usbDevice = NewPrinterUsbDeviceHelper.usbDeviceMap.get(usbDeviceName)?.usbDevice
            if (usbDevice == null) {
                PrinterLogUtils.writeUsbPrintLog(
                    "[PrinterDeviceHelper] USB设备重连失败: $usbDeviceName",
                    "",
                    PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                    ""
                )
                return false
            }
            NewPrinterUsbDeviceHelper.createPrinterConnection(
                usbDeviceName,
                usbDevice,
                if (targetPrinterConfig.isLabelPrinter()) LocalPrinterEnum.LABEL_PRINTER.id else LocalPrinterEnum.TICKET_PRINTER.id
            )

            // 检查重连是否成功
            val reconnectSuccess = targetPrinterConfig.usbDevice != null &&
                    targetPrinterConfig.usbDevice?.deviceName == usbDeviceName

            if (reconnectSuccess) {
                PrinterLogUtils.writeUsbPrintLog(
                    "[PrinterDeviceHelper] USB设备重连成功: $usbDeviceName",
                    "",
                    PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                    ""
                )
                if (BuildConfig.FLAVOR == "itest") {
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        Toast.makeText(
                            MyApplication.myAppInstance,
                            "USB设备重连成功",
                            Toast.LENGTH_SHORT
                        )
                            .show()
                    }
                }
                // 发送事件通知更新打印机管理列表
                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
            } else {
                PrinterLogUtils.writeUsbPrintLog(
                    "[PrinterDeviceHelper] USB设备重连失败: $usbDeviceName",
                    "",
                    PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                    ""
                )
                if (BuildConfig.FLAVOR == "itest") {
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        Toast.makeText(
                            MyApplication.myAppInstance,
                            "USB设备重连失败",
                            Toast.LENGTH_SHORT
                        )
                            .show()
                    }
                }
            }

            return reconnectSuccess

        } catch (e: Exception) {
            PrinterLogUtils.writeUsbPrintLog(
                "[PrinterDeviceHelper] USB设备重连异常: ${e.message}",
                "",
                PrinterLogUtils.PrintStatus.CONNECT_RETRY,
                ""
            )
            return false
        }
    }

    //获取USB和WIFI打印配置
    suspend fun getPrinterInfoListFromNet(repository: Repository) {
        try {
            val response = repository.getPrinterInfoList()
            if (response is ApiResponse.Success) {
                printerInfoList.clear()
                printerInfoList.addAll(response.data)

                dealWifiPrinterList()
                dealUsbPrinterList()

                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
            }

            val logoResponse = repository.getLogoBase64()
            if (logoResponse is ApiResponse.Success) {
                logoBase64 = logoResponse.data ?: ""
            }

        } catch (e: Exception) {

        }
    }

    /**
     *  处理Wifi打印机列表
     *
     */
    private fun dealWifiPrinterList() {
        var isHasWifiPrinter = false
        printerInfoList.forEach {
            if (it.type == PrinterTypeEnum.WIFI.type) {
                connectWifiPrinter(it.ipAddress)
                isHasWifiPrinter = true
            }
        }
        if (isHasWifiPrinter) {
            startCheckWifiPrinterStatus()
//            startSendWifiPrinter()
        }
    }

    /**
     *  处理USb打印机列表
     *  优先匹配SN码一一对应的USB打印机
     */
    fun dealUsbPrinterList() {
        NewPrinterUsbDeviceHelper.cleatALlPrinterObject()
        printerInfoList.forEachIndexed { index, printerConfigInfo ->
            if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                /**
                 * 先处理后台设置usb 打印 sn 不为空的情况 ，把usb打印设备对应后台sn 码的 一一对应的了
                 */
                if (!printerConfigInfo.sn.isNullOrEmpty()) {
                    //如果Sn不为空,查找指定USB打印机
                    var map =
                        NewPrinterUsbDeviceHelper.usbDeviceMap.filterValues { usbDevice ->
                            Timber.e("usbDevice.usbDevice?.serialNumber==>${usbDevice.usbDevice?.serialNumber}")
                            usbDevice.usbDevice?.serialNumber == printerConfigInfo.sn
                        }
                    map.forEach { s, usbPrinterDevice ->
                        if (printerConfigInfo.machineType == LocalPrinterEnum.LABEL_PRINTER.id) {
                            NewPrinterUsbDeviceHelper.createTSPLPrinter(usbPrinterDevice.usbDevice?.deviceName)
                        } else {
                            NewPrinterUsbDeviceHelper.createPOSPrinter(usbPrinterDevice.usbDevice?.deviceName)
                        }
                    }
                    map =
                        NewPrinterUsbDeviceHelper.usbDeviceMap.filterValues { usbDevice -> usbDevice.usbDevice?.serialNumber == printerConfigInfo.sn }
                    val first = map.toList().firstOrNull()
                    printerConfigInfo.usbDevice = first?.second?.usbDevice
                }
            }
        }

        Timber.e("开始处理后台没有设置sn码的，从为被配置的")
        printerInfoList.forEachIndexed { index, printerConfigInfo ->
            if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                /**
                 * 处理后台设置usb 小票 打印 sn 为空的情况  //如果后台没设Sn 那默认连的都是同一个打印设备
                 */
                Timber.e("index:${index}")
                if (printerConfigInfo.sn.isNullOrEmpty() && printerConfigInfo.machineType == LocalPrinterEnum.TICKET_PRINTER.id) {
                    //筛选出usb打印设备里面 里面还有没对应实例化的 打印设备 ，并且 不是标签打印
                    var usbPrinterDeviceMap =
                        NewPrinterUsbDeviceHelper.usbDeviceMap.filterValues { usbDevice ->
                            Timber.e("vendorId:${usbDevice.usbDevice?.vendorId}  productId:${usbDevice.usbDevice?.productId}")
                            usbDevice.tSPLPrinter == null && usbDevice.pOSPrinter == null && !NewPrinterUsbDeviceHelper.isLabelPrinter(
                                usbDevice.usbDevice?.vendorId,
                                usbDevice.usbDevice?.productId
                            )
                        }
                    Timber.e("usbPrinterDeviceMap :${usbPrinterDeviceMap.size}")
                    //如果列表不为空 从里面取出第一个用来示例
                    if (usbPrinterDeviceMap.isNotEmpty()) {
                        var usbPrinterDevice = usbPrinterDeviceMap.entries.firstOrNull()?.value
                        usbPrinterDevice =
                            if (printerConfigInfo.machineType == LocalPrinterEnum.LABEL_PRINTER.id) {
                                //这个判断是不会进来了，因为上面判断了小票类型
                                NewPrinterUsbDeviceHelper.createTSPLPrinter(usbPrinterDevice?.usbDevice?.deviceName)
                            } else {
                                NewPrinterUsbDeviceHelper.createPOSPrinter(usbPrinterDevice?.usbDevice?.deviceName)
                            }
                        printerConfigInfo.usbDevice = usbPrinterDevice?.usbDevice
                    }
                }
            }
        }
    }

    /**
     *  清理打印机列表 里面 Usb相关硬件数据
     *
     */
    fun clearUsbPrintInPrintList() {
        printerInfoList.forEach {
            it.usbDevice = null
        }
    }


    //是否打印过该已付款订单
    fun isOrderPrinter(orderNo: String?): Boolean {
        return orderPrinterMap.contains(orderNo)
    }

    fun setOrderPrinter(orderNo: String?) {
        if (!orderPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            orderPrinterMap[orderNo] = orderNo
        }
    }

    fun clearOrderPrinter(orderNo: String?) {
        if (orderPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            orderPrinterMap.remove(orderNo)
        }
    }


    fun isCreditRecordPrinter(orderNo: String?): Boolean {
        return creditRecordPrinterMap.contains(orderNo)
    }

    fun setCreditRecordPrinter(orderNo: String?) {
        if (!creditRecordPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            creditRecordPrinterMap[orderNo] = orderNo
        }
    }

    /**
     * 收银端 接的单时候已打印 防止ws 下发重复打印
     *
     * @param orderNo
     * @return
     */
    fun isAcceptOrderPrinter(orderNo: String?): Boolean {
        return acceptOrderPrinterMap.contains(orderNo)
    }

    fun setAcceptOrderPrinter(orderNo: String?) {
        if (!acceptOrderPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            acceptOrderPrinterMap[orderNo] = orderNo
        }
    }

    fun removeAcceptOrder(orderNo: String?) {
        if (acceptOrderPrinterMap.contains(orderNo)) {
            acceptOrderPrinterMap.remove(orderNo)
        }
    }


    //开始检测wifi打印机状态
    private fun startCheckWifiPrinterStatus() {
        Timber.e("开始计时1111")
        cancelCheckWifiPrinterStatus()
        val task = object : TimerTask() {
            override fun run() {
                Timber.e("定时器任务执行，查询wifi打印状态..")

                Timber.e("wifiPrinterDevice ${wifiPrinterDevice.size}")
                try {
                    wifiPrinterDevice.entries.forEach { (ip, device) ->
                        val posPrinter = device.pOSPrinter.get()
                        Timber.e("循环 查询 printerStatus  ${posPrinter} 发送ping  ${ip}")
//                        if (wifiPrinterDevice.pOSPrinter == null) {
//                            Timber.e("pOSPrinter 没有  重连  ${s}")
//                            closeWifiConnection(s, true)
//                            connectWifiPrinterByIp(s)
//                        } else {
//                            wifiPrinterDevice.pOSPrinter?.printerStatus {
//                                Timber.e("printerStatus11  :${it}")
//                                if (it != POSConst.STS_NORMAL) {
//                                    Timber.e("重连  ${s}")
//                                    closeWifiConnection(s, true)
//                                    connectWifiPrinterByIp(s)
//                                } else {
//                                    Timber.e("还连着  ${s}")
//                                }
//                            }
//                        }
                        val hasConfig = printerInfoList.any { it.ipAddress == ip }
                        if (!hasConfig) {
                            Timber.e("清理无配置的僵尸设备: $ip")
                            closeWifiConnection(ip)
                            wifiPrinterDevice.remove(ip) // 移除无配置的设备
                            return@forEach
                        }
                        device.iDeviceConnection.get()?.isConnect(byteArrayOf(0)) { status ->
                            if (status != 1) {
                                Timber.e("设备连接断开，尝试重连: $ip")
                                closeWifiConnection(ip, true) // 重连不删除设备记录
                                connectWifiPrinterByIp(ip)
                            }
                        }
//                        wifiPrinterDevice.iDeviceConnection?.isConnect(byteArrayOf(0)) { status ->
//                            if (status == 0) {
//                                Timber.e("重连  $s")
//                                closeWifiConnection(s, true)
//                                connectWifiPrinterByIp(s)
//                            } else {
//                                Timber.e("还连着 ->> ${s}")
//                            }
//                        }
//
                    }

                } catch (e: Exception) {
                    //   Timber.e("s->${s}  校验wifi 异常")
                    Timber.e("WiFi状态检测异常: ${e.message}")
                }
            }
        }
        timer = Timer()
        timer?.schedule(task, 14000, 10000)
    }

    //停止检测wifi打印机状态
    private fun cancelCheckWifiPrinterStatus() {
        timer?.cancel()
        timer = null
    }

    fun clear() {
//        noPrintList.clear()
        printerInfoList.clear()
        wifiPrinterDevice.forEach { s, wifiPrinterDevice ->
            wifiPrinterDevice.iDeviceConnection?.get()?.closeSync()
        }
        wifiPrinterDevice.clear()
//      wifiConnectionsStateMap.clear()

//      waitPrinterList.clear()
        cancelCheckWifiPrinterStatus()

        WifiPrintQueueManager.clearTask()

//      cancelSendWifiPrinter()
    }


//    private var printerSendTimer: Timer? = null

//    private var waitPrinterList = mutableListOf<NoPrintModel>()
//    private var lock = Object()

//    fun insetToWaitPrinterList(noPrintModel: NoPrintModel, isLock: Boolean = true) {
//        try {
//            val currentTimeStamp = Date().time
//            if (currentTimeStamp - noPrintModel.timeStamp < 30 * 60 * 1000) {
//                Timber.e("插入最后的时间")
//                /**
//                 * 只保留30分钟内的
//                 */
////                if (isLock) {
////                    synchronized(lock) {
////                        waitPrinterList.add(noPrintModel)
////                    }
////                } else
//                waitPrinterList.add(noPrintModel)
//                Timber.e("插入后剩余 ${waitPrinterList.size}")
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//
//    private fun startSendWifiPrinter() {
//        Timber.e("开始计时2222")
//        cancelSendWifiPrinter()
//
//        val task = object : TimerTask() {
//            override fun run() {
//                Timber.e("定时向wifi打印机发送")
//                try {
////                    synchronized(lock) {
//                    if (!waitPrinterList.isNullOrEmpty()) {
//                        Timber.e("剩余未打印wifi列表1111  ${waitPrinterList.size}")
//                        val noPrintModel = waitPrinterList.removeFirst()
//                        Timber.e("剩余未打印wifi列表22222  ${waitPrinterList.size}")
//                        if (wifiPrinterDevice.containsKey(noPrintModel.printerConfigInfo?.ipAddress)) {
//                            val time = Date().time
////                            Timber.e("向 ip 为 ${noPrintModel.printerConfigInfo?.ipAddress} 发送  ==>${time}    :${wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect}")
//                            Timber.e("wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect  ${wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect}")
//                            if (wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect == true) {
//                                Printer.printWifiTicket(
//                                    MyApplication.myAppInstance,
//                                    noPrintModel,
//                                    time
//                                )
//                            } else {
//                                Timber.e("ip 为 ${noPrintModel.printerConfigInfo?.ipAddress} 状态不对 ，加回待打印队列")
//                                insetToWaitPrinterList(noPrintModel, false)
//                            }
//                        }
//                    } else {
//                        Timber.e("打印列表为空")
//                    }
////                    }
//
//                } catch (e: Exception) {
//                    //   Timber.e("s->${s}  校验wifi 异常")
//                }
//            }
//        }
//
//        printerSendTimer = Timer()
//        printerSendTimer?.schedule(task, 1000, 3000)
//
//    }
//
//    private fun cancelSendWifiPrinter() {
//        printerSendTimer?.cancel()
//        printerSendTimer = null
//    }
}