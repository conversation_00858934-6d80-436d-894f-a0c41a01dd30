package com.metathought.food_order.casheir.ui.second_display.menu

import android.app.Presentation
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.cart.OrderMoreDataResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Group
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.databinding.SecondMenuOrderLayoutBinding
import com.metathought.food_order.casheir.extension.getDiningStyleString
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.ui.adapter.MenuAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondFoodCategoryAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondMenuAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondMenuOrderFoodAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondPrevoiusOrderedAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.second_display.menu.food_detail.OrderFoodDetailDialog
import com.metathought.food_order.casheir.ui.widget.DividerItemDecoration
import timber.log.Timber

/**
 * 点餐副屏(废弃)
 * Menu Order secondary
 * <AUTHOR>
 * @date 2024/5/1513:26
 * @description
 */
class MenuOrderScreenUI(private var mContext: Context, display: Display) :
    Presentation(mContext, display) {

    private lateinit var binding: SecondMenuOrderLayoutBinding
    private var adapterCategory: SecondFoodCategoryAdapter? = null
    private var menuAdapter: SecondMenuAdapter? = null
    private var menuOrderFoodAdapter: SecondMenuOrderFoodAdapter? = null
    private var gridLayoutManager: GridLayoutManager? = null
    private var detailDialog: OrderFoodDetailDialog? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = SecondMenuOrderLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initView()
        initResource()
    }

    private fun initResource() {
        binding.run {
            tvDeleteAll.text = mContext.getString(R.string.items)
            quantity.text = mContext.getString(R.string.quantity)
            amount.text = mContext.getString(R.string.amount)
            packingPrice.text = mContext.getString(R.string.packing_price)
            subtotal.text = mContext.getString(R.string.subtotal)
            vat.text = mContext.getString(R.string.vat)
            discounted.text = mContext.getString(R.string.discounted)
            totalPrice.text = mContext.getString(R.string.receivable)
        }
    }

    fun getDetailDialog(): OrderFoodDetailDialog? {
        if (detailDialog == null) {
            detailDialog = OrderFoodDetailDialog(context).apply {
                //适配商米系统副屏闪退问题
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                }
                window?.setBackgroundDrawableResource(R.drawable.background_dialog)
                show()
                //传入主屏上下文，解决多语言失效问题
                initResource(mContext)
            }
        }
        return detailDialog
    }

    fun dismissDialog() {
        detailDialog?.dismiss()
        detailDialog = null
    }

    private fun initView() {
        binding.run {
            adapterCategory = SecondFoodCategoryAdapter(arrayListOf(), mContext)
            recyclerViewCategories.adapter = adapterCategory
            gridLayoutManager = GridLayoutManager(mContext, 3)
            gridLayoutManager?.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (menuAdapter?.getItemViewType(position)) {
                        MenuAdapter.ViewType.Header.ordinal -> 3
                        MenuAdapter.ViewType.Content.ordinal -> 1
                        else -> 3
                    }
                }
            }
            menuAdapter = SecondMenuAdapter(arrayListOf(), mContext)
            recyclerViewMenu.run {
                layoutManager = gridLayoutManager
                adapter = menuAdapter
            }
            Glide.with(mContext).load(MainDashboardFragment.CURRENT_USER?.url).circleCrop()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgLogo)
            tvStoreName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
            menuOrderFoodAdapter = SecondMenuOrderFoodAdapter(arrayListOf(), mContext)
            recyclerOrderedFood.run {
                val dividerItemDecoration =
                    DividerItemDecoration(mContext, DividerItemDecoration.VERTICAL).apply {
                        setDrawable(mContext.getDrawable(R.drawable.divider_vertical_ddd))
                    }
                addItemDecoration(dividerItemDecoration)
                recyclerOrderedFood.adapter = menuOrderFoodAdapter
            }

        }
    }

    fun updateRecyclerOrderedFood(position: Int) {
        binding.run {
            recyclerOrderedFood.smoothScrollToPosition(position)
        }
    }

    fun updateRecyclerPreviousOrderedFood(position: Int) {
        binding.run {
            recyclerPreviousOrderedFood.smoothScrollToPosition(position)
        }
    }

    fun updateRecyclerMenu(position: Int) {
        binding.run {
            recyclerViewMenu.smoothScrollToPosition(position)
        }
    }

    fun updateRecyclerViewCategories(position: Int) {
        binding.run {
            recyclerViewCategories.smoothScrollToPosition(position)
        }
    }

    fun updateMenuOrderFoodAdapter(newItems: ArrayList<GoodsRequest>?) {
        newItems?.let {
            menuOrderFoodAdapter?.updateItems(it)
        }
    }

    fun updateMenuOrderFoodAdapterDining(diningStyle: Int) {
        menuOrderFoodAdapter?.updateDiningStyle(diningStyle)
    }

    fun updateByOrderMoreData(
        list: ArrayList<com.metathought.food_order.casheir.data.model.base.response_model.cart.Goods>,
        previousOrderCount: String,
        pricePreviousOrder: String
    ) {
        binding.run {
            recyclerPreviousOrderedFood.adapter = SecondPrevoiusOrderedAdapter(list, mContext)
            tvPreviousOrderCount.text = previousOrderCount
            tvPricePreviousOrder.text = pricePreviousOrder
        }
    }

    fun updateFromObserve(diningStyle: Int) {
        binding.run {
            val isOrderMore = ShoppingHelper.get(diningStyle)?.isOrderMore == true
            if (isOrderMore) {
                layoutOrderMore.isVisible = true
                layoutNewOrderTitle.isVisible = true
            } else if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                clearOrderMore()
            } else {
                clearOrderMore()
            }
        }
    }

    fun setListenerOrderMore(isNullOrEmpty: Boolean) {
        binding.run {
            if (recyclerPreviousOrderedFood.isVisible) {
                arrowNewOrder.rotation = 180f
                arrowOldOrder.rotation = 0f
                recyclerOrderedFood.isVisible = true
                recyclerPreviousOrderedFood.isVisible = false
                layoutEmpty.root.isVisible = isNullOrEmpty
                vTopLine.isVisible = !isNullOrEmpty
            } else {
                layoutEmpty.root.isVisible = false
                arrowNewOrder.rotation = 0f
                arrowOldOrder.rotation = 180f
                recyclerOrderedFood.isVisible = false
                recyclerPreviousOrderedFood.isVisible = true
                vTopLine.isVisible = true
            }
        }
    }

    fun setListenerOrderTitle(isNullOrEmpty: Boolean) {
        binding.run {
            layoutEmpty.root.isVisible = isNullOrEmpty
            vTopLine.isVisible = !isNullOrEmpty
            recyclerOrderedFood.isVisible = true
            recyclerPreviousOrderedFood.isVisible = false
        }
    }

    fun updateShoppingRecord(
        shoppingRecord: ShoppingRecord,
        localDingingStyle: Int?,
        orderMoreDataResponse: OrderMoreDataResponse?,
    ) {
        binding.run {
            shoppingRecord.let {
                val isOrderMore = it.isOrderMore ?: false
                if (isOrderMore) {
                    layoutOrderMore.isVisible = true
                    layoutNewOrderTitle.isVisible = true
                } else if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                    clearOrderMore()
                } else {
                    clearOrderMore()
                }
                tvDiningStyle.text = localDingingStyle?.getDiningStyleString(mContext)

                val goodsVoList = it.getGoodsVoList()
                layoutHeader.setVisibleInvisible(goodsVoList.isNotEmpty())
                layoutEmpty.let {
                    it.tvEmptyText.text = mContext.getString(R.string.please_select_item)
                    it.root.setVisibleInvisible(goodsVoList.isEmpty())
                    it.imgError.setImageDrawable(mContext.let { it1 ->
                        ContextCompat.getDrawable(
                            it1, R.drawable.ic_empty_food_order
                        )
                    })
                }

                vTopLine.isVisible = goodsVoList.isNotEmpty()

                layoutMainOrdered.setBackgroundResource(if (goodsVoList.isEmpty()) R.drawable.background_round_top_trasparent else R.drawable.background_round_top_white)
                recyclerOrderedFood.setVisibleInvisible(goodsVoList.isNotEmpty())
                layoutTotal.setVisibleInvisible(goodsVoList.isNotEmpty())
//                menuOrderFoodAdapter?.updateItems(goodsVoList)
//                localDingingStyle?.let { it1 ->
//                    updateMenuOrderFoodAdapter(menuOrderFoodAdapter?.list)
//                    updateMenuOrderFoodAdapterDining(it1)
//                }


                val totalPrice = it.totalPrice ?: 0
                var hasUnWeighed = false

                val sub =
                    (it.totalPrice ?: 0L) - (it.vatCharge ?: 0L)
                val subtotal = "$${sub.priceFormatTwoDigitZero()}"
                tvSubtotal.text = subtotal
                val vatStr = "$${it.vatCharge?.priceFormatTwoDigitZero()}"
                tvVat.text = vatStr
                //set customer
//                if (it.name.isNullOrEmpty()) {
//                    tvCustomerInfo.text = mContext.getString(R.string.customer_info)
//                } else {
//                    tvCustomerInfo.text = it.name
//                }
                if (it.tableLabel.isNullOrEmpty()) {
                    tvSelectTable.text = mContext.getString(R.string.select_table)
                } else {
                    tvSelectTable.text = it.tableLabel
                }

                var goodTotalNum = 0
//                    //计算会员总价
                var totalVipPrice = 0L
                //计算会员税费
                var totalVipVatPrice = 0L
                //计算打包费
                var totalPackPrice = 0
                for (goodsRequest in goodsVoList) {
                    goodsRequest.num?.let {
                        goodTotalNum += it
                    }
                    totalVipPrice += goodsRequest.totalVipPrice()

//                    totalVipVatPrice += goodsRequest.totalVipVatPrice()

                    if (it.diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
                        //打包费显示
                        totalPackPrice += goodsRequest.totalPackPrice()
                    }

                    //是否有称重商品
                    if (goodsRequest.goods?.isToBeWeighed() == true) {
                        hasUnWeighed = true
                    }
                }

                //会员价  菜品总价+税费+打包费
                totalVipPrice += totalVipVatPrice
                totalVipPrice += totalPackPrice
                tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()


//                Timber.e("totalVipVatPrice:${totalVipVatPrice}  totalPackPrice:${totalPackPrice}")
                llPackPrice.isVisible = totalPackPrice > 0
                tvPackingAmount.text = totalPackPrice.priceFormatTwoDigitZero2()


                val totalPriceFormatter =
                    (totalPrice + totalPackPrice).priceFormatTwoDigitZero2()
                tvTotalPrice.text = totalPriceFormatter

                tvVipPrice.isVisible =
                    totalVipPrice > 0 && (totalVipPrice != (totalPrice + totalPackPrice))

//                val isPaymentInAdvance = MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance
//                if (it.diningStyle == DiningStyleEnum.DINE_IN.id && isPaymentInAdvance != true && !it.isUniversalQr()) {
//                    //后付款且堂食的情况 Pay after and dine in
//                    btnPayNow.text = getString(R.string.confirmation)
//                } else {
//                    btnPayNow.text = getString(R.string.pay_now)
//                }

                //有待称重的这里统一显示
                if (hasUnWeighed) {
//                    btnPayNow.text = mContext.getString(R.string.submit_order)
                    tvTotalPrice.text = mContext.getString(R.string.to_be_weighed)
                    tvVat.text = mContext.getString(R.string.to_be_weighed)
                    tvSubtotal.text = mContext.getString(R.string.to_be_weighed)
                    tvVipPrice.isVisible = false
                }

                Timber.e("isOrderMore:  $isOrderMore")
                if (isOrderMore) {
                    layoutHeader.isVisible = true
                    layoutTotal.isVisible = true
                    layoutMainOrdered.setBackgroundResource(R.drawable.background_round_top_white)
                    if (goodsVoList.isNotEmpty()) {
                        recyclerPreviousOrderedFood.isVisible = false
                    }
                    tvNewOrderTotalPrice.text = totalPriceFormatter
                    if (it.isHasNeedProcess()) {
                        tvNewOrderTotalPrice.text = mContext.getString(R.string.to_be_confirmed)
                    }


                    tvOrderMoreCount.text = mContext.getString(R.string.new_order, goodTotalNum)
                    orderMoreDataResponse?.apply {
                        val oldOrderTotal = goodsJsonList?.totalPrice ?: 0
                        val oldOrderVipTotal = goodsJsonList?.totalVipPrice ?: 0

                        val oldOrderSubtotal = oldOrderTotal - goodsJsonList?.totalVatPrice!!
                        val finalTotalPrice = totalPrice + oldOrderTotal
                        val finalTotalVipPrice = totalVipPrice + oldOrderVipTotal
                        Timber.e("会员价:  $totalVipPrice   oldOrderVipTotal:${oldOrderVipTotal}")
                        val finalSubtotal = sub + oldOrderSubtotal
                        tvTotalPrice.text = "$${finalTotalPrice.priceFormatTwoDigitZero()}"
                        tvVipPrice.text = "$${finalTotalVipPrice.priceFormatTwoDigitZero()}"
                        tvVipPrice.isVisible =
                            finalTotalVipPrice > 0 && finalTotalVipPrice != finalTotalPrice
                        tvSubtotal.text = "$${finalSubtotal.priceFormatTwoDigitZero()}"
                        val totalSeriveCharge =
                            it.vatCharge?.plus(goodsJsonList.totalVatPrice)
                        tvVat.text = "$${totalSeriveCharge?.priceFormatTwoDigitZero()}"

                        if (!goodsJsonList.isHasNeedProcess() || hasUnWeighed) {
//                            btnPayNow.text = getString(R.string.submit_order)
                            tvTotalPrice.text = mContext.getString(R.string.to_be_weighed)
                            tvVat.text = mContext.getString(R.string.to_be_weighed)
                            tvSubtotal.text = mContext.getString(R.string.to_be_weighed)
                            tvVipPrice.isVisible = false
                        }

                    }

                }

            }
        }
    }

    private fun clearOrderMore() {
        binding.apply {
            layoutOrderMore.isVisible = false
            layoutNewOrderTitle.isVisible = false
            recyclerPreviousOrderedFood.isVisible = false
        }
    }

    /**
     * 更新菜品滑动距离
     * Update the sliding distance of dishes
     */
    fun updateMenuPosition(groupId: String) {
        binding.run {
            menuAdapter?.getHeaderPosition(groupId)?.let { it1 ->
                (recyclerViewMenu.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                    it1,
                    0
                )
            }
        }
    }

    /**
     * 更新菜品分类适配器
     * Update the dish classification adapter
     */
    fun updateCategoryAdapter(current: Group) {
        adapterCategory?.run {
//            val preIndex=categories.indexOf(pre)
//            val nextIndex=categories.indexOf(next)
//            if(preIndex!=-1){
//                notifyItemChanged(preIndex)
//            }
//            if(nextIndex!=-1){
//                notifyItemChanged(nextIndex)
//            }
            notifyDataSetChanged()
        }
    }


    fun updateMenuAdapterAndScrollPosition(list: ArrayList<BaseGoods>?) {
        updateMenuAdapter(list)
        binding.run {
            recyclerViewMenu.layoutManager?.scrollToPosition(0)
        }
    }

    /**
     * 更新菜品适配器
     * Update the dish adapter
     */
    fun updateMenuAdapter(list: ArrayList<BaseGoods>?) {
        list?.let {
            menuAdapter?.updateItems(it)
        }
    }

    fun updateMenuAdapterAndLocalDiningStyle(diningStyle: Int) {
        menuAdapter?.localDiningStyle = diningStyle
        updateMenuAdapter()
    }

    fun updateMenuAdapter() {
        menuAdapter?.notifyDataSetChanged()
    }

    fun updateCategoryAdapter(newItems: ArrayList<Group>?) {
        newItems?.let {
            adapterCategory?.updateItems(newItems)
        }
    }


    override fun onStart() {
        super.onStart()
        Log.e("SecondaryLog", "MenuOrderScreenUI_onStart")
    }

    override fun onStop() {
        super.onStop()
        Log.e("SecondaryLog", "MenuOrderScreenUI_onStop")
    }

    override fun onDisplayRemoved() {
        super.onDisplayRemoved()
        Log.e("SecondaryLog", "MenuOrderScreenUI_onDisplayRemoved")
    }

    /**
     * 初始化菜品分类 Initialize the food classification
     * 初始化菜品 Initialize the food
     */
//    fun initAdapter(adapterCategory: FoodCategoryAdapter?, menuAdapter: MenuAdapter?) {
//        if(!isShowing){
//            show()
//        }
//        this.adapterCategory = adapterCategory
//        this.menuAdapter = menuAdapter
//        binding.run {
//            val gridLayoutManager = GridLayoutManager(mContext, 3)
//            gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
//                override fun getSpanSize(position: Int): Int {
//                    return when (menuAdapter?.getItemViewType(position)) {
//                        MenuAdapter.ViewType.Header.ordinal -> 3
//                        MenuAdapter.ViewType.Content.ordinal -> 1
//                        else -> 3
//                    }
//                }
//            }
//            recyclerViewCategories.adapter = adapterCategory
//            recyclerViewMenu.layoutManager = gridLayoutManager
//            recyclerViewMenu.adapter = menuAdapter
//        }
//    }


}