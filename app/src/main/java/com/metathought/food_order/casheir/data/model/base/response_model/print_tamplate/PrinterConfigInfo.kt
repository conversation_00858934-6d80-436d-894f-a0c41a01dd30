package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate

import android.content.Context
import android.hardware.usb.UsbDevice
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum


/**
 *<AUTHOR>
 *@time  2024/7/22
 *@desc
 **/
enum class PrinterTypeEnum(val type: Int) {
    CLOUD(1),  //云打印机
    USB(2),  //USB打印机
    WIFI(3),  //WIFI打印机
}

enum class PrinterWidthTypeEnum(val type: String) {
    FIFTY_EIGHT("58"),  //云打印机
    EIGHTY("80"),  //USB打印机
}


data class PrinterConfigInfo(
    //
    var id: String? = null,
    //打印机名称
    val name: String? = null,
    //商店id
    val storeId: Long? = null,
    //型号:58、80
    var model: String? = null,
    //sn码
    var sn: String? = null,
    //品牌
    val brand: String? = null,
    //打印机放置位置
    val position: String? = null,
    //状态: 1未启用，2启用
    val status: Int? = null,
    //1在线，2其他状态
    val onlineStatus: Int? = null,
    //打印份数
//    val copies: Int? = null,
    //打印模版ids
    val templateIds: String? = null,
    //是否自动打印, 默认是自动打印
    val isAutoPrint: Boolean? = null,
    //打印机类型, 默认为1-云打印机, 2-usb打印机  3-wifi打印机
    var type: Int? = null,

    var ipAddress: String? = null,

    var createTime: String? = null,

    var updateTimePLATE: String? = null,

    //模板列表
    var printerTemplateList: ArrayList<PrintTamplateResponseItem>? = null,

    /**
     * 是否打印临时桌码
     */
    @SerializedName("printTempTableCode")
    val printTempTableCode: Boolean?,

    /**
     *  标签打印还是小票打印 1 小票 2标签
     */
    val machineType: Int? = null,

    /**
     *  usb 打印机信息  本地的
     */
    var usbDevice: UsbDevice? = null
) {

    fun isLabelPrinter(): Boolean {
        return machineType == LocalPrinterEnum.LABEL_PRINTER.id
    }

    fun getPrinterType(context: Context): String {
        if (type == PrinterTypeEnum.WIFI.type) {
            return context.getString(R.string.m_pos_wifi_printer)
        } else if (type == PrinterTypeEnum.USB.type) {
            return context.getString(R.string.m_pos_usb_printer)
        }
        return ""
    }

    //是否80
    fun isEightyWidth(): Boolean {
        return model == PrinterWidthTypeEnum.EIGHTY.type
    }

    //获取已配置模板描述
    fun getPrinterTempDesc(context: Context): String {
        var desc = ""
        //过滤掉其他打印
//        val list = printerTemplateList?.filter { (it.type ?: 0) <= 7 || (it.type ?: 0) == 10 }
        printerTemplateList?.forEach {
            val singleDesc = when (it.type) {
                PrintTemplateTypeEnum.KITCHEN.id -> {
                    if (!it.storeKitchenName.isNullOrEmpty()) {
                        "${context.getString(R.string.cook_bill)}-${it.storeKitchenName}"
                    } else {
                        "${context.getString(R.string.cook_bill)}-${context.getString(R.string.main_kitchen)}"
                    }
                }

                PrintTemplateTypeEnum.DINE_IN.id -> context.getString(R.string.dine_in_bill)
                PrintTemplateTypeEnum.TAKE_AWAY.id -> context.getString(R.string.take_away_bill)
                PrintTemplateTypeEnum.PRE_ORDER.id -> context.getString(R.string.pre_order_bill)
                PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id -> context.getString(R.string.checkout_bill)
                PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id -> context.getString(R.string.pre_checkout_bill)
                PrintTemplateTypeEnum.PRODUCT_REPORT.id -> context.getString(R.string.product_report_ticket)
                PrintTemplateTypeEnum.PAYMENT_METHOD_REPORT.id -> context.getString(R.string.payment_method_report)
                PrintTemplateTypeEnum.LABEL.id -> context.getString(R.string.label_stickers)
                PrintTemplateTypeEnum.SHIFT_HANDOVER.id -> context.getString(R.string.closing_report_ticket)
                PrintTemplateTypeEnum.TABLE_REPORT.id -> context.getString(R.string.table_report_receipt)
                PrintTemplateTypeEnum.STORE_PROFIT_REPORT.id -> context.getString(R.string.store_profit_report_ticket)
                PrintTemplateTypeEnum.MASTER_REPORT.id -> context.getString(R.string.comprehensive_report_ticket)
                PrintTemplateTypeEnum.TAKE_OUT_TICKET.id -> context.getString(R.string.take_out_ticket)
                else -> {
                    ""
                }
            }

            desc = if (desc.isEmpty()) {
                singleDesc
            } else {
                "${desc}、${singleDesc}"
            }
        }
        return desc
    }


    fun isPrintProductReport(): Boolean {
        return !printerTemplateList?.filter { it.type == PrintTemplateTypeEnum.PRODUCT_REPORT.id }
            .isNullOrEmpty()
    }

    fun isPrintPaymentMethodReport(): Boolean {
        return !printerTemplateList?.filter { it.type == PrintTemplateTypeEnum.PAYMENT_METHOD_REPORT.id }
            .isNullOrEmpty()
    }


    fun isPrintShiftHandoverReport(): Boolean {
        return !printerTemplateList?.filter { it.type == PrintTemplateTypeEnum.SHIFT_HANDOVER.id }
            .isNullOrEmpty()
    }

    fun getPrintShiftHandoverReportTmp(): PrintTamplateResponseItem? {
        return printerTemplateList?.first { it.type == PrintTemplateTypeEnum.SHIFT_HANDOVER.id }
    }

    fun getPrintStoreProfitReport(): PrintTamplateResponseItem? {
        return printerTemplateList?.firstOrNull { it.type == PrintTemplateTypeEnum.STORE_PROFIT_REPORT.id }

    }

    fun isPrintMasterReport(): Boolean {
        return !printerTemplateList?.filter { it.type == PrintTemplateTypeEnum.MASTER_REPORT.id }
            .isNullOrEmpty()
    }
}