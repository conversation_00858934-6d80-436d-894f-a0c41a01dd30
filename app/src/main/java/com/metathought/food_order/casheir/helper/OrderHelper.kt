package com.metathought.food_order.casheir.helper

import android.content.Context
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.SingleDiscountReasonTypeEnum
import com.metathought.food_order.casheir.constant.SingleDiscountType
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.MealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetChooseItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.SingleItemDiscount
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toHexStr
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import timber.log.Timber
import java.util.UUID


object OrderHelper {
    fun getGoodsBoList(shareRecord: ArrayList<OrderedGoods>?): ArrayList<GoodsBo> {
        val goodsBoList = ArrayList<GoodsBo>()
        shareRecord?.forEach {
            goodsBoList.add(
                orderedGoodsToGoodsBo(it)
            )
        }
        return goodsBoList
    }

    /**
     * OrderGood转成请求的bo
     *
     * @param orderedGood
     * @return
     */
    private fun orderedGoodsToGoodsBo(orderedGood: OrderedGoods): GoodsBo {
        val bo = GoodsBo(
            id = orderedGood.id,
            num = orderedGood.num,
            tagItemId = orderedGood.getTagItemId(),
            feeds = orderedGood.getFeedBo(),
            pricingMethod = orderedGood.pricingMethod,
            weight = orderedGood.weight,
            uuid = orderedGood.uuid,
            discountPrice = orderedGood.discountPrice,
            goodsType = orderedGood.goodsType,
            mealSetGoodsDTOList = getSelectMealSetGood(orderedGood.orderMealSetGoodsDTOList),
            singleItemDiscount = orderedGood.singleItemDiscount?.coverToSingleDiscountRequest(),
            goodsHashKey = orderedGood.getHash(),
            note = orderedGood.note,
        )
        if (orderedGood.isTimePriceGood()) {
            bo.pricingCompleted = orderedGood.isHasCompletePricing()
            bo.sellPrice = orderedGood.sellPrice
            bo.vipPrice = orderedGood.vipPrice
            bo.vipPrice = orderedGood.vipPrice
        }

        return bo
    }


    /**
     * 根据 合单顺序排序菜品
     *  removeGoodList. 退菜列表
     */
    fun sortingGoodsWithMergeOrder(
        context: Context,
        mergeOrderIds: String? = null,
        acceptOrderIds: String? = null,
        goods: ArrayList<OrderedGoods>? = null,
        removeGoodList: ArrayList<OrderedGoods>? = null
    ): ArrayList<BaseOrderGoods>? {
        var list: MutableList<BaseOrderGoods> = mutableListOf()
        if (mergeOrderIds.isNullOrEmpty()) {
            /**
             * 如果没有合并订单
             */
            list = sortingGoodsByAddTimes(context, acceptOrderIds, goods)
        } else {
            val mergeOrderIdsList =
                mergeOrderIds.split(",")
            mergeOrderIdsList.forEachIndexed { index, orderId ->
                val head = HeaderOrderGoods(
                    orderId = orderId,
                    title = context.getString(R.string.order_index, index + 1)
                )
                head.type = OrderedInfoAdapter.OrderGoodViewType.MERGE_INFO.name
                list.add(
                    head
                )
                list.addAll(
                    sortingGoodsByAddTimes(
                        context,
                        acceptOrderIds,
                        ArrayList(goods?.filter { good -> good.orderId == orderId }
                            ?: listOf())).toList())

            }
        }

        if (!removeGoodList.isNullOrEmpty()) {
            val head = HeaderOrderGoods(
                orderId = "",
                title = ""
            )
            head.type = OrderedInfoAdapter.OrderGoodViewType.REMOVE_INFO_TITLE.name
            list.add(
                head
            )
            removeGoodList.forEach {
                it.type = OrderedInfoAdapter.OrderGoodViewType.REMOVE_INFO.name
            }
            list.addAll(
                removeGoodList
            )
        }

        return ArrayList(list)
    }

    /**
     * 根据 加购顺序排序菜品
     *
     */
    private fun sortingGoodsByAddTimes(
        context: Context,
        acceptOrderIds: String? = null,
        goods: ArrayList<OrderedGoods>? = null
    ): ArrayList<BaseOrderGoods> {
        val list: MutableList<BaseOrderGoods> = mutableListOf()
        val acceptOrderIdsList =
            (acceptOrderIds ?: "").split(",")
        if (acceptOrderIdsList.size <= 1) {
            return goods?.let { ArrayList(it) } ?: ArrayList()
        } else {
            //根据acceptOrderId 去重后的数组
            val afterDistinctList = goods?.distinctBy { it.acceptOrderId } ?: listOf()
            acceptOrderIdsList.forEachIndexed { index, orderId ->
                val filterResult = goods?.filter { good -> good.acceptOrderId == orderId }
                    ?: listOf()
                if (filterResult.isNotEmpty()) {
                    if (afterDistinctList.size > 1) {
                        //如果加购次数大于1 才显示出来加购次数
                        val addInfo = HeaderOrderGoods(
                            orderId = orderId,
                            title = context.getString(
                                R.string.add_good_time,
                                filterResult.first().acceptOrderCount
                            ),
                            time = filterResult.first().acceptOrderTime
                        )
                        addInfo.type = OrderedInfoAdapter.OrderGoodViewType.ADD_INFO.name
                        list.add(addInfo)
                    }
                    list.addAll(filterResult)
                }
            }

            if (list.isEmpty()) {
                return goods?.let { ArrayList(it) } ?: ArrayList()
            }
        }
        return ArrayList(list)
    }


    /**
     * Merge goods for print
     *
     * @param orderedInfoResponse
     * @param isKitchen 是否厨打
     * @return
     */
    fun mergeGoodsForPrint(
        goods: ArrayList<OrderedGoods>?,
        isKitchen: Boolean
    ): ArrayList<OrderedGoods>? {
        val list: MutableList<OrderedGoods> = mutableListOf()

        val orderGroup = goods?.groupBy {
            getGoodSkyKey(
                orderedGood = it,
                goodsId = it.id!!,
                feedList = ArrayList(it.feeds ?: listOf()),
                goodsTagItemList = ArrayList(it.tagItems ?: listOf()),
                orderMealSetGoodList = it.orderMealSetGoodsDTOList,
                goodsPriceKey = if (isKitchen) null else (if (it.isModifyPriceSuccess()) it.goodsPriceKey else null),   //如果非改价这个要为null
                acceptOrderId = null,
                orderId = null,
                pricingMethod = it.pricingMethod,
                weight = it.weight,
                uuid = it.uuid,
                singleItemDiscount = if (isKitchen) null else it.singleItemDiscount,
                packingFeeKey = if (isKitchen) null else it.getPackingFeeKeyStr(),
                salePriceKey = if (isKitchen) null else "${it.sellPrice}${it.vipPrice}",
                note = it.note
            )
        }

        val result = orderGroup?.map { (key, group) ->
            val list = group.toMutableList()
            var firstData = list.removeFirst().copy()
            var totalSingleGoodsReducePrice = firstData.totalPriceWithSingleDiscount()
            var totalSingleGoodsReduceVipPrice = firstData.totalVipPriceWithSingleDiscount()
            //合并相同菜品  金额也要算  同个商品可能单品减免价格不一样
            list.forEach {
                firstData.num = (firstData.num ?: 0) + (it.num ?: 0)
                Timber.e("  ${firstData.totalPriceWithSingleDiscount()}  it.totalSingleGoodsReducePrice  ${it.totalPriceWithSingleDiscount()}")
                totalSingleGoodsReducePrice += it.totalPriceWithSingleDiscount()
                totalSingleGoodsReduceVipPrice += it.totalVipPriceWithSingleDiscount()
            }

            firstData.totalSingleGoodsReducePrice = totalSingleGoodsReducePrice
            firstData.totalSingleGoodsReduceVipPrice = totalSingleGoodsReduceVipPrice

            firstData
        } ?: listOf()

        list.addAll(result)

        return ArrayList(list)
    }

    /**
     * 获取HashKey
     *
     * @param goodsId
     * @param feedList
     * @param goodsTagItemList
     * @param pricingMethod
     * @param weight
     * @return
     */
    fun getGoodSkyKey(
        orderedGood: OrderedGoods,
        goodsId: String,
        feedList: ArrayList<Feed>?,
        goodsTagItemList: ArrayList<GoodsTagItem>?,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        goodsPriceKey: String? = null,
        acceptOrderId: String? = null,
        orderId: String? = null,
        pricingMethod: Int?,
        weight: Double?,
        uuid: String?,
        singleItemDiscount: SingleItemDiscount? = null,
        packingFeeKey: String? = null,
        salePriceKey: String? = null,
        note: String? = null
    ): String {
        val sbf = StringBuffer()
        sbf.append(goodsId)

        if (uuid != null) {
            sbf.append(uuid)
        }

        if (salePriceKey != null) {
            sbf.append(salePriceKey)
        }
        if (note != null) {
            sbf.append(note)
        }


        if (!packingFeeKey.isNullOrEmpty()) {
            sbf.append(packingFeeKey)
        }

        // 获取商品设置过单品减免、折扣、改价类型+原因key下划线拼接,没设置过单品改价则返回空字符串
        if (singleItemDiscount != null) {
            val discountTypeRemarkKey =
                "${singleItemDiscount.discountType ?: SingleDiscountReasonTypeEnum.DISCOUNT.code}${singleItemDiscount.remark}${singleItemDiscount.discountReduceActivityId ?: ""}"
            sbf.append("$discountTypeRemarkKey")
            if (singleItemDiscount.type == SingleDiscountType.PERCENTAGE.id) {
                if (singleItemDiscount.reduceRatio != null) {
                    sbf.append("${singleItemDiscount.reduceRatio?.decimalFormatTwoDigitZero2()}${singleItemDiscount.type}")
                }
                val singleItemDiscountUUid = if (singleItemDiscount.uuid.isNullOrEmpty()) {
                    UUID.randomUUID().toString()
                } else {
                    singleItemDiscount.uuid
                }
                sbf.append(singleItemDiscountUUid)
            } else if (singleItemDiscount.type == SingleDiscountType.FIXED_AMOUNT.id) {
                if (singleItemDiscount.saleReduce != null || singleItemDiscount.vipReduce != null) {
                    sbf.append(singleItemDiscount.type)
                }
                if (singleItemDiscount.discountReduceActivityId != null) {
                    //如果是选择后台减免的那就加个随机数不合并
                    val singleItemDiscountUUid = if (singleItemDiscount.uuid.isNullOrEmpty()) {
                        UUID.randomUUID().toString()
                    } else {
                        singleItemDiscount.uuid
                    }
                    sbf.append(singleItemDiscountUUid)
                }
            } else if (singleItemDiscount.type == SingleDiscountType.MODIFY_PRICE.id) {
                if (singleItemDiscount.adjustSalePrice != null) {
                    sbf.append(
                        "${
                            (singleItemDiscount.adjustSalePrice ?: 0.0).times(100.0).toLong()
                        }"
                    )
                }
                if (singleItemDiscount.adjustVipPrice != null) {
                    sbf.append(
                        "${
                            (singleItemDiscount.adjustVipPrice ?: 0.0).times(100.0).toLong()
                        }"
                    )
                }
                if (singleItemDiscount.adjustSalePrice != null || singleItemDiscount.adjustVipPrice != null) {
                    sbf.append(singleItemDiscount.type)
                }
            }
        }


        if (!goodsPriceKey.isNullOrEmpty()) {
            sbf.append(goodsPriceKey)
        }

        if (acceptOrderId != null) {
            sbf.append(acceptOrderId)
        }
        if (orderId != null) {
            sbf.append(orderId)
        }


        if (!feedList.isNullOrEmpty()) {
            feedList.sortedBy { it.id }.forEach {
                sbf.append("${it.id}${it.alreadyNum}")
            }
        }
        if (!goodsTagItemList.isNullOrEmpty()) {
            goodsTagItemList.sortedBy { it.id }.forEach {
                sbf.append(it.id)
            }
        }

        //先对套餐内商品分组排序， 在对分组内商品排序,在拼接分组id+商品id+数量+规格（排序）
        if (!orderMealSetGoodList.isNullOrEmpty()) {
            orderMealSetGoodList.sortedWith(
                compareBy<OrderMealSetGood> { it.mealSetGroupId }
                    .thenBy { it.mealSetGoodsId }
            ).forEach {
                sbf.append(it.mealSetGroupId)
                sbf.append(it.mealSetGoodsId)
                sbf.append(it.number)
                if (!it.uuid.isNullOrEmpty()) {
                    sbf.append(it.uuid)
                }
                //获取 套餐内商品的tagItems 列表，排序后拼接成字符串
                if (!it.mealSetTagItemList.isNullOrEmpty()) {
                    it.mealSetTagItemList?.sortedBy { it.id }?.forEach {
                        sbf.append(it.id)
                    }
                }
            }
        }

        if (pricingMethod != null && pricingMethod > 0) {
            sbf.append(weight ?: 0)
            // 随机数，称重商品是分开称重的，所以需要加上随机数,防止两个称重商品的hashKey一样
            sbf.append(UUID.randomUUID())
        }
        val hash = sbf.toString().toHexStr()
        return hash
    }


    /**
     * 点套餐转成返回的的model
     *
     * @param groupId
     * @param goods
     * @param item
     * @return
     */
    fun mealSetGoodsToOrderMealSetGood(
        groupId: String,
        goods: MealSetGoods,
        item: MealSetChooseItem,
        pricingMethod: Int? = null,
        weight: Double? = null,
        weighingCompleted: Boolean? = false,
    ): OrderMealSetGood {
        return OrderMealSetGood().apply {
            mealSetGroupId = groupId
            mealSetGoodsName = goods.name
            mealSetGoodsId = goods.goodsId
            mealSetTagItemList = item.selectTag
            num = goods.num
            priceMarkup = goods.priceMarkup
            number = item.selectNum
            this.pricingMethod = pricingMethod
            this.weight = weight
            this.weighingCompleted = weighingCompleted
            this.isProcessed = weighingCompleted
        }
    }


    /**
     * 套餐内容已选描述
     *
     * @return
     */
    fun getSelectMealSetGoodStr(orderMealSetGoodsDTOList: List<OrderMealSetGood>? = null): String {
        var desc = ""
        orderMealSetGoodsDTOList?.forEach { item ->
            val number = item.number ?: 0
            if (number >= 0) {
                var name = item.mealSetGoodsName
                var tagStr = ""

                item.mealSetTagItemList?.forEach { tag ->
                    if (tagStr.isEmpty()) {
                        tagStr = "${tag.name}"
                    } else {
                        tagStr = "${tagStr},${tag.name}"
                    }
                }
                if (item.isToBeWeighed() && item.isHasCompleteWeight()) {
                    tagStr = if (tagStr.isEmpty()) {
                        item.getWeightStr()
                    } else {
                        "${tagStr},${item.getWeightStr()}"
                    }
                }

                if (tagStr.isNotEmpty()) {
                    tagStr = "(${tagStr})"
                }
                if ((item.num ?: 1) > 1) {
                    tagStr = "${tagStr}*${item.num ?: 1} "
                }
                if (number > 1) {
                    tagStr = "${tagStr}x${number}"
                }

                if (desc.isEmpty()) {
                    desc = "${name}${tagStr}"
                } else {
                    desc = "${desc},${name}${tagStr}"
                }
            }
        }
        return desc
    }


    fun getMealSetGoodStr(good: OrderMealSetGood): String {
        val name = good.mealSetGoodsName
        var tagStr = ""

        good.mealSetTagItemList?.forEach { tag ->
            tagStr = if (tagStr.isEmpty()) {
                "${tag.name}"
            } else {
                "${tagStr},${tag.name}"
            }
        }

        if (good.isToBeWeighed() && good.isHasCompleteWeight()) {
            tagStr = if (tagStr.isEmpty()) {
                good.getWeightStr()
            } else {
                "${tagStr},${good.getWeightStr()}"
            }
        }

        if (tagStr.isNotEmpty()) {
            tagStr = "(${tagStr})"
        }
        if ((good.num ?: 1) > 1) {
            tagStr = "${tagStr}*${good.num ?: 1} "
        }

        return "${name}${tagStr}"
    }

    /**
     * 套餐内容转换
     *
     * @return
     */
    fun getSelectMealSetGood(orderMealSetGoodList: List<OrderMealSetGood>? = null): List<MealSetGood> {
        var list = mutableListOf<MealSetGood>()
        orderMealSetGoodList?.forEach { item ->
            var tagsId = ""
            if ((item.number ?: 0) >= 0) {
                item.mealSetTagItemList?.sortedBy { it.id }?.forEach { tag ->
                    if (tagsId.isEmpty()) {
                        tagsId = "${tag.id}"
                    } else {
                        tagsId = "${tagsId},${tag.id}"
                    }
                }
                list.add(
                    MealSetGood(
                        item.mealSetGroupId,
                        item.mealSetGoodsId,
                        item.number,
                        tagsId,
                        item.weight,
                        item.uuid
                    )
                )
            }
        }
        return list
    }

    /**
     * 获取最后一次加购的菜品
     *
     */
    fun getLastAddGoods(orderedInfoResponse: OrderedInfoResponse?): ArrayList<OrderedGoods> {
        val acceptOrderIdsList =
            (orderedInfoResponse?.acceptOrderIds ?: "").split(",")
        val mergeOrderIdsList =
            (orderedInfoResponse?.mergeOrderIds ?: "").split(",")
        var acceptOrderId = ""
        /**
         * 先查出主单最后一次加购的单号
         */
        if (orderedInfoResponse?.mergeOrderIds.isNullOrEmpty()) {
            /**
             * 没合单过，订单数大于1  打印最后一笔订单
             */
            if (acceptOrderIdsList.size > 1) {
                acceptOrderId = acceptOrderIdsList.last()
            }
        } else {
            val goods =
                orderedInfoResponse?.goods?.filter { it.orderId == mergeOrderIdsList.first() }

            //获取主单的订单id列表
            val afterDistinctList = (goods?.distinctBy { it.acceptOrderId } ?: listOf()).map {
                it.acceptOrderId ?: ""
            }

            if (afterDistinctList.size > 1) {
                //根据acceptOrderId 去重后的数组
                acceptOrderId = afterDistinctList.last()
            }
        }


        return ArrayList(
            (orderedInfoResponse?.goods
                ?: ArrayList()).filter { it.acceptOrderId == acceptOrderId })
    }


    /**
     * 计算规格小料价格
     *
     */
    fun calculateTagPrice(
        feedInfoList: ArrayList<Feed>?,
        goodsTagItems: ArrayList<GoodsTagItem>?
    ): Long {
        var targetPrice = 0L
        goodsTagItems?.forEach {
            targetPrice += it.price?.toLong() ?: 0
        }
        feedInfoList?.forEach {
            val price = it.sum?.times(it.alreadyNum!!)
            targetPrice += price?.toInt() ?: 0
        }
        return targetPrice
    }

}