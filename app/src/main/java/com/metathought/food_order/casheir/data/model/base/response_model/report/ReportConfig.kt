import com.google.gson.annotations.SerializedName

data class ReportConfig(
    @SerializedName("saasStoreId") val saasStoreId: String,
    @SerializedName("id") val id: String,
    @SerializedName("createTime") val createTime: String,
    @SerializedName("updateTime") val updateTime: String,
    @SerializedName("params") val params: Map<String, Any>,
    @SerializedName("userId") val userId: String,
    //报表名称
    @SerializedName("reportName") val reportName: String? = null,
    //报表配置数据
    @SerializedName("configJson") val configJson: String? = null
)