package com.metathought.food_order.casheir.ui.adapter

import android.graphics.Color
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.databinding.ItemPopupOfflineChannelBinding

/**
 * <AUTHOR>
 * @date 2025/04/03 11:22
 * @description
 */
class PopUpOfflineChannelsAdapter(
    val list: ArrayList<OfflineChannelModel>,
    val callBack: ((OfflineChannelModel) -> Unit)? = null
) : RecyclerView.Adapter<PopUpOfflineChannelsAdapter.OfflineChannelItemViewHolder>() {

    private var selectedPosition = 0

    inner class OfflineChannelItemViewHolder(val binding: ItemPopupOfflineChannelBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {

            itemView.setOnClickListener {
                if (bindingAdapterPosition != -1) {
                    callBack?.invoke(list[bindingAdapterPosition])
                }
            }
        }

        fun bind(resource: OfflineChannelModel?, position: Int) {
            resource?.let {
                binding.apply {
                    tvChannelName.text = resource.getSpannableStringBuilder(
                        itemView.context,
                        itemView.context.getColor(R.color.black),
                        itemView.context.getColor(R.color.black60)
                    )
                    root.setBackgroundColor(
                        if (position == selectedPosition) itemView.context.getColor(
                            R.color.color_efefef
                        ) else itemView.context.getColor(R.color.white)
                    )
                }
            }
        }
    }

    fun setSelectIndex(index: Int) {
        selectedPosition = index
        notifyDataSetChanged()
    }
//
//    fun updateData(mList: OfflineChannelTotalModel) {
//        list.clear()
//        val tmp = mList.filter { it.selected == true }
//        list.addAll(tmp)
//
//        notifyDataSetChanged()
//    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): OfflineChannelItemViewHolder {
        val itemView = ItemPopupOfflineChannelBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )

        return OfflineChannelItemViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OfflineChannelItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }
}