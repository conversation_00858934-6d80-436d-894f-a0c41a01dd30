package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.dboy.chips.ChipsLayoutManager
import com.lxj.xpopup.util.KeyboardUtils
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTag
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.databinding.SpecificationMainItemBinding
import com.metathought.food_order.casheir.databinding.SpecificationWeightItemBinding
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.WeightScaleManager
import timber.log.Timber


class MainSpecificationAdapter(
    val list: List<GoodsTag>,
    val context: Context,
    var isShowWeight: Boolean = false,  //是否显示称重
    var currentPrice: Long? = null,    //称重价格
    var weightUnit: String? = null,    //称重单位
    var isOpenManualInput: Boolean = false, //称重是否手动输入
    var oldWeight: Double? = null, //旧重量
    val onClickCallback: (GoodsTag) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    inner class MainSpecificationViewHolder(val binding: SpecificationMainItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodsTag?) {
            resource?.let { it1 ->
                binding.tvTitle.text = resource.name
                val chipsLayoutManager = ChipsLayoutManager.newBuilder(context).build()
//                chipsLayoutManager.isAutoMeasureEnabled = true
                binding.recyclerSpecificationItem.layoutManager = chipsLayoutManager
                resource.goodsTagItems.let {
                    resource.goodsTagItems?.let { item ->
                        binding.recyclerSpecificationItem.adapter = SpecificationSubItemAdapter(
                            resource.type ?: 0,
                            item,
                            context
                        ) {
                            onClickCallback.invoke(resource)
                        }
                    }
                }

                binding.apply {

                }
            }
        }
    }

    private lateinit var weightManager: WeightScaleManager

    var weight: String? = null
        private set


    var onSwitchOpenManualInput: ((Boolean) -> Unit)? = null

    inner class SpecificationWeightViewHolder(val binding: SpecificationWeightItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val Period = "."
        private val Zero = "0"

        private var beforeWeight = ""
        private var isOpenManualInput = <EMAIL>

        init {
            weightManager = WeightScaleManager.getInstance()
        }

        @SuppressLint("SetTextI18n")
        fun bind() {
            binding.apply {
                tvSinglePrice.text =
                    "${context.getString(R.string.single_price)}:${
                        (currentPrice ?: 0).priceFormatTwoDigitZero2()
                    }/${weightUnit}"
                tvUnit.text = weightUnit
//                edtWeight.apply {
//                    clearFocus()
//                    isFocusable = false  // 临时设置为不可获取焦点，防止自动弹出键盘
//                }
                llWeightSeting.isVisible = false
                val isSupportScale = weightManager.isSupportScale()
                if (isSupportScale && weightManager.isAllConnectedFail() == null) {
                    if (weightManager.isConnected()) {
                        binding?.llWeightSeting?.isVisible = true
                        updateSwitch()
                    } else {
                        weightManager.setConnectionCallback(object :
                            WeightScaleManager.ConnectionCallback {
                            override fun onConnected(portName: String) {
                                // 连接成功，显示开关
                                binding?.apply {
                                    llWeightSeting?.post {
                                        llWeightSeting?.isVisible = true
                                        updateSwitch()
                                    }
                                    // 连接成功时，输入框不获取焦点
//                                    edtWeight.clearFocus()
                                }
                                Timber.d("电子秤连接成功: $portName")
                            }

                            override fun onConnectionFailed(error: String) {
                                // 连接失败，保持隐藏状态
                                binding?.apply {
                                    llWeightSeting?.post {
                                        llWeightSeting?.isVisible = false
                                    }
                                    // 连接失败时，让输入框获取焦点
                                    edtWeight.post {
//                                        edtWeight.requestFocus()
//                                        KeyboardUtils.showSoftInput(edtWeight)
                                    }
                                }
                                Timber.e("电子秤连接失败: $error")
                            }
                        })
                    }
                } else {
                    Timber.e("不支持电子秤")
                    llWeightSeting.isVisible = false
                    edtWeight.apply {
//                        requestFocus()
//                        isFocusable = true  // 临时设置为不可获取焦点，防止自动弹出键盘
                    }
//                    KeyboardUtils.showSoftInput(edtWeight)
                }

                ivSwitch.setOnClickListener {
                    isOpenManualInput = !isOpenManualInput
                    updateSwitch()
                }
                if (oldWeight != null) {
                    edtWeight.setText(
                        (if (oldWeight?.isInt() == true) oldWeight!!.toInt() else (oldWeight
                            ?: 0)).toString()
                    )
                }
                edtWeight.addTextChangedListener(object : TextWatcher {
                    override fun afterTextChanged(s: Editable?) {
                        binding?.apply {
                            var text = s.toString()
                            if (text.isNullOrEmpty()) {
                                return
                            }

                            //加个这个是自动读取数值的时候会有小于0的情况
                            if ((text.toDoubleOrNull() ?: 0.0) < 0) {
                                s?.clear()
                                return
                            }

                            //自动补0
                            if (text.startsWith(Period)) {
                                text = Zero + s
                                s?.replace(0, s.length, text.trim());
                            }

                            //首位是0 只能输入1个0
                            if (text.trim().length > 1) {
                                if (text.startsWith("0.")) {

                                } else if (text.startsWith("0")) {
                                    text = beforeWeight
                                    s?.replace(0, s.length, text)
                                }
                            }

                            Timber.e("3 ->${text} ${s.toString()}")
                            val regex = "\\d{0,3}(\\.\\d{0,3})?"
                            if (!text.matches(regex.toRegex())) {
                                var index: Int = text.indexOf('.')
                                if (index == -1) {
                                    //如果没小数点
                                    index = text.length
                                    val sub: String = text.substring(0, index)
                                    s?.replace(
                                        0,
                                        s.length,
                                        if (sub.length > 3) sub.substring(0, 3) else sub
                                    )
                                } else {
                                    Timber.e("sssss ->${text}")
                                    s?.replace(
                                        0,
                                        s.length,
                                        beforeWeight
                                    )

                                }
                            }
                            Timber.e("4 ->${text} ${s.toString()}")
                        }
                    }

                    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                        beforeWeight = p0.toString()
                    }

                    override fun onTextChanged(
                        p0: CharSequence?,
                        start: Int,
                        before: Int,
                        count: Int
                    ) {
                        weight = p0.toString().trim()
                    }
                })
            }
        }

        private fun updateSwitch() {
            binding.apply {
                ivSwitch.setImageResource(if (isOpenManualInput) R.drawable.icon_switch_open else R.drawable.icon_switch_close)
                edtWeight.isEnabled = isOpenManualInput
                if (isOpenManualInput) {
                    weightManager.stopRead()
//                    weightManager.cancelAutoConnect()
//                    KeyboardUtils.showSoftInput(edtWeight)
                } else {
                    KeyboardUtils.hideSoftInput(edtWeight)
                    weightManager.startRead { weight ->
                        // 处理重量更新
                        binding.apply {
                            edtWeight.post {
                                edtWeight.setText("$weight")
                                edtWeight.setSelection(edtWeight.length())
                            }
                        }
                    }
                }
                onSwitchOpenManualInput?.invoke(isOpenManualInput)
            }
        }
    }

    fun getSelectGoodsTag(): ArrayList<GoodsTagItem> {
        val selectList = arrayListOf<GoodsTagItem>()
        list.forEach { tag ->
            val goodsTagItems =
                tag.goodsTagItems?.filter { it.isCheck == true }
            val list = goodsTagItems as ArrayList<GoodsTagItem>
            if (list.isNotEmpty()) {
                selectList.addAll(list)
            }
        }
        return selectList
    }

    override fun getItemViewType(position: Int): Int {
        if (isShowWeight) {
            if (position == list.count()) {
                return 1
            }
        }
        return super.getItemViewType(position)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == 1) {
            return SpecificationWeightViewHolder(
                SpecificationWeightItemBinding.inflate(
                    LayoutInflater.from(
                        parent.context
                    ), parent, false
                )
            )

        }
        return MainSpecificationViewHolder(
            SpecificationMainItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        if (isShowWeight) {
            return list.count() + 1
        }
        return list.count()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is MainSpecificationViewHolder) {
            holder.bind(list[position])
        } else if (holder is SpecificationWeightViewHolder) {
            holder.bind()
        }
    }

    fun disconnectScale() {
        if (this::weightManager.isInitialized) {
            weightManager.stopRead()
//            weightManager.disconnectScale()
//            weightManager.cancelAutoConnect()
        }
    }


}