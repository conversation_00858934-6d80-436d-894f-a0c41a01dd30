package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.PackPriceDetailItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper

/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class PackPriceDetailItemAdapter(
    val list: ArrayList<Goods?>,
    var takeOutPlatformModel: TakeOutPlatformModel? = null,
    var conversionRatio: Long
) : RecyclerView.Adapter<PackPriceDetailItemAdapter.OrderedInfoViewHolder>() {


    inner class OrderedInfoViewHolder(val binding: PackPriceDetailItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.apply {
            }

        }
//        OrderedGoods

        fun bind(resource: Goods) {
            itemView.context.run {
                resource.let { data ->
                    binding.apply {

                        tvFoodName.text = data.name
                        tvFoodSubName.isVisible = data.feedStr?.isNotEmpty() == true
                        tvFoodSubName.text = data.feedStr
                        tvFoodCount.text = "${data.totalCount}"
//                        val price = (data.getCalculatePackingFee() ?: 0).priceFormatTwoDigitZero2()
                        tvPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                            conversionRatio,
                            data.getCalculatePackingFee().toLong(),
                            takeOutPlatformModel?.isKhr() == true
                        )

                        val totalPrice =
                            (data.getCalculatePackingFee() ?: 0).times(data.totalCount ?: 0)

                        tvTotalPackPrice.text = FoundationHelper.getPriceStrByUnit(
                            conversionRatio,
                            totalPrice.toLong(),
                            takeOutPlatformModel?.isKhr() == true
                        )

                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedInfoViewHolder {
        val itemView =
            PackPriceDetailItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedInfoViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OrderedInfoViewHolder, position: Int) {
        holder.bind(list[position]!!)
    }


    fun replaceData(newData: ArrayList<Goods>?) {
        if (newData != null) {
            this.list.clear()
            this.list.addAll(newData)
            notifyDataSetChanged()
        }
    }


}