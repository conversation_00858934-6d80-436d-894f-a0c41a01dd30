package com.metathought.food_order.casheir.ui.second_display.ordered.refunds

import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.core.view.isVisible
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.databinding.DialogSecondOrderedCancelDishBinding
import com.metathought.food_order.casheir.ui.adapter.SecondPartialRefundItemAdapter
import com.tencent.bugly.crashreport.CrashReport
import timber.log.Timber

/**
 * 副屏退菜
 * Secondary item back
 * <AUTHOR>
 * @date 2024/5/2309:37
 * @description
 */
class OrderedCancelDishDialog : Dialog {

    constructor(context: Context) : this(context, 0)
    constructor(context: Context, themeResId: Int) : super(context, themeResId)

    private lateinit var binding: DialogSecondOrderedCancelDishBinding
    private var mAdapter: SecondPartialRefundItemAdapter? = null
    private lateinit var mContext: Context
    override fun onCreate(savedInstanceState: Bundle?) {
        /**
         * 防止子弹窗不会显示，还不知道有啥问题
         */
        try {
            if (window != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                } else {
                    window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                }
            }
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }

        super.onCreate(savedInstanceState)
        binding = DialogSecondOrderedCancelDishBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    fun initResource(mContext: Context) {
        this.mContext = mContext
        binding.run {
            tvDishedName.text = mContext.getString(R.string.cancel_dish)
            items.text = mContext.getString(R.string.items)
            quantity.text = mContext.getString(R.string.quantity)
            amount.text = mContext.getString(R.string.amount)
            refundQty.text = mContext.getString(R.string.return_back_dish_num)

            tvCancelNumTitle.text = mContext.getString(R.string.cancel_dish_num)
            tvCancelAmountTitle.text = mContext.getString(R.string.cancel_dish_amount)
            tvCancelVatTitle.text = mContext.getString(R.string.cancel_dish_vat)
            tvCancelServiceAmountTitle.text =
                mContext.getString(R.string.cancel_service_fee)
            tvCancelPackingAmountTitle.text =
                mContext.getString(R.string.cancel_dish_packing_charge)
            tvCancelTotalAmountTitle.text = mContext.getString(R.string.cancel_dish_total_price)
        }
    }

    fun initData(filterList: ArrayList<BaseOrderGoods>) {
        binding.run {
            recyclerOrderedFood.post {
                mAdapter = SecondPartialRefundItemAdapter(
                    filterList,
                    mContext = mContext,
                    width = recyclerOrderedFood.measuredWidth
                )
                recyclerOrderedFood.adapter = mAdapter
            }
        }
    }

    fun setCancelNum(num: String) {
        binding.run {
            tvCancelNum.text = num
        }
    }

    fun setCancelAmount(amount: String) {
        binding.run {
            tvCancelAmount.text = amount
        }
    }

    fun setCancelServiceFee(amount: String, isVisible: Boolean) {
        binding.run {
            tvCancelServiceAmount.text = amount
            layoutCancelServiceAmount.isVisible = isVisible
        }
    }

    fun setCancelVat(amount: String, isVisible: Boolean) {
        binding.run {
            tvCancelVat.text = amount
            layoutCancelVat.isVisible = isVisible
        }
    }

    fun setCancelPackingAmount(amount: String, isVisible: Boolean) {
        binding.run {
            tvCancelPackingAmount.text = amount
            layoutCancelPackingAmount.isVisible = isVisible
        }
    }

    fun setCancelTotalAmount(amount: String) {
        binding.run {
            tvCancelTotalAmount.text = amount
        }
    }

    fun updateRecyclerOrderedFood(position: Int) {
        binding.run {
            recyclerOrderedFood.smoothScrollToPosition(position)
        }
    }


    fun notifyAdapter(bindingAdapterPosition: Int) {
//        mAdapter?.notifyItemChanged(bindingAdapterPosition)
        mAdapter?.notifyDataSetChanged()
    }

}