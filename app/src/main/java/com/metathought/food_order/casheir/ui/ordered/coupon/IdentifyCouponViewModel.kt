package com.metathought.food_order.casheir.ui.ordered.coupon

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class IdentifyCouponViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    fun getCoupon(
        diningStyle: Int? = null,
        goodsList: List<GoodsBo>? = null,
        isPreOrder: Boolean? = null,
        note: String? = null,
        tableUuid: String? = null,
        couponCode: String? = null,
        orderNo: String? = null
    ) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val response = repository.couponCodeRecognition(
                    diningStyle,
                    goodsList,
                    isPreOrder,
                    note,
                    tableUuid,
                    couponCode,
                    orderNo
                )
                emitUiState(response)

            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(""))
            }
        }
    }


    private fun emitUiState(
        success: ApiResponse<CouponModel>? = null,
    ) {
        val uiModel = UIModel(success)
        _uiState.postValue(uiModel)
    }

    data class UIModel(
        val response: ApiResponse<CouponModel>?,
    )

}