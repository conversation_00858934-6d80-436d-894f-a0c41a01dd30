package com.metathought.food_order.casheir

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.helper.LocaleHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Locale


/**
 *<AUTHOR>
 *@time  2024/12/5
 *@desc
 **/

class SplashActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        val text = findViewById<TextView>(R.id.splash_text)
        Timber.e("Locale.getDefault() :${Locale.getDefault()}")
        text.text = this.getStringByLocale(R.string.your_best_business_partner, Locale.getDefault())

        val handler = Handler()
        handler.postDelayed({
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
            finish()
        }, 2000)

        GlobalScope.launch(Dispatchers.IO) {
            MyApplication.myAppInstance.initData()
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase?.let { LocaleHelper.onAttach(it) })
    }
}