package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.ItemCouponGoodBinding
import com.metathought.food_order.casheir.databinding.ItemDiscountActItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description
 */
class DiscountActivityListAdapter(
    val list: List<CouponActivityModel>,
    val isShowVip: Boolean?,
    val orderInfo: OrderedInfoResponse? = null
) : RecyclerView.Adapter<DiscountActivityListAdapter.CouponGiftGoodViewHolder>() {


    inner class CouponGiftGoodViewHolder(val binding: ItemDiscountActItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: CouponActivityModel) {
            binding.apply {
                if (resource.activityLabel != null) {
                    tvGoodName.text = resource.activityLabel?.name
                } else {
                    tvGoodName.text = resource.activityLabelName
                }
                if (orderInfo == null || !(orderInfo.isAfterPayStatus())) {
                    tvVipPrice.text =
                        "-${(resource.activityVipCouponAmount ?: 0)?.priceFormatTwoDigitZero2()}"
                    tvVipPrice.isVisible =
                        isShowVip == true && resource.vipMark == true && resource.weightMark == false
                    tvPrice.text =
                        "-${(resource.activityCouponAmount ?: 0)?.priceFormatTwoDigitZero2()}"
                    if (resource.weightMark == true) {
                        tvPrice.text = binding.root.context.getString(R.string.to_be_confirmed)
                    }
                } else {
                    tvPrice.text = if (orderInfo.payType == PayTypeEnum.USER_BALANCE.id) {
                        "-${(resource.activityVipCouponAmount ?: 0)?.priceFormatTwoDigitZero2()}"
                    } else {
                        "-${(resource.activityCouponAmount ?: 0)?.priceFormatTwoDigitZero2()}"
                    }
                    tvVipPrice.isVisible = false
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CouponGiftGoodViewHolder(
        ItemDiscountActItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: DiscountActivityListAdapter.CouponGiftGoodViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}