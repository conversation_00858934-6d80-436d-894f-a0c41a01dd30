package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.member.TopUpCanUseCouponRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponTemplateSDK
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeTierCouponTemplate
import com.metathought.food_order.casheir.databinding.ItemDialogCouponListBinding
import com.metathought.food_order.casheir.databinding.ItemDialogGiftCouponDetailListBinding
import com.metathought.food_order.casheir.databinding.ItemDialogRechargeGiftCouponsBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setNumberRange
import timber.log.Timber
import java.util.ArrayList
import kotlin.math.min


class RechargeGiftCouponsAdapter :
    RecyclerView.Adapter<RechargeGiftCouponsAdapter.MemberCouponListViewHolder>() {

    private var selectCouponIdList: MutableList<String> = mutableListOf()
    private val list: MutableList<RechargeTierCouponTemplate> = mutableListOf()

    inner class MemberCouponListViewHolder(val binding: ItemDialogRechargeGiftCouponsBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.etCouponNum.addTextChangedListener {
                list[layoutPosition].num = it.toString().toIntOrNull() ?: 0
            }
        }

        fun bind(resource: RechargeTierCouponTemplate?, position: Int) {
            itemView.context.let { context ->
                val couponTemplate = resource?.couponTemplate
                binding.apply {
                    tvAmount.text = couponTemplate?.getDiscountDesc(context)

                    tvLimitAmount.isVisible = couponTemplate?.isThresholdCoupon() == true
                    tvLimitAmount.text = couponTemplate?.getThresholdDesc(context)

                    tvCouponName.text = couponTemplate?.name

                    tvEffectiveTime.text = couponTemplate?.rule?.expiration?.getExpirationDate(context)
                    tvEffectiveTime.isVisible = tvEffectiveTime.text.isNotEmpty()


                    if (couponTemplate?.isGiftGoodCoupon() == true) {
                        tvGiftGoodNum.isVisible = true
                        if (couponTemplate.rule?.discount?.giveGoods.isNullOrEmpty()) {
                            //没有赠送商品的时候
                            tvGiftGoodNum.setEnable(false)
                            tvGiftGoodNum.text =
                                context.getString(R.string.free_item_removed_by_merchant)
                            tvGiftGoodNum.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                null,
                                null
                            )
                        } else {
                            tvGiftGoodNum.setEnable(true)
                            tvGiftGoodNum.text = context.getString(
                                R.string.gift_products_num,
                                "${couponTemplate.rule?.discount?.giveGoods?.size ?: 0}"
                            )
                            //赠送商品
                            var endDrawable = ContextCompat.getDrawable(
                                context,
                                R.drawable.icon_arrow_down
                            )
                            if (resource.isGiftGoodsExpand == true) {
                                endDrawable = ContextCompat.getDrawable(
                                    context,
                                    R.drawable.icon_arrow_up
                                )
                                rvGiftGoods.isVisible = true
                                rvGiftGoods.adapter =
                                    CouponGoodListAdapter(
                                        couponTemplate.rule?.discount?.giveGoods
                                            ?: listOf()
                                    )
                            } else {
                                rvGiftGoods.isVisible = false

                            }
                            tvGiftGoodNum.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                endDrawable,
                                null
                            )
                        }
                    } else {
                        tvGiftGoodNum.isVisible = false
                        rvGiftGoods.isVisible = false
                    }
                    etCouponNum.setNumberRange(1,10)
                    etCouponNum.setText((resource?.num ?: 0).toString())

                    val isSelected = selectCouponIdList.contains(couponTemplate?.id)
                    checkbox.isSelected = isSelected
                    groupNum.isVisible = isSelected

                    llTop.setOnClickListener {
                        selectItem(position)
                    }

                    tvGiftGoodNum.setOnClickListener {
                        list[position].isGiftGoodsExpand =
                            !(list[position].isGiftGoodsExpand ?: false)
                        notifyItemChanged(position)
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberCouponListViewHolder {
        val itemView = ItemDialogRechargeGiftCouponsBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return MemberCouponListViewHolder(itemView)
    }


    private fun selectItem(position: Int) {
        val itemId = list[position].couponTemplate?.id ?: return
        //如果点的是当前选择的
        if (selectCouponIdList.contains(itemId)) {
            selectCouponIdList.remove(itemId)
            notifyItemChanged(position)
        } else {
            selectCouponIdList.add(itemId)
            notifyItemChanged(position)
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: MemberCouponListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<RechargeTierCouponTemplate>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addData(list: List<RechargeTierCouponTemplate>?) {
        if (list != null) {
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun getSelectedCoupons(): List<RechargeTierCouponTemplate> {
        return list.filter { selectCouponIdList.contains(it.couponTemplate?.id) }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setSelectCouponIds(mapNotNull: List<String>?) {
        selectCouponIdList.clear()
        if (mapNotNull != null) {
            selectCouponIdList.addAll(mapNotNull)
        }
        notifyDataSetChanged()
    }

    fun addSelectedCoupon(id: String) {
        selectCouponIdList.add(id)
    }

}
