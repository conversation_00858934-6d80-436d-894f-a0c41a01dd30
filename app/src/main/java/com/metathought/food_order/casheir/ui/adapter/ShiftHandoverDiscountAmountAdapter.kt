package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.OfflinePayMethodData
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftOrderDiscountInfo
import com.metathought.food_order.casheir.databinding.ShiftHandoverDiscountAmountItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverOfflineRecordsItemBinding
import com.metathought.food_order.casheir.databinding.ShiftHandoverRecordsItemBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

class ShiftHandoverDiscountAmountAdapter(var list: ArrayList<ShiftOrderDiscountInfo> = ArrayList()) :
    RecyclerView.Adapter<ShiftHandoverDiscountAmountAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            ShiftHandoverDiscountAmountItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(position)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(list: List<ShiftOrderDiscountInfo>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addList(list: List<ShiftOrderDiscountInfo>) {
        this.list.addAll(list)
        notifyDataSetChanged()
    }


    inner class ViewHolder(val binding: ShiftHandoverDiscountAmountItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(position: Int) {
            binding.apply {
                list[position].let { info ->
                    tvOrderedID.text = info.ordersId
                    tvAmount.text = info.totalDiscountPrice?.priceFormatTwoDigitZero2("$") ?: ""

                    val list = mutableListOf<ItemData>()
                    info.orderDiscountInfo?.forEach {
                        //优惠类型下没有子商品且此订单不只有一个优惠类型才显示价格
                        val isTitleShowPrice =
                            (it.discountGoodInfo?.size ?: 0) == 0 && info.orderDiscountInfo.size > 1
                        list.add(
                            ItemData(
                                it.promotionName ?: "",
                                if (isTitleShowPrice) it.price else null,
                                true
                            )
                        )
                        it.discountGoodInfo?.forEach { good ->
                            list.add(ItemData(good.goodName ?: "", good.price ?: 0.0, false))
                        }
                    }
                    rvOrderDetail.adapter = ShiftHandoverDiscountAmountOrderAdapter(list)
                }

            }
        }

    }
}