package com.metathought.food_order.casheir.data.model.base.response_model.cart


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment

data class CartInfoResponse(
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("goodsList")
    val goodsList: List<Goods?>?,
//    @SerializedName("goodsTotalNum")
//    val goodsTotalNum: Int?,
//    @SerializedName("serviceCharge")
//    val serviceCharge: Long?,
//    @SerializedName("totalPrice")
//    val totalPrice: Long?,
//    @SerializedName("vipServiceCharge")
//    val vipServiceCharge: Long?,
//    @SerializedName("vipTotalPrice")
//    val vipTotalPrice: Long?,
//
//    @SerializedName("totalServiceCharge")
//    val totalServiceCharge: Long?,
//
//    @SerializedName("totalDiscountServiceCharge")
//    val totalDiscountServiceCharge: Long?,
//
//    @SerializedName("totalVipServiceCharge")
//    val totalVipServiceCharge: Long?,
//
//    @SerializedName("serviceChargePercentage")
//    val serviceChargePercentage: Long?,
//
//    @SerializedName("totalVatPrice")
//    val totalVatPrice: Long?,
//
//    @SerializedName("totalVipPrice")
//    val totalVipPrice: Long?,
//
//    @SerializedName("totalVipVatPrice")
//    val totalVipVatPrice: Long?,
//
//    @SerializedName("totalDiscountPrice")
//    val totalDiscountPrice: Long?,
//
//    @SerializedName("totalDiscountVatPrice")
//    val totalDiscountVatPrice: Long?,
//
//    @SerializedName("totalPackingFee")
//    val totalPackingFee: Long?,

    /**
     * 数据版本
     */
    @SerializedName("dataVersion")
    val dataVersion: Long? = null,

    /**
     * 用户token  自己的token不处理
     */
    @SerializedName("token")
    val token: String? = null,

    /**
     * 购物车 变动时价戳
     */
    @SerializedName("timestamp")
    val timestamp: String? = null,

    @SerializedName("totalDiscountPrice")
    val totalDiscountPrice: Long?,

    @SerializedName("totalDiscountVatPrice")
    val totalDiscountVatPrice: Long?,

    @SerializedName("totalPackingFee")
    val totalPackingFee: Long?,


    @SerializedName("weight")
    var weight: Double? = null, //商品重量
    @SerializedName("weighingCompleted")
    var weighingCompleted: Boolean?,//是否称重完成(服务端自动计算)
    @SerializedName("isProcessed")
    var isProcessed: Boolean?,//是否处理完成(服务端自动计算)
    @SerializedName("uuid")
    var uuid: String? = null,//商品唯一标识

    //ws 下发 其他端操作的语言
    @SerializedName("language")
    var language: String? = null,//语言

) {
    /**
     * 是否自己的token
     *
     * @return
     */
    fun isSelfToken(): Boolean {
        return MainDashboardFragment.CURRENT_USER?.token == token
    }
}