package com.metathought.food_order.casheir.ui.ordered.discount


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.databinding.DialogGiftProductListBinding
import com.metathought.food_order.casheir.ui.adapter.NoDiscountGoodListAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


/**
 *<AUTHOR>
 *@time  2025/01/01
 *@desc 不参与折扣菜品列表
 **/
@AndroidEntryPoint
class NoDiscountGoodsDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "NoDiscountGoodsDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            goodsList: List<GoodsRequest>? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(goodsList)

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            giftGoods: List<GoodsRequest>? = null
        ): NoDiscountGoodsDialog {
            val fragment = NoDiscountGoodsDialog()
            fragment.goodsList = giftGoods
            return fragment
        }
    }

    private var binding: DialogGiftProductListBinding? = null

    private var goodsList: List<GoodsRequest>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogGiftProductListBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {

    }

    private fun initView() {
        binding?.apply {
            topBar.setTitle(getString(R.string.not_participating_in_discounted_products))
        }
    }


    private fun initListener() {
        binding?.apply {
            rvList.adapter = NoDiscountGoodListAdapter(goodsList ?: listOf())

            topBar.getCloseBtn()?.setOnClickListener { dismissAllowingStateLoss() }
        }
    }

}