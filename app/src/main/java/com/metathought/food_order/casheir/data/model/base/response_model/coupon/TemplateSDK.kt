package com.metathought.food_order.casheir.data.model.base.response_model.coupon

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.os.Parcelable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.TextAppearanceSpan
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.CouponTypeEnum
import com.metathought.food_order.casheir.constant.CouponUsageTypeEnum
import com.metathought.food_order.casheir.constant.PricingMethodEnum
import com.metathought.food_order.casheir.extension.convertToTimestamp
import com.metathought.food_order.casheir.extension.convertToTimestamp2
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.formatTimestamp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.DisplayUtils
import kotlinx.parcelize.Parcelize
import java.util.Date
import java.util.Locale
import kotlin.math.ceil


/**
 *<AUTHOR>
 *@time  2024/8/27
 *@desc
 **/

data class CouponTemplateSDK(
    @SerializedName("saasStoreId")
    val saasStoreId: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("updateTime")
    val updateTime: String?,
    @SerializedName("storeId")
    val storeId: String?,
    @SerializedName("category")
    val category: String?,
    @SerializedName("threshold")
    val threshold: String?,
    @SerializedName("templateKey")
    val templateKey: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("usageType")
    val usageType: String?,
    @SerializedName("logo")
    val logo: String?,
    @SerializedName("available")
    val available: String?,
    @SerializedName("expired")
    val expired: String?,
    @SerializedName("goodIds")
    val goodIds: String?,
    @SerializedName("intro")
    val intro: String?,
    @SerializedName("collectableStartTime")
    val collectableStartTime: String?,
    @SerializedName("collectableEndTime")
    val collectableEndTime: String?,
    @SerializedName("couponCount")
    val couponCount: Int?,
    @SerializedName("singleDayGiveLimit")
    val singleDayGiveLimit: Int?,
    @SerializedName("singleGetLimit")
    val singleGetLimit: Int?,
    @SerializedName("singleDayGetLimit")
    val singleDayGetLimit: Int?,
    @SerializedName("reachAmount")
    val reachAmount: Int?,
    @SerializedName("usingRules")
    val usingRules: String?,
    @SerializedName("rule")
    val rule: Rule?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("activeState")
    val activeState: String?,
    @SerializedName("createUserId")
    val createUserId: String?,
    @SerializedName("updateUserId")
    val updateUserId: String?,
    @SerializedName("targetCustomer")
    val targetCustomer: String?,
    @SerializedName("usageGoods")
    val usageGoods: List<UsageGoods>?,
    @SerializedName("scategory")
    val scategory: String?,
) {

    //有限时间范围
    fun getEffectiveTimeRange(context: Context): String {
        if (collectableStartTime == null && collectableEndTime == null) {
            return ""
        }

        val currentTimeStamp = Date().time
        val effectTimeStartStamp = collectableStartTime?.convertToTimestamp2() ?: Date().time
        val effectTimeEndStamp = collectableEndTime?.convertToTimestamp2() ?: Date().time
        val effectTimeStartStr = effectTimeStartStamp.formatTimestamp()
        val effectTimeEndStr = effectTimeEndStamp.formatTimestamp()

//        val diffTime = effectTimeEndStamp - currentTimeStamp
//        //七天时间
//        val sevenDay = (60 * 60 * 24 * 7 * 1000).toLong()
//        val oneDay = (60 * 60 * 24 * 1000).toLong()
//        val oneHour = (60 * 60 * 1000).toLong()
//        val twoMinute = (60 * 2 * 1000).toLong()
//        if (diffTime < 0) {
//            //说明已经结束
//            return context.getString(R.string.expired)
//        } else if (diffTime > sevenDay) {
//            //大于七天  显示有效期至
//            return context.getString(R.string.effect_time_to, effectTimeEndStr)
//        } else if (diffTime in (oneDay + 1)..sevenDay) {
//            //小于七天 大于1天  显示几天后过期
//            val day = diffTime.div(1000.0).div(60).div(60).div(24).toInt()
//            return context.getString(R.string.expires_in_a_few_days, "${day}")
//        } else if (diffTime in (oneHour + 1)..oneDay) {
//            //小于1天 大于1小时  显示几小时后过期
//            val hours = diffTime.div(1000.0).div(60).div(60).toInt()
//            return context.getString(R.string.expires_in_a_few_hour, "$hours")
//        } else if (diffTime in (twoMinute + 1)..oneHour) {
//            //小于1小时 大于2分钟  显示几分钟后过期
//            val minutes = ceil(diffTime.div(1000.0).div(60)).toInt()
//            return context.getString(R.string.expires_in_a_few_minute, "$minutes")
//        } else if (diffTime <= twoMinute) {
//            //小于等于2分钟  显示几秒后过期
//            val second = ceil(diffTime.div(1000.0)).toInt()
//            return context.getString(R.string.expires_in_a_few_second, "$second")
//        }

        return "${effectTimeStartStr}-${effectTimeEndStr}"
    }

    //是否赠品券
    fun isGiftGoodCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.THRESHOLD_ZS.type,
            CouponTypeEnum.NOTHRESHOLD_ZS.type
        )
    }

    //获取满减描述
    fun getThresholdDesc(context: Context): String {
        return if (rule?.discount?.type == "PRICE") {
            context.getString(
                R.string.full_price_available,
                "$${rule.discount.base?.decimalFormatTwoDigit()}"
            )
        } else if (rule?.discount?.type == "NUM") {
            context.getString(
                R.string.full_num_available,
                "${rule.discount.itemNum}"
            )
        } else {
            ""
        }
    }


    //获取优惠描述
    fun getDiscountDesc(context: Context): SpannableStringBuilder {
        val span = SpannableStringBuilder()
        when (category) {
            CouponTypeEnum.THRESHOLD_LJ.type, CouponTypeEnum.NOTHRESHOLD_LJ.type -> {
                val unit = "$"
                val quota = "${rule?.discount?.quota?.decimalFormatTwoDigit()}"
                span.append(unit)
                span.append(quota)
                span.setSpan(
                    TextAppearanceSpan(
                        null, Typeface.BOLD, DisplayUtils.sp2px(context, 24f),
                        ColorStateList.valueOf(context.getColor(R.color.primaryColor)), null
                    ),
                    unit.length,
                    unit.length + quota.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }


            CouponTypeEnum.THRESHOLD_ZK.type, CouponTypeEnum.NOTHRESHOLD_ZK.type -> {

                val quota = "${rule?.discount?.quota?.decimalFormatTwoDigit()}"
                val quotaDesc = "% OFF"
                span.append(quota)
                span.append(quotaDesc)
                span.setSpan(
                    TextAppearanceSpan(
                        null, Typeface.BOLD, DisplayUtils.sp2px(context, 24f),
                        ColorStateList.valueOf(context.getColor(R.color.primaryColor)), null
                    ),
                    0,
                    quota.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

            }

            CouponTypeEnum.THRESHOLD_ZS.type, CouponTypeEnum.NOTHRESHOLD_ZS.type -> {
                val quota = context.getString(R.string.give_away_goods)
                span.append(quota)
                span.setSpan(
                    TextAppearanceSpan(
                        null, Typeface.BOLD, DisplayUtils.sp2px(context, 16f),
                        ColorStateList.valueOf(context.getColor(R.color.primaryColor)), null
                    ),
                    0,
                    quota.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

            }
        }
        return span
    }

    //是否折扣
    fun isZkCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.THRESHOLD_ZK.type,
            CouponTypeEnum.NOTHRESHOLD_ZK.type
        )
    }

    //是否赠送
    fun isZsCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.THRESHOLD_ZS.type,
            CouponTypeEnum.NOTHRESHOLD_ZS.type
        )
    }

    //是否立减
    fun isLjCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.THRESHOLD_LJ.type,
            CouponTypeEnum.NOTHRESHOLD_LJ.type
        )
    }

    //是否部分适用
    fun isPartialGoods(): Boolean {
        return usageType == CouponUsageTypeEnum.PARTIAL_GOODS.type
    }


    //是否使用所有商品
    fun isAllGoods(): Boolean {
        return usageType == CouponUsageTypeEnum.ALL_GOODS.type
    }

    //是否适用会员充值
    fun isTopUp(): Boolean {
        return usageType == CouponUsageTypeEnum.TOP_UP.type
    }

    //获取门槛金额
    fun getThresholdPrice(): Double? {
        return rule?.discount?.base
    }

    //获取门槛数量
    fun getThresholdNum(): Int? {
        return rule?.discount?.itemNum
    }

    //获取门槛数量
    fun getQuoTaPrice(): Double {
        return if (isZkCoupon()) {
            (rule?.discount?.quota ?: 0.0) / 100
        } else {
            rule?.discount?.quota ?: 0.0
        }

    }

    //是否有门槛
    fun isThresholdCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.THRESHOLD_LJ.type,
            CouponTypeEnum.THRESHOLD_ZK.type,
            CouponTypeEnum.THRESHOLD_ZS.type
        )
    }

    //是否无门槛
    fun isNoThresholdCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.NOTHRESHOLD_LJ.type,
            CouponTypeEnum.NOTHRESHOLD_ZK.type,
            CouponTypeEnum.NOTHRESHOLD_ZS.type
        )
    }
}


data class Rule(
    @SerializedName("expiration")
    val expiration: RuleExpiration?,
    @SerializedName("discount")
    val discount: RuleDiscount?,
    @SerializedName("usage")
    val usage: RuleUsage?,
    @SerializedName("distributionChannels")
    val distributionChannels: DistributionChannels?
)

data class DistributionChannels(
    val type: String?,    //	可用值:MAIN_PAGE,AFTER_PAY	string
    val threshold: String?, //		可用值:NO,ACTUAL_PAYMENT_AMOUNT,BUY_DESIGNATED_PRODUCTS	string
    val actualPaymentAmount: String?,    //		number
    val designatedGoodIds: String?,    //		string
    val designatedGoods: List<UsageGoods>?,        //	array	GoodBriefDTO
)


@Parcelize
data class RuleExpiration(
    @SerializedName("period")
    val period: String?,    //有效期规则 REGULAR "固定的(固定日期)") SHIFT "变动的(以领取之日开始计算)")
    @SerializedName("effectiveAfterGetDay")
    val effectiveAfterGetDay: Int?, //领取后x日有效:只对变动类型有效，天数 基于领取日期 + 天数 <= 当前时间 有效
    @SerializedName("gap")
    val gap: Int?,  //有效间隔: 只对变动类型有效，天数 基于effectiveDate 往上加gap的天数 不过期则有效

    @SerializedName("deadline")
    val deadline: String?,  //优惠券模板的失效日期，只对固定周期类型的有效 不超过deadline则表示有效
    @SerializedName("effectiveDate")
    val effectiveDate: String?  //优惠券模板的生效开始时间
) : Parcelable {

    fun getExpirationDate(context: Context): String {
        if (period == "REGULAR") {
            //固定日期 有效期:2025/04/15 06:15-2025/04/24 06:16
            val effectTimeStartStamp = effectiveDate?.convertToTimestamp() ?: Date().time
            val effectTimeEndStamp = deadline?.convertToTimestamp() ?: Date().time
            val effectTimeStartStr = effectTimeStartStamp.formatTimestamp()
            val effectTimeEndStr = effectTimeEndStamp.formatTimestamp()

            return "${effectTimeStartStr}-${effectTimeEndStr}"
        } else if (period == "SHIFT") {
            //以领取之日开始计算  领取后0天生效，有效期10天
            return context.getString(
                R.string.tips_effect_time_after_get_day,
                effectiveAfterGetDay.toString(),
                gap.toString()
            )
        } else {
            return ""
        }
    }
}


data class RuleDiscount(
    @SerializedName("type")
    val type: String?,
    @SerializedName("quota")
    val quota: Double?,
    @SerializedName("base")
    val base: Double?,
    @SerializedName("itemNum")
    val itemNum: Int?,
    @SerializedName("giveGoodIds")
    val giveGoodIds: String?,
    @SerializedName("giveGoods")
    val giveGoods: List<UsageGoods>?,
    @SerializedName("upperLimitPrice")
    val upperLimitPrice: Double?
)


data class RuleUsage(
    @SerializedName("period")
    val period: String?,
    @SerializedName("usageStartTime")
    val usageStartTime: String?,
    @SerializedName("usageEndTime")
    val usageEndTime: String?,
    @SerializedName("settleLimitDay")
    val settleLimitDay: String?,
    @SerializedName("timePeriod")
    val timePeriod: String?, //	可用值:ALL_DAY,SET_TIME	string
    @SerializedName("setTimes")
    val setTimes: String?,
)


@Parcelize
data class UsageGoods(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("name")
    var name: String? = null,
    @SerializedName("sellPrice")
    val sellPrice: Long? = null,
    @SerializedName("vipPrice")
    val vipPrice: Long? = null,
    @SerializedName("discountPrice")
    val discountPrice: Long? = null,
    @SerializedName("picUrl")
    val picUrl: String? = null,

    @SerializedName("pricingMethod")
    var pricingMethod: Int? = null,
    @SerializedName("kitchenMaking")
    val kitchenMaking: Boolean? = null,
    @SerializedName("seriesNo")
    val seriesNo: String? = null,

    //多端厨房功能  用于区分菜品用哪个打印机打印
    @SerializedName("storeKitchenId")
    val storeKitchenId: String?,

    /**
     * 后台自己设置的编号
     */
    @SerializedName("number")
    var number: String?,

    /**
     * 是否打印标签贴纸
     */
    @SerializedName("printLabel")
    var printLabel: Boolean? = null,

    /**
     * 是否时价菜
     */
    @SerializedName("currentPrice")
    var currentPrice: Boolean? = null,

    var nameEn: String? = null,
    var nameKm: String? = null,
) : Parcelable {

    fun getNameByLocal(locale: Locale): String? {
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (nameKm.isNullOrEmpty()) {
                name
            } else {
                nameKm
            }
        } else if (locale == Locale.CHINESE) {
            name
        } else if (locale == Locale.ENGLISH) {
            if (nameEn.isNullOrEmpty()) {
                name
            } else {
                nameEn
            }
        } else {
            name
        }
    }

    fun getPrice(): String? {
        var price = sellPrice

        if (discountPrice != null) {
            price = discountPrice
        }
        return "${price?.priceFormatTwoDigitZero2()}"
    }

    /**
     * 是否时价菜
     *
     * @return
     */
    fun isTimePriceGood(): Boolean {
        if (currentPrice == null) {
            return false
        }
        return currentPrice == true
    }


    private fun getWeightUnit(): String {
        return PricingMethodEnum.entries.find {
            it.id == (pricingMethod ?: 0)
        }?.unit ?: ""
    }
}