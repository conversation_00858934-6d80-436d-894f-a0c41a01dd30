package com.metathought.food_order.casheir.ui.work_handover

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.databinding.DialogShiftHandoverOfflineRecordsDetailBinding
import com.metathought.food_order.casheir.ui.adapter.ShiftHandoverOfflineRecordsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * 交班记录线下支付详情
 */
@AndroidEntryPoint
class ShiftHandoverOfflineRecordsDetailDialog : BaseDialogFragment() {
    private var binding: DialogShiftHandoverOfflineRecordsDetailBinding? = null
    private var logDeatil: CashRegisterHandoverLogVo? = null

    private val viewModel: ShiftHandoverViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogShiftHandoverOfflineRecordsDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
//        initObserver()
    }


    private fun initData() {
        val deatil: CashRegisterHandoverLogVo = arguments?.getParcelable(LOG_INFO) ?: return
        logDeatil = deatil
//        if (logDeatil?.id != null) {
//            viewModel.getShiftReportPrint(logDeatil?.id!!)
//        }
        binding?.apply {
            val adapter = ShiftHandoverOfflineRecordsAdapter(
                logDeatil?.offlinePayMethodData ?: ArrayList()
            )
            layoutEmpty.root.isVisible = adapter.list.isEmpty()
            rvList.isVisible = adapter.list.isNotEmpty()
            rvList.adapter = adapter
        }

    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

        }
    }

//    private fun initObserver() {
//        viewModel.shiftReportPrint.observe(viewLifecycleOwner) {
//            when (it) {
//                is ApiResponse.Loading -> {
////                    binding?.pbLogout?.isVisible = true
////                    binding?.btnPrint?.setEnable(false)
//                }
//
//                is ApiResponse.Error -> {
//                    binding?.apply {
////                        pbLogout.isVisible = false
////                        btnPrint.setEnable(true)
//                    }
//                    if (!it.message.isNullOrEmpty()) Toast.makeText(
//                        context,
//                        it.message.toString(),
//                        Toast.LENGTH_SHORT
//                    ).show()
//                }
//
//                is ApiResponse.Success -> {
//                    binding?.apply {
//                        it.data.data?.let { srPrint ->
//                            binding?.apply {
//                                val adapter = ShiftHandoverOfflineRecordsAdapter(
//                                    srPrint.offlinePayMethodData ?: ArrayList()
//                                )
//                                rvList.adapter = adapter
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.4).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val TAG = "ShiftHandoverOfflineRecordsDetailDialog"
        private const val LOG_INFO = "log_info"

        fun showDialog(
            fragmentManager: FragmentManager,
            log: CashRegisterHandoverLogVo,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(log)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? ShiftHandoverOfflineRecordsDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            log: CashRegisterHandoverLogVo,
        ): ShiftHandoverOfflineRecordsDetailDialog {
            val args = Bundle()
            args.putParcelable(LOG_INFO, log)
            val fragment = ShiftHandoverOfflineRecordsDetailDialog()
            fragment.arguments = args
            return fragment
        }
    }

}
