package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.MAXIMUM_QTY
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.databinding.ToppingItemBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.helper.FoundationHelper


class ToppingsAdapter(
    private var feeds: List<Feed>,
    val context: Context,
    val onClickCallback: (Feed) -> Unit
) : RecyclerView.Adapter<ToppingsAdapter.SpecificationViewHolder>() {

    inner class SpecificationViewHolder(val binding: ToppingItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(showIndex: Int) {
            binding.apply {
                feeds[showIndex].let { feed ->
                    //change restrict num from unlimited to 999
                    if (feed.alreadyNum == null) {
                        feed.alreadyNum = 0
                    }
                    if (feed.restrictNum == 0)
                        feed.restrictNum = MAXIMUM_QTY
                    tvFeedName.text = feed.name
//                    val formatPrice = feed.sum?.priceDecimalFormatTwoDigitZero()
                    tvPrice.text = "+${ FoundationHelper.getPriceStrByUnit(
                        FoundationHelper.useConversionRatio,(feed.sum?:0.0).toLong(),
                        FoundationHelper.isKrh)}"
                    tvPrice.isInvisible = (feed.sum ?: 0.0) == 0.0
                    tvQTY.text = feed.alreadyNum.toString()
                    imgMinus.setVisibleInvisible(feed.alreadyNum!! > 0)
                    imgPlus.setImageDrawable(
                        ContextCompat.getDrawable(
                            context,
                            if (feed.alreadyNum == feed.restrictNum) R.drawable.ic_add_disable else R.drawable.ic_add
                        )
                    )
                    tvQTY.setVisibleInvisible(feed.alreadyNum!! > 0)
                    imgMinus.setOnClickListener {
                        if (feed.alreadyNum!! > 0) {
                            feed.alreadyNum = feed.alreadyNum!! - 1
                            notifyItemChanged(showIndex)
                            onClickCallback.invoke(feed)
                        }
                    }
                    imgPlus.setOnClickListener {
                        if (feed.alreadyNum!! < feed.restrictNum!!) {
                            feed.alreadyNum = feed.alreadyNum!! + 1
                            notifyItemChanged(showIndex)
                            onClickCallback.invoke(feed)
                        }
                    }
                    layoutMain.strokeColor = ContextCompat.getColor(
                        context,
                        if (feed.alreadyNum!! > 0) R.color.primaryColor else R.color.black20
                    )
                }

            }
        }
    }

    fun getSelectFeed(): ArrayList<Feed> {
        val list = feeds.filter { it.alreadyNum!! > 0 }
        return list as ArrayList<Feed>
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SpecificationViewHolder {
        return SpecificationViewHolder(
            ToppingItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return feeds.size
    }

    override fun onBindViewHolder(holder: SpecificationViewHolder, position: Int) {
        holder.bind(position)
    }

}