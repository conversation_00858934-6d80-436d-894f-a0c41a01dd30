package com.metathought.food_order.casheir.data.model.base.response_model

/**
 * 处理后的综合报表数据
 * 用于按列组织数据，便于在表格中显示
 */
data class ProcessedReportData(
    /**
     * 表头列表
     */
    val headers: List<Header>,

    /**
     * 日期标识列表（按顺序）
     * 每个元素代表一个时间段的数据
     */
    val dateLabels: List<String>,

    /**
     * 按字段组织的数据
     * Key: 字段名（如 "totalAmount", "dineInAmount" 等）
     * Value: 该字段所有日期的值列表（按日期顺序）
     */
    val fieldDataMap: Map<String, List<Any?>>,

    /**
     * 按列组织的数据（便于在RecyclerView中使用）
     * 每个ColumnData代表一列数据
     */
    val columnDataList: List<ColumnData>
)

/**
 * 列数据
 * 代表表格中的一列
 */
data class ColumnData(
    /**
     * 列标题（字段显示名称）
     */
    val title: String,

    /**
     * 字段key
     */
    val fieldKey: String,

    /**
     * 该列所有行的数据值（按日期顺序）
     */
    val values: List<Any?>
)
