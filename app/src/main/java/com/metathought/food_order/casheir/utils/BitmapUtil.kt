package com.metathought.food_order.casheir.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color.TRANSPARENT
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.os.Build
import android.util.Base64
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat


object BitmapUtil {

    fun getBitmapFromVectorDrawable(context: Context, drawableId: Int): Bitmap? {
        var drawable = ContextCompat.getDrawable(context, drawableId) ?: return null
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            drawable = (DrawableCompat.wrap(drawable)).mutate()
        }
        val bitmap = Bitmap.createBitmap(
            drawable.intrinsicWidth,
            drawable.intrinsicHeight,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        canvas.drawColor(TRANSPARENT)//背景
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)
        return bitmap
    }

    fun base64ToBitmap(base64String: String): Bitmap? {
        try {
            val decodedBytes = Base64.decode(base64String, Base64.DEFAULT)
            return BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 黑白bitmap
     *
     * @param originalBitmap
     * @return
     */
    fun convertToMonochromeBitmap(originalBitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(0f) // 设置饱和度为0，即转换为灰度
        val colorFilter = ColorMatrixColorFilter(colorMatrix)
        val bmpGrayscale =
            Bitmap.createBitmap(
                originalBitmap.width,
                originalBitmap.height,
                Bitmap.Config.ARGB_8888
            )
        val canvas = Canvas(bmpGrayscale)
        val paint = Paint()
        paint.setColorFilter(colorFilter)
        canvas.drawBitmap(originalBitmap, 0f, 0f, paint)
        if (!originalBitmap.isRecycled) {
            originalBitmap.recycle()
        }
        return bmpGrayscale
    }

//    fun convertToBlackAndWhite(bitmap: Bitmap): Bitmap {
//        val resultBitmap = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
//        val canvas = Canvas(resultBitmap)
//        val paint = Paint()
//        val colorMatrix = ColorMatrix()
//        colorMatrix.setSaturation(0f) // 设置饱和度为 0，将图像转换为灰度
//        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
//        canvas.drawBitmap(bitmap, 0f, 0f, paint)
//        return resultBitmap
//    }
}