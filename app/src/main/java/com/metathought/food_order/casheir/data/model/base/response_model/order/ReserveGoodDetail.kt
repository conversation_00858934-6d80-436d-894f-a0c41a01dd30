package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName

data class ReserveGoodDetail(

    val goods: Goods?,
    val goodsReserve: GoodsReserve?,
    @SerializedName("cartsId")
    val cartsId: String?,
    @SerializedName("feeds")
    val feedInfoList: List<Feed>?,
    @SerializedName("groupIds")
    val groupIds: String?,
    @SerializedName("groups")
    val goodsGroups: List<GoodsGroup>?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("labels")
    val labels: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("picUrl")
    val picUrl: String?,
    @SerializedName("sellPrice")
    val sellPrice: Int?,
    @SerializedName("vatWhitelisting")
    val vatWhitelisting: Boolean?,
    @SerializedName("tags")
    val goodsTags: List<GoodsTag>?,
    @SerializedName("vipPrice")
    val vipPrice: Long?,
    @SerializedName("withSpecifications")
    val withSpecifications: Boolean?,
    @SerializedName("withVipPrice")
    val withVipPrice: Boolean?,
    @SerializedName("discountRate")
    val discountRate: Double?,
//
//    /**
//     * 套餐内容
//     */
//    @SerializedName("mealSetDetail")
//    val mealSetDetail: MealSetData? = null,
)

