package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo


/**
 *<AUTHOR>
 *@time  2025/2/24
 *@desc
 **/

data class OrderDiscountInfoListRequest(

    var orderNo: String? = null,
    /**
     *  适用场景 0整单减免 1单品减免
     */
    var applyRange: Int? = null,

    /**
     * 折扣活动类型，1:百分比折扣减免，2:固定金额减免
     */
    var type: Int? = null,

    /**
     * 就餐方式:0-堂食 1-外带 2-预定
     */
    var diningStyle: Int? = null,

//    /**
//     * 优惠券码  整单要传
//     */
//    var couponCode: String? = null,

    /**
     * 菜品列表-先付款场景使用
     */
    var cartGoodsList: List<GoodsBo>? = null,

//    /**
//     * 后付款 菜品 hashkey
//     */
//    var goodsHashKey: String? = null,
)