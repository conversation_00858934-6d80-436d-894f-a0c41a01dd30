package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import com.google.gson.annotations.SerializedName


data class GrabOrder(
    @SerializedName("address")
    val address: String? = null,
    @SerializedName("addressDetail")
    val addressDetail: String? = null,
    @SerializedName("grabStatus")
    val grabStatus: String? = null,
    @SerializedName("grabOrderNo")
    val grabOrderNo: String,
    @SerializedName("readyDate")
    val readyDate: String? = null,
    @SerializedName("cutlery")
    val cutlery: Bo<PERSON>an,
    @SerializedName("grabFundPromo")
    val grabFundPromo: Long,
    @SerializedName("merchantFundPromo")
    val merchantFundPromo: Long,
    @SerializedName("basketPromo")
    val basketPromo: Long,
    @SerializedName("smallOrderFee")
    val smallOrderFee: Long,
    @SerializedName("merchantChargeFee")
    val merchantChargeFee: Long,
    @SerializedName("deliveryFee")
    val deliveryFee: Long,
    @SerializedName("eaterPayment")
    val eaterPayment: Long,
    @SerializedName("total")
    val total: Long,
    @SerializedName("subtotal")
    val subtotal: Long,
    @SerializedName("readyStatus")
    val readyStatus: Int,
    @SerializedName("tax")
    val tax: Long

)