package com.metathought.food_order.casheir.ui.pending_order.confirm_dialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.pending.SerialNumberResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/3/2214:03
 * @description
 */
@HiltViewModel
class ConfirmPendingViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {


    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    fun getData(localDiningStyle: Int) {
        val shoppingRecord = ShoppingHelper.get(localDiningStyle)
        emitUiState(shoppingRecord = shoppingRecord)
    }

    fun getSerialNumber() {
        viewModelScope.launch {
//            emitUiState(result = ApiResponse.Loading)
            val response = repository.getSerialNumber()
            emitUiState(result = response)
        }

    }

    private fun emitUiState(
        result: ApiResponse<SerialNumberResponse>? = null,
        shoppingRecord: ShoppingRecord? = null,
    ) {
        val uiModel =
            UIModel(
                result,
                shoppingRecord,
            )
        _uiState.postValue(uiModel)
    }

    data class UIModel(
        val result: ApiResponse<SerialNumberResponse>?,
        var shoppingRecord: ShoppingRecord?,
    )

}
