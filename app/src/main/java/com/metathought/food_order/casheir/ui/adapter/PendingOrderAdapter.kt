package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingRecord
import com.metathought.food_order.casheir.databinding.PendingOrderItemBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class PendingOrderAdapter(
    val list: ArrayList<PendingRecord>,
    val onItemClickListener: (PendingRecord) -> Unit,
    val onDeleteClickListener: (PendingRecord) -> Unit
) : RecyclerView.Adapter<PendingOrderAdapter.PendingOrderViewHolder>() {

    private var selectedPosition = -1

    inner class PendingOrderViewHolder(val binding: PendingOrderItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    if (it != -1) {
                        if (selectedPosition != -1) {
                            list[selectedPosition].isSelect = false
                            notifyItemChanged(selectedPosition)
                        }
                        selectedPosition = it
                        list[it].isSelect = true
                        notifyItemChanged(it)

                        onItemClickListener.invoke(list[it])
                    }
                }
            }
        }

        fun bind(resource: PendingRecord, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvTableID.text = resource.serialNumber
                        clContent.setBackgroundColor(
                            ContextCompat.getColor(
                                this@run,
                                if (resource.isSelect == true) R.color.color_e7f5ee else android.R.color.transparent
                            )
                        )
//                        cardViewMain.strokeColor = ContextCompat.getColor(
//                            this@run,
//                            if (resource.isSelect == true) R.color.primaryColor else android.R.color.transparent
//                        )
                        val itemsValue =
                            "${getString(R.string.items)}: ${it?.totalNum}"
                        tvItems.text = itemsValue

                        val timeValue =
                            "${getString(R.string.date)}: ${it.createTime?.formatDate()}"
                        tvTime.text = timeValue

                        tvRemark.text = "${getString(R.string.remark)}:${
                            if (it.note.isNullOrEmpty()) getString(R.string.none) else it.note
                        }"

                        if (it.isHasNeedProcess()) {
                            tvPrice.text = getString(R.string.to_be_confirmed)
                        } else {
//                            var price = (it.totalPrice ?: 0) - it.getTotalCouponActivityAmount()
//                            if (it.getPendingGoodJson()?.isHasDiscountPrice() == true) {
//                                price = (it.getPendingGoodJson()?.totalDiscountPrice
//                                    ?: 0) - it.getTotalCouponActivityAmount()
//                            }
                            var price = it.getPendingGoodJson()?.getSubTotal() ?: 0L
                            if (it.getPendingGoodJson()?.isHasDiscountPrice() == true) {
                                price = it.getPendingGoodJson()?.getDiscountSubTotal() ?: 0L
                            }
                            tvPrice.text = price.priceFormatTwoDigitZero2()
                        }
                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PendingOrderViewHolder {
        val itemView =
            PendingOrderItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PendingOrderViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: PendingOrderViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun setSelectFirst() {
        if (this.list.isNotEmpty()) {
            this.list[0].isSelect = true
            selectedPosition = 0
        }
    }


    fun replaceData(list: ArrayList<PendingRecord>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<PendingRecord>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }

    fun updateStatus(record: PendingRecord) {
        if (selectedPosition != -1 && list[selectedPosition].id == record.id) {
            list[selectedPosition] = record
            notifyItemChanged(selectedPosition)
        }
    }


}