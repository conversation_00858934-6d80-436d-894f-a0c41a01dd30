package com.metathought.food_order.casheir.ui.adapter

import android.graphics.Typeface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.report.DailyReportData
import com.metathought.food_order.casheir.data.model.base.response_model.report.DailyReportPaymentChannels
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.databinding.ItemDailyReportContentBinding
import com.metathought.food_order.casheir.databinding.ItemDailyReportDateBinding
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.priceFormatZeroDigit
import com.metathought.food_order.casheir.ui.dialog.store_report.DailyReportDialog
import com.metathought.food_order.casheir.utils.DisplayUtils

/**
 * 每日报表内容列表适配器
 */
class DailyReportContentAdapter(
    val list: ArrayList<DailyReportData>,
) : RecyclerView.Adapter<DailyReportContentAdapter.ProduceReportItemViewHolder>() {

    var showCommission: Boolean = false
    private var paymentChannels: List<DailyReportPaymentChannels> = listOf()

    inner class ProduceReportItemViewHolder(val binding: ItemDailyReportContentBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: DailyReportData, position: Int) {
            binding.apply {
                //小计
                tvSubtotal.text = "${resource.subtotal?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                //减免金额
                tvDiscountAmount.text = "${resource.discountAmount?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                //服务费
                tvServiceFee.text = "${resource.serviceFee?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                //打包费
                tvPackFee.text = "${resource.packingFee?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                //增值税
                tvVat.text = "${resource.vatAmount?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                //佣金
                tvCommission.text = "${resource.commission?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                tvCommission.isVisible = showCommission
                //退款金额
                tvRefundAmount.text = "${resource.refundAmount?.priceFormatTwoDigitZero2("$")?:"$0.00"}"
                //总计
                tvTotal.text = "${resource.total?.priceFormatTwoDigitZero2("$")?:"$0.00"}"

                binding.llDynamicItem.removeAllViews()
                paymentChannels.forEach {
                    val textView = TextView(binding.root.context).apply {
                        layoutParams = LinearLayout.LayoutParams(
                            DisplayUtils.dp2px(context, DailyReportDialog.tableColW),
                            LinearLayout.LayoutParams.MATCH_PARENT
                        ).apply {
                            marginEnd = DisplayUtils.dp2px(context, 10f)
                        }

                        maxLines = 2
                        gravity = Gravity.CENTER
                        setTextColor(ContextCompat.getColor(context, R.color.black))
                        textSize = 14f
                       val amount = resource.paymentChannelAmounts?.get(it.channelCode)
                        text = "${amount?.priceFormatTwoDigitZero2("$")?:"$0.00"}"

                        setTextAppearance(R.style.FontLocalization)
                        setTypeface(null, Typeface.BOLD)
                    }
                    binding.llDynamicItem.addView(textView)
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemDailyReportContentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: DailyReportContentAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<DailyReportData>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    fun setupPayChannelList(paymentChannels: List<DailyReportPaymentChannels>?) {
        this.paymentChannels = paymentChannels ?: listOf()
    }

}