package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list.StoreDataOrderVo
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.databinding.StoreOrderItemBinding
import com.metathought.food_order.casheir.extension.getAcceptText
import com.metathought.food_order.casheir.extension.getDiningStyleString
import com.metathought.food_order.casheir.extension.getGrabPayText
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import java.math.BigDecimal


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class StoreOrderListAdapter(
    val list: ArrayList<StoreDataOrderVo?>,
    val onItemClickListener: (StoreDataOrderVo) -> Unit,
) : RecyclerView.Adapter<StoreOrderListAdapter.StoreOrdersListViewHolder>() {

    private var offlineChannelTotalModel: OfflineChannelTotalModel? = null

    inner class StoreOrdersListViewHolder(val binding: StoreOrderItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: StoreDataOrderVo?, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
                        resource?.apply {

                            if (resource.grabOrder != null) {
                                llStore.isVisible = false
                                llGrab.isVisible = true
                                var order = grabOrder;
                                order?.apply {
                                    tvOutterOrderIDGrab.text =
                                        if (!grabOrderNo.isNullOrEmpty()) grabOrder?.grabOrderNo else context.getString(
                                            R.string.n_a
                                        )
                                }
                                tvPickUpNoGrab.text =
                                    if (!pickupNo.isNullOrEmpty()) pickupNo else context.getString(
                                        R.string.n_a
                                    )
                                tvCustomerNameGrab.text =
                                    if (!customerName.isNullOrEmpty()) resource.customerName else context.getString(
                                        R.string.n_a
                                    )

                                tvOrderTypeGrab.text =
                                    diningStyle?.getDiningStyleString(context) ?: ""

                                tvOrderStatusGrab.text =
                                    grabOrder?.orderStatus?.getGrabPayText(context)
                                        ?: context.getString(
                                            R.string.n_a
                                        )

                                tvTotalGrab.text =
                                    grabOrder?.total?.priceFormatTwoDigitZero2() ?: "0.00"

                                tvCreatTimeGrab.text =
                                    if (!createDateTime.isNullOrEmpty()) createDateTime else context.getString(
                                        R.string.n_a
                                    )


                                tvActionGrab.setOnClickListener {
                                    onItemClickListener.invoke(resource)
                                }


                            } else {
                                llStore.isVisible = true
                                llGrab.isVisible = false
                                tvOrderID.text =
                                    if (!orderNo.isNullOrEmpty()) orderNo else context.getString(
                                        R.string.n_a
                                    )
                                tvOrderTime.text =
                                    if (!createDateTime.isNullOrEmpty()) createDateTime else context.getString(
                                        R.string.n_a
                                    )
                                tvPaymentTime.text =
                                    if (!payDateTime.isNullOrEmpty()) payDateTime else context.getString(
                                        R.string.n_a
                                    )
                                tvCustomerName.text =
                                    if (!customerName.isNullOrEmpty()) resource.customerName else context.getString(
                                        R.string.n_a
                                    )
                                tvTable.text =
                                    if (!diningTable.isNullOrEmpty()) diningTable else context.getString(
                                        R.string.n_a
                                    )
                                tvOrderStatus.text = orderStatus?.getPayText(context)
                                tvPaymentMethod.text =
                                    resource.getPaymentMethod(context, offlineChannelTotalModel)
                                tvTotal.text = FoundationHelper.getPriceStrByUnit(
                                    conversionRatio = conversionRatio
                                        ?: FoundationHelper.conversionRatio,
                                    usdPrice = BigDecimal(mayPayMoney ?: 0.0).halfUp(2)
                                        .times(BigDecimal(100.0))
                                        .toLong(),
                                    isKhr()
                                )    //"$${mayPayMoney}"
                                tvAction.setOnClickListener {
                                    onItemClickListener.invoke(resource)
                                }
                                if (weightMark == false) {
                                    tvTotal.text = "${context.getString(R.string.to_be_confirmed)}"
                                }
                            }


                        }

                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StoreOrdersListViewHolder {
        val itemView =
            StoreOrderItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return StoreOrdersListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: StoreOrdersListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    fun replaceData(
        list: ArrayList<StoreDataOrderVo?>?,
        offlineChannelTotalModel: OfflineChannelTotalModel? = null
    ) {
        if (list != null) {
            this.list.clear()
            this.offlineChannelTotalModel = offlineChannelTotalModel
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<StoreDataOrderVo?>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


}