package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName

data class PartialRefundRequest(
    @SerializedName("orderId")
    val orderId: String?,
    @SerializedName("payType")
    val payType: Int,
    @SerializedName("refundGoodsList")
    val refundGoodsList: List<RefundGoods?>?,
    @SerializedName("autoInStock")
    val autoInStock: Boolean? = null
)

data class RefundGoods(
    @SerializedName("goodSkuKey")
    val goodSkuKey: String?,
    @SerializedName("num")
    val num: Int?
)