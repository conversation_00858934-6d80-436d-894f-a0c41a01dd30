package com.metathought.food_order.casheir.helper

import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.halfUpKhr
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import java.math.BigDecimal
import java.math.RoundingMode


/**
 *<AUTHOR>
 *@time  2025/2/13
 *@desc
 **/

object FoundationHelper {
    //当前配置的汇率
    var conversionRatio: Long = 4100

    // 部分场景使用中的汇率
    var useConversionRatio: Long = 4100

    // 部分场景使用中的汇率
    var isKrh: Boolean = false

    //当前是否外卖平台
    var isTakeOut: Boolean = false


    /**
     * 美元转瑞尔 （price 美分）
     *
     */
    fun usdConverToKhr(conversionRatio: Long?, price: Long?): Long {
        return BigDecimal(price ?: 0).times(BigDecimal(conversionRatio ?: 4100))
            .divide(BigDecimal(100.0))
            .toLong()
            .halfUpKhr()
    }

    fun getPriceStrByUnit(conversionRatio: Long?, usdPrice: Long?, isKhr: Boolean): String {
        if (isKhr) {
            return "៛${usdConverToKhr(conversionRatio, usdPrice).decimalFormatZeroDigit()}"
        }
        return usdPrice?.priceFormatTwoDigitZero2() ?: ""
    }

    /**
     * 瑞尔转美元
     *
     */
    fun khrConverToUsd(conversionRatio: Long?, price: BigDecimal?): Long {
        return (price ?: BigDecimal.ZERO).divide(
            BigDecimal(conversionRatio ?: 4100), 2,
            RoundingMode.HALF_UP
        ).times(BigDecimal(100)).toLong()
    }

}