package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


/**
 *<AUTHOR>
 *@time  2025/2/27
 *@desc  单品折扣 请求的model
 **/

@Parcelize
data class SingleDiscountRequest(
    @SerializedName("type")
    var type: Int?,    //类型 1.百分比 2.固定金额		false
    @SerializedName("goodsId")
    var goodsId: String?,    //单品id		false
    @SerializedName("goodsHashKey")
    var goodsHashKey: String?,    //商品hashKey		false
    @SerializedName("reduceRatio")
    var reduceRatio: Double?,    //减免（百分比）        false
    @SerializedName("saleReduce")
    var saleReduce: Double?,    //销售减免        false
    @SerializedName("vipReduce")
    var vipReduce: Double?,    //会员减免        false
    /**
     *   销售-改价
     */
    @SerializedName("adjustSalePrice")
    var adjustSalePrice: Double? = null,

    /**
     *   会员-改价
     */
    @SerializedName("adjustVipPrice")
    var adjustVipPrice: Double? = null,

    @SerializedName("remark")
    var remark: String? = null,    //备注

    //原因类型
    @SerializedName("discountType")
    var discountType: Int? = null,

    //选择的后台配置的折扣id
    @SerializedName("discountReduceActivityId")
    var discountReduceActivityId: String? = null,

    /**
     * 如果是选择后台配的减免折扣 因为不合并 所以这个要有值 ，折扣相关的也不合并
     */
    @SerializedName("uuid")
    var uuid: String? = null,

    //修改了几个
    @SerializedName("goodsNum")
    var goodsNum: Int? = null


) : Parcelable

