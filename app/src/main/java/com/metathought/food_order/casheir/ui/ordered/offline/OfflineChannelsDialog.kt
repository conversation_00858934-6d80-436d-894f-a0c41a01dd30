package com.metathought.food_order.casheir.ui.ordered.offline


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.databinding.DialogOfflineChannelsBinding
import com.metathought.food_order.casheir.ui.adapter.OfflineChannelsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * 线下支付渠道弹窗
 * Offline payment channel dialog
 * <AUTHOR>
 * @date 2024/5/911:03
 * @description
 */
@AndroidEntryPoint
class OfflineChannelsDialog : BaseDialogFragment() {

    private var binding: DialogOfflineChannelsBinding? = null

    private var paymentClickListener: ((OfflineChannelModel) -> Unit)? = null
    private var offlineChannelList: List<OfflineChannelModel>? = null
    private var offlineChannelModel: OfflineChannelModel? = null

//    private val viewModel: OfflineChannelsViewModel by viewModels()

    private var mAdapter: OfflineChannelsAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogOfflineChannelsBinding.inflate(layoutInflater)
        return binding?.root
    }

    companion object {
        //        private const val AVAILABLE_OFFLINE_PAYMENT_CHANNEL = "AVAILABLE_OFFLINE_PAYMENT_CHANNEL"
        private const val TAG = "OfflineChannelsDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            offlineChannelList: List<OfflineChannelModel>? = null,
            offlineChannelModel: OfflineChannelModel? = null,
            paymentClickListener: ((OfflineChannelModel) -> Unit)
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                offlineChannelList = offlineChannelList,
                offlineChannelModel = offlineChannelModel,
                paymentClickListener = paymentClickListener
            )
            fragment.show(fragmentManager, TAG)
        }


        private fun newInstance(
            offlineChannelList: List<OfflineChannelModel>? = null,
            offlineChannelModel: OfflineChannelModel? = null,
            paymentClickListener: ((OfflineChannelModel) -> Unit)
        ): OfflineChannelsDialog {
            return OfflineChannelsDialog().apply {
                this.offlineChannelList = offlineChannelList
                this.offlineChannelModel = offlineChannelModel
                this.paymentClickListener = paymentClickListener
            }
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
//        initObserver()
        initListener()
    }

    private fun initView() {
        binding?.apply {
            context?.let {
                mAdapter = OfflineChannelsAdapter(arrayListOf()) {
                    paymentClickListener?.invoke(it)
                    dismissAllowingStateLoss()
                }
                recyclerViewChannels.adapter = mAdapter
            }
            mAdapter?.updateData(offlineChannelList ?: listOf())
            mAdapter?.setSelect(offlineChannelModel)
            val index = offlineChannelList?.indexOf(offlineChannelModel) ?: -1
            if (index != -1) {
                recyclerViewChannels.scrollToPosition(index)
            }
        }
    }

//    private fun initObserver() {
//        viewModel.uiModeState.observe(viewLifecycleOwner) { state ->
//            state.result?.let {
//                when (it) {
//                    is ApiResponse.Loading -> {
//                        binding?.apply {
//                            progressBar.isVisible = true
//                        }
//                    }
//
//                    is ApiResponse.Error -> {
//                        binding?.apply {
//                            progressBar.isVisible = false
//                        }
//                    }
//
//                    is ApiResponse.Success -> {
//                        binding?.apply {
//                            progressBar.isVisible = false
////                            mAdapter?.updateData(it.data)
////                            if (!mAdapter?.list.isNullOrEmpty()) {
////                                btnYes.isEnabled = true
////                                btnYes.isCheckable = true
////                                btnYes.alpha = 1f
////                            }
//                        }
//                    }
//
//                }
//            }
//
//        }
//    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener { dismissAllowingStateLoss() }

//            btnClose.setOnClickListener {
//                dismissAllowingStateLoss()
//            }
//            btnNo.setOnClickListener {
//                dismissAllowingStateLoss()
//            }
//
//            btnYes.setOnClickListener { _ ->
//                mAdapter?.let {
//                    paymentClickListener?.invoke(it.getSelect())
//                    dismissAllowingStateLoss()
//                }
//            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.6).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }

}