package com.metathought.food_order.casheir.data.model.base.request_model.member


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.PayInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeTierCouponTemplate
import java.math.BigDecimal

data class RechargeRequest(
    @SerializedName("id")
    val id: String?,    //账户id
    @SerializedName("addNum")
    val addNum: Long?,  //充值门店余额(变更) 单位:分
    @SerializedName("type")
    val type: Int?, //1-在线充值 2-线下充值
    @SerializedName("couponCode")
    val couponCode: String?,    //优惠券编码


    @SerializedName("offlinePayChannelsId")
    val offlinePayChannelsId: Long?, //线下收款渠道id，线下支付

    @SerializedName("collectCash")
    val collectCash: BigDecimal?, //收取现金 现金支付时使用,瑞尔
    @SerializedName("changeAmount")
    val changeAmount: BigDecimal?, //找零金额 现金支付时使用，瑞尔
    @SerializedName("collectCashDollar")
    val collectCashDollar: BigDecimal?, //收取现金 现金支付时使用,美元
    @SerializedName("changeAmountDollar")
    val changeAmountDollar: BigDecimal?, //找零金额 现金支付时使用,美元

    @SerializedName("rechargeTierId")
    val rechargeTierId: Long?, //档位id

    @SerializedName("giftAmount")
    val giftAmount: BigDecimal?, //储值赠送金额（单位：分）
    @SerializedName("giftCouponTemplateList")
    val giftCouponTemplateList: List<GiftCoupon>?, //储值赠送优惠券list

    @SerializedName("remark")
    val remark: String?,    //备注

//    //支付信息列表
//    @SerializedName("payInfoList")
//    var payInfoList: List<PayInfo>? = null,
)

data class GiftCoupon(
    var couponTemplateId: String?,    //优惠券模版id		false
    var num: Int?,        //数量		false
)