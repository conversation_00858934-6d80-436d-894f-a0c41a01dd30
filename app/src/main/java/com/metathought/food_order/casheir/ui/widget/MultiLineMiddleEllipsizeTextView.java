package com.metathought.food_order.casheir.ui.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Build;
import android.text.Layout;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.TextAppearanceSpan;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;

import com.metathought.food_order.casheir.R;

import java.lang.reflect.Field;

import timber.log.Timber;

public class MultiLineMiddleEllipsizeTextView extends AppCompatTextView {
    private static final String ELLIPSIS = "…";
    private int maxLines = 2;

    public MultiLineMiddleEllipsizeTextView(Context context) {
        super(context);
    }

    public MultiLineMiddleEllipsizeTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initAttributes(attrs);
    }

    public MultiLineMiddleEllipsizeTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttributes(attrs);
    }

    private void initAttributes(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.MultiLineMiddleEllipsizeTextView);
//            maxLines = a.getInt(android.R.styleable.TextView_maxLines, maxLines);
            a.recycle();
        }
    }

    @Override
    protected void onTextChanged(CharSequence text, int start, int lengthBefore, int lengthAfter) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter);
        Timber.e("text :" + text);
//        applyMiddleEllipsis(text);
    }

    public void applyMiddleEllipsis(CharSequence text, CharSequence endStr) {

        SpannableStringBuilder tempText = charSequenceToSpannable(text);
        Timber.e("text ==" + text);
        Layout layout = createStaticLayout(tempText);
        maxLines = 2;
        try {
            if (layout.getLineCount() > maxLines) {
                int totalChars = layout.getLineEnd(maxLines - 1);

                int start = totalChars - endStr.length() - ELLIPSIS.length();
                Timber.e("totalChars:" + totalChars + "   start:" + start);
                if (start < 0) {
                    start = 0;
                }

                SpannableString spannableString = new SpannableString(text.subSequence(0, start) + ELLIPSIS + endStr);
                setText(spannableString);
            } else {
                setText(text);
            }
        } catch (Exception e) {
            Timber.e("Exception  :");
            setText(text);
        }

    }

    private int initWidth = 0;

    public void initWidth(int width) {
        initWidth = width;
    }

    /**
     * @param spannable
     * @return
     */
    private Layout createStaticLayout(SpannableStringBuilder spannable) {
        Timber.e("initWidth  " + getWidth());
        int contentWidth = initWidth - getPaddingLeft() - getPaddingRight();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {

            StaticLayout.Builder builder = StaticLayout.Builder.obtain(spannable, 0, spannable.length(), getPaint(), contentWidth);
            builder.setAlignment(Layout.Alignment.ALIGN_NORMAL);
            builder.setIncludePad(getIncludeFontPadding());
            builder.setLineSpacing(getLineSpacingExtra(), getLineSpacingMultiplier());
            builder.setBreakStrategy(getBreakStrategy())
                    .setHyphenationFrequency(getHyphenationFrequency());

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder.setJustificationMode(getJustificationMode());
            }

            if (getEllipsize() != null && getKeyListener() == null) {
                builder.setEllipsize(getEllipsize())
                        .setEllipsizedWidth(contentWidth);

            }
            return builder.build();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            return new StaticLayout(spannable, getPaint(), contentWidth, Layout.Alignment.ALIGN_NORMAL,
                    getLineSpacingMultiplier(), getLineSpacingExtra(), getIncludeFontPadding());
        } else {
            return new StaticLayout(spannable, getPaint(), contentWidth, Layout.Alignment.ALIGN_NORMAL,
                    getFloatField("mSpacingMult", 1f), getFloatField("mSpacingAdd", 0f), getIncludeFontPadding());
        }
    }

    /**
     * @param charSequence
     * @return
     */
    private SpannableStringBuilder charSequenceToSpannable(@NonNull CharSequence charSequence) {
        SpannableStringBuilder spannableStringBuilder = null;
//        if (mCharSequenceToSpannableHandler != null) {
//            spannableStringBuilder = mCharSequenceToSpannableHandler.charSequenceToSpannable(charSequence);
//        }
        if (spannableStringBuilder == null) {
            spannableStringBuilder = new SpannableStringBuilder(charSequence);
        }

        return spannableStringBuilder;
    }

    private float getFloatField(String fieldName, float defaultValue) {
        float value = defaultValue;
        if (TextUtils.isEmpty(fieldName)) {
            return value;
        }
        try {
            // 获取该类的所有属性值域
            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (TextUtils.equals(fieldName, field.getName())) {
                    value = field.getFloat(this);
                    break;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return value;
    }

}
