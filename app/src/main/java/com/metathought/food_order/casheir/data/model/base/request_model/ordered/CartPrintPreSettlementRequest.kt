//package com.metathought.food_order.casheir.data.model.base.request_model.ordered
//
//import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
//import java.math.BigDecimal
//
//
///**
// *<AUTHOR>
// *@time  2024/7/30
// *@desc
// **/
//
//data class CartPrintPreSettlementRequest(
////    var orderNo: String? = null,
//
////    //减免金额（美元）
////    var reduceDollar: BigDecimal? = null,
////    //减免金额（瑞尔）
////    val reduceKhr: BigDecimal? = null,
////    //减免金额（vip美元）
////    var reduceVipDollar: BigDecimal? = null,
////    //减免金额（vip瑞尔）
////    val reduceVipKhr: BigDecimal? = null,
////
////    //减免百分比
////    var reduceRate: BigDecimal? = null,
////    //类型 1.减免 2.折扣
////    var reduceType: Int? = null,
//////    /**
//////     * 会员减免
//////     */
//////    var reduceVipDollar: BigDecimal? = null,
////    /**
////     * 折扣减免活动Id
////     */
////    var discountReduceActivityId: String? = null,
////
////    /**
////     * 是否要云打印机打印
////     */
////    var shouldCloudPrinterPrint: Boolean? = true
//
//    val diningStyle: Int,
//    val goodsList: List<GoodsBo>? = null,
//    val note: String? = null,
//    val tableUuid: String? = null,
//    //优惠券码
//    var couponCode: String? = null,
//
//    //预定时间
//    var peopleDate: String? = null
//)