package com.metathought.food_order.casheir.utils

import android.widget.HorizontalScrollView
import androidx.recyclerview.widget.RecyclerView

/**
 * RecyclerView滚动同步帮助类
 * 用于实现多个RecyclerView之间的滚动同步
 */
class RecyclerViewScrollSyncHelper {

    /**
     * 垂直滚动同步
     * 同步两个垂直滚动的RecyclerView
     */
    fun syncVerticalScroll(recyclerView1: RecyclerView, recyclerView2: RecyclerView) {
        var isScrolling1 = false
        var isScrolling2 = false

        recyclerView1.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrolling2) {
                    isScrolling1 = true
                    recyclerView2.scrollBy(0, dy)
                    isScrolling1 = false
                }
            }
        })

        recyclerView2.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrolling1) {
                    isScrolling2 = true
                    recyclerView1.scrollBy(0, dy)
                    isScrolling2 = false
                }
            }
        })
    }

    /**
     * 水平滚动同步
     * 同步两个水平滚动的RecyclerView
     */
    fun syncHorizontalScroll(recyclerView1: RecyclerView, recyclerView2: RecyclerView) {
        var isScrolling1 = false
        var isScrolling2 = false

        recyclerView1.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrolling2) {
                    isScrolling1 = true
                    recyclerView2.scrollBy(dx, 0)
                    isScrolling1 = false
                }
            }
        })

        recyclerView2.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrolling1) {
                    isScrolling2 = true
                    recyclerView1.scrollBy(dx, 0)
                    isScrolling2 = false
                }
            }
        })
    }

    /**
     * 混合滚动同步
     * 用于内容区域的RecyclerView，需要同时处理水平和垂直滚动同步
     */
    fun syncMixedScroll(
        contentRecyclerView: RecyclerView,
        verticalSyncRecyclerView: RecyclerView,
        horizontalSyncRecyclerView: RecyclerView
    ) {
        var isVerticalScrolling = false
        var isHorizontalScrolling = false

        // 内容区域滚动时，同步到其他两个RecyclerView
        contentRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                
                // 垂直滚动同步
                if (!isVerticalScrolling && dy != 0) {
                    isVerticalScrolling = true
                    verticalSyncRecyclerView.scrollBy(0, dy)
                    isVerticalScrolling = false
                }
                
                // 水平滚动同步
                if (!isHorizontalScrolling && dx != 0) {
                    isHorizontalScrolling = true
                    horizontalSyncRecyclerView.scrollBy(dx, 0)
                    isHorizontalScrolling = false
                }
            }
        })

        // 垂直同步RecyclerView滚动时，同步到内容区域
        verticalSyncRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isVerticalScrolling) {
                    isVerticalScrolling = true
                    contentRecyclerView.scrollBy(0, dy)
                    isVerticalScrolling = false
                }
            }
        })

        // 水平同步RecyclerView滚动时，同步到内容区域
        horizontalSyncRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isHorizontalScrolling) {
                    isHorizontalScrolling = true
                    contentRecyclerView.scrollBy(dx, 0)
                    isHorizontalScrolling = false
                }
            }
        })
    }

    /**
     * RecyclerView与HorizontalScrollView的水平滚动同步
     */
    fun syncRecyclerViewWithHorizontalScrollView(
        recyclerView: RecyclerView,
        horizontalScrollView: HorizontalScrollView
    ) {
        var isRecyclerViewScrolling = false
        var isHorizontalScrollViewScrolling = false

        // RecyclerView水平滚动时，同步到HorizontalScrollView
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isHorizontalScrollViewScrolling && dx != 0) {
                    isRecyclerViewScrolling = true
                    horizontalScrollView.scrollBy(dx, 0)
                    isRecyclerViewScrolling = false
                }
            }
        })

        // HorizontalScrollView滚动时，同步到RecyclerView
        horizontalScrollView.setOnScrollChangeListener { _, scrollX, _, oldScrollX, _ ->
            val dx = scrollX - oldScrollX
            if (!isRecyclerViewScrolling && dx != 0) {
                isHorizontalScrollViewScrolling = true
                recyclerView.scrollBy(dx, 0)
                isHorizontalScrollViewScrolling = false
            }
        }
    }

    /**
     * 为综合报表设置复杂的滚动同步
     * - rvHeaders 和 rvContent 垂直同步
     * - rvDateHeaders 和 rvContent 中的 HorizontalScrollView 水平同步
     */
    fun setupComprehensiveReportScrollSync(
        rvHeaders: RecyclerView,
        rvContent: RecyclerView,
        rvDateHeaders: RecyclerView,
        getHorizontalScrollViewFromContent: (Int) -> HorizontalScrollView?
    ) {
        // 1. 垂直滚动同步：rvHeaders 和 rvContent
        syncVerticalScroll(rvHeaders, rvContent)

        // 2. 水平滚动同步：rvDateHeaders 和 rvContent 中的 HorizontalScrollView
        var isDateHeaderScrolling = false
        var isContentScrolling = false

        // 当日期标题滚动时，同步所有内容行的HorizontalScrollView
        rvDateHeaders.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isContentScrolling && dx != 0) {
                    isDateHeaderScrolling = true
                    // 同步所有可见的内容行
                    for (i in 0 until rvContent.childCount) {
                        getHorizontalScrollViewFromContent(i)?.scrollBy(dx, 0)
                    }
                    isDateHeaderScrolling = false
                }
            }
        })
    }
}
