package com.metathought.food_order.casheir.utils


/**
 *<AUTHOR>
 *@time  2023/11/8
 *@desc
 **/

object TextInputUtil {

    fun amountInputUtil(text: String?): String {
        var result = text ?: ""
        //如果首次输入的是0 ，第二次输入的数字会替换掉0
        if (text?.startsWith("0.") == false) {
            //如果不是0. 开头的，
            if (text.startsWith("0") && text.length > 1) {
                result = text.replaceFirst(Regex("^0+"), "")
            }
        }
        return result
    }
}