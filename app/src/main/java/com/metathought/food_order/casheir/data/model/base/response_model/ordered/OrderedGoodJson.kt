package com.metathought.food_order.casheir.data.model.base.response_model.ordered


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel

data class OrderedGoodJson(
    @SerializedName("goodsList")
    var goodsList: MutableList<OrderedGoods>?,
    @SerializedName("goodsTotalNum")
    var goodsTotalNum: Int?,
    @SerializedName("isReservation")
    val isReservation: Boolean?,
    @SerializedName("peopleDate")
    val peopleDate: Any?,
    @SerializedName("peopleNum")
    val peopleNum: Any?,
    @SerializedName("totalPrice")
    val totalPrice: Long?,
    @SerializedName("totalVatPrice")
    val totalVatPrice: Long?,
    @SerializedName("totalVipPrice")
    val totalVipPrice: Long?,
    @SerializedName("totalVipVatPrice")
    val totalVipVatPrice: Long?,
    @SerializedName("totalDiscountPrice")
    val totalDiscountPrice: Long?,
    @SerializedName("totalDiscountVatPrice")
    val totalDiscountVatPrice: Long?,

    @SerializedName("totalServiceCharge")
    val totalServiceCharge: Long?,
    @SerializedName("totalVipServiceCharge")
    val totalVipServiceCharge: Long?,
    @SerializedName("totalDiscountServiceCharge")
    val totalDiscountServiceCharge: Long?,
    @SerializedName("giftGoodsList")
    var giftGoodsList: MutableList<UsageGoods>?,

    @SerializedName("couponActivityList")
    var couponActivityList: MutableList<CouponActivityModel>?,

    /**
     * 订单备注
     */
    @SerializedName("note")
    var note: String?


) {


    fun getTotalPackagingFee(): Int {
        var totalPackPrice = 0
        goodsList?.forEach {
            totalPackPrice += it.totalPackingFee()
        }
        return totalPackPrice
    }

    fun getSubTotal(): Long {
        return totalDiscountPrice?.minus(totalDiscountVatPrice ?: 0L)
            ?.minus(totalDiscountServiceCharge ?: 0L)?.minus(getTotalPackagingFee())
            ?: 0L
    }

    fun getDiscountSubTotal(): Long {
        return totalDiscountPrice?.minus(totalDiscountVatPrice ?: 0L)
            ?.minus(totalDiscountServiceCharge ?: 0L)?.minus(getTotalPackagingFee())
            ?: 0L
    }

    fun getVipSubTotal(): Long {
        return totalVipPrice?.minus(totalVipVatPrice ?: 0L)
            ?.minus(totalVipServiceCharge ?: 0L)?.minus(getTotalPackagingFee())
            ?: 0L
    }

    /**
     * 是否包含待定价商品
     *
     * @return
     */
    fun isHasNeedWeight(): Boolean {
        var isNeedWeight = false
        //判断是否含有 未处理价格的商品
        goodsList?.forEach {
            if (!it.isHasProcessed()) {
                isNeedWeight = true
            }
        }
        return isNeedWeight
    }

    fun isShowVipPrice(): Boolean {
        var hasVip = false
        goodsList?.forEach {
            if (it.isShowVipPrice()) {
                hasVip = true
            }
        }
        return hasVip
    }

    //是否有折扣价
    fun isHasDiscountPrice(): Boolean {
        return totalDiscountPrice != null && totalDiscountPrice != totalPrice
    }

}