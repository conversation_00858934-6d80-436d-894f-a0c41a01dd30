package com.metathought.food_order.casheir.ui.ordered.coupon

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Toast
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.databinding.DialogIdentifyCouponBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/8/25
 *@desc  识别优惠券
 **/

@AndroidEntryPoint
class IdentifyCouponDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "IdentifyCouponDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            diningStyle: Int? = null,
            goodsList: List<GoodsBo>? = null,
            isPreOrder: Boolean? = null,
            note: String? = null,
            tableUuid: String? = null,
            couponCode: String? = null,
            orderNo: String? = null,
            identifySuccessListener: ((CouponModel) -> Unit)
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    diningStyle,
                    goodsList,
                    isPreOrder,
                    note,
                    tableUuid,
                    couponCode,
                    orderNo,
                    identifySuccessListener
                )

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            diningStyle: Int? = null,
            goodsList: List<GoodsBo>? = null,
            isPreOrder: Boolean? = null,
            note: String? = null,
            tableUuid: String? = null,
            couponCode: String? = null,
            orderNo: String? = null,
            identifySuccessListener: ((CouponModel) -> Unit)
        ): IdentifyCouponDialog {
            val fragment = IdentifyCouponDialog()
            fragment.diningStyle = diningStyle
            fragment.goodsList = goodsList
            fragment.isPreOrder = isPreOrder
            fragment.note = note
            fragment.tableUuid = tableUuid
            fragment.couponCode = couponCode
            fragment.orderNo = orderNo
            fragment.identifySuccessListener = identifySuccessListener
            return fragment
        }
    }

    private var identifySuccessListener: ((CouponModel) -> Unit)? = null

    private var binding: DialogIdentifyCouponBinding? = null
    private val viewModel: IdentifyCouponViewModel by viewModels()
    private var diningStyle: Int? = null
    private var goodsList: List<GoodsBo>? = null
    private var isPreOrder: Boolean? = null
    private var note: String? = null
    private var tableUuid: String? = null
    private var couponCode: String? = null
    private var orderNo: String? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogIdentifyCouponBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) { state ->
            when (state.response) {
                is ApiResponse.Loading -> {
                    enableInput(false)
                }

                is ApiResponse.Success -> {

                    identifySuccessListener?.invoke(state.response.data)
                    dismissCurrentDialog()
                }

                is ApiResponse.Error -> {
                    enableInput(true)
                    if (state.response.message?.isNotEmpty() == true) {
                        Toast.makeText(
                            requireContext(),
                            state.response.message, Toast.LENGTH_SHORT
                        ).show()
                    }

                }

                else -> {

                }

            }
        }
    }

    override fun onStart() {
        super.onStart()
//        val dialog = dialog
//        if (dialog != null) {
//            dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
//        }
    }

    private fun initView() {
        binding?.apply {

        }
    }


    private fun initListener() {
        binding?.apply {
            btnConfirm.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    viewModel.getCoupon(
                        diningStyle,
                        goodsList,
                        isPreOrder,
                        note,
                        tableUuid,
                        edtCouponCode.text.toString(),
                        orderNo
                    )
                }
            }
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }

            edtCouponCode.addTextChangedListener {
                Timber.e("edtCouponCode  ${edtCouponCode.text}")
                btnConfirm.setEnableWithAlpha(!it.isNullOrEmpty())
            }

//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }


    private fun enableInput(enable: Boolean) {
        binding?.apply {
            edtCouponCode.setEnableWithAlpha(enable)
            btnConfirm.setEnableWithAlpha(enable)
        }
    }


    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * 0.5).toInt()
//            val screenWidth = (displayMetrics.widthPixels * 1).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }


//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }
}