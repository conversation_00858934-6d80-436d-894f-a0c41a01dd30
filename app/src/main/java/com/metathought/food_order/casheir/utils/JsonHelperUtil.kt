package com.metathought.food_order.casheir.utils

//import com.fasterxml.jackson.databind.ObjectMapper
//import com.fasterxml.jackson.databind.type.TypeFactory
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import timber.log.Timber

object JsonHelperUtil {
    val gson: Gson = GsonBuilder()
        .serializeSpecialFloatingPointValues()
        // .serializeNulls() // Default Gson will ignore null values on both serialize and deserialize
        .create()

    fun <T> saveObject(obj: T): String {
        return gson.toJson(obj)
    }

    fun <T> getObject(json: String?, clazz: Class<T>): T? {
        return try {
            if (json.isNullOrBlank().not()) Gson().fromJson(json, clazz) else null
        } catch (e: JsonSyntaxException) {
            Timber.e(e)
            null
        }
    }

//    fun <T> toHashMap(obj: T): HashMap<String, Any> {
//        return try {
//            // If you Gson -> bug: Convert Int -> double (not Int)
//            val hashMapType = TypeFactory.defaultInstance()
//                .constructMapType(HashMap::class.java, String::class.java, Any::class.java)
//            ObjectMapper().convertValue(obj, hashMapType) ?: hashMapOf()
////                gson.fromJson(json, object : TypeToken<HashMap<String, Any>>() {}.type)
//        } catch (e: JsonSyntaxException) {
//            Timber.e(e)
//            hashMapOf()
//        }
//    }

    inline fun <reified T> saveList(list: List<T>, clazz: Class<T>): String {
        return gson.toJson(list.toTypedArray(), TypeToken.getArray(clazz).type)
    }

    fun <T> getList(json: String?): ArrayList<T> {
        return try {
            if (json.isNullOrBlank().not()) {
                Gson().fromJson<List<T>>(json, object : TypeToken<List<T>>(){}.type) as ArrayList<T>
            } else arrayListOf()
        } catch (e: JsonSyntaxException) {
            Timber.e(e)
            arrayListOf()
        }
    }

    /**
     * using with primitive data type(Long,Integer,String,etc...)
     * [WARNING] NOT USING WITH MODEL CLASS
     */
    inline fun <reified T> fromJsonSafe(json: String?): T? {
        if (json == null) return null
        return try {
            gson.fromJson<T>(json, object : TypeToken<T>() {}.type)
        } catch (e: Exception) {
            Timber.e(e)
            null
        }
    }

    fun <T> toJson(data: T): String {
        return gson.toJson(data)
    }
}