package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName

data class GoodsTagItem(
//    val createTime: String,
//    val goodsId: String,
//    val goodsTagId: String,
//    val id: String,
//    val name: String,
//    val price: Double,
//    val saasStoreId: String,
//    val showSort: Int,
//    val storeId: String,
//    val updateTime: String,
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("price")
    var price: Double?,

    @Transient
    var isCheck: Boolean? = false,


    /**
     * 规格计算费用类型  套餐那边本地用到
     */
    @Transient
    var type: Int? = 0,
)