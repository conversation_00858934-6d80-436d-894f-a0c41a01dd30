package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.cart.Goods
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.view.text.addTextTag
import androidx.core.graphics.toColorInt

/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class SecondPrevoiusOrderedAdapter(
    val list: ArrayList<Goods>,
    val context: Context,
) : RecyclerView.Adapter<SecondPrevoiusOrderedAdapter.OrderedInfoViewHolder>() {

    inner class OrderedInfoViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun bind(resource: Goods) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvName.text = it.name
                        if (!resource.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                            tvName.addMealSetTag(context)
                        }
                        tvSpecification.text = it.getGoodsTagStr()
                        tvSpecification.isVisible = tvSpecification.text.isNotEmpty()
                        tvQTY.text = it.num.toString()
//                        tvVipPrice.text = "${it.totalVipPrice().priceFormatTwoDigitZero2()}"
//                        tvVipPrice.isVisible = it.isShowVipPrice()

                        val activityLabel = it?.activityLabels?.firstOrNull()
                        if (activityLabel != null) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = activityLabel.color.toColorInt()
                            )
                            tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (it?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.text = context.getString(R.string.temporary)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }

                        layoutPrice.tvWeight.isVisible = false
                        layoutPrice.tvTimePriceSign.isVisible = false
//                        layoutPrice.tvTimePriceSign.text =
//                            context.getString(R.string.time_price_parentheses)

//                        if (resource.isHasProcessed()) {
//                            val isShowVipPrice = resource.isShowVipPrice()
//                            layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
//                            layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
//                                FoundationHelper.useConversionRatio,
//                                resource.totalPrice(),
//                                FoundationHelper.isKrh
//                            )
//                            if (isShowVipPrice) {
//                                layoutPrice.tvVipPrice.text =
//                                    resource.totalVipPrice().priceFormatTwoDigitZero2()
//                            }
//                            if (resource.isTimePriceGood()) {
//                                layoutPrice.tvTimePriceSign.isVisible = true
//
//                            }
//                        } else {
//                            layoutPrice.tvFoodPrice.text = resource.getShowPrice(context)
//                            if (resource?.isTimePriceGood() == true && resource?.isToBeWeighed() == true && resource?.isHasCompletePricing() == true && resource?.isHasCompleteWeight() == false) {
//                                //如果是称重时价菜 且定价未称重
//                                layoutPrice.tvTimePriceSign.isVisible = true
//                            }
//                            layoutPrice.tvVipPrice.isVisible = false
//                        }
                        if (it.isToBeWeighed()) {
                            //如果是称重商品
                            if (resource.isHasCompleteWeight()) {
                                layoutPrice.tvWeight.isVisible = true
                                layoutPrice.tvWeight.text = "(${it.getWeightStr()})"
                                if (it.isMealSet()) {
                                    layoutPrice.tvWeight.isVisible = false
                                }
                            } else {
                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
                                layoutPrice.tvVipPrice.isVisible = false
                            }
                        }

                        if (it.isTimePriceGood()) {
                            //如果是时价菜
                            if (!resource.isHasCompletePricing()) {
                                layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
                            } else {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }

                        if (resource.isHasProcessed()) {
                            val isShowVipPrice = resource.isShowVipPrice()
                            layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
                            layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                                FoundationHelper.useConversionRatio,
                                resource.totalPrice(),
                                FoundationHelper.isKrh
                            )
                            if (isShowVipPrice) {
                                layoutPrice.tvVipPrice.text =
                                    resource.totalVipPrice().priceFormatTwoDigitZero2()
                            }
                            if (resource.isTimePriceGood()) {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }

                        tvRemark.isVisible = resource.getFinalNote().isNotEmpty()
                        tvRemark.text =
                            "${context.getString(R.string.remark)} : ${resource.getFinalNote()}"
                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedInfoViewHolder {
        val itemView =
            SelectedMenuItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return OrderedInfoViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OrderedInfoViewHolder, position: Int) {
        if (position < list.size)
            holder.bind(list[position])
    }


    fun replaceData(newData: ArrayList<Goods>?) {
        if (newData != null) {
            this.list.clear()
            this.list.addAll(newData)
            notifyDataSetChanged()
        }
    }


}