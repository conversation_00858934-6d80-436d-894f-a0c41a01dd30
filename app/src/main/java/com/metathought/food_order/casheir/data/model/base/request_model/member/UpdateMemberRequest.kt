package com.metathought.food_order.casheir.data.model.base.request_model.member

import com.google.gson.annotations.SerializedName


data class UpdateMemberRequest(
    @SerializedName("consumerId")
    var consumerId: String? = null,
    @SerializedName("type")
    var type: Int? = null,  //类型:1-修改昵称，2-修改账号
    @SerializedName("nickname")
    var nickname: String? = null,
    @SerializedName("phone")
    var phone: String? = null,
    @SerializedName("otp")
    var otp: String? = null,
    @SerializedName("memberNumber")
    var memberNumber: String? = null,
)