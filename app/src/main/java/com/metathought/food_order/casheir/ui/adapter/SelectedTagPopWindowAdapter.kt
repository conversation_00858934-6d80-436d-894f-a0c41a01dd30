package com.metathought.food_order.casheir.ui.adapter

import android.content.Context

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetChooseItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.databinding.SelectedTagPopWindowItemBinding
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils


class SelectedTagPopWindowAdapter(
    val mealSetGoods: MealSetGoods,
    val context: Context,
    val isSecondView: Boolean? = false,
    val onUpdateCallBack: (() -> Unit)? = null
) : RecyclerView.Adapter<SelectedTagPopWindowAdapter.MealSetViewHolder>() {


    inner class MealSetViewHolder(val binding: SelectedTagPopWindowItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: MealSetChooseItem?, position: Int) {
            resource?.let { it1 ->
                binding.apply {
                    tvTag.text = context.getString(
                        R.string.mealset_good_specifications,
                        "${position + 1}",
                        "${resource.getGoodsTagStr()}"
                    )
                    tvCount.text = "x${resource.selectNum}"

                    ivDelete.setOnClickListener {
                        SingleClickUtils.isFastDoubleClick(700) {
                            mealSetGoods.selectItems.removeAt(position)
                            notifyDataSetChanged()
                            onUpdateCallBack?.invoke()
                        }
                    }
                    ivDelete.isInvisible = isSecondView == true
                    (llRoot.layoutParams as RecyclerView.LayoutParams).bottomMargin =
                        if (position != mealSetGoods.selectItems.size - 1) DisplayUtils.dp2px(
                            context,
                            10f
                        ) else 0
                }
            }
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MealSetViewHolder {
        return MealSetViewHolder(
            SelectedTagPopWindowItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        return mealSetGoods.selectItems.count()
    }

    override fun onBindViewHolder(holder: MealSetViewHolder, position: Int) {
        holder.bind(mealSetGoods.selectItems[position], position)
    }


}