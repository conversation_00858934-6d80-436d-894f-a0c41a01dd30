package com.metathought.food_order.casheir.data.model.base.response_model.order


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class PaymentResponse(
    //payType=1
    @SerializedName("orderNo")
    var orderNo: String?,
    //payType=1
    @SerializedName("qrcode")
    val qrcode: String? = null,

//    @Transient
    @SerializedName("payType")
    var payType: Int? = null,

    //后付款的堂食接口返回 Return of the cartCreatOrder api for post-payment
    //Payment status 1.未支付，2.已支付，3部分退款，4全部退款，5取消订单 6. 待确认  7.已预定
    //Unpaid, 2. Paid, 3 Partial refund, 4 Full refund, 5 Cancellation of order 6. To be confirmed 7. Booked
    @SerializedName("payStatus")
    val payStatus: Int? = null,

) : Parcelable {

    @IgnoredOnParcel
    private var expiredTimestamp: Long? = null
    fun getExpiredTimestamp(): Long? {
        if (expiredTimestamp == null) {
            expiredTimestamp = System.currentTimeMillis() + (300 * 1000)
        }
        return expiredTimestamp
    }
}