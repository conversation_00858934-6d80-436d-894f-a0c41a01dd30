package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.databinding.ItemCouponGoodBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description
 */
class CouponGoodListAdapter(
    val list: List<UsageGoods>,
    val isDialog: Boolean? = false
) : RecyclerView.Adapter<CouponGoodListAdapter.CouponGiftGoodViewHolder>() {


    inner class CouponGiftGoodViewHolder(val binding: ItemCouponGoodBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: UsageGoods) {
            binding.apply {
                Glide.with(itemView.context).load(resource.picUrl)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .apply(
                        RequestOptions()
                            .transform(RoundedCorners(10))
                    )
                    .placeholder(R.drawable.icon_menu_default2)
                    .error(R.drawable.icon_menu_default2)

                    .into(ivGood)
                tvGoodName.text = resource.name
                tvPrice.text = resource.getPrice()
                if(resource.isTimePriceGood()){
                    tvPrice.text = itemView.context.getString(R.string.time_price)
                }
                tvNum.text = "x1"
                if (isDialog == true) {
                    tvGoodName.textSize = 14f
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CouponGiftGoodViewHolder(
        ItemCouponGoodBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: CouponGoodListAdapter.CouponGiftGoodViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}