package com.metathought.food_order.casheir.helper

import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.UsbPrinterDevice
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.listener.SettableFuture
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import net.posprinter.IDeviceConnection
import net.posprinter.POSConnect
import net.posprinter.POSPrinter
import net.posprinter.TSPLPrinter
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.Date


/**
 *<AUTHOR>
 *@time  2024/7/22
 *@desc
 **/


//object PrinterWifiDeviceHelper {
//    /**
//    //     * wifi 打印机列表
//    //     */
////    var wifiPrinterInfoList = mutableListOf<PrinterConfigInfo>()
//
//    //wifi 打印机
//    private var wifiConnectionsMap: MutableMap<String, IDeviceConnection> = mutableMapOf()
//    private var wifiConnectionsStateMap: MutableMap<String, Boolean> = mutableMapOf()
//
//
//    fun connectWifiPrinter(ipAddress: String?) {
//
//        if (ipAddress == null) {
//            return
//        }
//
//        if (wifiConnectionsMap.contains(ipAddress)) {
//            //如果是连过 ，判断一下是否正在连接
//            wifiConnectionsMap[ipAddress]?.isConnect(byteArrayOf(0)) { status ->
//                Timber.e("$ipAddress 存在 然后 status $status")
//                if (status != 1) {
//                    PrinterDeviceHelper.closeWifiConnection(ipAddress)
//                    connectWifiPrinterByIp(ipAddress)
//                }
//            }
//        } else {
//            //如果没连过
//            connectWifiPrinterByIp(ipAddress)
//        }
//    }
//
//    fun connectWifiPrinterByIp(ipAddress: String?) {
//        wifiConnectionsMap[ipAddress!!] =
//            POSConnect.createDevice(POSConnect.DEVICE_TYPE_ETHERNET)
//        wifiConnectionsMap[ipAddress]?.connect(ipAddress) { code: Int, msg: String ->
//            Timber.e("code111:${code}   msg ->${msg}  ip->${ipAddress}")
//            Timber.e("wifi hashcode ${wifiConnectionsMap[ipAddress]?.hashCode()}")
//            connectListener(ipAddress, code)
//        }
//    }
//
//
//    private fun connectListener(ipAddress: String?, code: Int) {
//        when (code) {
//            POSConnect.CONNECT_SUCCESS -> {
//                Timber.e("链接上了")
//
//                setWifiConnectState(ipAddress, true)
//                EventBus.getDefault()
//                    .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
//
//                autoPrintTicket()
//            }
//
//            POSConnect.CONNECT_INTERRUPT -> {
//                Timber.e("链接中断")
//                /**
//                 * 这里不做处理了 轮询查状态失败那边做重连
//                 */
//                setWifiConnectState(ipAddress, false)
////                closeWifiConnection(ipAddress)
////                connectWifiPrinter(ipAddress)
//            }
//
//            POSConnect.CONNECT_FAIL -> {
//                Timber.e("链接失败")
//                /**
//                 * 这里不做处理了 轮询查状态失败那边做重连
//                 */
////                closeWifiConnection(ipAddress)
//                setWifiConnectState(ipAddress, false)
//
//            }
//
//        }
//        EventBus.getDefault()
//            .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
//    }
//
//    /**
//     * 重连上以后  自动打印未打印小票
//     */
//    private fun autoPrintTicket() {
//
//        Timber.e("过滤前 ${PrinterDeviceHelper.noPrintList.size}")
//        val currentTimeStamp = Date().time
//        val list = PrinterDeviceHelper.noPrintList.map {
//            it.copy()
//        }.filter {
//            Timber.e(" it.timeStamp:${it.timeStamp}    currentTimeStamp:${currentTimeStamp}   ${currentTimeStamp - it.timeStamp}")
//            /**
//             * 只保留触发打印到当前时间30分钟内的
//             */
//            currentTimeStamp - it.timeStamp < 30 * 60 * 1000
//        }
//        PrinterDeviceHelper.noPrintList.clear()
//        Timber.e("过滤后  ${list.size}")
//        list.forEach {
//            Printer.printTicket(
//                MyApplication.myAppInstance,
//                it.printTemplateResponseItem,
//                it.currentOrderedInfo,
//                it.isPrinterAgain,
//                paymentQrCode = it.paymentQrCode,
//                printerConfigInfo = it.printerConfigInfo,
//                timeStamp = it.timeStamp,
//                isTaxAdministrationTicket = it.isTaxAdministrationTicket
//            )
//        }
//    }
//
//    fun setWifiConnectState(ipAddress: String?, isConnect: Boolean) {
//        if (ipAddress != null) {
//            wifiConnectionsStateMap[ipAddress] = isConnect
//        }
//    }
//
////    fun getWifiConnectState(printerConfigInfo: PrinterConfigInfo?): Boolean {
////        if (printerConfigInfo?.ipAddress != null) {
////            if (wifiConnectionsStateMap.contains(printerConfigInfo.ipAddress)) {
////                return wifiConnectionsStateMap[printerConfigInfo.ipAddress!!]!!
////            }
////            return false
////        }
////        return false
////    }
//
//    fun getWifiConnection(ipAddress: String?): IDeviceConnection? {
//        if (wifiConnectionsMap.contains(ipAddress)) {
//            return wifiConnectionsMap[ipAddress]
//        }
//        Timber.e("不存在")
//        return null
//    }
//
//    fun closeWifiConnection(ipAddress: String?, isReConnection: Boolean? = false) {
//        try {
//            val connect = getWifiConnection(ipAddress)
//            Timber.e("关闭连接wifi连接 ${connect.hashCode()} $ipAddress")
//            connect?.close()
//            if (isReConnection == false) {
//                //做重连的时候删除
//                wifiConnectionsMap.remove(ipAddress)
//            }
//            PrinterDeviceHelper.setWifiConnectState(ipAddress, false)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//}