package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.ServiceFeeDetailItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import androidx.core.graphics.toColorInt

/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class ServiceFeeDetailItemAdapter(
    val list: ArrayList<Goods?>,
    val orderedInfo: OrderedInfoResponse? = null
) : RecyclerView.Adapter<ServiceFeeDetailItemAdapter.OrderedInfoViewHolder>() {

    inner class OrderedInfoViewHolder(val binding: ServiceFeeDetailItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        @SuppressLint("SetTextI18n")
        fun bind(resource: Goods) {
            itemView.context.run {
                resource.let { data ->
                    binding.apply {

                        val activityLabel = data?.activityLabels?.firstOrNull()
                        if (activityLabel != null) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = activityLabel.color.toColorInt()
                            )
                            tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (data.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }
                        tvRemark.text = "${getString(R.string.remark)}:${data.goodNote}"
                        tvRemark.isVisible = data.goodNote?.isNotEmpty() == true
                        tvFoodName.text = data.name
                        tvFoodSubName.isVisible = data.feedStr?.isNotEmpty() == true
                        tvFoodSubName.text = data.feedStr
                        tvFoodCount.text = "${data.totalCount}"
                        tvServiceFeePer.text =
                            "${data.getServiceChargePercentage()}%"

                        if (orderedInfo == null || orderedInfo?.isAfterPayStatus() == false) {
                            if (!data.isHasProcessed()) {
                                tvServiceFee.text = getString(R.string.to_be_confirmed)
                                tvVipServiceFee.isVisible = false
                            } else {
                                if (data.serviceChargeWhitelisting == true) {
                                    tvServiceFee.text = "$0.00"
                                } else {
                                    tvServiceFee.text =
                                        "${data.totalDiscountServiceCharge?.priceFormatTwoDigitZero2()}"

                                    tvVipServiceFee.text =
                                        "${data.totalVipServiceCharge?.priceFormatTwoDigitZero2()}"

                                    tvVipServiceFee.isVisible = data.isShowVipPrice()
                                    if (orderedInfo?.isOrderExpire() == true) {
                                        tvVipServiceFee.isVisible = false
                                    }
                                }
                            }
                        } else {
                            tvVipServiceFee.isVisible = false
                            var servicePrice = data.totalServiceCharge
                            if (orderedInfo?.isUseDiscount == true) {
                                servicePrice = data.totalVipServiceCharge
                            } else if (data.isHasDiscountPrice()) {
                                servicePrice = data.totalDiscountServiceCharge
                            }
                            tvServiceFee.text =
                                "${servicePrice?.priceFormatTwoDigitZero2()}"
                        }

//                        if (orderedInfo != null) {
//                            tvServiceFeePer.text =
//                                "${orderedInfo.getServicePercentage()}%"
//                        }
                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedInfoViewHolder {
        val itemView =
            ServiceFeeDetailItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedInfoViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OrderedInfoViewHolder, position: Int) {
        holder.bind(list[position]!!)
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(newData: ArrayList<Goods>?) {
        if (newData != null) {
            this.list.clear()
            this.list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}