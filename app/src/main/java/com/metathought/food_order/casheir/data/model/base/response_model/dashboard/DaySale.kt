package com.metathought.food_order.casheir.data.model.base.response_model.dashboard

import com.google.gson.annotations.SerializedName

data class DaySale(
    @SerializedName("orderDate") val orderDate: String= "",
    @SerializedName("turnover") val turnover: Int = 0,
    @SerializedName("basketPromo") val basketPromo: Int = 0,
    @SerializedName("cancelledAmount") val cancelledAmount: Int = 0,
    @SerializedName("failedAmount") val failedAmount: Int = 0,
    @SerializedName("orderNum") val orderNum: Int = 0,
    @SerializedName("promoOrderNum") val promoOrderNum: Int = 0,
    @SerializedName("cancelledOrderNum") val cancelledOrderNum: Int = 0,
    @SerializedName("failedOrderNum") val failedOrderNum: Int = 0
)
