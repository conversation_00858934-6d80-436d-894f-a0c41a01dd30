package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName


/**
 *<AUTHOR>
 *@time  2024/11/18
 *@desc  优惠活动 满n减N
 **/

data class ActivityLabel(
    /**
     * id
     */
    @SerializedName("id")
    val id: String,

    /**
     * 类型    1-买一送一  -2第二件半价 -3第N件优惠
     */
    @SerializedName("type")
    val type: Int,


    /**
     * 标签名称
     */
    @SerializedName("name")
    val name: String,

    /**
     * 标签颜色
     */
    @SerializedName("color")
    val color: String,

    /**
     * 是否连续优惠 0否 1是
     */
    @SerializedName("continuous")
    val continuous: Int,


    /**
     *    type = 3 的时候才返
     */
    @SerializedName("typeRule")
    val typeRule: TypeRule?
) {


}


data class TypeRule(
    /**
     * 第几件
     */
    @SerializedName("itemSort")
    val itemSort: Int,
    /**
     * 减免折扣
     */
    @SerializedName("discount")
    val discount: Int
)