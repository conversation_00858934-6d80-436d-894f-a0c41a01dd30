package com.metathought.food_order.casheir.filter

import android.text.InputFilter
import android.text.Spanned
import android.text.TextUtils
import android.util.Log
import timber.log.Timber
import java.util.regex.Pattern


/**
 *<AUTHOR>
 *@time  2023/10/24
 *@desc
 **/

/**
 * Created by fby on 2017/9/7.
 * 过滤用户输入只能为金额格式 限制数值支持小于1
 */
class CashierInputFilterV2(
    var isKhr: Boolean = false,
    private var maxValue: Double?,
    private var isCanEqual: Boolean? = false
) :
    InputFilter {

    var mPattern //输入的最大金额
            : Pattern = Pattern.compile("([0-9]|\\.)*")
    var mPatternInt //输入的最大金额
            : Pattern = Pattern.compile("([0-9])*")

//    fun setIntType(isInt: Boolean) {
//        this.isInt = isInt
//    }


    /**
     * @param source    新输入的字符串
     * @param start     新输入的字符串起始下标，一般为0
     * @param end       新输入的字符串终点下标，一般为source长度-1
     * @param dest      输入之前文本框内容
     * @param dstart    原内容起始坐标，一般为0
     * @param dend      原内容终点坐标，一般为dest长度-1
     * @return          输入内容
     */
    override fun filter(
        source: CharSequence,
        start: Int,
        end: Int,
        dest: Spanned,
        dstart: Int,
        dend: Int
    ): CharSequence {
        Log.e("CashierInputFilter", "source:${source}")
        val sourceText = source.toString()
        val destText = dest.toString() //验证删除等按键
        if (TextUtils.isEmpty(sourceText)) {
            return ""
        }
        //用整数 还是 用小数
        var tmpPattern = if (isKhr) {
            mPatternInt
        } else {
            mPattern
        }

//        //防止各种方式输入（包括复制黏贴， 部分机型还能从一个输入框拖到另外一个输入框），只能手动一个一个输入，房子出问题
//        if (sourceText.length > 1) {
//            try {
//                val source = sourceText.toBigDecimalOrNull()
//                if (source != null) {
//                    return "${source}"
//                } else {
//                    return ""
//                }
//            } catch (e: Exception) {
//                return ""
//            }
//        }

        val matcher = tmpPattern.matcher(source)
        //已经输入小数点的情况下，只能输入数字
        if (destText.contains(POINTER)) {
            if (!matcher.matches()) {
                return ""
            } else {
                if (POINTER == source.toString()) {  //只能输入一个小数点
                    return ""
                }
            } //验证小数点精度，保证小数点后只能输入两位
            val index = destText.indexOf(POINTER)
            val length = dend - index
            if (length > POINTER_LENGTH) {
                return dest.subSequence(dstart, dend)
            }
        } else {
            /**
             * 没有输入小数点的情况下，只能输入小数点和数字
             * 1. 首位不能输入小数点
             * 2. 如果首位输入0，则接下来只能输入小数点了
             */
            if (!matcher.matches()) {
                return ""
            } else {
                if (POINTER == source.toString() && TextUtils.isEmpty(destText)) {  //首位不能输入小数点
                    return "0."
                } else if (POINTER != source.toString() && ZERO == destText) { //如果首位输入0，接下来只能输入小数点
//                    if (ZERO == source.toString()) {
//                        //如果首位输入0 接下去还输入0 就不变
//                        return ""
//                    }
                    if (POINTER != source.toString()) {
                        //如果首位输入0 接下去只能输入小数点，输入其他的就不变
                        return ""
                    }
//                    Log.e("zzzzzz", "首位为0  走这里  ${source}")
                    return source
                }
            }
        } //验证输入金额的大小
//        val sumText = (destText + sourceText).toDouble()
        val sumText = (dest.subSequence(0, dstart).toString() + source + dest.subSequence(
            dend,
            dest.length
        )).toDouble()

        if (isCanEqual == true) {
            return if (sumText <= (maxValue ?: Double.MAX_VALUE)) {
                dest.subSequence(dstart, dend).toString() + sourceText
            } else
                dest.subSequence(dstart, dend)
        } else {
            return if (sumText < (maxValue ?: Double.MAX_VALUE)) {
                dest.subSequence(dstart, dend).toString() + sourceText
            } else
                dest.subSequence(dstart, dend)
        }

    }

    companion object {
        //        private const val FLOAT_MAX_VALUE = 999999999.99
//        private const val INT_MAX_VALUE = 999999999
        private const val MAX_VALUE = 1000000

        //小数点后的位数
        private const val POINTER_LENGTH = 2
        private const val POINTER = "."
        private const val ZERO = "0"
    }
}