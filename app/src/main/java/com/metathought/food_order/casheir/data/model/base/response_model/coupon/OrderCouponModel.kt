package com.metathought.food_order.casheir.data.model.base.response_model.coupon

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.constant.CouponTypeEnum


/**
 *<AUTHOR>
 *@time  2024/8/27
 *@desc
 **/

data class OrderCouponModel(
    /**
     * id
     */
    @SerializedName("id")
    val id: Long? = null,
    /**
     * 名称
     */
    @SerializedName("name")
    val name: String? = null,
    /**
     * 编码
     */
    @SerializedName("code")
    val code: String? = null,
    /**
     * 使用(线上/上下支付)该优惠券是否有效(未完成订单会返回该值)
     */
    @SerializedName("isValid")
    val isValid: Boolean? = null,
    /**
     * 使用(余额支付)该优惠券是否有效(未完成订单会返回该值)
     */
    @SerializedName("isVipValid")
    val isVipValid: Boolean? = null,
    /**
     * 优惠金额(未完成订单会返回该值)
     */
    @SerializedName("couponPrice")
    val couponPrice: Long? = null,
    /**
     * 余额支付下的优惠金额(未完成订单会返回该值)
     */
    @SerializedName("vipCouponPrice")
    val vipCouponPrice: Long? = null,
    /**
     * 赠品(未完成订单会返回该值)
     */
    @SerializedName("giftGoods")
    val giftGoods: List<UsageGoods>? = null,

    /**
     * 优惠券类型
     */
    @SerializedName("category")
    val category: String? = null,


    /**
     * 优惠券sdk
     */
    @SerializedName("templateSDK")
    val templateSDK: CouponTemplateSDK? = null,

    /**
     * 优惠券sdk ID
     */
    @SerializedName("templateId")
    val templateId: String? = null,


    ) {


    //是否折扣
    fun isZkCoupon(): Boolean {
        return category in listOf(
            CouponTypeEnum.THRESHOLD_ZK.type,
            CouponTypeEnum.NOTHRESHOLD_ZK.type
        )
    }

    fun isZsCoupon(): Boolean {
        val category = category ?: templateSDK?.category
//        if (category == null) {
//            return !giftGoods.isNullOrEmpty()
//        }
        return category in listOf(
            CouponTypeEnum.THRESHOLD_ZS.type,
            CouponTypeEnum.NOTHRESHOLD_ZS.type
        )
    }

    fun toCouponModel(): CouponModel {
        return CouponModel(
            id = id,
            couponCode = code,
            isValid = isValid,
            isVipValid = isVipValid,
            couponPrice = couponPrice,
            vipCouponPrice = vipCouponPrice,
            giftGoods = giftGoods,
            category = category,
            templateSDK = templateSDK,
            templateId = templateId,
        )
    }
}