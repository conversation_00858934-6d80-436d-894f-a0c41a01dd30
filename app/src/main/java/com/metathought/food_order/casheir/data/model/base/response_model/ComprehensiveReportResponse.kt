package com.metathought.food_order.casheir.data.model.base.response_model

import com.google.gson.annotations.SerializedName

//data class ComprehensiveReportResponse(
//    @SerializedName("code")
//    val code: Int,
//    @SerializedName("msg")
//    val msg: String?,
//    @SerializedName("data")
//    val data: ComprehensiveReportData,
//    @SerializedName("ok")
//    val ok: Boolean
//)

data class ComprehensiveReportData(
    @SerializedName("masterReportDataList")
    val masterReportDataList: List<MutableMap<String, Any?>>,
    @SerializedName("headerList")
    var headerList: List<Header>,
    @SerializedName("deliveryPlatformList")
    var deliveryPlatformList: List<String>,
)

//data class MasterReportData(
//    @SerializedName("date")
//    val date: String? = null,
//    @SerializedName("storeId")
//    val storeId: String,
//    @SerializedName("storeName")
//    val storeName: String,
//    @SerializedName("totalAmount")
//    val totalAmount: Double,
//    @SerializedName("dineInAmount")
//    val dineInAmount: Double,
//    @SerializedName("takeawayAmount")
//    val takeawayAmount: Double,
//    @SerializedName("reservationTotalAmount")
//    val reservationTotalAmount: Double,
//    @SerializedName("deliveryAmount")
//    val deliveryAmount: Double,
//    @SerializedName("itemNetSalesWithoutTax")
//    val itemNetSalesWithoutTax: Double,
//    @SerializedName("dineInNetSales")
//    val dineInNetSales: Double,
//    @SerializedName("takeawayNetSales")
//    val takeawayNetSales: Double,
//    @SerializedName("reservationTotalAmountExcludesVat")
//    val reservationTotalAmountExcludesVat: Double,
//    @SerializedName("deliveryNetSales")
//    val deliveryNetSales: Double,
//    @SerializedName("itemNetSalesWithTax")
//    val itemNetSalesWithTax: Double,
//    @SerializedName("dineInTotalAmountIncludesVat")
//    val dineInTotalAmountIncludesVat: Double,
//    @SerializedName("takeawayTotalAmountIncludesVat")
//    val takeawayTotalAmountIncludesVat: Double,
//    @SerializedName("reservationTotalAmountIncludesVat")
//    val reservationTotalAmountIncludesVat: Double,
//    @SerializedName("deliveryTotalAmountIncludesVat")
//    val deliveryTotalAmountIncludesVat: Double,
//    @SerializedName("totalDiscounts")
//    val totalDiscounts: Double,
//    @SerializedName("itemDiscounts")
//    val itemDiscounts: Double,
//    @SerializedName("orderDiscounts")
//    val orderDiscounts: Double,
//    @SerializedName("totalAmountOfCoupons")
//    val totalAmountOfCoupons: Double,
//    @SerializedName("marketingManagement")
//    val marketingManagement: Double,
//    @SerializedName("totalCommission")
//    val totalCommission: Double,
//    @SerializedName("totalTaxes")
//    val totalTaxes: Double,
//    @SerializedName("serviceFee")
//    val serviceFee: Double,
//    @SerializedName("packagingFee")
//    val packagingFee: Double,
//    @SerializedName("totalVoids")
//    val totalVoids: Double,
//    @SerializedName("totalPax")
//    val totalPax: Double,
//    @SerializedName("orderCount")
//    val orderCount: Double,
//    @SerializedName("avgOrderAmount")
//    val avgOrderAmount: Double,
//    @SerializedName("avgCustomerAmount")
//    val avgCustomerAmount: Double
//)

data class Header(
    @SerializedName("key")
    val key: String,
    @SerializedName("value")
    val value: String,
    @SerializedName("isHighlight")
    val isHighlight: Boolean,
    @SerializedName("describe")
    val describe: String? = null,
)
