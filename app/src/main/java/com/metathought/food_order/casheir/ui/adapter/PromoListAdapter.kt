package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.TopSaleRecord
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StorePromoDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesOrdersDetailVo
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.databinding.ItemPromoListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleReportListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleTopListBinding

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description 销售报表预览adapter
 */
class PromoListAdapter(
    val ctx:Context,
    val list: ArrayList<StorePromoDetail>,
) : RecyclerView.Adapter<PromoListAdapter.ProduceReportItemViewHolder>() {


    inner class ProduceReportItemViewHolder(val binding: ItemPromoListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: StorePromoDetail, position: Int) {
            binding.apply {
                tvMoney.text = "${resource.basketPromo ?: ""}"
                tvOrderId.text="${resource.grabOrderNum ?: ""}"
              //  vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemPromoListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PromoListAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<StorePromoDetail>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}