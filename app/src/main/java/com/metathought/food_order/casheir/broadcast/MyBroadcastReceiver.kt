package com.metathought.food_order.casheir.broadcast

import android.R
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/7/26
 *@desc
 **/

//class MyBroadcastReceiver : BroadcastReceiver() {
//    override fun onReceive(context: Context?, intent: Intent) {
//        val action = intent.action
//        if (action == Intent.ACTION_MEDIA_MOUNTED) {
//            // USB设备已插入
//            Timber.e("USB设备已成功连接")
////            val builder: NotificationCompat.Builder = Builder(context, "channel_id")
////                .setSmallIcon(R.drawable.ic_usb)
////                .setContentTitle("USB已插入")
////                .setContentText("USB设备已成功连接")
////                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
////            val notificationManager = NotificationManagerCompat.from(
////                context!!
////            )
////            notificationManager.notify(1, builder.build())
//        } else if (action == Intent.ACTION_MEDIA_EJECT) {
//            // USB设备已拔出
////            val builder: NotificationCompat.Builder = Builder(context, "channel_id")
////                .setSmallIcon(R.drawable.ic_usb)
////                .setContentTitle("USB已拔出")
////                .setContentText("USB设备已成功断开连接")
////                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
////            val notificationManager = NotificationManagerCompat.from(
////                context!!
////            )
////            notificationManager.notify(2, builder.build())
//            Timber.e("USB设备已成功断开连接")
//        } else if (action == Intent.ACTION_MEDIA_UNMOUNTED) {
////            // USB设备已卸载
////            val builder: NotificationCompat.Builder = Builder(context, "channel_id")
////                .setSmallIcon(R.drawable.ic_usb)
////                .setContentTitle("USB已卸载")
////                .setContentText("USB设备已被卸载")
////                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
////            val notificationManager = NotificationManagerCompat.from(
////                context!!
////            )
////            notificationManager.notify(3, builder.build())
//            Timber.e("USB设备已被卸载")
//        }
//    }
//}
