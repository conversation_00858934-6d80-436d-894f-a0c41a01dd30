package com.metathought.food_order.casheir.data.model.base.request_model.member

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/8/26
 *@desc
 **/

data class TopUpCanUseCouponRequest(
    @SerializedName("addNum")
    val addNum: BigDecimal? = null,
    @SerializedName("id")
    val id: String? = null,
)