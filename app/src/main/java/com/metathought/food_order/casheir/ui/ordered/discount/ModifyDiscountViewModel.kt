package com.metathought.food_order.casheir.ui.ordered.discount

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.WholeDiscountType
import com.metathought.food_order.casheir.constant.WholeReduceType
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.OrderDiscountInfoListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ReduceDiscountDetailModel
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.ShoppingCartHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.math.BigDecimal
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/5/913:44
 * @description
 */
@HiltViewModel

class ModifyDiscountViewModel @Inject
constructor(private val repository: Repository) : ViewModel() {


    val uiModeState get() = _uiModeState
    private val _uiModeState = MutableLiveData<UIModel>()

    /**
     * 减免列表
     */
    var reduceList: MutableList<DiscountReduceInfo>? = null

    /**
     * 折扣列表
     */
    var discountList: MutableList<DiscountReduceInfo>? = null

    /**
     * 本地减免的id
     */
    var localReduce: DiscountReduceInfo? = null

    /**
     * 本地打折的id
     */
    var localDiscount: DiscountReduceInfo? = null


    fun getDetail(orderNo: String?) {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val result = repository.getReduceDiscountDetail(orderNo)
                if (result is ApiResponse.Success) {
                    if (result.data.type == WholeDiscountType.FIXED_AMOUNT.id) {
                        localReduce = result.data.discountReduceInfo
                    } else if (result.data.type == WholeDiscountType.PERCENTAGE.id) {
                        localDiscount = result.data.discountReduceInfo
                    }
                }
                emitUIState(result = result)
            } catch (e: Exception) {
                emitUIState(result = ApiResponse.Error(e.message))
            }
        }
    }

    fun getOrderDiscountInfoList(
        orderNo: String? = null,
        cartGoods: List<GoodsRequest>? = null,
        applyRange: Int? = 0,
        dingStyle: Int? = null
    ) {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val request = OrderDiscountInfoListRequest()
                request.orderNo = orderNo
                request.applyRange = applyRange
                request.diningStyle = dingStyle
                if (cartGoods != null) {
                    request.cartGoodsList = cartGoods.map {
                        ShoppingCartHelper.goodsRequestToGoodsBo(it)
                    }
                }
                val result = repository.getOrderDiscountInfoList(request)
                if (result is ApiResponse.Success) {
                    reduceList = result.data.filter { it.type == WholeDiscountType.FIXED_AMOUNT.id }
                        .toMutableList()
                    discountList = result.data.filter { it.type == WholeDiscountType.PERCENTAGE.id }
                        .toMutableList()
                }
                emitUIState(discountList = result)
            } catch (e: Exception) {
                emitUIState(discountList = ApiResponse.Error(e.message))
            }
        }
    }

    fun inertDataToList(data: DiscountReduceInfo) {
        if (data.isPercentDiscount()) {
            discountList?.add(0, data)
        } else if (data.isFixedAmount()) {
            reduceList?.add(0, data)
        }
    }


    fun setDiscountPrice(
        orderNo: String? = null,
        reduceDollar: BigDecimal? = null,
        reduceVipDollar: BigDecimal? = null,
        reduceKhr: BigDecimal? = null,
        reduceVipKhr: BigDecimal? = null,
        reduceRate: Double? = null,
        reduceType: Int? = null,
        discountType: Int? = null,
        couponId: Long? = null,
        reduceReason: String? = null,
        discountReduceActivityId: String? = null
    ) {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val result = repository.getReduceDiscountConfirm(
                    orderNo = orderNo,
                    reduceDollar = reduceDollar,
                    reduceVipDollar = reduceVipDollar,
                    reduceKhr = reduceKhr,
                    reduceVipKhr = reduceVipKhr,
                    reduceRate = reduceRate,
                    discountType = discountType,
                    reduceType = reduceType,
                    couponId = couponId,
                    reduceReason = reduceReason,
                    discountReduceActivityId = discountReduceActivityId
                )
                if (result is ApiResponse.Success) {
                    emitUIState(confirm = result)
                } else if (result is ApiResponse.Error) {
                    Timber.e("result:${result.status}  ${result.message}  ${result.errorCode}")
                    emitUIState(confirm = result)
                }

            } catch (e: Exception) {
                emitUIState(result = ApiResponse.Error(e.message))
            }
        }
    }

    private suspend fun emitUIState(
        discountList: ApiResponse<List<DiscountReduceInfo>>? = null,
        result: ApiResponse<ReduceDiscountDetailModel>? = null,
        confirm: ApiResponse<BaseBooleanResponse>? = null

    ) {
        withContext(Dispatchers.Main) {
            _uiModeState.value =
                UIModel(result = result, confirm = confirm, discountList = discountList)
        }
    }

    data class UIModel(
        val discountList: ApiResponse<List<DiscountReduceInfo>>?,
        val result: ApiResponse<ReduceDiscountDetailModel>?,
        val confirm: ApiResponse<BaseBooleanResponse>?,
    )

}