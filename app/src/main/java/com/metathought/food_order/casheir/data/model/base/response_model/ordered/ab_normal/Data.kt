package com.metathought.food_order.casheir.data.model.base.response_model.ordered.ab_normal


import com.google.gson.annotations.SerializedName

data class Data(
    @SerializedName("applyStatus")
    val applyStatus: Int?,
    @SerializedName("cashRefundPrice")
    val cashRefundPrice: Int?,
    @SerializedName("changeAmount")
    val changeAmount: Int?,
    @SerializedName("collectCash")
    val collectCash: Int?,
    @SerializedName("consumerAddressId")
    val consumerAddressId: Any?,
    @SerializedName("consumerId")
    val consumerId: String?,
    @SerializedName("consumerName")
    val consumerName: String?,
    @SerializedName("couponPrice")
    val couponPrice: Int?,
    @SerializedName("couponTicketId")
    val couponTicketId: Any?,
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("createdUserId")
    val createdUserId: Any?,
    @SerializedName("customerJson")
    val customerJson: String?,
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("finalPrice")
    val finalPrice: Any?,
    @SerializedName("getOrderNumber")
    val getOrderNumber: Any?,
    @SerializedName("goodsJson")
    val goodsJson: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("isPreOrder")
    val isPreOrder: Boolean?,
    @SerializedName("kioskId")
    val kioskId: Any?,
    @SerializedName("mainStoreId")
    val mainStoreId: Any?,
    @SerializedName("mealCost")
    val mealCost: Int?,
    @SerializedName("newJson")
    val newJson: Boolean?,
    @SerializedName("note")
    val note: String?,
    @SerializedName("offlinePaymentChannelsId")
    val offlinePaymentChannelsId: String?,
    @SerializedName("offlinePaymentChannelsName")
    val offlinePaymentChannelsName: String?,
    @SerializedName("onlineRefundPrice")
    val onlineRefundPrice: Int?,
    @SerializedName("openid")
    val openid: Any?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("outOrderNo")
    val outOrderNo: Any?,
    @SerializedName("outOrderNo2")
    val outOrderNo2: Any?,
    @SerializedName("over")
    val over: Boolean?,
    @SerializedName("payOrderNo")
    val payOrderNo: Any?,
    @SerializedName("payStatus")
    val payStatus: Int?,
    @SerializedName("payTime")
    val payTime: Any?,
    @SerializedName("payType")
    val payType: Any?,
    @SerializedName("payTypeStr")
    val payTypeStr: String?,
    @SerializedName("paymentAdvance")
    val paymentAdvance: Boolean?,
    @SerializedName("peopleNum")
    val peopleNum: Any?,
    @SerializedName("pickupCode")
    val pickupCode: Any?,
    @SerializedName("pickupNo")
    val pickupNo: Any?,
    @SerializedName("planStatus")
    val planStatus: Int?,
    @SerializedName("profitsharingOrderNo")
    val profitsharingOrderNo: Any?,
    @SerializedName("realPrice")
    val realPrice: Int?,
    @SerializedName("refundDateTime")
    val refundDateTime: Any?,
    @SerializedName("refundGoodsInfo")
    val refundGoodsInfo: Any?,
    @SerializedName("refundNote")
    val refundNote: Any?,
    @SerializedName("refundPrice")
    val refundPrice: Int?,
    @SerializedName("reminderNumber")
    val reminderNumber: Int?,
    @SerializedName("saasStoreId")
    val saasStoreId: String?,
    @SerializedName("settlementId")
    val settlementId: Any?,
    @SerializedName("settlementStatus")
    val settlementStatus: Int?,
    @SerializedName("settlementTime")
    val settlementTime: Any?,
    @SerializedName("shareCart")
    val shareCart: Boolean?,
    @SerializedName("sourcePlatform")
    val sourcePlatform: Int?,
    @SerializedName("startTime")
    val startTime: String?,
    @SerializedName("status")
    val status: Int?,
    @SerializedName("statusStr")
    val statusStr: Any?,
    @SerializedName("store")
    val store: Any?,
    @SerializedName("storeId")
    val storeId: String?,
    @SerializedName("tableName")
    val tableName: String?,
    @SerializedName("tableTmpid")
    val tableTmpid: String?,
    @SerializedName("tableType")
    val tableType: Any?,
    @SerializedName("tableUuid")
    val tableUuid: String?,
    @SerializedName("tempTableCodeId")
    val tempTableCodeId: String?,
    @SerializedName("totalPrice")
    val totalPrice: Int?,
    @SerializedName("type")
    val type: Int?,
    @SerializedName("updateTime")
    val updateTime: String?
)