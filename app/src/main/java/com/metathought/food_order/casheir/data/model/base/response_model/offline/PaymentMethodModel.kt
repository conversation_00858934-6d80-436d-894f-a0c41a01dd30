package com.metathought.food_order.casheir.data.model.base.response_model.offline

import android.content.Context
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum

/**
 *
 * 支付方式
 * <AUTHOR>
 * @date 2024/5/2711:25
 * @description
 */
data class PaymentMethodModel(
    //线下支付渠道id
    var offlineChannelId: Int? = null,
    //线下支付渠道名称 | 支付名称
    var channelsName: String? = null,
    //支付方式
    var paymentMethod: Int,
    var isSelectPayment: Boolean = false
) {

    fun getPayMethod(context: Context): String {
        return when (paymentMethod) {
            PayTypeEnum.CREDIT.id -> {
                context.getString(R.string.credit_payment)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                getOfflinePayMethod(context)
            }

            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getString(R.string.online_payment)
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getString(R.string.pay_by_balance)
            }

            PayTypeEnum.PAY_OTHER.id -> {
                context.getString(R.string.pay_by_other)
            }

            else -> {
                context.getString(R.string.all_payment_method)
            }
        }
    }

    /**
     * 获取线下支付方式的描述字符串。
     * 根据 [offlineChannelId] 的值，返回不同格式的线下支付方式描述。
     * 如果 [offlineChannelId] 对应现金支付或应收账款，则使用固定的字符串资源；
     * 否则，使用 [channelsName] 作为支付渠道名称。
     *
     * @param context 用于获取字符串资源的上下文对象。
     * @return 线下支付方式的描述字符串。
     */
    private fun getOfflinePayMethod(context: Context): String {
        // 检查 offlineChannelId 是否为现金支付渠道的 ID
        return if (offlineChannelId == OfflinePaymentChannelEnum.CASH.id) {
            // 若为现金支付渠道，返回格式为 "线下支付 - 现金支付" 的字符串
            "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
        }
        // 检查 offlineChannelId 是否为应收账款支付渠道的 ID
        else if (offlineChannelId == OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id) {
            // 若为应收账款支付渠道，返回格式为 "线下支付 - 应收账款" 的字符串
            "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        }
        // 若以上条件都不满足
        else {
            // 返回格式为 "线下支付 - [渠道名称]" 的字符串，[渠道名称] 由 channelsName 变量提供
            "${context.getString(R.string.offline_payments)} - $channelsName"
        }
    }
}