package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesPayMethodReportDetail
import com.metathought.food_order.casheir.databinding.ItemCouponGoodBinding
import com.metathought.food_order.casheir.databinding.ItemPayChannelReportListBinding
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description
 */
class PayChannelReportListAdapter(
    val list: ArrayList<SalesPayMethodReportDetail>,
) : RecyclerView.Adapter<PayChannelReportListAdapter.ProduceReportItemViewHolder>() {


    inner class ProduceReportItemViewHolder(val binding: ItemPayChannelReportListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: SalesPayMethodReportDetail, position: Int) {
            binding.apply {
                tvChannelName.text = "${resource.payMethod ?: ""}"
                tvOrderNum.text = "${resource.orderNum ?: "0"}"
                tvOrderAmount.text = "${resource.amount ?: "0.00"}"
                itemTotalAmount.text = "${resource.amountRatio ?: "0.00"}"
                vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemPayChannelReportListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PayChannelReportListAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<SalesPayMethodReportDetail>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}