package com.metathought.food_order.casheir.ui.ordered.weight

import android.annotation.SuppressLint
import android.content.Context
import android.hardware.display.DisplayManager
import android.text.Editable
import android.text.TextWatcher
import android.util.DisplayMetrics
import android.view.Display
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.animator.PopupAnimator
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.lxj.xpopup.util.KeyboardUtils
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogEditWeightBinding
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.WeightScaleManager
import com.metathought.food_order.casheir.helper.XpopHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File


data class WeightData(
    var weight: Double? = null,
    var weightUnit: String? = null,
    var singleDiscount: Long? = null,
)

@AndroidEntryPoint
class EditWeightDialog
    (private val act: Context?) : CenterPopupView(act!!) {
    companion object {

        private const val Period = "."

        private const val Zero = "0"

    }

    private var binding: DialogEditWeightBinding? = null
    private var onConfirmClickListener: ((String) -> Unit)? = null

    private var weightData: WeightData? = null

    fun showDialog(
        weightData: WeightData?,
        onConfirmClickListener: ((String) -> Unit)? = null,
    ): EditWeightDialog {
        this.weightData = weightData
        this.onConfirmClickListener = onConfirmClickListener

        XPopup.Builder(act)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }

                override fun onDismiss(popupView: BasePopupView?) {
                    KeyboardUtils.hideSoftInput(binding?.edtWeight)
                    super.onDismiss(popupView)
                }
            })
            .autoOpenSoftInput(false)
            .autoFocusEditText(false)
            .asCustom(this)
            .show()
        // 添加到XpopHelper管理
        XpopHelper.addToMap(this)
        return this
    }


    // 返回自定义弹窗的布局
    override fun getImplLayoutId(): Int {
        return R.layout.dialog_edit_weight
    }

    private lateinit var weightManager: WeightScaleManager
    override fun onCreate() {
        super.onCreate()
        binding = DialogEditWeightBinding.bind(popupImplView)


        initData()
        initListener()
        initObserver()

        // 初始化电子秤管理器
        weightManager = WeightScaleManager.getInstance()
        val isSupportScale = weightManager.isSupportScale()

        // 默认先隐藏开关区域
        binding?.llWeightSeting?.isVisible = false

        binding?.edtWeight?.apply {
            clearFocus()
            isFocusable = false  // 临时设置为不可获取焦点，防止自动弹出键盘
        }

        if (isSupportScale && weightManager.isAllConnectedFail() == null) {
            // 如果支持电子秤，尝试连接并设置回调
            if (weightManager.isConnected()) {
                binding?.llWeightSeting?.isVisible = true
                updateSwitch()
                updateWeightManager()
            } else {
                weightManager.setConnectionCallback(object : WeightScaleManager.ConnectionCallback {
                    override fun onConnected(portName: String) {
                        // 连接成功，显示开关
                        binding?.apply {
                            llWeightSeting.isVisible = true
                            updateSwitch()
                            updateWeightManager()
                            // 连接成功时，输入框不获取焦点
                            edtWeight.clearFocus()
                        }
                        Timber.d("电子秤连接成功: $portName")
                    }

                    override fun onConnectionFailed(error: String) {
                        // 连接失败，保持隐藏状态
                        binding?.apply {
                            llWeightSeting.isVisible = false
                            // 连接失败时，让输入框获取焦点
                            edtWeight.post {
                                edtWeight.requestFocus()
                                KeyboardUtils.showSoftInput(edtWeight)
                            }
                        }
                        Timber.e("电子秤连接失败: $error")
                    }
                })
            }
        } else {
            // 不支持电子秤，直接让输入框获取焦点
            binding?.edtWeight?.post {
                binding?.edtWeight?.requestFocus()
                KeyboardUtils.showSoftInput(binding?.edtWeight)
            }
        }

        Timber.d("isSupportScale:${isSupportScale} isConnected:${weightManager.isConnected()}")
    }

    private fun updateWeightManager() {
        if (isOpenManualInput) {
            weightManager.stopRead()
            KeyboardUtils.showSoftInput(binding?.edtWeight)
        } else {
            KeyboardUtils.hideSoftInput(binding?.edtWeight)
            weightManager.startRead { weight ->
                // 处理重量更新
                binding?.apply {
                    edtWeight.post {
                        edtWeight.setText("$weight")
                        edtWeight.setSelection(edtWeight.length())
                    }
                }
            }
        }
        lifecycleScope.launch {
            PreferenceHelper.setOpenManualInputWeight(isOpenManualInput)
        }
    }

    private fun initObserver() {

    }

    @SuppressLint("SetTextI18n")
    private fun initData() {
        lifecycleScope.launch {
            isOpenManualInput = PreferenceHelper.getOpenManualInputWeight()
        }
        weightData?.let { weightData ->
            binding?.apply {
                tvSinglePrice.text =
                    "${context.getString(R.string.single_price)}:${
                        weightData.singleDiscount?.priceFormatTwoDigitZero2()
                    }/${weightData.weightUnit}"
                tvUnit.text = "${weightData.weightUnit}"
                val weight =
                    (if (weightData.weight?.isInt() == true) weightData.weight!!.toInt() else (weightData.weight
                        ?: 0)).toString()
                if (weight != "0") {
                    edtWeight.setText(weight)
                    edtWeight.setSelection(weight.length)
                } else {
                    btnConfirm.isEnabled = false
                    btnConfirm.alpha = 0.5f
                }
            }
        }

    }

    private var beforeWeight = ""

    private var isOpenManualInput = true
    private fun initListener() {
        binding?.apply {

            btnCancel.setOnClickListener {
                KeyboardUtils.hideSoftInput(binding?.edtWeight)
                dismiss()
            }

            btnConfirm.setOnClickListener {
                if (!checkWeight()) {
                    return@setOnClickListener
                }
                KeyboardUtils.hideSoftInput(binding?.edtWeight)
                onConfirmClickListener?.invoke(edtWeight.text.toString())
                dismiss()
            }

            ivSwitch.setOnClickListener {
                isOpenManualInput = !isOpenManualInput
                updateSwitch()
                updateWeightManager()
            }
            //000.000
            edtWeight.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    binding?.apply {
                        var text = s.toString()
                        if (text.isNullOrEmpty()) {
                            btnConfirm.isEnabled = false
                            btnConfirm.alpha = 0.5f
                            return
                        }

                        //自动补0
                        if (text.startsWith(Period)) {
                            text = Zero + s
                            s?.replace(0, s.length, text.trim());
                        }

                        //首位是0 只能输入1个0
                        if (text.trim().length > 1) {
                            if (text.startsWith("0.")) {

                            } else if (text.startsWith("0")) {
                                text = beforeWeight
                                s?.replace(0, s.length, text)
                            }
                        }

                        val regex = "\\d{0,3}(\\.\\d{0,3})?"
                        if (!text.matches(regex.toRegex())) {
                            var index: Int = text.indexOf('.')
                            if (index == -1) {
                                //如果没小数点
                                index = text.length
                                val sub: String = text.substring(0, index)
                                s?.replace(
                                    0,
                                    s.length,
                                    if (sub.length > 3) sub.substring(0, 3) else sub
                                )
                            } else {
                                Timber.e("sssss ->${text}")
                                s?.replace(
                                    0,
                                    s.length,
                                    beforeWeight
                                )

                            }
                        }
                        Timber.e("ends =====${text}")

                        val textDouble = s?.toString()?.toDoubleOrNull()
                        Timber.e("ends textDouble =====${textDouble}")
                        if (textDouble == null) {
                            btnConfirm.isEnabled = false
                            btnConfirm.alpha = 0.5f
                            return
                        } else {
                            if (textDouble.isInt() && textDouble == 0.0) {

                                btnConfirm.isEnabled = false
                                btnConfirm.alpha = 0.5f
                                return
                            }
                        }


                        btnConfirm.isEnabled = true
                        btnConfirm.alpha = 1f
                    }
                }

                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    beforeWeight = p0.toString()
//                    Timber.e("beforeWeight ${beforeWeight}")
                }

                override fun onTextChanged(p0: CharSequence?, start: Int, before: Int, count: Int) {


                }
            })


        }
    }

    private fun updateSwitch() {
        binding?.apply {
            ivSwitch.setImageResource(if (isOpenManualInput) R.drawable.icon_switch_open else R.drawable.icon_switch_close)
            edtWeight.isEnabled = isOpenManualInput
        }
    }

    private fun checkWeight(): Boolean {
        binding?.apply {
            val inputWeight = edtWeight.text.toString()
            val regex = "\\d{0,3}(\\.\\d{0,3})?"
            if (!inputWeight.matches(regex.toRegex())) {
                Toast.makeText(
                    context,
                    context.getString(R.string.input_weight_rule_cue),
                    Toast.LENGTH_LONG
                ).show()
                return false
            }
        }
        return true
    }

    // 设置最大宽度，看需要而定，
    override fun getMaxWidth(): Int {
        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
//                val screenHeight = (displayMetrics.heightPixels * 0.9).toInt()
                val screenWidth = (displayMetrics.widthPixels * 0.30).toInt()
                return screenWidth
            }
        }

        return super.getMaxWidth()
    }

    // 设置最大高度，看需要而定
    override fun getMaxHeight(): Int {
        return super.getMaxHeight()
    }

    // 设置自定义动画器，看需要而定
    override fun getPopupAnimator(): PopupAnimator {
        return super.getPopupAnimator()
    }

    /**
     * 弹窗的宽度，用来动态设定当前弹窗的宽度，受getMaxWidth()限制
     *
     * @return
     */
    override fun getPopupWidth(): Int {
        return 0
    }

    /**
     * 弹窗的高度，用来动态设定当前弹窗的高度，受getMaxHeight()限制
     *
     * @return
     */
    override fun getPopupHeight(): Int {
        return 0
    }

    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

    override fun onDismiss() {
        KeyboardUtils.hideSoftInput(binding?.edtWeight)
//        CloseDevice()
        // 断开电子秤连接
        weightManager.stopRead()
//        weightManager.cancelAutoConnect()  // 先取消所有正在进行的连接尝试
//        weightManager.disconnectScale()
//        weightManager. cancelAutoConnect()
        super.onDismiss()
    }

//    private var scale: AclasScale? = null
//    var listener: AclasScaleListener? = null
//    private var m_strKeyPre = "--"
//    private var m_iKeyPre = -1
//    private var m_iKeyDisplayCnt = 10
//    private var m_strDeviceId = ""
//    private var m_Data: St_Data? = null
//
//    var myhandle: Handler = @SuppressLint("HandlerLeak")
//    object : Handler() {
//        override fun handleMessage(msg: Message) {
//            when (msg.arg1) {
//                1 -> {
//                    Toast.makeText(context, "The Firmware need update", Toast.LENGTH_LONG)
//                        .show()
//                    scale!!.StopRead()
//                }
//
//                2 -> {
//                    Toast.makeText(context, "There is no data!", Toast.LENGTH_LONG).show()
//                    scale!!.StopRead()
//                }
//
//                4 -> {
//                    Toast.makeText(context, "Stream channel error!", Toast.LENGTH_LONG)
//                        .show()
//                    CloseDevice()
//                }
//
//                5 -> {
//                    Toast.makeText(context, "Scaler disconnect!", Toast.LENGTH_LONG)
//                        .show()
//                    CloseDevice()
//                }
//
//                6 -> {
//                    binding?.apply {
//                        edtWeight.setText("${msg.obj}")
//                    }
////                    tWei.setText(msg.obj as String)
//                    if (m_Data != null) {
////                    	 CheckStableWeight();
//                    }
//                }
//
////                7 -> m_etRdTareVal.setText(msg.obj as String)
////                MainActivity.MESSAGE_OPEN -> btnClose.setEnabled(true)
////                MainActivity.MESSAGE_CLOSE -> {
////                    btnConfirm.setEnabled(true)
////                    tWei.setText("")
////                }
//
//                else -> Toast.makeText(context, "Unknown error", Toast.LENGTH_LONG).show()
//            }
//        }
//    }
//
//    private fun initScale() {
//        listener = object : AclasScaleListener {
//            override fun OnError(code: Int) {
//                Timber.e("OnError!!!!$code")
//                val msg: Message = myhandle.obtainMessage()
//                msg.arg1 = 0
//                if (code == AclasScale.CODENEEDUPDATE) {
//                    msg.arg1 = 1
//                    Timber.e(
//                        "AclasScale\$AclasScaleListener onError CODENEEDUPDATE---------"
//                    )
//                    //    		        myhandle.sendEmptyMessage(0);
//                    myhandle.sendMessage(msg)
//                } else if (code == AclasScale.NODATARECEIVE) {
//                    msg.arg1 = 2
//                    Timber.e(
//                        "AclasScale\$AclasScaleListener onError NODATARECEIVE---------"
//                    )
//                    myhandle.sendMessage(msg)
//                } else if (code == AclasScale.STREAMERROR) {
//                    msg.arg1 = 4
//                    Timber.e(
//                        "AclasScale\$AclasScaleListener onError STREAMERROR---------"
//                    )
//                    myhandle.sendMessage(msg)
//                } else if (code == AclasScale.DISCONNECT) {
//                    msg.arg1 = 5
//                    Timber.e(
//                        "AclasScale\$AclasScaleListener onError DISCONNECT---------"
//                    )
//                    myhandle.sendMessage(msg)
//                }
//            }
//
//            @SuppressLint("DefaultLocale")
//            override fun OnDataReceive(data: St_Data) {
//                if (data.m_iStatus == -1) {
//                    Timber.e("data error")
//                    m_Data = null
//                } else {
////        			Log.d(tag, "data --------------------------");
//                    m_Data = data
//                    val msg_read: Message = myhandle.obtainMessage()
//                    msg_read.arg1 = 6
//
//                    msg_read.obj = data.m_fWeight
////                        (if (data.m_iStatus == 0) "U" else "S") + " " + data.m_fWeight + data.m_strUnit + " id:" + m_strDeviceId
//
//                    myhandle.sendMessage(msg_read)
//
//                    Timber.e(
//                        ("data:" + (if (data.m_iStatus == 0) "Unstable" else "Stable") + " weight:" + String.format(
//                            "%.3f",
//                            data.m_fWeight
//                        ) + " price:" + String.format("%.3f", data.m_fPrice) + " total:"
//                                + data.m_fTotal + " key:" + data.m_stKey.m_iValue + " str:" + data.m_stKey.m_strKey)
//                    )
//                }
//            }
//
//            override fun OnReadTare(fVal: Float, bFlag: Boolean) {
//                val string = if (bFlag) fVal.toString() else "Error"
//                val msg = Message.obtain(myhandle, 0, 7, 0, string)
//                myhandle.sendMessageAtFrontOfQueue(msg)
//                Timber.e("data len OnReadTare:$bFlag $fVal")
//            }
//        }
//
//
//        val list = AclasScale.getAvailableUartList()
//        if (list.size > 0) {
//            openScale(list[1])
//        }
//    }
//
//    private fun openScale(str: String) {
//        try {
//            CloseDevice()
//
//            val strAdd: String = str
//            Timber.e("scale new aclas scale:$strAdd")
//            val iType = 0
//            scale = AclasScale(File(strAdd), iType, listener)
//            scale!!.bLogFlag = true
//            scale!!.open()
//            m_strDeviceId = scale!!.GetId()
//
//            //	            btnReadAd.setVisibility(iType==0?View.INVISIBLE:View.VISIBLE);
//        } catch (e: SecurityException) {
//            // TODO Auto-generated catch block
//            scale = null
//            Toast.makeText(context, "OpenScale exception", Toast.LENGTH_SHORT).show()
//            e.printStackTrace()
//        } catch (e: Exception) {
//            // TODO: handle exception
//            scale = null
//            Toast.makeText(context, "OpenScale exception", Toast.LENGTH_SHORT).show()
//            e.printStackTrace()
//        }
//        if (scale != null) {
//            Timber.e("scale start run thread")
//            //scale.setAclasScaleListener(arg0)
//            // scale.setAclasScaleListener(listener);
//            scale!!.StartRead()
//        } else {
//            Timber.e("scale null!!!!!!!!!!!!!!!!!!!")
//        }
//    }
//
//
//    private fun CloseDevice() {
//        if (scale != null) {
//            scale?.StopRead()
//            scale?.close()
//            scale = null
//        }
//    }

}

