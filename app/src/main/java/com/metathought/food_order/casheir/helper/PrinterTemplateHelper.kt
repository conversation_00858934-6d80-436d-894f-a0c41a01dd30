package com.metathought.food_order.casheir.helper

import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.extension.convertToTimestamp
import timber.log.Timber
import java.util.Date


/**
 *<AUTHOR>
 *@time  2024/8/27
 *@desc
 **/

object PrinterTemplateHelper {

    fun getPerOrderTemplate(): PrintTamplateResponseItem? {
        var preOrderTemplate: PrintTamplateResponseItem? = null
        PrinterDeviceHelper.getPrinterList().forEach {
            preOrderTemplate =
                it.printerTemplateList?.find { it.type == PrintTemplateTypeEnum.PRE_ORDER.id }
        }
        return preOrderTemplate
    }

    //获取预定模板
    fun isTimeToPrinterPerOrderTicket(
        preOrderTemplate: PrintTamplateResponseItem?,
        orderedInfo: OrderedInfoResponse?
    ): Boolean {
        if (preOrderTemplate != null) {
            if (preOrderTemplate.reservationDuration == 0) {
                return true
            } else {
                if (!orderedInfo?.getDingTime().isNullOrEmpty()) {
                    val timestamp = orderedInfo?.getDingTime()?.convertToTimestamp()
                    if (timestamp != null) {
                        Timber.e("timestamp  :${timestamp}")
                        val currentTimeStamp = Date().time
                        Timber.e("currentTimeStamp  :${currentTimeStamp}")
                        val reservationTime =
                            preOrderTemplate.reservationDuration?.times(60L)?.times(1000) ?: 0L
                        if (currentTimeStamp < (timestamp - reservationTime)) {

                            return false
                        }
                    }
                }
                return true
            }
        }
        return true
    }

//    fun getCheckoutReceiptTemplate(): PrintTamplateResponseItem? {
//        var template: PrintTamplateResponseItem? = null
//        PrinterDeviceHelper.getPrinterList().forEach {
//            template =
//                it.printerTemplateList?.find { it.type == PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id }
//        }
//        return template
//    }


//    /**
//     * 修改结账模板是否自动打印
//     */
//
//    fun modifyAutoPrintCheckoutReceipt(isAuto: Boolean) {
//        PrinterDeviceHelper.getPrinterList().forEach {
//            val template =
//                it.printerTemplateList?.find { it.type == PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id }
//            template?.informationShow?.isAutoCheckoutTicket = isAuto
//        }
//    }


    /**
     * Get kitchen print model
     *
     * @return   first:是否合并打印  second 是否分开打印
     */
    fun getKitchenPrintModel(printTemplateResponseItem: PrintTamplateResponseItem): Pair<Boolean, Boolean> {
        //是否合并打印
        var isMergePrint = false
        //是否分开打印
        var isSplitPrint = false
        if (printTemplateResponseItem.printerWayV2.isNullOrEmpty()) {
            if (printTemplateResponseItem.printerWay == 2) {
                isSplitPrint = true
            } else {
                isMergePrint = true
            }
        } else {
            val printerWayV2List =
                (printTemplateResponseItem.printerWayV2 ?: "").split(",")
            printerWayV2List.forEach {
                if (it == "1") {
                    isMergePrint = true
                } else if (it == "2") {
                    isSplitPrint = true
                }
            }
        }
        return Pair(isMergePrint, isSplitPrint)
    }
}