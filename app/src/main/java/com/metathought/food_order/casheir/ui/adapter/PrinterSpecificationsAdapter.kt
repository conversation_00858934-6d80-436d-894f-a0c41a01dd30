package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterFeedBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2024/5/2716:42
 * @description
 */
class PrinterSpecificationsAdapter(
    val list: List<GoodsTagItem>,
    val orderedGoods: OrderedGoods?,
    val isKitchen: Boolean,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机,
    val isShowPrice: Boolean?,
) : RecyclerView.Adapter<PrinterSpecificationsAdapter.PrinterFeedViewHolder>() {


    inner class PrinterFeedViewHolder(val binding: ItemPrinterFeedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodsTagItem) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
                var nameString = ""
                localeList.forEachIndexed { index, locale ->
                    val name = orderedGoods?.getLocaleSingleGoodsTagStr(locale, resource) ?: ""
                    if (isEightyWidth == true) {
//                        Timber.e("80  name => ${name}")
                        if (index == 0) {
                            tvFeedNameEn.text = "$name"
                            tvFeedNameEn.isVisible = !name.isNullOrEmpty()
                        } else if (index == 1) {
                            tvFeedNameKm.text = "$name"
                            tvFeedNameKm.isGone =
                                tvFeedNameKm.text == tvFeedNameEn.text || name.isNullOrEmpty()
                        }

                    } else {
                        if (nameString.isEmpty()) {
                            nameString = "$name"
                        } else {
                            if (nameString != name) {
                                nameString = "$nameString $name"
                            }
                        }
                    }
                }

                tvFeedFoodCount.text = ""
                tvFeedFoodPrice.text = ""
                if (isShowPrice == true && (resource.price ?: 0.0) > 0.0) {
                    tvFeedFoodPrice.text = "$${resource.price?.priceDecimalFormatTwoDigitZero()}"
                }

                if (isEightyWidth == true) {
                    tvFeedName.isVisible = false
                    llFeedName.isVisible = true
                    llItemIndex.isInvisible = true
//                    llDiscountPrice.isInvisible = true

                    (llItemIndex.layoutParams as LinearLayout.LayoutParams).weight = 1f
                    (llFeedName.layoutParams as LinearLayout.LayoutParams).weight = 2.5f
                    (llFeedFoodCount.layoutParams as LinearLayout.LayoutParams).weight = 1f
                    (llFeedFoodPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
//                    (llDiscountPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                    (llFeedFoodTotal.layoutParams as LinearLayout.LayoutParams).weight =
                        1.5f
//                    tvFeedFoodPrice.isVisible = (resource.price ?: 0.0) > 0.0
                    tvFeedNameKm.setPadding(80, 0, 0, 0)
                } else {
                    Timber.e("nameString :${nameString}  price:${resource.price}")
                    tvFeedName.text = "$nameString"
                    llBottom.isVisible =
                        !tvFeedFoodCount.text.isNullOrEmpty() || !tvFeedFoodPrice.text.isNullOrEmpty()
                    tvFeedName.setPadding(20, 0, 0, 0)

                }

            }
        }


    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterFeedBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterSpecificationsAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}