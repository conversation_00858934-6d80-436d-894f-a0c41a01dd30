package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.MergeType
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding

class MergeTypeAdapter(
    val list: List<MergeType>,
    val selected: MergeType?,
    val context: Context
) : RecyclerView.Adapter<MergeTypeAdapter.MergeTypeViewHolder>() {

    var onItemClickCallback: ((MergeType) -> Unit)? = null

    inner class MergeTypeViewHolder(val binding: FilterTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: MergeType) {
            binding.apply {
                tvTableID.text = when (item) {
                    MergeType.MERGED_TIME -> context.getString(R.string.merged_time)
                    MergeType.INDEPENDENT_TIME -> context.getString(R.string.independent_time)
                }
                tvTableID.gravity = Gravity.CENTER
                tvTableID.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (selected == item) R.color.primaryColor else android.R.color.black
                    )
                )
                root.setCardBackgroundColor(
                    ContextCompat.getColor(
                        context,
                        if (selected == item) R.color.filter_table_background else android.R.color.transparent
                    )
                )
                root.setOnClickListener {
                    onItemClickCallback?.invoke(item)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MergeTypeViewHolder {
        val binding =
            FilterTableItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MergeTypeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MergeTypeViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size
}
