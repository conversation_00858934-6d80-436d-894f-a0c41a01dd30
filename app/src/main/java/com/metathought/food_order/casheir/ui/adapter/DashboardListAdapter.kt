package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreMoneyOrderDetailVo
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.DashboardRevenueItemBinding


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class DashboardListAdapter(
    val list: ArrayList<StoreMoneyOrderDetailVo?>,
    val onItemClickListener: (StoreMoneyOrderDetailVo) -> Unit,
) : RecyclerView.Adapter<DashboardListAdapter.DashboardListViewHolder>() {


    inner class DashboardListViewHolder(val binding: DashboardRevenueItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: StoreMoneyOrderDetailVo?, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
                        tvDate.text = resource?.orderDate
                        tvRevenueAmount.text = "$${resource?.turnover}"
                        tvActualRevenue.text = "$${resource?.payMoney}"
                        tvOrder.text = "${resource?.orderNum}"
                        tvPaidOrder.text= "${resource?.payOrderNum}"
                        tvRefundOrder.text= "${resource?.refundOrderNum}"
                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DashboardListViewHolder {
        val itemView =
            DashboardRevenueItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DashboardListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: DashboardListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }



    fun replaceData(list: ArrayList<StoreMoneyOrderDetailVo?>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<StoreMoneyOrderDetailVo?>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }

}