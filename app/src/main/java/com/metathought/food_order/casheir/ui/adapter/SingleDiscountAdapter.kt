package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.SingleDiscountItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber


class SingleDiscountAdapter(
    val onItemClickListener: ((SingleDiscountGoods) -> Unit)? = null
) : RecyclerView.Adapter<SingleDiscountAdapter.SingleDiscountHolder>() {

    private val list = mutableListOf<SingleDiscountGoods>()

    inner class SingleDiscountHolder(val binding: SingleDiscountItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick {
                        if (it != -1) {
                            onItemClickListener?.invoke(list[it])
                        }
                    }
                }
            }
        }

        fun bind(resource: SingleDiscountGoods) {
            Timber.d("Binding ${resource.toJson()}")
            binding.apply {
                tvName.text = resource.name
                tvCount.text = "x ${resource.num}"
                tvPrice.text =
                    "${tvPrice.context.getString(R.string.original_price)} ${resource.totalPrice.priceFormatTwoDigitZero2()}"
                tvVipPrice.isVisible = resource.isShowVipPrice

                if (resource.isShowVipPrice) {
                    tvVipPrice.text = resource.vipPrice.priceFormatTwoDigitZero2()
                }
                if (resource.type == null) {
                    tvNewPrice.isVisible = false
                    tvNewVipPrice.isVisible = false
                } else {
                    if (resource.reduceRatio == null && resource.saleReduce == null && resource.vipReduce == null) {
                        tvNewPrice.isVisible = false
                        tvNewVipPrice.isVisible = false
                    } else {
                        tvNewPrice.isVisible = true
                        tvNewVipPrice.isVisible = resource.isShowVipPrice
                        if (resource.type == 1) {
                            val percent = resource.reduceRatio ?: 0.0
                            val newTotalPrice = resource.totalPrice -
                                    (resource.totalPrice * percent / 100.0).toLong()
                            val newVipPrice =
                                resource.vipPrice - (resource.vipPrice * percent / 100.0).toLong()
                            tvNewPrice.text =
                                "${tvPrice.context.getString(R.string.new_price)} ${newTotalPrice.priceFormatTwoDigitZero2()}"
                            tvNewVipPrice.text = newVipPrice.priceFormatTwoDigitZero2()
                        } else {
                            //固定金额折扣
                            val newTotalPrice =
                                if (resource.saleReduce == null) resource.totalPrice else (resource.totalPrice - (resource.saleReduce!! * 100.0).toLong())
                            tvNewPrice.text =
                                "${tvPrice.context.getString(R.string.new_price)} ${newTotalPrice.priceFormatTwoDigitZero2()}"

                            val newVipPrice = if (resource.vipReduce == null) resource.vipPrice else
                                (resource.vipPrice - (resource.vipReduce!! * 100.0).toLong())
                            tvNewVipPrice.text = newVipPrice.priceFormatTwoDigitZero2()
                        }
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = SingleDiscountHolder(
        SingleDiscountItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: SingleDiscountAdapter.SingleDiscountHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<SingleDiscountGoods>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

}