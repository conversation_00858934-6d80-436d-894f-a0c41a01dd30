package com.metathought.food_order.casheir.data.model.base.request_model.export


/**
 *<AUTHOR>
 *@time  2025/2/24
 *@desc
 **/

data class SalesOrdersExportRequest(
    /**
     *  开始时间 2025/02/27   //00:00:00
     */
    var startTime: String? = null,
    /**
     *  结束时间 2025/02/27   //00:00:00
     */
    var endTime: String? = null,


    var timeType: Int? = null,

    /**
     * 报表类型  //导出类型 1-销售报表 2-商品报表 3-支付渠道报表
     */
    var type: Int? = null,

    /**
     * 文件类型 2-excel
     */
    var fileType: Int? = null,

    /**
     * 关键字
     */

    var keyword: String? = null,
    /**
     * 商品分组
     */
    var groupIds: String? = null,
    /**
     * 排序
     */
    var orderByColumn: String? = null
)