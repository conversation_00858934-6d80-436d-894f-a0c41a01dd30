package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ConsumerResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.databinding.FilterNicknameItemBinding
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding


class FilterNicknameAdapter(
    val context: Context,
    val onClickCallback: (ConsumerResponse) -> Unit,
) : RecyclerView.Adapter<FilterNicknameAdapter.FilterNicknameViewHolder>() {

    private val list = mutableListOf<ConsumerResponse>()
    private var selectedId: String? = null

    inner class FilterNicknameViewHolder(val binding: FilterNicknameItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: ConsumerResponse?, index: Int) {
            resource?.let { _ ->
                binding.apply {
                    tvNickname.text =
                        if (!resource.nickName.isNullOrEmpty()) resource.nickName else context.getString(
                            R.string.n_a
                        )
                    tvAccount.text = resource.telephone

                    root.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            if (selectedId == resource.id) R.color.filter_table_background else android.R.color.transparent
                        )
                    )
                    root.setOnClickListener {
                        val lastIndex = list.indexOfFirst { it.id == selectedId }
                        if (selectedId == resource.id) {
                            selectedId = null
                        } else {
                            selectedId = resource.id
                        }
                        if (lastIndex != -1) {
                            notifyItemChanged(lastIndex)
                        }
                        notifyItemChanged(index)
                        onClickCallback.invoke(resource)
                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FilterNicknameViewHolder {
        val itemView = FilterNicknameItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
        return FilterNicknameViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: FilterNicknameViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun getSelected(): String? {
        return selectedId
    }

    fun getSelecteAccount(): ConsumerResponse? {
        val lastIndex = list.indexOfFirst { it.id == selectedId }
        if (lastIndex != -1) {
            return list[lastIndex]
        }
        return null
    }

    fun resetSelect() {
        val lastIndex = list.indexOfFirst { it.id == selectedId }
        selectedId = null
        if (lastIndex != -1) {
            notifyItemChanged(lastIndex)
        }
    }

    fun replaceData(records: List<ConsumerResponse>?) {
        list.clear()
        list.addAll(records ?: emptyList())
        notifyDataSetChanged()
    }

    fun addData(records: List<ConsumerResponse>?) {
        list.addAll(records ?: emptyList())
        notifyDataSetChanged()
    }

    fun setSelecteAccount(selecteAccount: ConsumerResponse?) {
        selectedId = selecteAccount?.id
        notifyDataSetChanged()
    }


}