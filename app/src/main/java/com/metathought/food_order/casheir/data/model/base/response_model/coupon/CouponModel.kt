package com.metathought.food_order.casheir.data.model.base.response_model.coupon

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.TextAppearanceSpan
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.CouponTypeEnum
import com.metathought.food_order.casheir.extension.convertToTimestamp2
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.formatTimestamp
import com.metathought.food_order.casheir.utils.DisplayUtils
import java.util.Date
import kotlin.math.ceil


/**
 *<AUTHOR>
 *@time  2024/8/26
 *@desc 优惠券model
 **/

data class CouponModel(
    @SerializedName("id")
    var id: Long? = null,
    @SerializedName("category")
    val category: String? = null,
    @SerializedName("templateId")
    var templateId: String? = null,
    @SerializedName("consumerId")
    var consumerId: String? = null,
    @SerializedName("couponCode")
    var couponCode: String? = null,
    @SerializedName("assignTime")
    var assignTime: String? = null,
    @SerializedName("effectTimeStart")
    var effectTimeStart: String? = null,
    @SerializedName("effectTimeEnd")
    var effectTimeEnd: String? = null,
    @SerializedName("useTime")
    var useTime: String? = null,
    @SerializedName("usedBy")
    var usedBy: String? = null,
    @SerializedName("status")
    var status: String? = null,
    @SerializedName("templateSDK")
    var templateSDK: CouponTemplateSDK? = null,
    @SerializedName("lockOrderId")
    var lockOrderId: String? = null,
    @SerializedName("isValid")
    var isValid: Boolean? = null,
    @SerializedName("isVipValid")
    var isVipValid: Boolean? = null,
    @SerializedName("couponPrice")
    var couponPrice: Long? = null,
    @SerializedName("vipCouponPrice")
    var vipCouponPrice: Long? = null,
    @SerializedName("giftGoods")
    var giftGoods: List<UsageGoods>? = null,


    //规则是否展开
    var isRuleExpand: Boolean? = false,
    //适用商品是否展开
    var isGoodsExpand: Boolean? = false,
    //是否已选中
    var isSelected: Boolean? = false,
    //赠送商品是否展开
    var isGiftGoodsExpand: Boolean? = false,
) {

    //是否有门槛券
    fun isThresholdCoupon(): Boolean {
        return templateSDK?.category in listOf(
            CouponTypeEnum.THRESHOLD_LJ.type,
            CouponTypeEnum.THRESHOLD_ZK.type,
            CouponTypeEnum.THRESHOLD_ZS.type
        )
    }

    //获取优惠描述
    fun getDiscountDesc(context: Context): SpannableStringBuilder {
        val span = SpannableStringBuilder()
        val rule = templateSDK?.rule
        when (templateSDK?.category) {
            CouponTypeEnum.THRESHOLD_LJ.type, CouponTypeEnum.NOTHRESHOLD_LJ.type -> {
                val unit = "$"
                val quota = "${rule?.discount?.quota?.decimalFormatTwoDigit()}"
                span.append(unit)
                span.append(quota)
                span.setSpan(
                    TextAppearanceSpan(
                        null, Typeface.BOLD, DisplayUtils.sp2px(context, 24f),
                        ColorStateList.valueOf(context.getColor(R.color.primaryColor)), null
                    ),
                    unit.length,
                    unit.length + quota.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }


            CouponTypeEnum.THRESHOLD_ZK.type, CouponTypeEnum.NOTHRESHOLD_ZK.type -> {

                val quota = "${rule?.discount?.quota?.decimalFormatTwoDigit()}"
                val quotaDesc = "% OFF"
                span.append(quota)
                span.append(quotaDesc)
                span.setSpan(
                    TextAppearanceSpan(
                        null, Typeface.BOLD, DisplayUtils.sp2px(context, 24f),
                        ColorStateList.valueOf(context.getColor(R.color.primaryColor)), null
                    ),
                    0,
                    quota.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

            }

            CouponTypeEnum.THRESHOLD_ZS.type, CouponTypeEnum.NOTHRESHOLD_ZS.type -> {
                val quota = context.getString(R.string.give_away_goods)
                span.append(quota)
                span.setSpan(
                    TextAppearanceSpan(
                        null, Typeface.BOLD, DisplayUtils.sp2px(context, 16f),
                        ColorStateList.valueOf(context.getColor(R.color.primaryColor)), null
                    ),
                    0,
                    quota.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

            }
        }
        return span
    }

    //是否赠品券
    fun isGiftGoodCoupon(context: Context): Boolean {
        return templateSDK?.category in listOf(
            CouponTypeEnum.THRESHOLD_ZS.type,
            CouponTypeEnum.NOTHRESHOLD_ZS.type
        )
    }

    //获取满减描述
    fun getThresholdDesc(context: Context): String {
        return if (templateSDK?.rule?.discount?.type == "PRICE") {
            context.getString(
                R.string.full_price_available,
                "$${templateSDK?.rule?.discount?.base?.decimalFormatTwoDigit()}"
            )
        } else if (templateSDK?.rule?.discount?.type == "NUM") {
            context.getString(
                R.string.full_num_available,
                "${templateSDK?.rule?.discount?.itemNum}"
            )
        } else {
            ""
        }
    }

    fun getGiftGoodsList(): List<UsageGoods>? {
        return giftGoods ?: templateSDK?.rule?.discount?.giveGoods
    }

    //获取适用商品名描述
    fun getGoodsListDesc(): String {
        val sbf = StringBuffer()
        templateSDK?.usageGoods?.forEachIndexed { index, goods ->
            sbf.append(goods.name)
            if (index != (templateSDK?.usageGoods?.size ?: 0) - 1) {
                sbf.append("、")
            }
        }
        if (sbf.isEmpty()) {
            return ""
        }
        return sbf.toString()

    }

    //是否部分适用
    fun isPartialGoods(): Boolean {
        return templateSDK?.isPartialGoods() == true
    }

    //是否使用所有商品
    fun isAllGoods(): Boolean {
        return templateSDK?.isAllGoods() == true
    }

    //是否适用会员充值
    fun isTopUp(): Boolean {
        return templateSDK?.isTopUp() == true
    }

    //获取规则描述
    fun getRuleDesc(): String {
        return templateSDK?.usingRules ?: ""
    }

    //有限时间范围
    fun getEffectiveTimeRange(context: Context): String {
        if (effectTimeStart == null && effectTimeEnd == null) {
            return ""
        }

        val currentTimeStamp = Date().time
        val effectTimeStartStamp = effectTimeStart?.convertToTimestamp2() ?: Date().time
        val effectTimeEndStamp = effectTimeEnd?.convertToTimestamp2() ?: Date().time
        val effectTimeStartStr = effectTimeStartStamp.formatTimestamp()
        val effectTimeEndStr = effectTimeEndStamp.formatTimestamp()
        val diffTime = effectTimeEndStamp - currentTimeStamp
        //七天时间
        val sevenDay = (60 * 60 * 24 * 7 * 1000).toLong()
        val oneDay = (60 * 60 * 24 * 1000).toLong()
        val oneHour = (60 * 60 * 1000).toLong()
        val twoMinute = (60 * 2 * 1000).toLong()
        if (diffTime < 0) {
            //说明已经结束
            return context.getString(R.string.expired)
        } else if (diffTime > sevenDay) {
            //大于七天  显示有效期至
            return context.getString(R.string.effect_time_to, effectTimeEndStr)
        } else if (diffTime in (oneDay + 1)..sevenDay) {
            //小于七天 大于1天  显示几天后过期
            val day = diffTime.div(1000.0).div(60).div(60).div(24).toInt()
            return context.getString(R.string.expires_in_a_few_days, "${day}")
        } else if (diffTime in (oneHour + 1)..oneDay) {
            //小于1天 大于1小时  显示几小时后过期
            val hours = diffTime.div(1000.0).div(60).div(60).toInt()
            return context.getString(R.string.expires_in_a_few_hour, "$hours")
        } else if (diffTime in (twoMinute + 1)..oneHour) {
            //小于1小时 大于2分钟  显示几分钟后过期
            val minutes = ceil(diffTime.div(1000.0).div(60)).toInt()
            return context.getString(R.string.expires_in_a_few_minute, "$minutes")
        } else if (diffTime <= twoMinute) {
            //小于等于2分钟  显示几秒后过期
            val second = ceil(diffTime.div(1000.0)).toInt()
            return context.getString(R.string.expires_in_a_few_second, "$second")
        }


        return "${effectTimeStartStr}-${effectTimeEndStr}"
    }

    //是否折扣
    fun isZkCoupon(): Boolean {
        return templateSDK?.isZkCoupon() == true
    }

    fun isZsCoupon(): Boolean {
        return templateSDK?.isZsCoupon() == true
    }

    fun toOrderCoupon(): OrderCouponModel {
        return OrderCouponModel(
            id = id,
            name = templateSDK?.name,
            code = couponCode,
            isValid = isValid,
            isVipValid = isVipValid,
            couponPrice = couponPrice,
            vipCouponPrice = vipCouponPrice,
            giftGoods = giftGoods,
            category = templateSDK?.category,
            templateSDK = templateSDK
        )
    }
}


