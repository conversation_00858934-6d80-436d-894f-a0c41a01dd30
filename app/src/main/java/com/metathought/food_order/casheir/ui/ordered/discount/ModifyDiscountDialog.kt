//package com.metathought.food_order.casheir.ui.ordered.discount
//
//import android.os.Build
//import android.os.Bundle
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import android.widget.FrameLayout
//import android.widget.Toast
//import androidx.core.view.isInvisible
//import androidx.core.view.isVisible
//import androidx.core.widget.addTextChangedListener
//import androidx.fragment.app.FragmentManager
//import androidx.fragment.app.viewModels
//import com.metathought.food_order.casheir.R
//import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
//import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
//import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ReduceDiscountDetailModel
//import com.metathought.food_order.casheir.databinding.DialogModifyDiscountBinding
//import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
//import com.metathought.food_order.casheir.extension.disableCopy
//import com.metathought.food_order.casheir.extension.halfUp
//import com.metathought.food_order.casheir.extension.hideKeyboard
//import com.metathought.food_order.casheir.extension.hideKeyboard2
//import com.metathought.food_order.casheir.extension.isInt
//import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
//import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
//import com.metathought.food_order.casheir.extension.setEnable
//import com.metathought.food_order.casheir.filter.CashierInputFilter
//import com.metathought.food_order.casheir.network.ApiResponse
//import com.metathought.food_order.casheir.network.COUPON_EXPIRED
//import com.metathought.food_order.casheir.network.COUPON_LOCKED
//import com.metathought.food_order.casheir.network.COUPON_UNEABLE
//import com.metathought.food_order.casheir.network.COUPON_USED
//import com.metathought.food_order.casheir.network.GOODS_HAS_BEEN_MERGE_OR_SPLIT
//import com.metathought.food_order.casheir.network.ORDER_STATUS_ERROR
//import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
//import com.metathought.food_order.casheir.utils.DisplayUtils
//import com.metathought.food_order.casheir.utils.TextInputUtil
//import dagger.hilt.android.AndroidEntryPoint
//import timber.log.Timber
//import java.math.BigDecimal
//
//
///**
// *<AUTHOR>
// *@time  2024/7/4
// *@desc
// **/
//
//@AndroidEntryPoint
//class ModifyDiscountDialog : BaseDialogFragment() {
//    companion object {
//        private const val TAG = "ModifyDiscountDialog"
//
//        //        private const val ORDER_INFO = "ORDER_INFO"
//        private const val DETAIL_MODEL = "DETAIL_MODEL"
//        fun showDialog(
//            fragmentManager: FragmentManager,
//            orderInfo: OrderedInfoResponse? = null,
//            reduceDiscountDetailModel: ReduceDiscountDetailModel? = null,
//            cartGoods: List<GoodsRequest>? = null,  //购物车不参与整单折扣的菜品
//            modifyClickListener: ((reduceType: Int?, reduceRate: Double?, reduceDollar: BigDecimal?, reduceKhr: BigDecimal?) -> Unit),
//            errorListener: ((Int?) -> Unit)
//        ) {
//            var fragment = fragmentManager.findFragmentByTag(TAG)
//            if (fragment != null) return
//            fragment = newInstance(
//                orderInfo,
//                reduceDiscountDetailModel,
//                cartGoods,
//                modifyClickListener = modifyClickListener, errorListener = errorListener
//            )
//
//            fragment.show(fragmentManager, TAG)
//        }
//
//        fun getCurrentModifyDiscountDialog(fragmentManager: FragmentManager): ModifyDiscountDialog? {
//            val fragment = fragmentManager.findFragmentByTag(TAG) as? ModifyDiscountDialog
//            return fragment
//        }
//
//        fun dismissDialog(fragmentManager: FragmentManager) {
//            val fragment = fragmentManager.findFragmentByTag(TAG) as? ModifyDiscountDialog
//            fragment?.dismissAllowingStateLoss()
//        }
//
//        private fun newInstance(
//            orderInfo: OrderedInfoResponse? = null,
//            model: ReduceDiscountDetailModel? = null,
//            cartGoods: List<GoodsRequest>? = null,
//            modifyClickListener: ((reduceType: Int?, reduceRate: Double?, reduceDollar: BigDecimal?, reduceKhr: BigDecimal?) -> Unit),
//            errorListener: ((Int?) -> Unit)
//        ): ModifyDiscountDialog {
//            return ModifyDiscountDialog().apply {
//                val args = Bundle()
//                args.putParcelable(DETAIL_MODEL, model)
//                this.orderInfo = orderInfo
//                this.arguments = args
//                this.cartGoods = cartGoods
//                this.modifyClickListener = modifyClickListener
//                this.errorListener = errorListener
//            }
//        }
//    }
//
//    private var modifyClickListener: ((reduceType: Int?, reduceRate: Double?, reduceDollar: BigDecimal?, reduceKhr: BigDecimal?) -> Unit)? =
//        null
//
//    private var errorListener: ((Int?) -> Unit)? = null
//
//    private var binding: DialogModifyDiscountBinding? = null
//    private val viewModel: ModifyDiscountViewModel by viewModels()
//
//    private var orderInfo: OrderedInfoResponse? = null
//    private var cartGoods: List<GoodsRequest>? = null
//
//
//    //    private var currentOrderNo: String? = null
//    private var reduceDiscountDetailModel: ReduceDiscountDetailModel? = null
//
//    fun getCurrentOrderNo(): String? {
//        return orderInfo?.orderNo
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        savedInstanceState: Bundle?
//    ): View? {
//        binding = DialogModifyDiscountBinding.inflate(layoutInflater)
//        return binding?.root
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
//        dialog?.setCancelable(false)
//
//        openKeyBoardListener()
//        onTouchOutSide(binding?.root)
//
//        initListener()
//        initView()
//        initObserver()
//
//
//    }
//
//    private fun initView() {
//        binding?.apply {
//            context?.let {
//                reduceDiscountDetailModel =
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
//                        arguments?.getParcelable(
//                            DETAIL_MODEL,
//                            ReduceDiscountDetailModel::class.java
//                        )
//                    } else {
//                        arguments?.getParcelable(DETAIL_MODEL)
//                    }
//                Timber.e("reduceDiscountDetailModel => ${reduceDiscountDetailModel.toString()}")
//
//                viewModel.getDetail(orderInfo?.orderNo)
//
//
//            }
//        }
//    }
//
//
//    private fun initObserver() {
//        viewModel.uiModeState.observe(viewLifecycleOwner) { state ->
//            when (state.result) {
//                is ApiResponse.Success -> {
//                    val data = state.result.data
//                    if (orderInfo == null) {
//                        cartGoods?.forEach {
//                            //不参与折扣的只刨去本身的价格
//                            reduceDiscountDetailModel?.totalAmount =
//                                (reduceDiscountDetailModel?.totalAmount
//                                    ?: 0) - it.totalDiscountPrice() //- it.totalServiceChargePrice() - it.totalVatPrice()
//                            reduceDiscountDetailModel?.totalVipAmount =
//                                (reduceDiscountDetailModel?.totalVipAmount
//                                    ?: 0) - it.totalVipPrice() //  - it.totalVipServiceChargePrice() - it.totalVipVatPrice()
//                        }
//                        binding?.apply {
//                            ivWarn.isVisible = !cartGoods.isNullOrEmpty()
//                        }
//                        //销售价
//                        reduceDiscountDetailModel?.conversionRatio = data.conversionRatio
//                        reduceDiscountDetailModel?.totalAmountKhr = KHRroundToTwoDecimalPlaces(
//                            (reduceDiscountDetailModel?.totalAmount ?: 0).times(
//                                data.conversionRatio ?: 0
//                            )
//                        )
//
//                        //会员价
//                        if (reduceDiscountDetailModel?.totalVipAmount != null) {
//                            reduceDiscountDetailModel?.totalVipAmountKhr =
//                                KHRroundToTwoDecimalPlaces(
//                                    (reduceDiscountDetailModel?.totalVipAmount
//                                        ?: 0).times(data.conversionRatio ?: 0)
//                                )
//
//                        }
//                    } else {
//                        reduceDiscountDetailModel = data
//
//                        if ((orderInfo?.reduceDollar == null || orderInfo?.reduceDollar == 0L) && (orderInfo?.reduceKhr == null || orderInfo?.reduceKhr == 0L) && orderInfo?.reduceRate == null && (orderInfo?.reduceAmount == null || orderInfo?.reduceAmount == 0L)) {
//                            //切换优惠券的时候会重置  所以这里要重置
//                            reduceDiscountDetailModel?.type = null
//
//                            reduceDiscountDetailModel?.reduceRate = null
//                            reduceDiscountDetailModel?.reduceAmount = null
//                            reduceDiscountDetailModel?.reduceKhr = null
//                            reduceDiscountDetailModel?.reduceDollar = null
//
//                            reduceDiscountDetailModel?.reduceVipRate = null
//                            reduceDiscountDetailModel?.reduceVipAmount = null
//                            reduceDiscountDetailModel?.reduceVipKhr = null
//                            reduceDiscountDetailModel?.reduceVipDollar = null
//
//                        }
//
//                        //输入框不显示0，防止服务端返回的0，如果为0 置为null
//                        if (reduceDiscountDetailModel?.reduceRate == 0.0) {
//                            reduceDiscountDetailModel?.reduceRate = null
//                        }
//                        //防止服务端返回的0
//                        if (reduceDiscountDetailModel?.reduceVipRate == 0.0) {
//                            reduceDiscountDetailModel?.reduceVipRate = null
//                        }
//
//                        //获取只带优惠券后的金额
//                        val totalAmount =
//                            orderInfo?.getParticipatingWholeDiscountsAmount()
//                        reduceDiscountDetailModel?.totalAmount = totalAmount
//                        reduceDiscountDetailModel?.totalAmountKhr = KHRroundToTwoDecimalPlaces(
//                            (totalAmount ?: 0).times(
//                                reduceDiscountDetailModel?.conversionRatio ?: 0
//                            )
//                        )
//
//
//                        val totalVipAmount =
//                            orderInfo?.getVipParticipatingWholeDiscountsAmount()
//                        reduceDiscountDetailModel?.totalVipAmount = totalVipAmount
//                        reduceDiscountDetailModel?.totalVipAmountKhr = KHRroundToTwoDecimalPlaces(
//                            (totalVipAmount ?: 0).times(
//                                reduceDiscountDetailModel?.conversionRatio ?: 0
//                            )
//                        )
//
//                        binding?.apply {
//                            ivWarn.isVisible =
//                                !orderInfo?.goods?.filter { it.isDiscountItemWhitelisting() == true }
//                                    .isNullOrEmpty()
//                        }
//                    }
//
//                    initData(reduceDiscountDetailModel)
//
//                    binding?.apply {
//                        progressBar.isVisible = false
//                        llContent.isInvisible = false
//                    }
//                }
//
//                is ApiResponse.Loading -> {
//                    binding?.apply {
//                        progressBar.isVisible = true
//                        llContent.isInvisible = true
//                    }
//                }
//
//                is ApiResponse.Error -> {
//                    if (!state.result.message.isNullOrEmpty()) {
//                        Toast.makeText(requireActivity(), state.result.message, Toast.LENGTH_LONG)
//                            .show()
//                    }
//                    errorListener?.invoke(state.result.errorCode)
//                    dismissAllowingStateLoss()
//                }
//
//                else -> {
//
//                }
//            }
//
//            state.confirm?.apply {
//                if (state.confirm is ApiResponse.Success) {
//                    binding?.apply {
//                        val reduceDollar = edtUsd.text.toString().toBigDecimalOrNull()
//                        val reduceKhr = edtKhr.text.toString().toBigDecimalOrNull()
//                        val reduceRate = edtPercent.text.toString().toDoubleOrNull()
//                        modifyClickListener?.invoke(
//                            if (isVip()) 2 else 1,
//                            reduceRate,
//                            reduceDollar,
//                            reduceKhr
//                        )
//                    }
//
//                    dismissAllowingStateLoss()
//                }
//                if (state.confirm is ApiResponse.Error) {
//                    if (!state.confirm.message.isNullOrEmpty()) {
//                        Toast.makeText(requireActivity(), state.confirm.message, Toast.LENGTH_LONG)
//                            .show()
//                    }
//                    binding?.apply {
//                        progressBar.isVisible = false
//                        llContent.isVisible = true
//                    }
//
//                    Timber.e("state.confirm.errorCode :${state.confirm.errorCode}  state.confirm.status:${state.confirm.status} ")
//                    if (state.confirm.errorCode == ORDER_STATUS_ERROR || state.confirm.errorCode == COUPON_USED || state.confirm.errorCode == COUPON_LOCKED || state.confirm.errorCode == COUPON_UNEABLE || state.confirm.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT || state.confirm.errorCode == COUPON_EXPIRED) {
//                        errorListener?.invoke(state.confirm.errorCode)
//                        dismissAllowingStateLoss()
//                    }
//                }
//            }
//
//        }
//    }
//
//    private fun initListener() {
//        binding?.apply {
//
//            ivWarn.setOnClickListener {
//                if (orderInfo == null) {
//                    NoDiscountGoodsDialog.showDialog(parentFragmentManager, cartGoods)
//                } else {
//                    NoDiscountGoodsDialog.showDialog(
//                        parentFragmentManager,
//                        orderInfo?.goods?.filter { it.isDiscountItemWhitelisting() == true }?.map {
//                            GoodsRequest(
//                                num = it.num,
//                                feedInfoList = ArrayList(it.feeds ?: listOf()),
//                                goodsTagItems = ArrayList(it.tagItems ?: listOf()),
//                                goods = it.orderedGoodsConvertToGoods(),
//                                finalSinglePrice = it.finalSinglePrice,
//                                singleDiscountGoods = it.singleItemDiscount?.toCartSingleDiscountGood()
//                            )
//                        })
//                }
//            }
//
//            viewKeyBoard.setonConfirmClick {
////                val reduceDollar = edtUsd.text.toString().toBigDecimalOrNull()
////                val reduceKhr = edtKhr.text.toString().toBigDecimalOrNull()
////                val reduceRate = edtPercent.text.toString().toDoubleOrNull()
////                if (orderInfo == null) {
////                    binding?.apply {
////                        modifyClickListener?.invoke(
////                            if (isVip()) 2 else 1,
////                            reduceRate,
////                            reduceDollar,
////                            reduceKhr
////                        )
////                    }
////                    dismissAllowingStateLoss()
////                } else {
////                    viewModel.setDiscountPrice(
////                        orderInfo?.orderNo,
////                        reduceDollar,
////                        reduceKhr,
////                        reduceRate,
////                        if (isVip()) 2 else 1,
////                        orderInfo?.getCurrentCoupon()?.id
////                    )
////                }
//
//            }
//
//            btnClose.setOnClickListener {
//                dismissAllowingStateLoss()
//            }
//
//
//            radioGroupPaymentMethod.setOnCheckedChangeListener { radioGroup, i ->
//                if (radioGroup.checkedRadioButtonId == R.id.radioNormal) {
//                    initSalePriceView(reduceDiscountDetailModel)
//                } else {
//                    initVipPriceView(reduceDiscountDetailModel)
//                }
//                binding?.apply {
//                    edtPercent.clearFocus()
//                    edtUsd.clearFocus()
//                    edtKhr.clearFocus()
//                    requireActivity().hideKeyboard(edtPercent)
//                    requireActivity().hideKeyboard(edtUsd)
//                    requireActivity().hideKeyboard(edtKhr)
//
//                }
//            }
//
//            edtPercent.disableCopy()
//            edtPercent.filters = arrayOf(CashierInputFilter(false, 100, true))
//            edtPercent.addTextChangedListener {
//                val result = TextInputUtil.amountInputUtil(edtPercent.toString())
//                if (result != edtPercent.toString()) {
//                    edtPercent.setText(result)
//                    edtPercent.setSelection(result.length)
//                }
//
//                if (isVip()) {
//                    reduceDiscountDetailModel?.reduceVipRate =
//                        edtPercent.text.toString().toDoubleOrNull()
//
//                } else {
//                    reduceDiscountDetailModel?.reduceRate =
//                        edtPercent.text.toString().toDoubleOrNull()
//                }
//
//                Timber.e("reduceDiscountDetailModel?.reduceRate  ${reduceDiscountDetailModel?.reduceRate}")
//                calculateFinal()
//            }
//            edtPercent.setOnClickListener {
//                root.post {
//                    hideKeyboard2()
//                }
//            }
//            edtPercent.setOnFocusChangeListener { view, b ->
//                Timber.e("edtPercent  $b")
//                root.post {
//                    hideKeyboard2()
//                }
//                viewKeyBoard.setCurrentEditText(null)
//                if (b) {
//                    viewKeyBoard.setIsInit(false)
//                    viewKeyBoard.setRange(0.0, 100.00)
//                    viewKeyBoard.setCurrentEditText(edtPercent)
//                }
//            }
//
//
//            edtUsd.disableCopy()
//            edtUsd.filters = arrayOf(CashierInputFilter(false, 100000000, false))
//            edtUsd.addTextChangedListener {
//                Timber.e("edtUsdedtUsd")
//                val result = TextInputUtil.amountInputUtil(edtUsd.toString())
//                if (result != edtUsd.toString()) {
//                    edtUsd.setText(result)
//                    edtUsd.setSelection(result.length)
//                }
//                //如果这个有值 则禁用掉edtKhr
//                if (edtUsd.text.isNullOrEmpty()) {
//                    edtKhr.setEnable(true)
//                } else {
//                    edtKhr.setEnable(false)
//                }
//
//                if (isVip()) {
//                    reduceDiscountDetailModel?.reduceVipDollar =
//                        if (edtUsd.text.isNullOrEmpty()) null else ((edtUsd.text.toString()
//                            .toBigDecimalOrNull()
//                            ?: BigDecimal.ZERO) * BigDecimal.valueOf(100)).toLong()
//                } else {
//                    reduceDiscountDetailModel?.reduceDollar =
//                        if (edtUsd.text.isNullOrEmpty()) null else ((edtUsd.text.toString()
//                            .toBigDecimalOrNull()
//                            ?: BigDecimal.ZERO) * BigDecimal.valueOf(100)).toLong()
//                }
//                calculateFinal()
//            }
//            edtUsd.setOnClickListener {
//                root.post {
//                    hideKeyboard2()
//                }
//            }
//            edtUsd.setOnFocusChangeListener { view, b ->
//                Timber.e("edtUsd  $b")
//                root.post {
//                    hideKeyboard2()
//                }
//                viewKeyBoard.setCurrentEditText(null)
//                if (b) {
//                    viewKeyBoard.setIsInit(false)
//                    viewKeyBoard.setRange(
//                        0.0,
//                        100000000.0 - 0.01
//                    )
//                    viewKeyBoard.setCurrentEditText(edtUsd)
//                }
//            }
//
//            edtKhr.disableCopy()
//            edtKhr.filters = arrayOf(CashierInputFilter(false, 100000000, false))
//            edtKhr.addTextChangedListener {
//
//                //如果这个有值 则禁用掉edtUsd
//                if (edtKhr.text.isNullOrEmpty()) {
//                    edtUsd.setEnable(true)
//                } else {
//                    edtUsd.setEnable(false)
//                }
//                Timber.e("data.reduceKhr  ${reduceDiscountDetailModel.toString()}")
//
//                if (isVip()) {
//                    reduceDiscountDetailModel?.reduceVipKhr =
//                        edtKhr.text.toString().toLongOrNull()
//                } else {
//
//                    reduceDiscountDetailModel?.reduceKhr =
//                        edtKhr.text.toString().toLongOrNull()
//                }
//                calculateFinal()
//            }
//            edtKhr.setOnClickListener {
//                root.post {
//                    hideKeyboard2()
//                }
//            }
//            edtKhr.setOnFocusChangeListener { view, b ->
//                Timber.e("edtUsd  $b")
//                root.post {
//                    hideKeyboard2()
//                }
//                viewKeyBoard.setCurrentEditText(null)
//                if (b) {
//                    viewKeyBoard.setIsInit(true)
//                    viewKeyBoard.setRange(
//                        0.0,
//                        100000000.0 - 1.0
//                    )
//                    viewKeyBoard.setCurrentEditText(edtKhr)
//                }
//            }
//        }
//
//    }
//
//
//    private fun initData(data: ReduceDiscountDetailModel?) {
//
//
//        binding?.apply {
//            //如果有会员价 显示 会员价选项
//            radioGroupPaymentMethod.isVisible = data?.vipTabFlag == true
//
//        }
//        binding?.apply {
//            if (data?.type == 2) {
//                radioGroupPaymentMethod.check(
//                    R.id.radioVip
//                )
//                initVipPriceView(data)
//            } else {
//                radioGroupPaymentMethod.check(
//                    R.id.radioNormal
//                )
//                initSalePriceView(data)
//            }
//
//        }
//
//    }
//
//    private fun initSalePriceView(data: ReduceDiscountDetailModel?) {
//        binding?.apply {
//            clearInputError()
//            totalPrice.text = "${data?.totalAmount?.priceFormatTwoDigitZero2()}"
//            totalPriceKhr.text =
//                "($1=KHR${data?.conversionRatio?.decimalFormatZeroDigit()})=KHR${data?.totalAmountKhr?.decimalFormatZeroDigit()}"
//
//            val rate = data?.reduceRate
//            edtPercent.setText(if (rate == null) "" else "${if (rate.isInt()) rate.toInt() else rate}")
//            edtPercent.setSelection(edtPercent.text?.length ?: 0)
//
//            edtUsd.setText(
//                if (data?.reduceDollar == null) "" else ((data.reduceDollar
//                    ?: 0)).priceFormatTwoDigitZero3()
//            )
//            edtUsd.setSelection(edtUsd.text?.length ?: 0)
//
//            edtKhr.setText(if (data?.reduceKhr == null) "" else "${data.reduceKhr}")
//            edtKhr.setSelection(edtKhr.text?.length ?: 0)
//            updateFinalPrice(data)
//        }
//    }
//
//    private fun initVipPriceView(data: ReduceDiscountDetailModel?) {
//        binding?.apply {
//            clearInputError()
//            totalPrice.text = "${data?.totalVipAmount?.priceFormatTwoDigitZero2()}"
//            totalPriceKhr.text =
//                "($1=KHR${data?.conversionRatio?.decimalFormatZeroDigit()})=KHR${data?.totalVipAmountKhr?.decimalFormatZeroDigit()}"
//
//            val rate = data?.reduceVipRate
//            edtPercent.setText(if (rate == null) "" else "${if (rate.isInt()) rate.toInt() else rate}")
//            edtPercent.setSelection(edtPercent.text?.length ?: 0)
//
//            edtUsd.setText(
//                if (data?.reduceVipDollar == null) "" else ((data.reduceVipDollar
//                    ?: 0)).priceFormatTwoDigitZero3()
//            )
//            edtUsd.setSelection(edtUsd.text?.length ?: 0)
//
//            edtKhr.setText(if (data?.reduceVipKhr == null) "" else "${data.reduceVipKhr}")
//            edtKhr.setSelection(edtKhr.text?.length ?: 0)
//
//            Timber.e("reduceVipDollar : ${data?.reduceVipDollar}")
//            updateFinalPrice(data)
//        }
//    }
//
//    private fun clearInputError() {
//        binding?.apply {
//
//            isInputPercentError(false)
//            isInputUSDError(false)
//            isInputKHRError(false)
//        }
//
//    }
//
//    private fun isVip(): Boolean {
//        var isVip = false
//        binding?.apply {
//            if (radioGroupPaymentMethod.checkedRadioButtonId == R.id.radioVip) {
//                isVip = true
//            }
//        }
//        return isVip
//    }
//
//    private fun updateFinalPrice(data: ReduceDiscountDetailModel?) {
//
//        binding?.apply {
//            if (isVip()) {
//                tvDiscountPercent.text =
//                    getString(R.string.discounts_percent, "${edtPercent.text.toString()}%")
//
//                if (data?.reduceVipRate == null && data?.reduceVipDollar == null && data?.reduceVipKhr == null) {
//                    llResult.isVisible = false
//                    viewKeyBoard.setConfirm(true)
////                    btnConfirm.setEnableWithAlpha(true)
//                    isInputPercentError(false)
//                    isInputUSDError(false)
//                    isInputKHRError(false)
//                } else {
//
//                    //校验数据减免金额数据是否合法
//                    //校验KHR
//
//
//                    if (data.reduceVipDollar == 0L) {
//                        isInputUSDError(
//                            true,
//                            getString(R.string.please_input_greater_than_zero)
//                        )
//                    } else if ((reduceDiscountDetailModel?.reduceVipRealPrice
//                            ?: 0) < 0 && data.reduceVipDollar != null
//                    ) {
//                        isInputUSDError(
//                            true,
//                            getString(R.string.discount_price_can_not_small_than_discount)
//                        )
//                    } else {
//                        isInputUSDError(false)
//                    }
//
//                    //KHR 只能输入100的倍数
//                    if (data.reduceVipKhr != null && data.reduceVipKhr == 0L) {
//
//                        isInputKHRError(
//                            true,
//                            getString(R.string.please_input_greater_than_zero)
//                        )
//
//                    } else if (data.reduceVipKhr != null && ((data.reduceVipKhr
//                            ?: 0) % 100) > 0
//                    ) {
//                        isInputKHRError(true, getString(R.string.discount_input_khr_cue))
//                    } else {
//                        if ((reduceDiscountDetailModel?.reduceVipRealPrice
//                                ?: 0) < 0 && data.reduceVipKhr != null
//                        ) {
//                            if (reduceDiscountDetailModel?.reduceVipRealPriceKhr == 0L) {
//                                reduceDiscountDetailModel?.reduceVipRealPrice = 0L
//                                isInputKHRError(
//                                    false
//                                )
//                            } else {
//                                isInputKHRError(
//                                    true,
//                                    getString(R.string.discount_price_can_not_small_than_discount)
//                                )
//                            }
//
//                        } else {
//
//                            isInputKHRError(false)
//                        }
//                    }
//
//
//                    if (data.reduceVipRate == 0.0) {
//                        isInputPercentError(
//                            true,
//                            getString(R.string.please_input_greater_than_zero)
//                        )
//                    } else {
//                        isInputPercentError(false)
//                    }
//
//
//                    if (textInputLayoutKhr.isErrorEnabled || textInputLayoutUsd.isErrorEnabled || textInputLayoutPercent.isErrorEnabled) {
////                        btnConfirm.setEnableWithAlpha(false)
//                        viewKeyBoard.setConfirm(false)
//                    } else {
////                        btnConfirm.setEnableWithAlpha(true)
//                        viewKeyBoard.setConfirm(true)
//                    }
//
//                    llResult.isVisible = true
//                    tvDiscountValue.text =
//                        if (data.reduceVipAmount != null) "- ${data.reduceVipAmount?.priceFormatTwoDigitZero2()}" else "-"
//
//                    tvReducePriceTitle.text = getString(R.string.amount_of_reduction_usd)
//                    if (data.reduceVipDollar == null && data.reduceVipKhr == null) {
//                        tvReducePrice.text = "-"
//                    } else if (data.reduceVipDollar == null && data.reduceVipKhr != null) {
//                        tvReducePrice.text =
//                            "- ៛${data.reduceVipKhr?.decimalFormatZeroDigit()}"
//                        tvReducePriceTitle.text = getString(R.string.amount_of_reduction_khr)
//                    } else if (data.reduceVipDollar != null && data.reduceVipKhr == null) {
//                        tvReducePrice.text =
//                            "- ${data.reduceVipDollar?.priceFormatTwoDigitZero2()}"
//                    }
//
//                    tvReduceRealPrice.text = if ((data.reduceVipRealPrice ?: 0) >= 0)
//                        "${data.reduceVipRealPrice?.priceFormatTwoDigitZero2()}"
//                    else
//                        "- ${data.reduceVipRealPrice?.times(-1)?.priceFormatTwoDigitZero2()}"
//
//                    tvReduceRealPriceKhr.isVisible = (data.reduceVipRealPriceKhr ?: 0) != 0L
//                    tvReduceRealPriceKhr.text = if ((data.reduceVipRealPriceKhr ?: 0) >= 0)
//                        "=៛${data.reduceVipRealPriceKhr?.decimalFormatZeroDigit()}"
//                    else
//                        "=- ៛${
//                            data.reduceVipRealPriceKhr?.times(-1)?.decimalFormatZeroDigit()
//                        }"
//
//
//                }
//            } else {
//                tvDiscountPercent.text =
//                    getString(R.string.discounts_percent, "${edtPercent.text.toString()}%")
//                Timber.e("data?.reduceRate  ${data?.reduceRate}")
//                if (data?.reduceRate == null && data?.reduceDollar == null && data?.reduceKhr == null) {
//                    llResult.isVisible = false
////                    btnConfirm.setEnableWithAlpha(true)
//                    viewKeyBoard.setConfirm(true)
//                    isInputPercentError(false)
//                    isInputUSDError(false)
//                    isInputKHRError(false)
//                } else {
//
//                    //校验数据减免金额数据是否合法
//                    //校验KHR
//
//                    if (data.reduceDollar == 0L) {
//                        isInputUSDError(
//                            true,
//                            getString(R.string.please_input_greater_than_zero)
//                        )
//                    } else if ((reduceDiscountDetailModel?.reduceRealPrice
//                            ?: 0) < 0 && data.reduceDollar != null
//                    ) {
//                        isInputUSDError(
//                            true,
//                            getString(R.string.discount_price_can_not_small_than_discount)
//                        )
//                    } else {
//                        isInputUSDError(false)
//                    }
//
//
//                    //KHR 只能输入100的倍数
//                    if (data.reduceKhr != null && ((data.reduceKhr ?: 0) % 100) > 0) {
//                        Timber.e("1111111111")
//                        isInputKHRError(true, getString(R.string.discount_input_khr_cue))
//                    } else if (data.reduceKhr != null && data.reduceKhr == 0L) {
//                        Timber.e("2222222")
//                        isInputKHRError(
//                            true,
//                            getString(R.string.please_input_greater_than_zero)
//                        )
//                    } else {
//                        if ((reduceDiscountDetailModel?.reduceRealPrice
//                                ?: 0) < 0 && data.reduceKhr != null
//                        ) {
//                            if (reduceDiscountDetailModel?.reduceRealPriceKhr == 0L) {
//                                //防止换算的时候美元是负数
//                                reduceDiscountDetailModel?.reduceRealPrice = 0L
//                                isInputKHRError(
//                                    false,
//                                )
//                            } else {
//                                Timber.e("3333333  ${reduceDiscountDetailModel?.reduceRealPriceKhr}")
//                                isInputKHRError(
//                                    true,
//                                    getString(R.string.discount_price_can_not_small_than_discount)
//                                )
//                            }
//
//                        } else {
//                            isInputKHRError(false)
//                        }
//                    }
//
//
//                    if (data.reduceRate == 0.0) {
//                        isInputPercentError(
//                            true,
//                            getString(R.string.please_input_greater_than_zero)
//                        )
//                    } else {
//                        isInputPercentError(false)
//                    }
//
//                    if (textInputLayoutKhr.isErrorEnabled || textInputLayoutUsd.isErrorEnabled || textInputLayoutPercent.isErrorEnabled) {
////                        btnConfirm.setEnableWithAlpha(false)
//                        viewKeyBoard.setConfirm(false)
//                    } else {
////                        btnConfirm.setEnableWithAlpha(true)
//                        viewKeyBoard.setConfirm(true)
//                    }
//
//
//                    llResult.isVisible = true
//                    tvDiscountValue.text =
//                        if (data.reduceAmount != null) "-${data.reduceAmount?.priceFormatTwoDigitZero2()}" else "-"
//
//                    tvReducePriceTitle.text = getString(R.string.amount_of_reduction_usd)
//                    if (data.reduceDollar == null && data.reduceKhr == null) {
//                        tvReducePrice.text = "-"
//                    } else if (data.reduceDollar == null && data.reduceKhr != null) {
//                        tvReducePrice.text =
//                            "- ៛${data.reduceKhr?.decimalFormatZeroDigit()}"
//                        tvReducePriceTitle.text =
//                            getString(R.string.amount_of_reduction_khr)
//                    } else if (data.reduceDollar != null && data.reduceKhr == null) {
//                        tvReducePrice.text =
//                            "- ${data.reduceDollar?.priceFormatTwoDigitZero2()}"
//                    }
//
//
//                    tvReduceRealPrice.text = if ((data.reduceRealPrice
//                            ?: 0) >= 0
//                    ) "${data.reduceRealPrice?.priceFormatTwoDigitZero2()}" else "- ${
//                        data.reduceRealPrice?.times(
//                            -1
//                        )?.priceFormatTwoDigitZero2()
//                    }"
//
//                    tvReduceRealPriceKhr.isVisible = (data.reduceRealPriceKhr ?: 0) != 0L
//                    tvReduceRealPriceKhr.text = if ((data.reduceRealPriceKhr
//                            ?: 0) >= 0
//                    ) "=៛${data.reduceRealPriceKhr?.decimalFormatZeroDigit()}" else "=- ៛${
//                        data.reduceRealPriceKhr?.times(
//                            -1
//                        )?.decimalFormatZeroDigit()
//                    }"
//
//
//                }
//            }
//        }
//    }
//
//
//    //最终计算相关金额
//    private fun calculateFinal() {
//        if (reduceDiscountDetailModel == null) {
//            return
//        }
//        binding?.apply {
//
//            llResult.isVisible = true
//            if (isVip()) {
//                val percent = reduceDiscountDetailModel?.reduceVipRate
//
//                //计算减免折扣百分比后的价格  四舍五入
//                val afterDiscountPercentPrice = if (percent == null) null else
//                    BigDecimal(
//                        (reduceDiscountDetailModel?.totalVipAmount ?: 0) * (percent
//                                / 100.0)
//                    ).halfUp(0).toLong()
//
//
//                Timber.e("afterDiscountPercentPrice:${afterDiscountPercentPrice}")
//                reduceDiscountDetailModel?.reduceVipAmount = afterDiscountPercentPrice
//                val afterDiscountUsd = reduceDiscountDetailModel?.reduceVipDollar
//
//                reduceDiscountDetailModel?.reduceVipDollar = afterDiscountUsd
//
//                val afterDiscountKhr = reduceDiscountDetailModel?.reduceVipKhr
//
//                reduceDiscountDetailModel?.reduceVipKhr = afterDiscountKhr
//
//                var afterDiscount =
//                    (reduceDiscountDetailModel?.totalVipAmount
//                        ?: 0) - (afterDiscountPercentPrice ?: 0)
//                if (afterDiscountUsd != null) {
//                    afterDiscount -= afterDiscountUsd
//                }
//
//                if (afterDiscountKhr != null) {
//                    afterDiscount -= BigDecimal(
//                        afterDiscountKhr.toDouble().div(
//                            (reduceDiscountDetailModel?.conversionRatio
//                                ?: 4100)
//                        )
//                    ).halfUp(2).times(BigDecimal(100)).toLong()
//                }
//
//                reduceDiscountDetailModel?.reduceVipRealPrice = afterDiscount
//                reduceDiscountDetailModel?.reduceVipRealPriceKhr = KHRroundToTwoDecimalPlaces(
//                    afterDiscount * (reduceDiscountDetailModel?.conversionRatio ?: 0)
//                )
//
//            } else {
//                val percent =
//                    reduceDiscountDetailModel?.reduceRate
//
//                //计算减免折扣百分比后的价格  四舍五入
//                val afterDiscountPercentPrice = if (percent == null) null else
//                    BigDecimal(
//                        (reduceDiscountDetailModel?.totalAmount ?: 0) * (percent
//                                / 100.0)
//                    ).halfUp(0).toLong()
//
//                Timber.e("afterDiscountPercentPrice:${afterDiscountPercentPrice}   reduceDollar:${reduceDiscountDetailModel?.reduceDollar}")
//                reduceDiscountDetailModel?.reduceAmount = afterDiscountPercentPrice
//                val afterDiscountUsd = reduceDiscountDetailModel?.reduceDollar
//                reduceDiscountDetailModel?.reduceDollar = afterDiscountUsd
//
//                val afterDiscountKhr =
//                    reduceDiscountDetailModel?.reduceKhr
//                reduceDiscountDetailModel?.reduceKhr = afterDiscountKhr
//
//                var afterDiscount =
//                    (reduceDiscountDetailModel?.totalAmount ?: 0) - (afterDiscountPercentPrice
//                        ?: 0)
//                if (afterDiscountUsd != null) {
//                    afterDiscount -= afterDiscountUsd
//                }
//
//                if (afterDiscountKhr != null) {
//                    afterDiscount -= BigDecimal(
//                        afterDiscountKhr.toDouble().div(
//                            (reduceDiscountDetailModel?.conversionRatio
//                                ?: 1)
//                        )
//                    ).halfUp(2).times(BigDecimal(100)).toLong()
//                }
//                Timber.e("afterDiscount  $afterDiscount")
//                reduceDiscountDetailModel?.reduceRealPrice = afterDiscount
//                reduceDiscountDetailModel?.reduceRealPriceKhr = KHRroundToTwoDecimalPlaces(
//                    afterDiscount * (reduceDiscountDetailModel?.conversionRatio ?: 0)
//                )
////                    ceil(
////                        afterDiscount * (reduceDiscountDetailModel?.conversionRatio
////                            ?: 0).div(10000.0)
////                    ).toLong() * 100
//            }
//
//            updateFinalPrice(reduceDiscountDetailModel)
//
//        }
//    }
//
//    private fun isInputPercentError(isError: Boolean = false, errorMsg: String? = "") {
//        binding?.apply {
//            textInputLayoutPercent.error = errorMsg
//            textInputLayoutPercent.isErrorEnabled = isError
//            val lp = tvUnit.layoutParams as FrameLayout.LayoutParams
//            lp.marginEnd = DisplayUtils.dp2px(requireContext(), if (isError) 40f else 12f)
//            tvUnit.layoutParams = lp
//        }
//    }
//
//    private fun isInputUSDError(isError: Boolean = false, errorMsg: String? = "") {
//        binding?.apply {
//            textInputLayoutUsd.error = errorMsg
//            textInputLayoutUsd.isErrorEnabled = isError
//        }
//    }
//
//    private fun isInputKHRError(isError: Boolean = false, errorMsg: String? = "") {
//        binding?.apply {
//            textInputLayoutKhr.error = errorMsg
//            textInputLayoutKhr.isErrorEnabled = isError
//        }
//    }
//
//    override fun onResume() {
//        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
//            val screenWidth = (displayMetrics.widthPixels * 0.7).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
//    }
//
//    //瑞尔的四舍五入
//    fun KHRroundToTwoDecimalPlaces(price: Long): Long {
//        return BigDecimal(
//            price
//                .div(10000.0)
//        ).halfUp(0).toLong() * 100
//    }
//
//    //美元四舍五入
//    fun USDroundToTwoDecimalPlaces(price: Double): Long {
//        return BigDecimal(
//            price
//        ).halfUp(2).toLong()
//    }
//}