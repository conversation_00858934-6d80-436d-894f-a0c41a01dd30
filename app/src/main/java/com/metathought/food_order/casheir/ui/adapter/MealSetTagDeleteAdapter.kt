package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.dboy.chips.ChipsLayoutManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.SpecificationTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTag
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetChooseItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.MealsetTagDeleteItemBinding
import com.metathought.food_order.casheir.databinding.SpecificationMainItemBinding
import timber.log.Timber.Forest.tag


class MealSetTagDeleteAdapter(
    var mealSetGroup: MealSetGroup? = null,
    var list: List<MealSetChooseItem>,
    val context: Context,
    val onClickCallback: (MealSetChooseItem) -> Unit
) : RecyclerView.Adapter<MealSetTagDeleteAdapter.MealSetTagDeleteViewHolder>() {

    private val maxNum = mutableListOf<Int>()

    inner class MealSetTagDeleteViewHolder(val binding: MealsetTagDeleteItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: MealSetChooseItem?, position: Int) {
            resource?.let { it1 ->
                binding.apply {
                    tvTag.text = context.getString(
                        R.string.mealset_good_specifications,
                        "${position + 1}",
                        resource.getGoodsTagStr()
                    )

                    tvQTY.text = "${resource.selectNum}"

                    //这边按钮不隐藏 不能用就禁用
                    imgMinus.isEnabled = resource.selectNum != 0
                    imgMinus.setImageResource(if (resource.selectNum != 0) R.drawable.ic_minus else R.drawable.ic_minus_disable)

                    val isEnable = resource.selectNum < maxNum[position]
                    imgPlus.isEnabled =
                        isEnable  //  mealSetGroup?.isMaxNum() == false
                    imgPlus.setImageResource(if (isEnable) R.drawable.ic_add else R.drawable.ic_add_disable)

                    imgMinus.setOnClickListener {
                        resource.selectNum -= 1
                        notifyDataSetChanged()
                    }

                    imgPlus.setOnClickListener {
                        resource.selectNum += 1
                        notifyDataSetChanged()
                    }

                }
            }
        }
    }

    fun replace(list: List<MealSetChooseItem>) {
        list.forEach {
            maxNum.add(it.selectNum)
        }
        this.list = list
        notifyDataSetChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MealSetTagDeleteViewHolder {
        return MealSetTagDeleteViewHolder(
            MealsetTagDeleteItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        return list.count()
    }

    override fun onBindViewHolder(holder: MealSetTagDeleteViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


}