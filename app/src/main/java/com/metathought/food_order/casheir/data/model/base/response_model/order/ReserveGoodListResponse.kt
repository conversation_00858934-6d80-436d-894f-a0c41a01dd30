package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName

data class ReserveGoodListResponse(

    val goodsInfo: Map<String, StoreIndexBaseVo>,

    val role: String,

    val tableName: String,
    val lastChangeDate: String,

    val menuChange: <PERSON>olean,
)


data class StoreIndexBaseVo(
    //菜品list
    @SerializedName("goodsReserveList", alternate = ["goodsList"])
    val goodsReserveList: Any? = null,
    //菜品分组List
    val groupList: ArrayList<Group>? = null,

    //赠品活动列表
    val ongoingGiftPromotionList: ArrayList<PromotionActivity>? = null,

    //分店店铺
    val store: Store,
)