package com.metathought.food_order.casheir.ui.work_handover

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.text.InputFilter
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.databinding.DialogStartClassesBinding
import com.metathought.food_order.casheir.extension.NumberInputFilter
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.setDecimalFormat
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setMaxLength
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 开班与备用金
 */
@AndroidEntryPoint
class StartClassesDialog : BaseDialogFragment() {
    private var binding: DialogStartClassesBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    private val viewModel: StartClassesViewModel by viewModels()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogStartClassesBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbProgress?.isVisible = true
                    binding?.btnSaveImprest?.setEnable(false)
                    binding?.btnStartClasses?.setEnable(false)
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbProgress.isVisible = false
                        btnSaveImprest.setEnable(true)
                        btnStartClasses.setEnable(true)
                    }
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        pbProgress.isVisible = false
                        btnSaveImprest.setEnable(true)
                        btnStartClasses.setEnable(true)
                    }
                    dismissCurrentDialog()

                }
            }
        }
        viewModel.shiftLogtState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbProgress?.isVisible = true
                }

                is ApiResponse.Error -> {
                    binding?.pbProgress?.isVisible = false
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.pbProgress?.isVisible = false
                    it.data.data?.let {
                        binding?.apply {
                            edtUSD.setText(it.openingCashUsd?.decimalFormatTwoDigitZero())
                            edtKhmer.setText(it.openingCashKhr?.decimalFormatTwoDigit())
                            edtRemark.setText(it.remark)
                        }
                    }
                }
            }
        }
    }

    override fun dismiss() {
        //强制隐藏 防止 有没隐藏的情况
        hideKeyboard2()
        super.dismiss()
    }

    private fun initData() {
        val typeStr = arguments?.getString(TYPE) ?: Type.CLASSES_AND_IMPREST.name
        val type = Type.valueOf(typeStr)
        when (type) {
            Type.START_CLASSES -> {
                (binding?.btnStartClasses?.layoutParams as? MarginLayoutParams)?.let {
                    it.marginStart = 0
                    it.marginEnd = 0

                }
                binding?.btnSaveImprest?.isVisible = false
                binding?.btnStartClasses?.isVisible = true
            }

            Type.SAVE_IMPREST -> {
                (binding?.btnSaveImprest?.layoutParams as? MarginLayoutParams)?.let {
                    it.marginStart = 0
                    it.marginEnd = 0
                }
                binding?.btnSaveImprest?.isVisible = true
                binding?.btnStartClasses?.isVisible = false
            }

            Type.CLASSES_AND_IMPREST -> {

            }
        }
        binding?.apply {
            btnSaveImprest.setEnable(false)
            edtUSD.filters = arrayOf(NumberInputFilter(7, 2))
            edtKhmer.filters = arrayOf(NumberInputFilter(7, 0))
        }
        if (MainDashboardFragment.CURRENT_USER?.isNeedStartShift != false) {
            //没开班前需要获取之前缓存的开班信息
            viewModel.getShiftLog()
        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            btnSaveImprest.setOnClickListener {
                viewModel.saveOrUpdateOpeningCash(
                    edtUSD.text.toString(),
                    edtKhmer.text.toString(),
                    edtRemark.text.toString(),
                )
            }
            btnStartClasses.setOnClickListener {
                viewModel.startClasses(
                    edtUSD.text.toString(),
                    edtKhmer.text.toString(),
                    edtRemark.text.toString(),
                )
            }
            edtKhmer.addTextChangedListener { s ->
                s?.let {
                    if (it.isNotEmpty() && it.toString().first() == '0') {
                        if (it.length > 1) {
                            s.replace(0, 2, it.toString()[1].toString())
                        }
                    }
                }
                checkLogoutEnable()
            }
            edtUSD.addTextChangedListener { s ->
                s?.let {
                    if (it.isNotEmpty() && it.toString().first() == '.') {
                        s.replace(0, 1, "0.")
                    }
                    if (it.isNotEmpty() && it.toString().first() == '0') {
                        if (it.length > 1 && it.toString()[1] != '.') {
                            s.replace(0, 2, it.toString()[1].toString())
                        }
                    }
                }
                checkLogoutEnable()
            }
            edtRemark.addTextChangedListener { s ->
                checkLogoutEnable()
            }
//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    private fun checkLogoutEnable() {
        binding?.apply {
            btnSaveImprest.setEnable(
                edtUSD.text.toString().isNotEmpty() || edtKhmer.text.toString()
                    .isNotEmpty() || edtRemark.text.toString().isNotEmpty()
            )
        }
    }

    enum class Type {
        START_CLASSES,
        SAVE_IMPREST,
        CLASSES_AND_IMPREST
    }

    companion object {
        private const val START_CLASSES_DIALOG = "START_CLASSES_DIALOG"
        private const val TYPE = "type"


        fun showDialog(
            fragmentManager: FragmentManager,
            type: Type = Type.CLASSES_AND_IMPREST,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(START_CLASSES_DIALOG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener, type)
            fragment.show(fragmentManager, START_CLASSES_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(START_CLASSES_DIALOG) as? StartClassesDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
            type: Type,
        ): StartClassesDialog {
            val args = Bundle()
            args.putString(TYPE, type.name)
            val fragment = StartClassesDialog()
            fragment.positiveButtonListener = iAgreeListener
            fragment.arguments = args
            return fragment
        }
    }
}
