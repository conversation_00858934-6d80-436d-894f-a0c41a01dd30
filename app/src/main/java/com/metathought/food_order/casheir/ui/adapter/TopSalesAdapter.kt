package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.SalesRanking
import com.metathought.food_order.casheir.databinding.TopSalesListItemBinding


class TopSalesAdapter(
    private var topProducts: ArrayList<SalesRanking?>,
    val context: Context) : RecyclerView.Adapter<TopSalesAdapter.TopSalesViewHolder>() {
    var maxValue = topProducts.sortedBy { it?.productNum }.reversed().firstOrNull()?.productNum
    inner class TopSalesViewHolder(val binding: TopSalesListItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(saleRanking: SalesRanking?) {
            binding.apply {
                tvValue.text = saleRanking?.productNum.toString()
                tvTitleName.text = saleRanking?.productName
                pbSaleAmount.max = maxValue ?: 0
                pbSaleAmount.progress = saleRanking?.productNum ?: 0
            }
        }
    }



    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TopSalesViewHolder {
        return TopSalesViewHolder(
            TopSalesListItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return topProducts.size
    }

    override fun onBindViewHolder(holder: TopSalesViewHolder, position: Int) {
        holder.bind(topProducts[position])
    }

    fun updateItems(newItem : ArrayList<SalesRanking?>){
        topProducts.clear()
        topProducts.addAll(newItem)
        maxValue = topProducts.sortedBy { it?.productNum }.reversed().firstOrNull()?.productNum
        notifyDataSetChanged()
    }
}