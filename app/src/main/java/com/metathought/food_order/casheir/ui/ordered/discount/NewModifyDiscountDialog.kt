package com.metathought.food_order.casheir.ui.ordered.discount

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.Toast
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.constant.WholeDiscountCalculationTypeEnum
import com.metathought.food_order.casheir.constant.WholeDiscountType
import com.metathought.food_order.casheir.constant.WholeReduceType
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.ReduceDiscountDetailRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ReduceDiscountDetailModel
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.DialogNewModifyDiscountBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.disableCopy
import com.metathought.food_order.casheir.extension.getRedStar
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.halfUpKhr
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.showKeyboard
import com.metathought.food_order.casheir.filter.CashierInputFilter
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.COUPON_EXPIRED
import com.metathought.food_order.casheir.network.COUPON_LOCKED
import com.metathought.food_order.casheir.network.COUPON_UNEABLE
import com.metathought.food_order.casheir.network.COUPON_USED
import com.metathought.food_order.casheir.network.GOODS_HAS_BEEN_MERGE_OR_SPLIT
import com.metathought.food_order.casheir.network.ORDER_STATUS_ERROR
import com.metathought.food_order.casheir.ui.adapter.PreconfigurationDiscountListAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.min


/**
 *<AUTHOR>
 *@time  2024/7/4
 *@desc
 **/


@AndroidEntryPoint
class NewModifyDiscountDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "NewModifyDiscountDialog"

        private const val DETAIL_MODEL = "DETAIL_MODEL"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderInfo: OrderedInfoResponse? = null,
            reduceDiscountDetailModel: ReduceDiscountDetailModel? = null,
            cartGoods: List<GoodsRequest>? = null,  //购物车菜品,
//            couponCode: String? = null,  //先付款 优惠券码
            takeOutPlatformModel: TakeOutPlatformModel? = null,
            modifyClickListener: ((
                reduceDiscountDetailRequest: ReduceDiscountDetailRequest?
            ) -> Unit),
            errorListener: ((Int?) -> Unit)
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                orderInfo,
                reduceDiscountDetailModel,
                cartGoods,
//                couponCode,
                takeOutPlatformModel,
                modifyClickListener = modifyClickListener, errorListener = errorListener
            )

            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentModifyDiscountDialog(fragmentManager: FragmentManager): NewModifyDiscountDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? NewModifyDiscountDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? NewModifyDiscountDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            orderInfo: OrderedInfoResponse? = null,
            model: ReduceDiscountDetailModel? = null,
            cartGoods: List<GoodsRequest>? = null,
//            couponCode: String? = null,  //先付款 优惠券码
            takeOutPlatformModel: TakeOutPlatformModel? = null,//外卖平台
            modifyClickListener: ((reduceDiscountDetailRequest: ReduceDiscountDetailRequest?) -> Unit),
            errorListener: ((Int?) -> Unit)
        ): NewModifyDiscountDialog {
            return NewModifyDiscountDialog().apply {
                val args = Bundle()
                args.putParcelable(DETAIL_MODEL, model)
                this.orderInfo = orderInfo
                this.arguments = args
                this.cartGoods = cartGoods
//                this.couponCode = couponCode
                this.takeOutPlatformModel = takeOutPlatformModel
                this.modifyClickListener = modifyClickListener
                this.errorListener = errorListener
            }
        }
    }

    //是否自定义
    private var isCustomize = false

    //整单减免 减免活动类型
    private var wholeDiscountType = WholeDiscountType.FIXED_AMOUNT.id

    private var wholeDiscountUnitType = WholeReduceType.NONE.id

    private var modifyClickListener: ((reduceDiscountDetailRequest: ReduceDiscountDetailRequest?) -> Unit)? =
        null

    private var errorListener: ((Int?) -> Unit)? = null

    private var binding: DialogNewModifyDiscountBinding? = null

    private val viewModel: ModifyDiscountViewModel by viewModels()

    private var orderInfo: OrderedInfoResponse? = null
    private var cartGoods: List<GoodsRequest>? = null

    private var preconfigurationDiscountListAdapter: PreconfigurationDiscountListAdapter? = null

    private var reduceDiscountDetailModel: ReduceDiscountDetailModel? = null

    private var takeOutPlatformModel: TakeOutPlatformModel? = null

    private var minInput = BigDecimal(0.01).halfUp(2)

    private val keyBoardRunnable = Runnable {
        try {
            binding?.apply {
                requireActivity().showKeyboard(edtRemark)
            }
        } catch (e: Exception) {

        }
    }

    fun getCurrentOrderNo(): String? {
        return orderInfo?.orderNo
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogNewModifyDiscountBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        isCustomize = false

        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initListener()
        initView()
        initObserver()
    }

    private fun initView() {
        binding?.apply {
            context?.let {
                reduceDiscountDetailModel =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        arguments?.getParcelable(
                            DETAIL_MODEL,
                            ReduceDiscountDetailModel::class.java
                        )
                    } else {
                        arguments?.getParcelable(DETAIL_MODEL)
                    }
                Timber.e("reduceDiscountDetailModel => ${reduceDiscountDetailModel.toString()}")
                preconfigurationDiscountListAdapter =
                    PreconfigurationDiscountListAdapter(listOf()) {
                        if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                            if (viewModel.localReduce?.id == it.id) {
                                viewModel.localReduce = null
                            } else {
                                viewModel.localReduce = it
                            }

                        } else if (wholeDiscountType == WholeDiscountType.PERCENTAGE.id) {
                            if (viewModel.localDiscount?.id == it.id) {
                                viewModel.localDiscount = null
                            } else {
                                viewModel.localDiscount = it
                            }
                        }

                        preconfigurationDiscountListAdapter?.setSelectItem(it)

                        calculateFinal()
                        calculateFinalVip()
                    }
                viewModel.getDetail(orderInfo?.orderNo)
            }

        }
    }


    private fun initObserver() {
        viewModel.uiModeState.observe(viewLifecycleOwner) { state ->
            when (state.result) {
                is ApiResponse.Success -> {
                    val data = state.result.data
                    if (orderInfo == null) {
                        if (takeOutPlatformModel == null) {
                            //非外卖平台要进这段逻辑
                            val noDiscountList =
                                cartGoods?.filter { it.goods?.isDiscountItemWhitelisting() == true }
                            noDiscountList?.forEach {
                                /**
                                 * 不参与折扣的只刨去本身的价格
                                 */
                                reduceDiscountDetailModel?.totalAmount =
                                    (reduceDiscountDetailModel?.totalAmount
                                        ?: 0) - it.totalDiscountPrice() //
                                reduceDiscountDetailModel?.totalVipAmount =
                                    (reduceDiscountDetailModel?.totalVipAmount
                                        ?: 0) - it.totalVipPrice() //
                            }
                            binding?.apply {
                                ivWarn.isVisible = !noDiscountList.isNullOrEmpty()
                            }
                        }

                        if (data.wholeDiscountCalculationType == WholeDiscountCalculationTypeEnum.WITHOUT_VAT.id) {
                            //如果是类型 2 整单减免不包含vat
                            reduceDiscountDetailModel?.totalAmount =
                                (reduceDiscountDetailModel?.totalAmount
                                    ?: 0) - (reduceDiscountDetailModel?.totalVatPrice ?: 0L) //
                            reduceDiscountDetailModel?.totalVipAmount =
                                (reduceDiscountDetailModel?.totalVipAmount
                                    ?: 0) - (reduceDiscountDetailModel?.totalVipVatPrice
                                    ?: 0L)
                        }


                        //销售价
                        reduceDiscountDetailModel?.conversionRatio = data.conversionRatio
                        reduceDiscountDetailModel?.totalAmountKhr =
                            (reduceDiscountDetailModel?.totalAmount ?: 0).times(
                                data.conversionRatio ?: FoundationHelper.conversionRatio
                            ).div(100).halfUpKhr()

                        //会员价
                        if (reduceDiscountDetailModel?.totalVipAmount != null) {
                            reduceDiscountDetailModel?.totalVipAmountKhr =
                                (reduceDiscountDetailModel?.totalVipAmount ?: 0).times(
                                    data.conversionRatio ?: FoundationHelper.conversionRatio
                                ).div(100).halfUpKhr()
                        }

                        viewModel.getOrderDiscountInfoList(
                            cartGoods = if (takeOutPlatformModel == null) cartGoods?.filter { it.goods?.isDiscountItemWhitelisting() != true } else cartGoods,
                        )

                    } else {
                        reduceDiscountDetailModel = data
                        if (reduceDiscountDetailModel?.vipTabFlag == null) {
                            reduceDiscountDetailModel?.vipTabFlag =
                                orderInfo?.isShowVipPrice() == true
                        }
                        if (reduceDiscountDetailModel?.conversionRatio == null) {
                            reduceDiscountDetailModel?.conversionRatio = orderInfo?.conversionRatio
                        }

//                        if (reduceDiscountDetailModel?.discountReduceActivityId == null) {
//                            reduceDiscountDetailModel?.discountReduceActivityId = orderInfo?.discountReduceActivity?.id
//                        }


                        reduceDiscountDetailModel?.discountReduceInfo =
                            orderInfo?.discountReduceActivity
                        reduceDiscountDetailModel?.discountType = orderInfo?.discountType

                        if (orderInfo?.isSetWholeDiscount() == false) {
                            //切换优惠券的时候会重置  所以这里要重置
                            reduceDiscountDetailModel?.type = null

                            reduceDiscountDetailModel?.reduceRate = null
                            reduceDiscountDetailModel?.reduceAmount = null
                            reduceDiscountDetailModel?.reduceKhr = null
                            reduceDiscountDetailModel?.reduceDollar = null

                            reduceDiscountDetailModel?.reduceVipRate = null
                            reduceDiscountDetailModel?.reduceVipAmount = null
                            reduceDiscountDetailModel?.reduceVipKhr = null
                            reduceDiscountDetailModel?.reduceVipDollar = null
                            reduceDiscountDetailModel?.reduceReason = null
                            reduceDiscountDetailModel?.discountReduceActivityId = null
                            reduceDiscountDetailModel?.discountType = null
                            reduceDiscountDetailModel?.discountReduceInfo = null
                        }

                        //输入框不显示0，防止服务端返回的0，如果为0 置为null
                        if (reduceDiscountDetailModel?.reduceRate == 0.0) {
                            reduceDiscountDetailModel?.reduceRate = null
                        }
                        //防止服务端返回的0
                        if (reduceDiscountDetailModel?.reduceVipRate == 0.0) {
                            reduceDiscountDetailModel?.reduceVipRate = null
                        }


                        //获取只带优惠券后的金额
                        val totalAmount =
                            orderInfo?.getParticipatingWholeDiscountsAmount()
                        reduceDiscountDetailModel?.totalAmount = totalAmount
                        reduceDiscountDetailModel?.totalAmountKhr =
                            (reduceDiscountDetailModel?.totalAmount ?: 0).times(
                                orderInfo?.conversionRatio ?: FoundationHelper.conversionRatio
                            ).div(100).halfUpKhr()

                        val totalVipAmount =
                            orderInfo?.getVipParticipatingWholeDiscountsAmount()
                        reduceDiscountDetailModel?.totalVipAmount = totalVipAmount

                        reduceDiscountDetailModel?.totalVipAmountKhr =
                            (reduceDiscountDetailModel?.totalVipAmount ?: 0).times(
                                orderInfo?.conversionRatio ?: FoundationHelper.conversionRatio
                            ).div(100).halfUpKhr()

                        binding?.apply {
                            ivWarn.isVisible =
                                !orderInfo?.goods?.filter { it.isDiscountItemWhitelisting() == true }
                                    .isNullOrEmpty() && takeOutPlatformModel == null
                        }

                        viewModel.getOrderDiscountInfoList(orderNo = orderInfo?.orderNo)
                    }

                    binding?.apply {
                        progressBar.isVisible = false
                    }
                }

                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                        llContent.isInvisible = true
                    }
                }

                is ApiResponse.Error -> {
                    if (!state.result.message.isNullOrEmpty()) {
                        Toast.makeText(requireActivity(), state.result.message, Toast.LENGTH_LONG)
                            .show()
                    }
                    errorListener?.invoke(state.result.errorCode)
                    dismissCurrentDialog()
                }

                else -> {

                }
            }

            state.discountList?.apply {
                if (state.discountList is ApiResponse.Loading) {
                    binding?.apply {
                        progressBar.isVisible = true
                    }
                }

                if (state.discountList is ApiResponse.Success) {
                    binding?.apply {
                        progressBar.isVisible = false
                        llContent.isVisible = true
                        rvList.adapter = preconfigurationDiscountListAdapter

                        var model: DiscountReduceInfo? =
                            state.discountList.data.firstOrNull { it.id == reduceDiscountDetailModel?.discountReduceActivityId }
                        var tmp: DiscountReduceInfo? = null
                        if (model == null) {
                            //如果列表没返回 把订单内的model 插入
                            if (orderInfo?.discountReduceActivity != null) {
                                tmp = orderInfo?.discountReduceActivity
                                viewModel.inertDataToList(orderInfo?.discountReduceActivity!!)
                            }
                        }

                        if (tmp != null) {
                            model = tmp
                        }

                        if (model == null) {
                            reduceDiscountDetailModel?.discountReduceActivityId = null
                            reduceDiscountDetailModel?.discountReduceInfo = null
                            reduceDiscountDetailModel?.discountType = null
                            wholeDiscountUnitType =
                                reduceDiscountDetailModel?.type ?: WholeReduceType.CUSTOMIZE_USD.id
                        } else {
                            wholeDiscountType = model.type ?: WholeDiscountType.FIXED_AMOUNT.id
                            if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                                viewModel.localReduce = model
                            } else {
                                viewModel.localDiscount = model
                            }
                        }

                        initData(reduceDiscountDetailModel)

                        initList()
                    }
                }

                if (state.discountList is ApiResponse.Error) {
                    binding?.apply {
                        progressBar.isVisible = false
                        llContent.isVisible = true
                        initData(reduceDiscountDetailModel)
                        initList()
                    }
                }
            }

            state.confirm?.apply {
                if (state.confirm is ApiResponse.Success) {
                    binding?.apply {
                        Timber.e("整单减免设置成功")
                        var reduceDollar = reduceDiscountDetailModel?.reduceDollar

                        var reduceVipDollar = reduceDiscountDetailModel?.reduceVipDollar

                        var reduceKhr = reduceDiscountDetailModel?.reduceKhr

                        var reduceVipKhr = reduceDiscountDetailModel?.reduceVipKhr

                        if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_USD.id) {
                            reduceKhr = null
                            reduceVipKhr = null
                        } else if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id) {
                            reduceDollar = null
                            reduceVipDollar = null
                        }

                        val reduceRate = edtPercent.text.toString().toDoubleOrNull()
                        val reduceReason = edtRemark.text.toString()
                        val reduceType = if (isCustomize) {
                            wholeDiscountUnitType
                        } else {
                            WholeReduceType.REDUCE_ACTIVITY.id
                        }
                        modifyClickListener?.invoke(
                            ReduceDiscountDetailRequest(
                                reduceType = reduceType,
                                reduceRate = reduceRate,
                                reduceDollar = reduceDollar,
                                reduceVipDollar = reduceVipDollar,
                                reduceKhr = reduceKhr,
                                reduceVipKhr = reduceVipKhr,
                                discountType = if (radioReasonType.checkedRadioButtonId == R.id.radioReasonDiscount) 0 else 1,
                                reduceReason = reduceReason,
                                discountReduceActivityId = reduceDiscountDetailModel?.discountReduceActivityId
                            )
                        )
                    }
                    Timber.e("关闭弹窗")
                    dismissCurrentDialog()
                }
                if (state.confirm is ApiResponse.Error) {
                    if (!state.confirm.message.isNullOrEmpty()) {
                        Toast.makeText(requireActivity(), state.confirm.message, Toast.LENGTH_LONG)
                            .show()
                    }
                    binding?.apply {
                        progressBar.isVisible = false
                        llContent.isVisible = true
                    }

                    if (state.confirm.errorCode == ORDER_STATUS_ERROR || state.confirm.errorCode == COUPON_USED || state.confirm.errorCode == COUPON_LOCKED || state.confirm.errorCode == COUPON_UNEABLE || state.confirm.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT || state.confirm.errorCode == COUPON_EXPIRED) {
                        errorListener?.invoke(state.confirm.errorCode)
                        dismissCurrentDialog()
                    }
                }
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            tvCustomize.setOnClickListener {
                isCustomize = true

                if (reduceDiscountDetailModel?.type == WholeReduceType.CUSTOMIZE_KHR.id) {
                    wholeDiscountUnitType = WholeReduceType.CUSTOMIZE_KHR.id
                } else {
                    wholeDiscountUnitType = WholeReduceType.CUSTOMIZE_USD.id
                }
                tvCustomize.isVisible = false
                llBottom.isVisible = false
                viewKeyBoard.isVisible = true
                viewChooseList.isVisible = false
                topBar.setTitle(getString(R.string.customize))
                onChangeUnit()
                updateEnableTotalAmountView()
                updateView()
                calculateFinal()
                calculateFinalVip()
            }

            ivWarn.setOnClickListener {
                //非外卖平台才进
                if (takeOutPlatformModel == null) {
                    if (orderInfo == null) {
                        NoDiscountGoodsDialog.showDialog(
                            parentFragmentManager,
                            cartGoods?.filter { it.goods?.isDiscountItemWhitelisting() == true })
                    } else {
                        NoDiscountGoodsDialog.showDialog(
                            parentFragmentManager,
                            orderInfo?.goods?.filter { it.isDiscountItemWhitelisting() == true }
                                ?.map {
                                    GoodsRequest(
                                        num = it.num,
                                        feedInfoList = ArrayList(it.feeds ?: listOf()),
                                        goodsTagItems = ArrayList(it.tagItems ?: listOf()),
                                        goods = it.orderedGoodsConvertToGoods(),
                                        finalSinglePrice = it.finalSinglePrice,
                                        singleDiscountGoods = it.singleItemDiscount?.toCartSingleDiscountGood()
                                    )
                                })
                    }
                }
            }

            radioGroupDiscountMethod.setOnCheckedChangeListener { radioGroup, i ->
                edtRemark.removeCallbacks(keyBoardRunnable)

                if (radioGroup.checkedRadioButtonId == R.id.radioDeduct) {
                    wholeDiscountType = WholeDiscountType.FIXED_AMOUNT.id
                    val hintText = getString(R.string.choose_discount_require).getRedStar(resources)
                    tvTitle.text = hintText
                } else {
                    wholeDiscountType = WholeDiscountType.PERCENTAGE.id
                    val hintText =
                        getString(R.string.choose_discount_require2).getRedStar(resources)
                    tvTitle.text = hintText
                }

                if (isCustomize) {
                    //如果是自定义的时候切换
                    binding?.apply {
                        edtPercent.clearFocus()
                        edtSalePrice.clearFocus()
                        edtVipPrice.clearFocus()
                        requireActivity().hideKeyboard(edtPercent)
                        requireActivity().hideKeyboard(edtSalePrice)
                        requireActivity().hideKeyboard(edtVipPrice)
                    }
                }
                onChangeUnit()
                updateEnableTotalAmountView()
                updateView()
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                    preconfigurationDiscountListAdapter?.setSelectItem(viewModel.localReduce)
                } else if (wholeDiscountType == WholeDiscountType.PERCENTAGE.id) {
                    preconfigurationDiscountListAdapter?.setSelectItem(viewModel.localDiscount)
                }
                initList()
                calculateFinal()
                calculateFinalVip()

            }

            radioGroupUnit.setOnCheckedChangeListener { radioGroup, i ->
                edtRemark.removeCallbacks(keyBoardRunnable)
                Timber.e("radioGroupUnit  check ${radioGroup.checkedRadioButtonId}")
                if (radioGroup.checkedRadioButtonId == R.id.radioKhr) {
                    wholeDiscountUnitType = WholeReduceType.CUSTOMIZE_KHR.id
                } else {
                    wholeDiscountUnitType = WholeReduceType.CUSTOMIZE_USD.id
                }

                onChangeUnit()
                updateEnableTotalAmountView()
                calculateFinal()
                calculateFinalVip()
            }

            radioReasonType.setOnCheckedChangeListener { radioGroup, i ->
                if (radioGroup.checkedRadioButtonId == R.id.radioReasonDiscount) {
                    textInputLayoutRemark.hint = getString(R.string.please_enter_the_reason)
                } else {
                    textInputLayoutRemark.hint =
                        getString(R.string.please_enter_the_reason_require).getRedStar(resources)
                }
                checkEnable()

            }

            edtPercent.disableCopy()
            edtPercent.filters = arrayOf(CashierInputFilter(false, 100, true))
            edtPercent.addTextChangedListener {
                reduceDiscountDetailModel?.reduceVipRate =
                    edtPercent.text.toString().toDoubleOrNull()
                reduceDiscountDetailModel?.reduceRate =
                    edtPercent.text.toString().toDoubleOrNull()
                Timber.e("reduceDiscountDetailModel?.reduceRate  ${reduceDiscountDetailModel?.reduceRate}")
                calculateFinal()
                calculateFinalVip()
            }
            edtPercent.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtPercent.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(minInput, BigDecimal(100.00))
                    viewKeyBoard.setCurrentEditText(edtPercent)
                }
            }


            edtSalePrice.disableCopy()
            edtSalePrice.filters =
                arrayOf(CashierInputFilter(false, 100000000, false))
            edtSalePrice.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtSalePrice.addTextChangedListener {
                Timber.e("edtSalePrice")

                var salePrice = edtSalePrice.text.toString().toBigDecimalOrNull()

//                if (takeOutPlatformModel?.isKhr() == true && salePrice != null) {
//                    //如果外卖平台币种是khr 转成输入的是瑞尔 要换算成美元
//                    salePrice = BigDecimal(
//                        FoundationHelper.khrConverToUsd(
//                            FoundationHelper.conversionRatio,
//                            salePrice
//                        ).div(100.0)
//                    )
////                        salePrice.divide(
////                            BigDecimal(FoundationHelper.conversionRatio),
////                            2,
////                            RoundingMode.HALF_UP
////                        )
//                }
//                val totalAmount = BigDecimal((reduceDiscountDetailModel?.totalAmount ?: 0)).divide(
//                    BigDecimal(100)
//                )
//                if (salePrice != null && salePrice > totalAmount
//                ) {
//                    salePrice = totalAmount
//                    edtSalePrice.setText("$salePrice")
//                    edtSalePrice.setSelection(edtSalePrice.length())
//                }

                reduceDiscountDetailModel?.reduceDollar = salePrice
//                    if (salePrice == null) null else ((salePrice ?: BigDecimal.ZERO) * BigDecimal.valueOf(100)).toLong()

                calculateFinal()
            }
            edtSalePrice.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        minInput,
                        BigDecimal(100000000.0 - 0.01)
//                        BigDecimal(
//                            if (takeOutPlatformModel?.isKhr() == true) {
//                                FoundationHelper.usdConverToKhr(
//                                    FoundationHelper.conversionRatio,
//                                    ((reduceDiscountDetailModel?.totalAmount ?: 0))
//                                ).toDouble()
//                            } else {
//                                ((reduceDiscountDetailModel?.totalAmount ?: 0).toDouble()
//                                    .div(100.0))
//                            }
//                        ),
                    )
                    viewKeyBoard.setCurrentEditText(edtSalePrice)
                }
            }

            edtVipPrice.disableCopy()
            edtVipPrice.filters =
                arrayOf(CashierInputFilter(false, 100000000, false))
            edtVipPrice.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtVipPrice.addTextChangedListener {
                var salePrice = edtVipPrice.text.toString().toBigDecimalOrNull()
                if (takeOutPlatformModel?.isKhr() == true && salePrice != null) {
                    //如果外卖平台币种是khr 转成输入的是瑞尔 要换算成美元
                    salePrice =
                        salePrice!!.divide(
                            BigDecimal(FoundationHelper.conversionRatio),
                            2,
                            RoundingMode.HALF_UP
                        )
                }
                val totalAmount =
                    BigDecimal((reduceDiscountDetailModel?.totalVipAmount ?: 0)).divide(
                        BigDecimal(100)
                    )
                if (salePrice != null && salePrice!! > totalAmount
                ) {
                    salePrice = totalAmount
                    edtVipPrice.setText("$salePrice")
                    edtVipPrice.setSelection(edtVipPrice.length())
                }

                reduceDiscountDetailModel?.reduceVipDollar = salePrice
//                    if (salePrice == null) null else (("$salePrice".toBigDecimalOrNull()
//                        ?: BigDecimal.ZERO) * BigDecimal.valueOf(100)).toLong()

                calculateFinalVip()
            }
            edtVipPrice.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        minInput,
                        BigDecimal(100000000.0 - 0.01)
//                        BigDecimal(
//                            if (takeOutPlatformModel?.isKhr() == true) {
//                                FoundationHelper.usdConverToKhr(
//                                    FoundationHelper.conversionRatio,
//                                    ((reduceDiscountDetailModel?.totalVipAmount ?: 0))
//                                ).toDouble()
//                            } else {
//                                ((reduceDiscountDetailModel?.totalVipAmount ?: 0).toDouble()
//                                    .div(100.0))
//                            }
//                        )
                    )
                    viewKeyBoard.setCurrentEditText(edtVipPrice)
                }
            }

            edtSaleKhrPrice.disableCopy()
            edtSaleKhrPrice.filters =
                arrayOf(CashierInputFilter(true, 1000000000 - 1, false))
            edtSaleKhrPrice.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtSaleKhrPrice.addTextChangedListener {
                var salePrice = edtSaleKhrPrice.text.toString().toBigDecimalOrNull()
                reduceDiscountDetailModel?.reduceKhr = salePrice
                calculateFinal()
            }
            edtSaleKhrPrice.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal(100),
                        BigDecimal(1000000000 - 1)
                    )
                    viewKeyBoard.setCurrentEditText(edtSaleKhrPrice)
                }
            }


            edtVipKhrPrice.disableCopy()
            edtVipKhrPrice.filters =
                arrayOf(CashierInputFilter(true, 1000000000 - 1, false))
            edtVipKhrPrice.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtVipKhrPrice.addTextChangedListener {
                var salePrice = edtVipKhrPrice.text.toString().toBigDecimalOrNull()
                reduceDiscountDetailModel?.reduceVipKhr = salePrice
                calculateFinalVip()
            }
            edtVipKhrPrice.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal(100),
                        BigDecimal(1000000000 - 1)
                    )
                    viewKeyBoard.setCurrentEditText(edtVipKhrPrice)
                }
            }




            edtRemark.addTextChangedListener {
                checkEnable()
            }

            viewKeyBoard.setonConfirmClick {
                SingleClickUtils.isFastDoubleClick(500) {
                    onConfirm()
                }
            }

            tvConfirm.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(500) {
                    onConfirm()
                }
            }
        }
    }

    private fun onConfirm() {
        binding?.apply {
            var reduceDollar = reduceDiscountDetailModel?.reduceDollar

            var reduceVipDollar = reduceDiscountDetailModel?.reduceVipDollar

            var reduceKhr = reduceDiscountDetailModel?.reduceKhr

            var reduceVipKhr = reduceDiscountDetailModel?.reduceVipKhr

            var reduceRate = edtPercent.text.toString().toDoubleOrNull()
            var reason = edtRemark.text.toString()
            var discountReduceActivityId =
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) viewModel.localReduce?.id else viewModel.localDiscount?.id
            if (isCustomize) {
                discountReduceActivityId = null
                if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_USD.id) {
                    reduceKhr = null
                    reduceVipKhr = null
                } else if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id) {
                    reduceDollar = null
                    reduceVipDollar = null
                }
            } else {
                reduceDollar = null
                reduceVipDollar = null
                reduceRate = null
                reduceKhr = null
                reduceVipKhr = null
            }
            val reduceType = if (isCustomize) {
                wholeDiscountUnitType
            } else {
                WholeReduceType.REDUCE_ACTIVITY.id
            }
            if (orderInfo == null) {
                modifyClickListener?.invoke(
                    ReduceDiscountDetailRequest(
                        reduceType = reduceType,
                        reduceRate = reduceRate,
                        reduceDollar = reduceDollar,
                        reduceVipDollar = reduceVipDollar,
                        reduceKhr = reduceKhr,
                        reduceVipKhr = reduceVipKhr,
                        discountType = if (radioReasonType.checkedRadioButtonId == R.id.radioReasonDiscount) 0 else 1,
                        reduceReason = reason,
                        discountReduceActivityId = discountReduceActivityId
                    )
                )
                dismissCurrentDialog()
            } else {
                viewModel.setDiscountPrice(
                    orderNo = orderInfo?.orderNo,
                    reduceDollar = reduceDollar,
                    reduceVipDollar = reduceVipDollar,
                    reduceKhr = reduceKhr,
                    reduceVipKhr = reduceVipKhr,
                    reduceRate = reduceRate,
                    reduceType = reduceType,
                    discountType = if (radioReasonType.checkedRadioButtonId == R.id.radioReasonDiscount) 0 else 1,
                    couponId = orderInfo?.getCurrentCoupon()?.id,
                    reduceReason = reason,
                    discountReduceActivityId = discountReduceActivityId
                )
            }
        }
    }

    private fun initList() {
        binding?.apply {
            var list: List<DiscountReduceInfo> = listOf()
            list = if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                viewModel.reduceList ?: listOf()
            } else {
                viewModel.discountList ?: listOf()
            }
            Timber.e("最终列表 ${list.size}")
            if (list.isNotEmpty()) {
                rvList.isVisible = true
                layoutEmpty.root.isVisible = false
                tvTitle.isVisible = true
                preconfigurationDiscountListAdapter?.updateData(list)
            } else {
                rvList.isVisible = false
                layoutEmpty.root.isVisible = true
                layoutEmpty.tvEmptyText.text =
                    getString(R.string.empty_data)
                tvTitle.isVisible = false
            }
            textInputLayoutRemark.clearFocus()
        }
    }

    private fun updateView() {
        binding?.apply {

            if (isCustomize) {
                radioGroupDiscountMethod.isVisible = false
                radioGroupUnit.isVisible = true
//                llModifyInput.isVisible = true
                flPercent.isVisible = true
                edtPercent.requestFocus()
                llReasonType.isVisible = true
                textInputLayoutRemark.isVisible = true
            } else {
                flPercent.isVisible = false
                llModifyInput.isVisible = false
                radioGroupDiscountMethod.isVisible = true
                radioGroupUnit.isVisible = false
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                    llReasonType.isInvisible = viewModel.reduceList.isNullOrEmpty()
                    textInputLayoutRemark.isInvisible = viewModel.reduceList.isNullOrEmpty()
                } else {
                    llReasonType.isInvisible = viewModel.discountList.isNullOrEmpty()
                    textInputLayoutRemark.isInvisible = viewModel.discountList.isNullOrEmpty()
                }
                textInputLayoutRemark.clearFocus()
            }
        }
    }


    /**
     * 初始数据
     *
     * @param data
     */
    private fun initData(data: ReduceDiscountDetailModel?) {
        binding?.apply {


            //如果有会员价 显示 会员价选项
            tvVipPrice.isVisible = data?.vipTabFlag == true
            tvNewVipPrice.isVisible = data?.vipTabFlag == true
            textInputLayoutVipPrice.isVisible = data?.vipTabFlag == true
            textInputLayoutVipKhrPrice.isVisible = data?.vipTabFlag == true

            edtRemark.setText(data?.reduceReason)
            edtRemark.setSelection(edtRemark.length())

            if (data?.discountType == 1) {
                //如果理由是void 类型
                radioReasonType.check(R.id.radioVoid)
            }
            if (data?.type == WholeReduceType.CUSTOMIZE_KHR.id) {
                radioGroupUnit.check(R.id.radioKhr)
            } else if (data?.type == WholeReduceType.CUSTOMIZE_USD.id) {
                radioGroupUnit.check(R.id.radioUsd)
            }

            if (wholeDiscountType == WholeDiscountType.PERCENTAGE.id) {
                radioGroupDiscountMethod.check(R.id.radioDiscount)
                preconfigurationDiscountListAdapter?.setSelectItem(viewModel.localDiscount)
            } else if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                preconfigurationDiscountListAdapter?.setSelectItem(viewModel.localReduce)
            }

//            if (takeOutPlatformModel?.isKhr() == true) {
//                textInputLayoutSalePrice.startIconDrawable =
//                    ContextCompat.getDrawable(requireActivity(), R.drawable.icon_km_unit)
//                textInputLayoutVipPrice.startIconDrawable =
//                    ContextCompat.getDrawable(requireActivity(), R.drawable.icon_km_unit)
//            }


            val rate = data?.reduceRate
            edtPercent.setText(if (rate == null) "" else "${if (rate.isInt()) rate.toInt() else rate}")
            edtPercent.setSelection(edtPercent.text?.length ?: 0)

            edtSalePrice.setText(
                if (data?.reduceDollar == null || data?.reduceDollar == BigDecimal.ZERO) "" else data.reduceDollar!!.decimalFormatTwoDigitZero2()
            )
            edtSalePrice.setSelection(edtSalePrice.text?.length ?: 0)

            edtVipPrice.setText(
                if (data?.reduceVipDollar == null || data?.reduceVipDollar == BigDecimal.ZERO) "" else data.reduceVipDollar!!.decimalFormatTwoDigitZero2()
            )
            edtVipPrice.setSelection(edtVipPrice.text?.length ?: 0)

            edtSaleKhrPrice.setText(
                if (data?.reduceKhr == null || data.reduceKhr == BigDecimal.ZERO) "" else "${data.reduceKhr}"
            )
            edtSaleKhrPrice.setSelection(edtSaleKhrPrice.text?.length ?: 0)

            edtVipKhrPrice.setText(
                if (data?.reduceVipKhr == null || data.reduceVipKhr == BigDecimal.ZERO) "" else "${data.reduceVipKhr}"
            )
            edtVipKhrPrice.setSelection(edtVipKhrPrice.text?.length ?: 0)


            //如果没有选择折扣的权限
            if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.SELECT_DISCOUNT.type) != true || takeOutPlatformModel != null
            ) {
                isCustomize = true
                tvCustomize.isVisible = false
                llBottom.isVisible = false
                viewKeyBoard.isVisible = true
                viewChooseList.isVisible = false
                topBar.setTitle(getString(R.string.customize))
                radioGroupUnit.check(R.id.radioUsd)
            }
            //如果没有自定义权限
            if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.CUSTOMIZE_DISCOUNT.type) != true
            ) {
                Timber.e("没有自定义权限")
                tvCustomize.isVisible = false
            }

            updateEnableTotalAmountView()
            onChangeUnit()


            updateView()

            calculateFinal()
            calculateFinalVip()
        }
    }


    /**
     * 更新可减免金额UI
     *
     */
    private fun updateEnableTotalAmountView() {
        binding?.apply {
            if (isCustomize) {
                tvPrice.text = FoundationHelper.getPriceStrByUnit(
                    reduceDiscountDetailModel?.conversionRatio,
                    reduceDiscountDetailModel?.totalAmount ?: 0,
                    wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id
                )
                tvVipPrice.text = FoundationHelper.getPriceStrByUnit(
                    reduceDiscountDetailModel?.conversionRatio,
                    reduceDiscountDetailModel?.totalVipAmount ?: 0,
                    wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id
                )
            } else {
                tvPrice.text = FoundationHelper.getPriceStrByUnit(
                    reduceDiscountDetailModel?.conversionRatio,
                    reduceDiscountDetailModel?.totalAmount ?: 0,
                    takeOutPlatformModel?.isKhr() == true
                )
                tvVipPrice.text =
                    "${reduceDiscountDetailModel?.totalVipAmount?.priceFormatTwoDigitZero2()}"
            }
        }
    }

    /**
     * 切换币种
     *
     */
    private fun onChangeUnit() {
        binding?.apply {
            if (isCustomize) {
                llModifyInputKhr.isVisible = false
                llModifyInput.isVisible = false
                if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id) {
                    llModifyInputKhr.isVisible = true
                    llModifyInput.isVisible = false
                } else {
                    llModifyInputKhr.isVisible = false
                    llModifyInput.isVisible = true
                }
            } else {
                llModifyInputKhr.isVisible = false
                llModifyInput.isVisible = false
            }
        }
    }

    //最终计算相关金额
    private fun calculateFinal() {
        if (reduceDiscountDetailModel == null) {
            return
        }
        binding?.apply {
            var afterDiscount =
                (reduceDiscountDetailModel?.totalAmount
                    ?: 0)
            if (isCustomize) {
                /**
                 * 自定义的时候 先计算折扣 在计算减免
                 */
                val reduceRate = reduceDiscountDetailModel?.reduceRate
                if (reduceRate != null) {
                    //计算减免折扣百分比后的价格  四舍五入
                    var afterDiscountPercentPrice = BigDecimal(
                        afterDiscount
                    ).times(
                        BigDecimal(reduceRate)
                    ).divide(BigDecimal(100)).halfUp(0).toLong()

                    reduceDiscountDetailModel?.reduceAmount = BigDecimal(afterDiscountPercentPrice)
                        .divide(BigDecimal(100))
                    afterDiscount -= afterDiscountPercentPrice
                }

                isInputUsdError(false)
                isInputKhrError(false)

                /**
                 * 自定义的时候 再计算固定减免金额
                 */
                if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_USD.id) {
                    if (reduceDiscountDetailModel?.reduceDollar != null) {
                        afterDiscount -= ((reduceDiscountDetailModel?.reduceDollar
                            ?: BigDecimal.ZERO).times(BigDecimal(100))).toLong()
                    }
                    if (reduceDiscountDetailModel?.reduceDollar != null && reduceDiscountDetailModel?.reduceDollar?.isZero() == true) {
                        /**
                         * 输入大于0 的数
                         */
                        isInputUsdError(
                            true,
                            getString(R.string.please_input_greater_than_zero)
                        )
                    }
                    if (afterDiscount < 0) {
                        afterDiscount = 0
                        isInputUsdError(
                            true,
                            getString(R.string.discount_price_can_not_small_than_discount)
                        )
                    }
                } else if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id) {
                    if (reduceDiscountDetailModel?.reduceKhr != null) {
                        val krhToUsd = FoundationHelper.khrConverToUsd(
                            reduceDiscountDetailModel?.conversionRatio,
                            reduceDiscountDetailModel?.reduceKhr
                        )

                        afterDiscount -= krhToUsd
                    }
                    if (reduceDiscountDetailModel?.reduceKhr != null && reduceDiscountDetailModel?.reduceKhr?.isZero() == true) {
                        /**
                         * 输入大于0 的数
                         */
                        isInputKhrError(
                            true,
                            getString(R.string.please_input_greater_than_zero)
                        )
                    } else if (reduceDiscountDetailModel?.reduceKhr != null && ((reduceDiscountDetailModel?.reduceKhr
                            ?: BigDecimal.ZERO).remainder(BigDecimal(100))) > BigDecimal.ZERO
                    ) {
                        /**
                         * 输入的数要整百
                         */
                        isInputKhrError(
                            true,
                            getString(R.string.discount_input_khr_cue)
                        )
                    }
                    if (afterDiscount < 0) {
                        afterDiscount = 0
                        isInputKhrError(
                            true,
                            getString(R.string.discount_price_can_not_small_than_discount)
                        )
                    }
                }

            } else {
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                    //计算减免
                    if (getAfterReducePriceById() != null) {
                        afterDiscount = getAfterReducePriceById()!!
                    }
                } else {
                    if (getAfterDiscountPriceById() != null) {
                        afterDiscount = getAfterDiscountPriceById()!!
                    }
                }
            }

            Timber.e("afterDiscount  $afterDiscount")
            reduceDiscountDetailModel?.reduceRealPrice = afterDiscount
            updateFinalPriceView()
        }
    }

    /**
     * 获取使用后台配置减免后的金额
     *
     * @return
     */
    private fun getAfterReducePriceById(): Long? {
        var afterDiscount =
            (reduceDiscountDetailModel?.totalAmount
                ?: 0)
        if (viewModel.localReduce == null) {
            return null
        }
        //直接减免的金额
        val afterDiscountUsd = (viewModel.localReduce?.reduceRateAmount?.times(BigDecimal(100))
            ?: BigDecimal.ZERO).toLong()

        afterDiscount -= afterDiscountUsd
        if (afterDiscount < 0) {
            afterDiscount = 0
        }
        return afterDiscount
    }


    /**
     * 获取使用后台配置折扣后的金额
     *
     * @return
     */
    private fun getAfterDiscountPriceById(): Long? {
        var afterDiscount =
            (reduceDiscountDetailModel?.totalAmount
                ?: 0)
        if (viewModel.localDiscount == null) {
            return null
        }
        //计算打折
        val percent =
            viewModel.localDiscount?.reduceRateAmount ?: BigDecimal.ZERO
        //计算减免折扣百分比后的价格  四舍五入
        var afterDiscountPercentPrice = BigDecimal(
            afterDiscount
        ).times(
            percent
        ).divide(BigDecimal(100.0)).halfUp(0).toLong()

        if (viewModel.localDiscount?.reduceAmountLimit != null) {
            val maxDiscountPrice = (viewModel.localDiscount?.reduceAmountLimit
                ?: BigDecimal.ZERO).times(BigDecimal(100)).toLong()
            afterDiscountPercentPrice = min(maxDiscountPrice, afterDiscountPercentPrice)
        }

        afterDiscount -= (afterDiscountPercentPrice ?: 0)

        return afterDiscount
    }


    private fun calculateFinalVip() {
        if (reduceDiscountDetailModel == null) {
            return
        }
        binding?.apply {
            var afterDiscount =
                (reduceDiscountDetailModel?.totalVipAmount
                    ?: 0)
            if (isCustomize) {
                /**
                 * 自定义的时候 先计算折扣 在计算减免
                 */
                val reduceRate = edtPercent.text.toString().toDoubleOrNull()
                if (reduceRate != null) {
                    //计算减免折扣百分比后的价格  四舍五入
                    var afterDiscountPercentPrice = BigDecimal(
                        afterDiscount
                    ).times(
                        BigDecimal(reduceRate)
                    ).divide(BigDecimal(100)).halfUp(0).toLong()

                    reduceDiscountDetailModel?.reduceAmount =
                        BigDecimal(afterDiscountPercentPrice).divide(
                            BigDecimal(100)
                        )
                    afterDiscount -= afterDiscountPercentPrice
                }
                isInputVipUsdError(false)
                isInputVipKhrError(false)
                isInputPercentError(false)

                if (reduceDiscountDetailModel?.reduceRate != null && reduceDiscountDetailModel?.reduceRate == 0.0) {
                    isInputPercentError(
                        true,
                        getString(R.string.please_input_greater_than_zero)
                    )
                }

                /**
                 * 自定义的时候 再计算固定减免金额
                 */
                if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_USD.id) {
                    if (reduceDiscountDetailModel?.reduceVipDollar != null) {
                        afterDiscount -= ((reduceDiscountDetailModel?.reduceVipDollar
                            ?: BigDecimal.ZERO).times(BigDecimal(100))).toLong()
                    }
                    if (reduceDiscountDetailModel?.reduceVipDollar != null && reduceDiscountDetailModel?.reduceVipDollar?.isZero() == true) {
                        /**
                         * 输入大于0 的数
                         */
                        isInputVipUsdError(
                            true,
                            getString(R.string.please_input_greater_than_zero)
                        )
                    }
                    if (afterDiscount < 0) {
                        afterDiscount = 0
                        isInputVipUsdError(
                            true,
                            getString(R.string.discount_price_can_not_small_than_discount)
                        )
                    }
                } else if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id) {
                    if (reduceDiscountDetailModel?.reduceVipKhr != null) {
                        val krhToUsd = FoundationHelper.khrConverToUsd(
                            reduceDiscountDetailModel?.conversionRatio,
                            reduceDiscountDetailModel?.reduceVipKhr
                        )
                        afterDiscount -= krhToUsd
                    }
                    if (reduceDiscountDetailModel?.reduceVipKhr != null && reduceDiscountDetailModel?.reduceVipKhr?.isZero() == true) {
                        /**
                         * 输入大于0 的数
                         */
                        isInputVipKhrError(
                            true,
                            getString(R.string.please_input_greater_than_zero)
                        )
                    } else if (reduceDiscountDetailModel?.reduceVipKhr != null && ((reduceDiscountDetailModel?.reduceVipKhr
                            ?: BigDecimal.ZERO).remainder(BigDecimal(100))) > BigDecimal.ZERO
                    ) {
                        /**
                         * 输入的数要整百
                         */
                        isInputVipKhrError(
                            true,
                            getString(R.string.discount_input_khr_cue)
                        )
                    }
                    if (afterDiscount < 0) {
                        afterDiscount = 0
                        isInputVipKhrError(
                            true,
                            getString(R.string.discount_price_can_not_small_than_discount)
                        )
                    }
                }

                if (afterDiscount < 0) {
                    afterDiscount = 0
                }

            } else {
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                    //计算减免
                    if (getAfterReduceVipPriceById() != null) {
                        afterDiscount = getAfterReduceVipPriceById()!!
                    }
                } else {
                    if (getAfterDiscountVipPriceById() != null) {
                        afterDiscount = getAfterDiscountVipPriceById()!!
                    }
                }
            }

            Timber.e("afterDiscount  $afterDiscount")
            reduceDiscountDetailModel?.reduceVipRealPrice = afterDiscount
            updateFinalPriceView()
        }
    }

    /**
     * 获取使用后台配置减免后的会员价金额
     *
     * @return
     */
    private fun getAfterReduceVipPriceById(): Long? {
        var afterDiscount =
            (reduceDiscountDetailModel?.totalVipAmount
                ?: 0)
        if (viewModel.localReduce == null) {
            return null
        }
        //直接减免的金额
        val afterDiscountUsd = (viewModel.localReduce?.reduceRateAmount?.times(BigDecimal(100))
            ?: BigDecimal.ZERO).toLong()

        afterDiscount -= afterDiscountUsd
        if (afterDiscount < 0) {
            afterDiscount = 0
        }
        return afterDiscount
    }

    /**
     * 获取使用后台配置折扣后的会员价金额
     *
     * @return
     */
    private fun getAfterDiscountVipPriceById(): Long? {
        var afterDiscount =
            (reduceDiscountDetailModel?.totalVipAmount
                ?: 0)
        if (viewModel.localDiscount == null) {
            return null
        }
        //计算打折
        val percent =
            viewModel.localDiscount?.reduceRateAmount ?: BigDecimal.ZERO
        //用美分去计算 小数点后舍去
        var afterDiscountPercentPrice = BigDecimal(
            afterDiscount
        ).times(
            percent
        ).divide(BigDecimal(100.0)).halfUp(0).toLong()

        if (viewModel.localDiscount?.reduceAmountLimit != null) {
            val maxDiscountPrice = (viewModel.localDiscount?.reduceAmountLimit
                ?: BigDecimal.ZERO).times(BigDecimal(100)).toLong()
            afterDiscountPercentPrice = min(maxDiscountPrice, afterDiscountPercentPrice)
        }

        afterDiscount -= (afterDiscountPercentPrice ?: 0)

        return afterDiscount
    }


    /**
     * 更新最后价格view
     *
     */
    private fun updateFinalPriceView() {
        binding?.apply {
            if (isCustomize) {
                tvNewPrice.text = FoundationHelper.getPriceStrByUnit(
                    reduceDiscountDetailModel?.conversionRatio,
                    reduceDiscountDetailModel?.reduceRealPrice ?: 0,
                    wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id
                )
                tvNewVipPrice.text = FoundationHelper.getPriceStrByUnit(
                    reduceDiscountDetailModel?.conversionRatio,
                    reduceDiscountDetailModel?.reduceVipRealPrice ?: 0,
                    wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id
                )

                //没设置过
                if (reduceDiscountDetailModel?.reduceRate == null && reduceDiscountDetailModel?.reduceDollar == null && reduceDiscountDetailModel?.reduceKhr == null && reduceDiscountDetailModel?.reduceVipDollar == null && reduceDiscountDetailModel?.reduceVipKhr == null) {
                    tvNewPrice.text = "-"
                    tvNewVipPrice.isVisible = false
                } else {
                    tvNewVipPrice.isVisible = reduceDiscountDetailModel?.vipTabFlag == true
                }
            } else {
                tvNewPrice.text = FoundationHelper.getPriceStrByUnit(
                    reduceDiscountDetailModel?.conversionRatio,
                    reduceDiscountDetailModel?.reduceRealPrice ?: 0,
                    takeOutPlatformModel?.isKhr() == true
                )

                tvNewVipPrice.text =
                    reduceDiscountDetailModel?.reduceVipRealPrice?.priceFormatTwoDigitZero2()
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                    if (viewModel.localReduce == null) {
                        //没设置过
                        tvNewPrice.text = "-"
                        tvNewVipPrice.isVisible = false
                    } else {
                        tvNewVipPrice.isVisible = reduceDiscountDetailModel?.vipTabFlag == true
                    }
                } else if (wholeDiscountType == WholeDiscountType.PERCENTAGE.id) {
                    if (viewModel.localDiscount == null) {
                        //没设置过
                        tvNewPrice.text = "-"
                        tvNewVipPrice.isVisible = false
                    } else {
                        tvNewVipPrice.isVisible = reduceDiscountDetailModel?.vipTabFlag == true
                    }
                }
            }
            checkEnable()
        }
    }

    private fun checkEnable() {
        binding?.apply {
            //备注是否满足
            var isSatisfyRemark = false
            if (radioReasonType.checkedRadioButtonId == R.id.radioVoid) {
                //必选的时候有填就满足
                isSatisfyRemark = edtRemark.text.toString().trim().isNotEmpty()
            } else {
                isSatisfyRemark = true
            }

            if (isCustomize) {
                val isSatisfyPercent =
                    if (edtPercent.text.isNullOrEmpty()) true else !(edtPercent.text.toString()
                        .toBigDecimalOrNull() ?: BigDecimal.ZERO).isZero()

//                Timber.e(
//                    "isSatisfyPercent ${isSatisfyPercent}  ${
//                        (edtPercent.text.toString()
//                            .toBigDecimalOrNull() ?: BigDecimal.ZERO).compareTo(
//                            BigDecimal.ZERO
//                        )
//                    }"
//                )
                Timber.e("wholeDiscountUnitType ${wholeDiscountUnitType}")
                if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_KHR.id) {
                    val isSatisfySalePrice = !textInputLayoutSaleKhrPrice.isErrorEnabled
                    val isSatisfyVipPrice = !textInputLayoutVipKhrPrice.isErrorEnabled
                    viewKeyBoard.setConfirm(isSatisfySalePrice && isSatisfyVipPrice && isSatisfyRemark && isSatisfyPercent)
                    tvConfirm.setEnableWithAlpha(isSatisfySalePrice && isSatisfyVipPrice && isSatisfyRemark && isSatisfyPercent)
                } else if (wholeDiscountUnitType == WholeReduceType.CUSTOMIZE_USD.id) {
                    val isSatisfySalePrice = !textInputLayoutSalePrice.isErrorEnabled
                    val isSatisfyVipPrice = !textInputLayoutVipPrice.isErrorEnabled
                    viewKeyBoard.setConfirm(isSatisfySalePrice && isSatisfyVipPrice && isSatisfyRemark && isSatisfyPercent)
                    tvConfirm.setEnableWithAlpha(isSatisfySalePrice && isSatisfyVipPrice && isSatisfyRemark && isSatisfyPercent)
                }
            } else {
                if (wholeDiscountType == WholeDiscountType.FIXED_AMOUNT.id) {
                    viewKeyBoard.setConfirm(isSatisfyRemark && !viewModel.reduceList.isNullOrEmpty())
                    tvConfirm.setEnableWithAlpha(isSatisfyRemark && !viewModel.reduceList.isNullOrEmpty())
                } else if (wholeDiscountType == WholeDiscountType.PERCENTAGE.id) {
                    viewKeyBoard.setConfirm(isSatisfyRemark && !viewModel.discountList.isNullOrEmpty())
                    tvConfirm.setEnableWithAlpha(isSatisfyRemark && !viewModel.discountList.isNullOrEmpty())
                }
            }
        }
    }

    private fun isInputUsdError(isError: Boolean = false, errorMsg: String? = "") {
        binding?.apply {
            textInputLayoutSalePrice.error = errorMsg
            textInputLayoutSalePrice.isErrorEnabled = isError
        }
    }

    private fun isInputVipUsdError(isError: Boolean = false, errorMsg: String? = "") {
        binding?.apply {
            textInputLayoutVipPrice.error = errorMsg
            textInputLayoutVipPrice.isErrorEnabled = isError
        }
    }

    private fun isInputKhrError(isError: Boolean = false, errorMsg: String? = "") {
        binding?.apply {
            textInputLayoutSaleKhrPrice.error = errorMsg
            textInputLayoutSaleKhrPrice.isErrorEnabled = isError
        }
    }

    private fun isInputVipKhrError(isError: Boolean = false, errorMsg: String? = "") {
        binding?.apply {
            textInputLayoutVipKhrPrice.error = errorMsg
            textInputLayoutVipKhrPrice.isErrorEnabled = isError
        }
    }

    private fun isInputPercentError(isError: Boolean = false, errorMsg: String? = "") {
        binding?.apply {
            textInputLayoutPercent.error = errorMsg
            textInputLayoutPercent.isErrorEnabled = isError

            val lp = tvUnit.layoutParams as FrameLayout.LayoutParams
            lp.marginEnd = DisplayUtils.dp2px(requireActivity(), if (isError) 40f else 12f)
            tvUnit.layoutParams = tvUnit.layoutParams

        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.84).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.7).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}