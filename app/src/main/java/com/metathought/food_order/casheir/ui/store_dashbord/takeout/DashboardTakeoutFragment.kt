package com.metathought.food_order.casheir.ui.store_dashbord.takeout

import android.content.res.Resources
import android.os.Bundle
import android.text.TextPaint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.ChartTimeType
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.GrabStoreMoneyOrder
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreGrabStatisticResponse
import com.metathought.food_order.casheir.databinding.FragmentDashboardTakeoutBinding
import com.metathought.food_order.casheir.extension.formatPLusMinus
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4
import com.metathought.food_order.casheir.ui.adapter.DashboardGrabListAdapter
import com.metathought.food_order.casheir.ui.adapter.TakeoutTopSalesAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.store_dashboard.DaySalesDialog
import com.metathought.food_order.casheir.ui.dialog.store_dashboard.PromoDialog
import com.metathought.food_order.casheir.ui.dialog.store_dashboard.SalesTopDialog
import com.metathought.food_order.casheir.ui.store_dashbord.TakeoutDashboardViewModel
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar


@AndroidEntryPoint
class DashboardTakeoutFragment : BaseFragment() {

    companion object {
        fun newInstance() = DashboardTakeoutFragment()
    }

    private var _binding: FragmentDashboardTakeoutBinding? = null
    private val binding get() = _binding
    private val viewModel: TakeoutDashboardViewModel by viewModels()
    private var startDateString: String = ""
    private var endDateString: String = ""
    private var type: String = "1"
    private var topSaleAdapter: TakeoutTopSalesAdapter? = null
    private var dashboardListAdapter: DashboardGrabListAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentDashboardTakeoutBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.uiStoreStatisticState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        showProgress()
                }
                if (it.showEnd) {
                    dismissProgress()
                    if (it.isRefresh != false) {
                        dashboardListAdapter?.replaceData(arrayListOf())
                        it.showSuccess?.let {
                            setData(it)
                        }

                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    dismissProgress()
                    showToast(error)
                }
                it.showSuccess?.let { response ->
                    dismissProgress()
                    if (it.isRefresh != false) {
                        ArrayList(
                            response.storeMoneyOrderDetailVos ?: listOf()
                        )?.let { it1 ->
                            dashboardListAdapter?.replaceData(
                                it1
                            )
                        }
                        setData(response)
                        refreshLayout.finishRefresh()
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    } else {
                        ArrayList(
                            response.storeMoneyOrderDetailVos ?: listOf()
                        ).let { it1 ->
                            dashboardListAdapter?.addData(
                                it1
                            )
                        }
//                        refreshLayout.finishLoadMore()
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
            }
        }
    }


    private fun initView() {
        binding?.apply {
            arguments?.let {
                startDateString = it.getString("startDateString") ?: ""
                endDateString = it.getString("endDateString") ?: ""
                type = it.getString("type") ?: ""
            }
            pieChartView.setNoDataText(getString(R.string.no_chart_data_available))

            tvGrabKey.isVisible = false
            tvMerchatKey.isVisible = false

            setTitle()
            topSaleAdapter =
                context?.let {
                    TakeoutTopSalesAdapter(
                        ArrayList(viewModel.getDefaultTopSaleList()),
                        it
                    )
                }
            recyclerViewTopSale.adapter = topSaleAdapter
            dashboardListAdapter = DashboardGrabListAdapter(arrayListOf()) {}
            recyclerviewDashboardRevenue.adapter = dashboardListAdapter
        }
    }


    override fun onLoad() {
        super.onLoad()
        context?.let {
            getStoreData()
        }
    }

    fun isShowYesToday(type: String?): Boolean {
        if (type == null)
            return false
        if (type.equals(ChartTimeType.TODAY.type) || type.equals(ChartTimeType.YESTODAY.type) || type.equals(
                ChartTimeType.RECENT7DAY.type
            ) || type.equals(ChartTimeType.RECENT30DAY.type)
        )
            return true
        return false
    }

    fun getComparyKey(): String {
        val calendar = Calendar.getInstance()
        // 获取周几（1=周日，2=周一，...，7=周六）
        var dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        if (type == ChartTimeType.TODAY.type || type == ChartTimeType.YESTODAY.type) {
            if (type == ChartTimeType.YESTODAY.type) {
                dayOfWeek = dayOfWeek - 1;
                if (dayOfWeek < 0) {
                    dayOfWeek = 7
                }
            }
            return when (dayOfWeek) {
                1 -> getString(R.string.compare_last_sunday)
                2 -> getString(R.string.compare_last_monday)
                3 -> getString(R.string.compare_last_tuesday)
                4 -> getString(R.string.compare_last_wednsday)
                5 -> getString(R.string.compare_last_thursday)
                6 -> getString(R.string.compare_last_friday)
                7 -> getString(R.string.compare_last_saturday)
                else -> ""
            }
        }
        if (type == ChartTimeType.RECENT7DAY.type) {
            return getString(R.string.compare_last_7_day)
        }
        if (type == ChartTimeType.RECENT30DAY.type) {
            return getString(R.string.compare_30_day)
        }
        return ""
    }


    private fun getStoreData(isRefresh: Boolean? = null) {
        viewModel.getStoreStatistic(isRefresh, type, startDateString, endDateString)
    }

    private fun setData(storeData: StoreGrabStatisticResponse) {
        binding?.apply {
            layoutActualRevenue.tvValue.text =
                "${storeData.curStoreMoneyOrder?.turnover?.priceFormatTwoDigitZero2()}"
            layoutPromoRevenueAmount.tvDetail.isVisible = true
            layoutPromoRevenueAmount.tvValue.text =
                "${storeData.curStoreMoneyOrder?.basketPromo?.priceFormatTwoDigitZero2()}"
            layoutCancelAmount.tvValue.text =
                "${storeData.curStoreMoneyOrder?.cancelledAmount?.priceFormatTwoDigitZero2()}"
            layoutFailAmount.tvValue.text =
                "${storeData.curStoreMoneyOrder?.failedAmount?.priceFormatTwoDigitZero2()}"
            layoutOrderNumbers.tvValue.text = "${storeData.curStoreMoneyOrder?.orderNum}"
            layoutPromoOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.promoOrderNum}"
            layoutCancleOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.cancelledOrderNum}"
            layoutFailOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.failedOrderNum}"
            var compareType = getComparyKey()
            layoutActualRevenue.tvKeyYestoday.text = compareType
            layoutPromoRevenueAmount.tvKeyYestoday.text = compareType
            layoutCancelAmount.tvKeyYestoday.text = compareType
            layoutFailAmount.tvKeyYestoday.text = compareType
            layoutOrderNumbers.tvKeyYestoday.text = compareType
            layoutPromoOrders.tvKeyYestoday.text = compareType
            layoutCancleOrders.tvKeyYestoday.text = compareType
            layoutFailOrders.tvKeyYestoday.text = compareType
            if (storeData.lastStoreMoneyOrder?.turnover != null && storeData.curStoreMoneyOrder?.turnover != null) {
                var value =
                    storeData.curStoreMoneyOrder?.turnover - storeData.lastStoreMoneyOrder?.turnover;
                layoutActualRevenue.tvValueYesterday.text =
                    value.priceFormatTwoDigitZero4()
                setTextColorWithValue(layoutActualRevenue.tvValueYesterday, value)
            }
            if (storeData.lastStoreMoneyOrder?.basketPromo != null && storeData.curStoreMoneyOrder?.basketPromo != null) {
                var value =
                    storeData.curStoreMoneyOrder?.basketPromo - storeData.lastStoreMoneyOrder?.basketPromo;
                layoutPromoRevenueAmount.tvValueYesterday.text =
                    value.priceFormatTwoDigitZero4()
                setTextColorWithValue(layoutPromoRevenueAmount.tvValueYesterday, value)
            }

            if (storeData.lastStoreMoneyOrder?.cancelledAmount != null && storeData.curStoreMoneyOrder?.cancelledAmount != null) {
                var value =
                    storeData.curStoreMoneyOrder?.cancelledAmount - storeData.lastStoreMoneyOrder?.cancelledAmount;
                layoutCancelAmount.tvValueYesterday.text =
                    value.priceFormatTwoDigitZero4()
                setTextColorWithValue(layoutCancelAmount.tvValueYesterday, value)
            }

            if (storeData.lastStoreMoneyOrder?.failedAmount != null && storeData.curStoreMoneyOrder?.failedAmount != null) {
                var value =
                    storeData.curStoreMoneyOrder?.failedAmount - storeData.lastStoreMoneyOrder?.failedAmount;
                layoutFailAmount.tvValueYesterday.text =
                    value.priceFormatTwoDigitZero4()
                setTextColorWithValue(layoutFailAmount.tvValueYesterday, value)
            }

            if (storeData.lastStoreMoneyOrder?.orderNum != null && storeData.curStoreMoneyOrder?.orderNum != null) {
                var value =
                    storeData.curStoreMoneyOrder?.orderNum - storeData.lastStoreMoneyOrder?.orderNum;
                layoutOrderNumbers.tvValueYesterday.text =
                    value.formatPLusMinus()
                setTextColorWithValue(layoutOrderNumbers.tvValueYesterday, value)
            }
            if (storeData.lastStoreMoneyOrder?.promoOrderNum != null && storeData.curStoreMoneyOrder?.promoOrderNum != null) {
                var value =
                    storeData.curStoreMoneyOrder?.promoOrderNum - storeData.lastStoreMoneyOrder?.promoOrderNum;
                layoutPromoOrders.tvValueYesterday.text =
                    value.formatPLusMinus()
                setTextColorWithValue(layoutPromoOrders.tvValueYesterday, value)
            }
            if (storeData.lastStoreMoneyOrder?.cancelledOrderNum != null && storeData.curStoreMoneyOrder?.cancelledOrderNum != null) {
                var value =
                    storeData.curStoreMoneyOrder?.cancelledOrderNum - storeData.lastStoreMoneyOrder?.cancelledOrderNum;
                layoutCancleOrders.tvValueYesterday.text =
                    value.formatPLusMinus()
                setTextColorWithValue(layoutCancleOrders.tvValueYesterday, value)
            }
            if (storeData.lastStoreMoneyOrder?.failedOrderNum != null && storeData.curStoreMoneyOrder?.failedOrderNum != null) {
                var value =
                    storeData.curStoreMoneyOrder?.failedOrderNum - storeData.lastStoreMoneyOrder?.failedOrderNum;
                layoutFailOrders.tvValueYesterday.text =
                   value.formatPLusMinus()
                setTextColorWithValue(layoutFailOrders.tvValueYesterday, value)
            }
            layoutActualRevenue.llComare.isVisible = isShowYesToday(type)
            layoutPromoRevenueAmount.llComare.isVisible = isShowYesToday(type)
            layoutCancelAmount.llComare.isVisible = isShowYesToday(type)
            layoutFailAmount.llComare.isVisible = isShowYesToday(type)
            layoutOrderNumbers.llComare.isVisible = isShowYesToday(type)
            layoutPromoOrders.llComare.isVisible = isShowYesToday(type)
            layoutCancleOrders.llComare.isVisible = isShowYesToday(type)
            layoutFailOrders.llComare.isVisible = isShowYesToday(type)
            storeData.salesRankingList?.let {
                if (it.isEmpty())
                    topSaleAdapter?.updateItems(ArrayList(viewModel.getDefaultTopSaleList()))
                else
                    topSaleAdapter?.updateItems(ArrayList(it))
            }
            storeData.curStoreMoneyOrder?.let { showPieChart(it) }
        }
    }

    private fun setTextColorWithValue(tvValue: TextView, value: Int?) {
        context?.let {
            if (value != null) {
                if (value > 0) {
                    tvValue.setTextColor(it.getColor(R.color.primaryColor))
                } else if (value < 0) {
                    tvValue.setTextColor(it.getColor(R.color.red_clear_data))
                } else {
                    tvValue.setTextColor(it.getColor(R.color.black60))
                }
            }
        }
    }

    private fun showPieChart(storeData: GrabStoreMoneyOrder) {
        binding?.apply {
            tvGrabKey.isVisible = true
            tvMerchatKey.isVisible = true
        }
        binding?.pieChartView?.apply {
            setTouchEnabled(false)
            holeRadius = 75f
            rotationAngle = 90f
            description?.isEnabled = false
            centerText = getString(R.string.account)
            setCenterTextColor(ContextCompat.getColor(context, R.color.black))
            setCenterTextSize(10f)
            legend?.isEnabled = false
            setDrawRoundedSlices(true)
        }
        val pieEntries = ArrayList<PieEntry>()
        val label = "type"

        val grabPromo = storeData.grabPromo?.toFloat() ?: 0.0f
        val merchantPromo = storeData.merchantPromo?.toFloat() ?: 0.0f
        //initializing colors for the entries
        val colors = ArrayList<Int>()
        colors.add("#0F9D58".toColorInt())
        colors.add("#2C99FF".toColorInt())
        if (grabPromo > 0.0f || merchantPromo > 0.0f) {
            pieEntries.add(PieEntry(grabPromo, ""))
            pieEntries.add(PieEntry(merchantPromo, ""))
        } else {
            pieEntries.add(PieEntry(1.0f, ""))
            pieEntries.add(PieEntry(1.0f, ""))
        }
        val pieDataSet = PieDataSet(pieEntries, label)
        pieDataSet.valueTextSize = 12f
        pieDataSet.colors = colors
        pieDataSet.setDrawValues(false)
        val pieData = PieData(pieDataSet)
        binding?.apply {
            pieChartView.setData(pieData)
            pieChartView.animateY(1000)
            pieChartView.invalidate()
            tvMerchatValue.text = "${storeData.merchantPromo?.priceFormatTwoDigitZero2()}"
            tvGrabValue.text = "${storeData.grabPromo?.priceFormatTwoDigitZero2()}"
            var allNum = grabPromo + merchantPromo;
            if (allNum > 0) {
                val grabStr = getString(R.string.grab_radio, "" + (grabPromo / allNum));
                val merchatStr = getString(R.string.merchant_radio, "" + (merchantPromo / allNum));
                tvGrabKey.text = grabStr
                tvMerchatKey.text = merchatStr
            } else {
                val grabStr = getString(R.string.grab_radio, "0");
                val merchatStr = getString(R.string.merchant_radio, "0");
                tvGrabKey.text = grabStr
                tvMerchatKey.text = merchatStr
            }
        }
    }

    private fun setTitle() {
        binding?.apply {
            layoutActualRevenue.tvTitle.text = getString(R.string.turn_over_amoute)
            layoutPromoRevenueAmount.tvTitle.text = getText(R.string.promo_order_amoute)
            layoutCancelAmount.tvTitle.text = getString(R.string.cancle_order_amoute)
            layoutFailAmount.tvTitle.text = getString(R.string.fail_order_amoute)
            layoutOrderNumbers.tvTitle.text = getString(R.string.order_numbers)
            layoutPromoOrders.tvTitle.text = getString(R.string.promo_order_num)
            layoutCancleOrders.tvTitle.text = getString(R.string.cancle_order_num)
            layoutFailOrders.tvTitle.text = getString(R.string.fail_order_num)

        }
    }


    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getStoreData(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getStoreData(false)
                }
            })
            tvSaleMore.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    SalesTopDialog.showDialog(parentFragmentManager)
                }
            }
            tvDayDataMore.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    DaySalesDialog.showDialog(parentFragmentManager)
                }
            }
            layoutActualRevenue.root.setOnClickListener {
                showSmartTipPopup(
                    layoutActualRevenue.tvTitleCue,
                    getString(R.string.acturl_revenu_tip_1)
                )
            }
            layoutPromoRevenueAmount.tvDetail.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    viewModel.currentStoreGrabStatistic?.let {
                        var promoList =
                            viewModel.currentStoreGrabStatistic?.curStoreMoneyOrder?.promoDetails
                                ?: emptyList();
                        PromoDialog.showDialog(parentFragmentManager, promoList)
                    }
                }
            }
            layoutPromoRevenueAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutPromoRevenueAmount.tvTitleCue,
                    getString(R.string.order_promo_tip)
                )
            }
            layoutCancelAmount.tvTitleCue.isVisible = true
            layoutCancelAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutCancelAmount.tvTitleCue,
                    getString(R.string.cancel_money_tip)
                )
            }
            layoutFailAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutFailAmount.tvTitleCue,
                    getString(R.string.fail_order_tip)
                )
            }
            layoutOrderNumbers.root.setOnClickListener {
                showSmartTipPopup(
                    layoutOrderNumbers.tvTitleCue,
                    getString(R.string.order_numbers_tip)
                )
            }
            layoutPromoOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutPromoOrders.tvTitleCue,
                    getString(R.string.order_promo_tip)
                )
            }
            layoutCancleOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutCancleOrders.tvTitleCue,
                    getString(R.string.cancel_money_tip)
                )
            }
            layoutFailOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutFailOrders.tvTitleCue,
                    getString(R.string.fail_order_tip)
                )
            }
            llChart.setOnClickListener {
                showSmartTipPopup(
                    tvPaymentMethodTitleCue,
                    getString(R.string.accout_tip)
                )
            }
            pieChartView.setOnClickListener {
                showSmartTipPopup(
                    tvPaymentMethodTitleCue,
                    getString(R.string.accout_tip)
                )
            }
        }
    }

    /**
     * 显示智能提示弹窗，根据可用空间自动选择显示位置
     * - 左右空间都足够时，显示在中间
     * - 左侧空间不足时，显示在右侧
     * - 右侧空间不足时，显示在左侧
     *
     * @param anchorView 锚点视图，弹窗将显示在此视图附近
     * @param message 要显示的提示信息
     */
    private fun showSmartTipPopup(anchorView: View, message: String) {
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val anchorX = location[0]
        val anchorY = location[1]
        val anchorWidth = anchorView.width

        // 获取屏幕宽度
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenWidth = displayMetrics.widthPixels

// 估算消息宽度 (使用TextPaint计算实际文本宽度)
        val textPaint = TextPaint()
        textPaint.textSize =
            resources.getDimension(R.dimen._12ssp) // 使用与CustomBubbleAttachPopup相同的文本大小
        val estimatedMessageWidth = textPaint.measureText(message).toInt() +
                resources.getDimensionPixelSize(R.dimen.fragment_horizontal_margin) * 2 // 添加左右内边距
        val halfMessageWidth = estimatedMessageWidth / 2

        // 计算锚点中心位置
        val anchorCenterX = anchorX + (anchorWidth / 2)

        // 判断左右空间是否足够
        val hasEnoughSpaceOnLeft = anchorCenterX > halfMessageWidth
        val hasEnoughSpaceOnRight = (screenWidth - anchorCenterX) > halfMessageWidth

        // 创建XPopup构建器
        val popupBuilder = XPopup.Builder(requireContext())
            .hasShadowBg(false)
            .isTouchThrough(true)
            .isDestroyOnDismiss(true)

        // 根据空间决定显示位置
        if (hasEnoughSpaceOnLeft && hasEnoughSpaceOnRight) {
            // 左右空间都足够，显示在中间
            popupBuilder.popupPosition(PopupPosition.Top) // 使用Top位置，但实际上是居中的
            // 设置居中显示
            popupBuilder.isCenterHorizontal(true)
        } else if (hasEnoughSpaceOnRight) {
            // 右侧空间足够，显示在右侧
            popupBuilder.popupPosition(PopupPosition.Right)
        } else {
            // 默认显示在左侧
            popupBuilder.popupPosition(PopupPosition.Left)
        }

        // 显示弹窗
        popupBuilder
            .atView(anchorView)
//            .atPoint(PointF(anchorCenterX.toFloat(), anchorY.toFloat()))
            .asCustom(CustomBubbleAttachPopup(requireContext(), message, 5000))
            .show()
    }

}