package com.metathought.food_order.casheir.utils

import android.util.Log

object SingleClickUtils {
//    private const val TIME = 1000
    private var lastClickTime: Long = 0

    /**
     * 处理快速双击，多击事件，在TIME时间内只执行一次事件
     *
     * @return
     */
    fun isFastDoubleClick(times:Long = 1000,call:()->Unit) {
        val currentTime = System.currentTimeMillis()
        val timeInterval = currentTime - lastClickTime
        if (timeInterval in 1 until times) {
            return
        }
        lastClickTime = currentTime
        Log.e("isFastDoubleClick","间隔点击")
        call()
        return
    }
}