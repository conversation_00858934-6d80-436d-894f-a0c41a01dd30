package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.QuickRemarkModel
import com.metathought.food_order.casheir.databinding.QuickRemarkItemBinding
import com.metathought.food_order.casheir.utils.SingleClickUtils

/**
 * 快捷备注
 *
 * @property list
 * @property onClickCallback
 * @constructor Create empty Quick remark adapter
 */
class QuickRemarkAdapter(
    private var list: ArrayList<QuickRemarkModel>,
    val onClickCallback: (String) -> Unit
) : RecyclerView.Adapter<QuickRemarkAdapter.QuickRemarkItemViewHolder>() {

    inner class QuickRemarkItemViewHolder(val binding: QuickRemarkItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.run {
                layoutMain.setOnClickListener {
                    if (bindingAdapterPosition != -1) {
                        SingleClickUtils.isFastDoubleClick(300) {
                            onClickCallback.invoke(list[bindingAdapterPosition].name ?: "")
                        }
                    }
                }
            }
        }

        fun bind(position: Int) {
            binding.apply {
                tvRemark.text = list[position].name
            }
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): QuickRemarkItemViewHolder {
        return QuickRemarkItemViewHolder(
            QuickRemarkItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    fun replaceData(datas: ArrayList<QuickRemarkModel>) {
        list.clear()
        list.addAll(datas)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: QuickRemarkItemViewHolder, position: Int) {
        holder.bind(position)
    }

}