package com.metathought.food_order.casheir.data.model.base.response_model.order

import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2025/5/19
 *@desc
 **/

data class PromotionActivity(
//    val activationTime: String?,
    val activeState: ActiveState?,
    val available: Boolean?,
    val base: BigDecimal?, // 数值类型根据业务需求调整（如 Int 或 BigDecimal）
    val createTime: String?, // 建议根据实际时间格式解析
    val createUserBelongType: Int?,
    val createUserId: String?,
    val createUserType: String?,
    val designatedGoodIds: String?,
//    val designatedGoods: List<Any?>?,
    val endTime: String?,
    val expired: Boolean?,
//    val giftGoodBrieList: List<Any?>?, // 字段含义不明确，按原样保留
    val giftGoods: List<GiftGood>?,
    val giftNum: Int?,
    val id: String?,
    val intro: String?,
    val labelColor: String?,
    val labelName: String?,
    val mainStoreId: String?,
    val name: String?,
    val saasStoreId: String?,
    val startTime: String?,
    val status: Status?,
    val storeId: String?,
    val storeIds: String?,
    val storeUsageType: StoreUsageType?,
    val targetCustomer: String?,
    val templateKey: String?,
    val threshold: String?, // "THRESHOLD_ITEM_NUM" 可能是枚举值
    val updateTime: String?,
    val updateUserId: String?,

    val updateUserType: String,

    val usageStores: List<String>?,

    val usageType: UsageType?,
    val usingRules: String? = null,
)

data class GiftGood(
    val id: String,
    val num: Int
)


// 枚举类型定义（根据实际业务补充）
enum class UsageType {
    PARTIAL_GOODS // 示例值，需补充完整枚举
}

enum class StoreUsageType {
    PARTIAL // 示例值，需补充完整枚举
}

enum class Status {
    RELEASE // 示例值，需补充完整枚举
}

enum class ActiveState {
    IN_PROGRESS // 示例值，需补充完整枚举
}

enum class PromotionActivityThreshold {
    NOTHRESHOLD,
    THRESHOLD_ORDER_AMOUNT,
    THRESHOLD_ITEM_NUM,
    THRESHOLD_DESIGNATED_GOOD

}

