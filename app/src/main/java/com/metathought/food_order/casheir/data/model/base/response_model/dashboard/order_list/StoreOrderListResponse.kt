package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel

data class StoreOrderListResponse(
    @SerializedName("storeDataOrderVos")
    val storeDataOrderVos: List<StoreDataOrderVo?>?,
    @Transient
    var offlineChannelTotalModel: OfflineChannelTotalModel?=null,
    @SerializedName("total")
    val total: Int?
)