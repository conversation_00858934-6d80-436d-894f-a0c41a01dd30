package com.metathought.food_order.casheir.data.model.base.response_model.member.statistic


import com.google.gson.annotations.SerializedName

data class CurrentMemberOverview(
    @SerializedName("consumerMembersNum")
    val consumerMembersNum: Long?,
    @SerializedName("consumptionAmount")
    val consumptionAmount: Long?,
    @SerializedName("currentAccountBalance")
    val currentAccountBalance: Long?,
    @SerializedName("currentMembersNum")
    val currentMembersNum: Long?,
    @SerializedName("rechargeAmount")
    val rechargeAmount: Long?,
    @SerializedName("rechargeMembersNum")
    val rechargeMembersNum: Long?
)