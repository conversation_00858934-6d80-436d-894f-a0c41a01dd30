package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding

class OrderTypeAdapter(
    val list: List<DiningStyleEnum>,
    private var selectedItems: DiningStyleEnum? = null,
    val context: Context
) : RecyclerView.Adapter<OrderTypeAdapter.OrderTypeViewHolder>() {

    var onItemClickCallback: ((DiningStyleEnum) -> Unit)? = null

    inner class OrderTypeViewHolder(val binding: FilterTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: DiningStyleEnum) {
            binding.apply {
                tvTableID.text = getDisplayName(item)
                tvTableID.gravity = Gravity.CENTER
                tvTableID.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (selectedItems == item) R.color.primaryColor else android.R.color.black
                    )
                )
                root.setCardBackgroundColor(
                    ContextCompat.getColor(
                        context,
                        if (selectedItems == item) R.color.filter_table_background else android.R.color.transparent
                    )
                )
                root.setOnClickListener {
                    onItemClickCallback?.invoke(item)
                }
            }
        }
    }

    private fun getDisplayName(style: DiningStyleEnum): String {
        return when (style) {
            DiningStyleEnum.DINE_IN -> context.getString(R.string.dine_in)
            DiningStyleEnum.TAKE_AWAY -> context.getString(R.string.take_away)
            DiningStyleEnum.PRE_ORDER -> context.getString(R.string.pre_order)
            DiningStyleEnum.TAKE_OUT -> context.getString(R.string.take_out)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderTypeViewHolder {
        val binding =
            FilterTableItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderTypeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderTypeViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

//    fun setSelectedItems(selectedItems: MutableList<DiningStyleEnum>) {
//        this.selectedItems = selectedItems
//        notifyDataSetChanged()
//    }
}
