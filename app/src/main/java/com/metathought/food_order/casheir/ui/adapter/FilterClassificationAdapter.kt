package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.GoodClassificationModel
import com.metathought.food_order.casheir.databinding.FilterTableItemBinding


class FilterClassificationAdapter(
    val list: ArrayList<GoodClassificationModel>,
    val context: Context,
    val selected: List<GoodClassificationModel>,
) : RecyclerView.Adapter<FilterClassificationAdapter.TableItemViewHolder>() {

    init {
        selected.isNotEmpty().let {
            for (model in selected) {
                list.firstOrNull {
                    it.groupId == model.groupId
                }?.ischeck = true
            }
        }
    }

    inner class TableItemViewHolder(val binding: FilterTableItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: GoodClassificationModel?, index: Int) {
            resource?.let { _ ->
                binding.apply {
                    tvTableID.text = resource.groupName
                    tvTableID.setTextColor(
                        ContextCompat.getColor(
                            context,
                            if (resource.ischeck) R.color.primaryColor else android.R.color.black
                        )
                    )
                    root.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            if (resource.ischeck) R.color.filter_table_background else android.R.color.transparent
                        )
                    )
                    root.setOnClickListener {
                        resource.ischeck = resource.ischeck != true
                        notifyItemChanged(index)
                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableItemViewHolder {
        var itemView = FilterTableItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )
        return TableItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: TableItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun getSelectedArrayList(): List<GoodClassificationModel> {
        return list.filter { it.ischeck }
    }

    fun resetSelect() {
        val selectedTable = list.filter { it.ischeck }
        for (table in selectedTable) {
            table.ischeck = false
        }
        notifyDataSetChanged()
    }

//    fun updateItems(newItems: List<GoodClassificationModel>) {
//        list.clear()
//        list.addAll(newItems)
//        selected.isNotEmpty().let {
//            for (tableID in selected) {
//                list.firstOrNull {
//                    it.groupId == tableID.groupId
//                }?.ischeck = true
//            }
//        }
//        notifyDataSetChanged()
//    }

//    fun updateItemCheck(selectedTableItem: ArrayList<GoodClassificationModel>) {
//        list.forEach {
//            it.ischeck = false
//        }
//        selectedTableItem.forEach {
//            val indexOf = list.indexOf(it)
//            if (indexOf != -1) {
//                list[indexOf].ischeck = true
//            }
//        }
//        notifyDataSetChanged()
//    }

}