package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.graphics.Color
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dboy.chips.ChipsLayoutManager
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.MealSetGroupItemBinding


class MealSetAdapter(
    val isShowImage: Boolean? = false,
    val list: List<MealSetGroup>,
    val good: Goods,
    val mContext: Context,
    val onAddCallback: ((MealSetGroup, Int, Int) -> Unit)? = null,
    val onSubCallback: ((MealSetGroup, Int, Int) -> Unit)? = null,
    val onUpdateCallback: ((MealSetGroup, Int, Int) -> Unit)? = null,
    val onShowSelectedTag: ((MealSetGroup, Int, Int, View) -> Unit)? = null,
    val isSecondView: Boolean? = false
) : RecyclerView.Adapter<MealSetAdapter.MealSetViewHolder>() {

    private var currentGoodSelectIndex = -1

    fun setCurrentGoodSelectIndex(selectIndex: Int) {
        this.currentGoodSelectIndex = selectIndex
    }

    inner class MealSetViewHolder(val binding: MealSetGroupItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: MealSetGroup?, position: Int) {
            resource?.let { it1 ->

                if (resource.repeatSelect == true) {
                    val gridLayoutManager =
                        GridLayoutManager(mContext, if (isShowImage == true) 2 else 3)
                    binding.rvMealGood.layoutManager = gridLayoutManager
                    //重复选择样式
                    resource.mealSetGoodsList.let {
                        binding.rvMealGood.adapter = MealSetGoodsRepeatAdapter(
                            isShowImage,
                            resource,
                            good,
                            mContext,
                            onAddCallback = { goodIndex ->
                                onAddCallback?.invoke(resource, position, goodIndex)
                            },
                            onSubCallback = { goodIndex ->
                                //  notifyItemChanged(position)
                                onSubCallback?.invoke(resource, position, goodIndex)
                            },
                            onUpdateCheck = { goodIndex ->
                                onUpdateCallback?.invoke(resource, position, goodIndex)
                            },
                            onShowSelectedTag = { goodIndex, view ->
                                onShowSelectedTag?.invoke(resource, position, goodIndex, view)
                            },
                            isSecondView = isSecondView,
                            selectIndex = currentGoodSelectIndex
                        )
                    }
                } else {
                    //不能重复选样式
//                    val chipsLayoutManager = ChipsLayoutManager.newBuilder(mContext).build()
//                    binding.rvMealGood.layoutManager = chipsLayoutManager

                    FlexboxLayoutManager(itemView.context).run {
                        flexDirection = FlexDirection.ROW
                        flexWrap = FlexWrap.WRAP
                        justifyContent = JustifyContent.FLEX_START
                        binding.rvMealGood.layoutManager = this
                    }

                    resource.mealSetGoodsList.let {
                        binding.rvMealGood.adapter = MealSetGoodsNoRepeatAdapter(
                            isShowImage,
                            resource,
                            good,
                            mContext,
                            onClickCallback = { goodIndex ->
                                onAddCallback?.invoke(resource, position, goodIndex)
                            },
                            onUpdateCheck = { goodIndex ->
                                onUpdateCallback?.invoke(resource, position, goodIndex)
                            })
                    }
                }

                val part1 = "${resource.name} "
                val spannable1 = SpannableString(part1)
                val colorSpan1 = ForegroundColorSpan(Color.BLACK)
                spannable1.setSpan(colorSpan1, 0, part1.length, 0)
                val part2 = resource.getHasSelectStr(mContext)
                val spannable2 = SpannableString(part2)
                val colorSpan2 = ForegroundColorSpan(mContext.getColor(R.color.black60))
                spannable2.setSpan(colorSpan2, 0, part2.length, 0)

                val spannableStringBuilder = SpannableStringBuilder()
                spannableStringBuilder.append(spannable1)
                spannableStringBuilder.append(spannable2)
                binding.tvTitle.text = spannableStringBuilder

            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MealSetViewHolder {
        return MealSetViewHolder(
            MealSetGroupItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }


    override fun getItemCount(): Int {
        return list.count()
    }

    override fun onBindViewHolder(holder: MealSetViewHolder, position: Int) {
        holder.bind(list[position], position)
    }




}