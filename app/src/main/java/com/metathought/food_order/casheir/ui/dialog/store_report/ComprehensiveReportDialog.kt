package com.metathought.food_order.casheir.ui.dialog.store_report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.Toast
import androidx.fragment.app.viewModels
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.lxj.xpopup.enums.PopupPosition
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum

import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.constant.MergeType
import com.metathought.food_order.casheir.constant.QuickTimeType
import com.metathought.food_order.casheir.data.model.base.request_model.ComprehensiveReportRequest
import com.metathought.food_order.casheir.databinding.DialogComprehensiveReportBinding
import com.metathought.food_order.casheir.ui.adapter.MergeTypeAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderTypeAdapter
import com.metathought.food_order.casheir.ui.adapter.ComprehensiveReportHeaderAdapter
import com.metathought.food_order.casheir.ui.adapter.ProcessedReportContentAdapter
import com.metathought.food_order.casheir.ui.adapter.DateHeaderAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.widget.TimeSelectionView
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.data.model.base.response_model.Header
import com.metathought.food_order.casheir.data.model.base.response_model.ProcessedReportData
import com.metathought.food_order.casheir.data.model.base.response_model.ColumnData
import com.metathought.food_order.casheir.data.model.base.response_model.ComprehensiveReportData
import com.metathought.food_order.casheir.databinding.DialogFilterTimeSlotBinding
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.PermissionHelper
import com.metathought.food_order.casheir.model.QuickTimeItem
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.PopupUtils
import com.metathought.food_order.casheir.utils.RecyclerViewScrollSyncHelper
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TableScrollController
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 综合报表弹窗
 * ┌─────────────┬─────────────┬─────────────┬─────────────┐
 * │    日期     │ 2024-01-01  │ 2024-01-02  │ 2024-01-03  │ ← 水平同步
 * ├─────────────┼─────────────┼─────────────┼─────────────┤
 * │   总金额  ↕ │  15000.50   │  12000.30   │   8000.25   │ ← 水平同步
 * │   堂食金额↕ │   8000.25   │   6000.15   │   4000.15   │ ← 水平同步
 * │   外带金额↕ │   4000.15   │   3000.10   │   2000.05   │ ← 水平同步
 * │    ... ↕   │     ...     │     ...     │     ...     │ ← 水平同步
 * └─────────────┴─────────────┴─────────────┴─────────────┘
 *      ↕ 垂直同步
 */
@AndroidEntryPoint
class ComprehensiveReportDialog : BaseDialogFragment() {


    private val viewModel: ComprehensiveReportViewModel by viewModels()

    private var binding: DialogComprehensiveReportBinding? = null
    private var orderTypePopupWindow: PopupWindow? = null
    private var mergeTypePopupWindow: PopupWindow? = null
    private var selectedOrderType: DiningStyleEnum? = null
    private var selectedMergeType = MergeType.MERGED_TIME
//    private var selectedQuickTime: QuickTimeType? = QuickTimeType.TODAY
//    private var startDate: Date? = null
//    private var endDate: Date? = null

    // 表格适配器
    private var headerAdapter: ComprehensiveReportHeaderAdapter? = null
    private var contentAdapter: ProcessedReportContentAdapter? = null
    private var dateHeaderAdapter: DateHeaderAdapter? = null

    // 滚动同步帮助类
    private val scrollSyncHelper = RecyclerViewScrollSyncHelper()

    // 表格滚动控制器
    private val tableScrollController = TableScrollController()

    // 时间选择组件
    private var timeSelectionView: TimeSelectionView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DialogComprehensiveReportBinding.inflate(inflater, container, false)
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initRecyclerViews()
        initTimeSelectionView()
        initClickListeners()
        observeViewModel()


        val request = ComprehensiveReportRequest(
            // 传入选中的订单类型列表
            diningStyle = null,
            // 传入合并类型的 code 值
            mergeType = selectedMergeType?.code,
            // 传入快捷时间的 code 值
            timeType = timeSelectionView?.getSelectedQuickTime()?.type
        )
        // 调用 ViewModel 的方法发起请求
        viewModel.getReportFormPage(request)
    }

    private fun initRecyclerViews() {
        // 初始化日期标题RecyclerView - 水平滑动
        dateHeaderAdapter = DateHeaderAdapter(arrayListOf(), requireContext())
        binding?.rvDateHeaders?.apply {
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = dateHeaderAdapter
        }

        // 初始化字段名称RecyclerView - 垂直滑动
        headerAdapter = ComprehensiveReportHeaderAdapter(
            arrayListOf(),
            requireContext()
        ) { infoButton, describe ->
            PopupUtils.showSmartTipPopup(
                requireContext(),
                infoButton,
                message = describe,
                position = PopupPosition.Right
            )
        }

        binding?.rvHeaders?.apply {
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            adapter = headerAdapter
        }

        // 初始化内容RecyclerView - 垂直滑动
        contentAdapter = ProcessedReportContentAdapter(requireContext())
        binding?.rvContent?.apply {
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            adapter = contentAdapter
        }

        // 设置表格滚动控制器
        binding?.rvDateHeaders?.let { tableScrollController.setDateHeaderRecyclerView(it) }
        contentAdapter?.setScrollController(tableScrollController)

        // 让日期标题适配器使用内容区域的宽度，确保完全一致
        binding?.rvContent?.let { dateHeaderAdapter?.setContentRecyclerView(it) }
        println("日期标题适配器将使用内容区域宽度来计算列宽")

        // 现在滚动完全由 TableScrollController 处理，不需要额外的监听器
        println("滚动同步由 TableScrollController 统一处理")


        // 设置滚动同步
        setupScrollSync()
    }

    private fun setupScrollSync() {
        val rvHeaders = binding?.rvHeaders
        val rvContent = binding?.rvContent
        val rvDateHeaders = binding?.rvDateHeaders

        if (rvHeaders != null && rvContent != null && rvDateHeaders != null) {
            // 1. 垂直滚动同步：rvHeaders 和 rvContent
            scrollSyncHelper.syncVerticalScroll(rvHeaders, rvContent)

            // 2. 添加垂直滚动监听，处理ViewHolder复用问题
            setupVerticalScrollListener(rvContent)

            println("滚动同步设置完成，现在由 TableScrollController 处理水平滚动")
        }
    }

    private fun initTimeSelectionView() {
        timeSelectionView = binding?.timeSelectionView

        timeSelectionView?.setQuickTimeList(
            listOf(
                QuickTimeItem(getString(R.string.today), QuickTimeType.TODAY.code),
                QuickTimeItem(getString(R.string.yesterday), QuickTimeType.YESTERDAY.code),
                QuickTimeItem(getString(R.string.last_7_days), QuickTimeType.SEVEN_DAYS.code),
                QuickTimeItem(getString(R.string.last_30_days), QuickTimeType.THIRTY_DAYS.code),
            ), QuickTimeType.SEVEN_DAYS.code
        )

        // 设置 FragmentManager
        timeSelectionView?.setFragmentManager(parentFragmentManager)
        timeSelectionView?.setDateRangeValidator { startDate, endDate ->
            val diffInMillis = endDate.time - startDate.time
            val diffInDays = diffInMillis / (1000 * 60 * 60 * 24)
            if (diffInDays > 31) {
                getString(R.string.date_range_too_long)
            } else {
                null // 校验通过
            }
        }

        // 设置 Toast 监听器
        timeSelectionView?.setOnShowToastListener { message ->
            // 这里可以显示 Toast 或其他提示
            showToast(message)
        }

        // 设置快捷时间选择监听器
        timeSelectionView?.setOnQuickTimeSelectedListener { quickTimeType ->
//            selectedQuickTime = quickTimeType
        }

        // 设置日期选择监听器
        timeSelectionView?.setOnDateSelectedListener { startDateParam, endDateParam ->
//            startDate = startDateParam
//            endDate = endDateParam
        }
    }


    private fun setupVerticalScrollListener(rvContent: androidx.recyclerview.widget.RecyclerView) {
        rvContent.addOnScrollListener(object :
            androidx.recyclerview.widget.RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                newState: Int
            ) {
                super.onScrollStateChanged(recyclerView, newState)
                // 当垂直滚动停止时，强制同步所有ViewHolder的水平位置
                if (newState == androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE) {
                    println("=== 垂直滚动停止，强制同步水平位置 ===")
                    println("当前滚动位置: ${tableScrollController.getCurrentScrollX()}")
                    tableScrollController.forceSync()
                    println("=== 强制同步完成 ===")
                }
            }
        })
    }

    private fun observeViewModel() {
        viewModel.reportResponse.observe(viewLifecycleOwner, Observer { response ->
            when (response) {
                is ApiResponse.Loading -> {
                    // 显示查询loading状态
                    binding?.layoutLoading?.visibility = View.VISIBLE
                    binding?.rvDateHeaders?.visibility = View.GONE
                    binding?.rvHeaders?.visibility = View.GONE
                    binding?.rvContent?.visibility = View.GONE
                }

                is ApiResponse.Success -> {
                    // 隐藏loading状态
                    binding?.layoutLoading?.visibility = View.GONE
                    binding?.rvDateHeaders?.visibility = View.VISIBLE
                    binding?.rvHeaders?.visibility = View.VISIBLE
                    binding?.rvContent?.visibility = View.VISIBLE

                    // 处理接口返回的数据，添加合并时间信息
                    viewModel.printerData = processApiResponse(response.data)
                    // 更新表格数据
                    if (viewModel.printerData != null) {
                        updateTableData(
                            viewModel.printerData!!.headerList,
                            viewModel.printerData!!.masterReportDataList
                        )
                    }
                }

                is ApiResponse.Error -> {
                    // 隐藏loading状态
                    binding?.layoutLoading?.visibility = View.GONE
                    binding?.rvDateHeaders?.visibility = View.VISIBLE
                    binding?.rvHeaders?.visibility = View.VISIBLE
                    binding?.rvContent?.visibility = View.VISIBLE

                    // 显示错误信息
                    Toast.makeText(
                        requireContext(),
                        "查询失败: ${response.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        })
        viewModel.exportUrlResponse.observe(viewLifecycleOwner, Observer { response ->
            when (response) {
                is ApiResponse.Loading -> {
                    // 显示加载状态
                    binding?.tvExport?.isEnabled = false
                }

                is ApiResponse.Success -> {
                    download(response.data)
//                    updateTableData(response.data.headerList, response.data.masterReportDataList)
                }

                is ApiResponse.Error -> {
                    binding?.tvExport?.isEnabled = true
                    // 显示错误信息
                }
            }
        })

    }

    /**
     * 处理API响应，添加合并时间信息
     */
    /**
     * 处理API响应，添加合并时间信息
     */
    private fun processApiResponse(data: ComprehensiveReportData): ComprehensiveReportData {
        // 检查是否满足特定条件
        val isMergedTime = selectedMergeType == MergeType.MERGED_TIME
        val startDate = binding?.timeSelectionView?.getStartDate()
        val endDate = binding?.timeSelectionView?.getEndDate()
        val selectedQuickTime = binding?.timeSelectionView?.getSelectedQuickTime()?.type
        val isTodayOrYesterday =
            selectedQuickTime in listOf(QuickTimeType.TODAY.code, QuickTimeType.YESTERDAY.code)
        val isOneDayRange = startDate != null && endDate != null &&
                startDate.time == endDate.time
        val isMultiDays =
            selectedQuickTime in listOf(
                QuickTimeType.SEVEN_DAYS.code,
                QuickTimeType.THIRTY_DAYS.code
            ) ||
                    (startDate != null && endDate != null && startDate.time != endDate.time)

        Timber.d("isMergedTime: $isMergedTime")
        Timber.d("isTodayOrYesterday: $isTodayOrYesterday")
        Timber.d("isOneDayRange: $isOneDayRange")
        Timber.d("isMultiDays: $isMultiDays")

        data.headerList = data.headerList.filter { header ->
            // 过滤掉 storeId 和 date 字段
            header.key !in listOf("storeId", "date", "storeName")
        }

        // 如果是合并类型且没有日期信息，添加日期标签到数据中
        val processedDataList = data.masterReportDataList.mapIndexed { index, dataMap ->
            val mutableMap = dataMap.toMutableMap()
            if (isMergedTime) {
                //如果是合并打印
                if (isTodayOrYesterday || isOneDayRange) {
                    val calendar = Calendar.getInstance()
                    if (selectedQuickTime == QuickTimeType.YESTERDAY.code) {
                        calendar.add(Calendar.DAY_OF_YEAR, -1)
                    } else if (startDate != null) {
                        calendar.time = startDate!!
                    }
                    val dateFormat = SimpleDateFormat("yyyy/MM/dd", Locale.US)
                    val formattedDate = dateFormat.format(calendar.time)
                    mutableMap["date"] = formattedDate
                    Timber.d("Added single date: $formattedDate to item at index $index")
                } else {
                    val dateFormat = SimpleDateFormat("yyyy/MM/dd", Locale.US)
                    val startDateStr = if (startDate != null) {
                        dateFormat.format(startDate!!)
                    } else if (selectedQuickTime == QuickTimeType.SEVEN_DAYS.code) {
                        val calendar = Calendar.getInstance()
                        calendar.add(Calendar.DAY_OF_YEAR, -6)
                        dateFormat.format(calendar.time)
                    } else if (selectedQuickTime == QuickTimeType.THIRTY_DAYS.code) {
                        val calendar = Calendar.getInstance()
                        calendar.add(Calendar.DAY_OF_YEAR, -29)
                        dateFormat.format(calendar.time)
                    } else {
//                        dateFormat.format(
//                            dateLabels.firstOrNull()
//                                ?.let { SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(it) })
                    }

                    val endDateStr = if (endDate != null) {
                        dateFormat.format(endDate!!)
                    } else {
                        dateFormat.format(Calendar.getInstance().time)
                    }
                    mutableMap["date"] = "$startDateStr—$endDateStr"
                    Timber.d("Added date range: $startDateStr-$endDateStr to item at index $index")
                }
            } else {
                Timber.d("isMergedTime condition not met, date not added to item at index $index")
            }

            mutableMap.toMutableMap()
        }

        return data.copy(masterReportDataList = processedDataList)
    }


    private fun updateTableData(headers: List<Header>, dataList: List<Map<String, Any?>>) {
        // 处理数据：根据headerList顺序整理每个字段的值列表
        val processedData = processReportData(headers, dataList)

        // 设置表头信息到内容适配器（用于高亮判断）
        contentAdapter?.setHeaders(headers)

        // 预先计算列宽，然后再更新数据，避免闪烁
        preCalculateColumnWidthAndUpdateData(processedData, headers)

        // 打印处理后的数据结构（用于调试）
        printProcessedData(processedData)
    }

    /**
     * 预先计算列宽，然后更新数据，避免界面闪烁
     */
    private fun preCalculateColumnWidthAndUpdateData(
        processedData: ProcessedReportData,
        headers: List<Header>
    ) {
        val dataCount = processedData.dateLabels.size
        if (dataCount <= 0) {
            // 如果没有数据，直接更新
            updateAdaptersWithData(processedData, headers)
            return
        }

        // 获取内容区域的RecyclerView
        val contentRv = binding?.rvContent
        if (contentRv == null) {
            // 如果RecyclerView不存在，直接更新
            updateAdaptersWithData(processedData, headers)
            return
        }

        // 等待RecyclerView布局完成后计算列宽
        contentRv.post {
            val contentAreaWidth = contentRv.width
            if (contentAreaWidth > 0) {
                // 使用统一的计算工具计算列宽
                val calculatedWidth =
                    com.metathought.food_order.casheir.utils.ColumnWidthCalculator.calculateColumnWidth(
                        requireContext(), contentAreaWidth, dataCount, 110f
                    )

                println("=== 预先计算列宽 ===")
                println("数据数量: $dataCount")
                println("内容区域宽度: ${contentAreaWidth}px")
                println("计算得到的列宽: ${calculatedWidth}dp")
                println("=== 开始更新数据 ===")

                // 先设置列宽到适配器
                contentAdapter?.setPreCalculatedColumnWidth(calculatedWidth)
                dateHeaderAdapter?.setPreCalculatedColumnWidth(calculatedWidth)

                // 然后更新数据
                updateAdaptersWithData(processedData, headers)
            } else {
                // 如果宽度为0，直接更新（使用默认宽度）
                updateAdaptersWithData(processedData, headers)
            }
        }
    }

    /**
     * 使用预计算的列宽更新所有适配器的数据
     */
    private fun updateAdaptersWithData(processedData: ProcessedReportData, headers: List<Header>) {
        // 更新日期标题（水平）
        dateHeaderAdapter?.replaceDataWithPreCalculatedWidth(ArrayList(processedData.dateLabels))
        // 刷新后滚动到起始位置
        binding?.rvDateHeaders?.scrollToPosition(0)

        // 更新内容列表（使用预计算的列宽）
        contentAdapter?.updateDataWithPreCalculatedWidth(processedData)

        // 更新字段名称（垂直）
        headerAdapter?.replaceData(ArrayList(headers))

        println("=== 数据更新完成 ===")
    }


    /**
     * 根据headerList的顺序，将masterReportDataList中每个Map的相同字段值整理成列表
     * @param headers 表头列表
     * @param dataList 数据列表（每个元素是一个Map，包含该条记录的所有字段）
     * @return 处理后的数据结构
     */
    private fun processReportData(
        headers: List<Header>,
        dataList: List<Map<String, Any?>>
    ): ProcessedReportData {
        val fieldDataMap = mutableMapOf<String, List<Any?>>()
        val columnDataList = mutableListOf<ColumnData>()

//        // 检查是否满足特定条件
//        val isMergedTime = selectedMergeType == MergeType.MERGED_TIME
//        val isTodayOrYesterday =
//            selectedQuickTime in listOf(QuickTimeType.TODAY, QuickTimeType.YESTERDAY)
//        val isOneDayRange = startDate != null && endDate != null &&
//                startDate!!.time == endDate!!.time
//        val isMultiDays =
//            selectedQuickTime in listOf(QuickTimeType.SEVEN_DAYS, QuickTimeType.THIRTY_DAYS) ||
//                    (startDate != null && endDate != null && startDate!!.time != endDate!!.time)


        // 生成日期标识列表（从每个Map中提取日期信息）
        var dateLabels = dataList.mapIndexed { index, dataMap ->
            // 尝试从Map中获取日期字段，如果没有则使用索引生成
            dataMap["date"] as? String ?: ""
        }

//        if (isMergedTime) {
//            if (isTodayOrYesterday || isOneDayRange) {
//                val calendar = Calendar.getInstance()
//                if (selectedQuickTime == QuickTimeType.YESTERDAY) {
//                    calendar.add(Calendar.DAY_OF_YEAR, -1)
//                } else if (startDate != null) {
//                    calendar.time = startDate!!
//                }
//                val dateFormat = SimpleDateFormat("MM/dd", Locale.US)
//                val formattedDate = dateFormat.format(calendar.time)
//                dateLabels = listOf(formattedDate)
//            } else if (isMultiDays) {
//                val dateFormat = SimpleDateFormat("MM/dd", Locale.US)
//                val startDateStr = if (startDate != null) {
//                    dateFormat.format(startDate!!)
//                } else if (selectedQuickTime == QuickTimeType.SEVEN_DAYS) {
//                    val calendar = Calendar.getInstance()
//                    calendar.add(Calendar.DAY_OF_YEAR, -6)
//                    dateFormat.format(calendar.time)
//                } else if (selectedQuickTime == QuickTimeType.THIRTY_DAYS) {
//                    val calendar = Calendar.getInstance()
//                    calendar.add(Calendar.DAY_OF_YEAR, -29)
//                    dateFormat.format(calendar.time)
//                } else {
//                    dateFormat.format(
//                        dateLabels.firstOrNull()
//                            ?.let { SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(it) })
//                }
//
//                val endDateStr = if (endDate != null) {
//                    dateFormat.format(endDate!!)
//                } else {
//                    dateFormat.format(Calendar.getInstance().time)
//                }
//
//                dateLabels = listOf("$startDateStr-$endDateStr")
//            } else if (dateLabels.size == 1) {
//                dateLabels = listOf(dateLabels.first())
//            }
//        }

        // 遍历每个表头字段
        headers.forEach { header ->
            val fieldKey = header.key
            val fieldValues = mutableListOf<Any?>()

            // 遍历每个数据Map，提取对应字段的值
            dataList.forEach { dataMap ->
                val fieldValue = dataMap[fieldKey]
                fieldValues.add(fieldValue)
            }

            // 将该字段的所有日期值存储到map中
            fieldDataMap[fieldKey] = fieldValues

            // 创建列数据
            val columnData = ColumnData(
                title = header.value,
                fieldKey = fieldKey,
                values = fieldValues
            )
            columnDataList.add(columnData)
        }

        return ProcessedReportData(
            headers = headers,
            dateLabels = dateLabels,
            fieldDataMap = fieldDataMap,
            columnDataList = columnDataList
        )
    }

    /**
     * 通过反射获取对象指定字段的值
     * @param obj 目标对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private fun getFieldValue(obj: Any, fieldName: String): Any? {
        return try {
            val field = obj.javaClass.getDeclaredField(fieldName)
            field.isAccessible = true
            field.get(obj)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 打印处理后的数据结构（用于调试）
     */
    private fun printProcessedData(processedData: ProcessedReportData) {
        println("=== 综合报表数据处理结果 ===")
        println("日期数量: ${processedData.dateLabels.size}")
        println("字段数量: ${processedData.headers.size}")
        println("日期列表: ${processedData.dateLabels}")

        println("\n=== 按列组织的数据 ===")
        processedData.columnDataList.forEachIndexed { index, columnData ->
            println("列 $index: ${columnData.title} (${columnData.fieldKey})")
            println("  数据: ${columnData.values}")
        }

        println("\n=== 按字段组织的数据 ===")
        processedData.fieldDataMap.forEach { (fieldKey, values) ->
            val header = processedData.headers.find { it.key == fieldKey }
            println("${header?.value ?: fieldKey} ($fieldKey): $values")
        }
        println("=== 数据处理完成 ===")
    }

    private fun initClickListeners() {
        binding?.topBar?.getCloseBtn()?.setOnClickListener {
            dismissCurrentDialog()
        }

        // 订单类型点击事件
        binding?.dropdownOrderType?.setOnClickListener {
            showOrderTypePopupWindow(it)
        }

        // 合并类型点击事件
        binding?.dropdownMergeType?.setOnClickListener {
            showMergeTypePopupWindow(it)
        }

        // 快捷时间和日期选择现在由 TimeSelectionView 组件处理

        // 重置按钮点击事件
        binding?.btnReset?.setOnClickListener {
            resetFilters()
            val selectedQuickTime = binding?.timeSelectionView?.getSelectedQuickTime()
            // 重新发起请求
            val request = ComprehensiveReportRequest(
                diningStyle = null,
                mergeType = selectedMergeType.code,
                timeType = selectedQuickTime?.type
            )
            viewModel.getReportFormPage(request)
        }

        // 搜索按钮点击事件
        binding?.btnSearch?.setOnClickListener {
            val diningStyle = if (selectedOrderType != null) {
                selectedOrderType!!.id
            } else {
                null
            }
//            val startDate = binding?.timeSelectionView?.getStartDate()
//            val endDate = binding?.timeSelectionView?.getEndDate()
//            val startDateStr = formatDateForApi(startDate, true)
//            val endDateStr = formatDateForApi(endDate, false)
            val selectedQuickTime = binding?.timeSelectionView?.getSelectedQuickTime()
            val startTime = if (binding?.timeSelectionView?.getStartDate() != null) {
                binding?.timeSelectionView?.getStartDate()?.formatDateStr(FORMAT_DATE_REALIZED)
            } else {
                null
            }
            val endTime = if (binding?.timeSelectionView?.getEndDate() != null) {
                binding?.timeSelectionView?.getEndDate()?.formatDateStr(FORMAT_DATE_REALIZED)
            } else {
                null
            }

            val request = ComprehensiveReportRequest(
                // 传入选中的订单类型列表，如果未选择则传所有类型
                diningStyle = diningStyle,
                // 传入合并类型的 code 值
                mergeType = selectedMergeType.code,
                // 传入快捷时间的 code 值
                timeType = selectedQuickTime?.type,
                // 传入开始日期
                startTime = startTime,
                // 传入结束日期
                endTime = endTime
            )
            // 调用 ViewModel 的方法发起请求
            viewModel.getReportFormPage(request)
        }

        // 打印按钮点击事件
        binding?.tvPrint?.setOnClickListener {
            // 处理打印逻辑
            SingleClickUtils.isFastDoubleClick {
                if ((viewModel.printerData?.masterReportDataList?.size ?: 0) >= 3) {
                    ConfirmDialog.showDialog(
                        parentFragmentManager,
                        content = getString(
                            R.string.sure_to_print_comprehensive,
                            "${viewModel.printerData?.masterReportDataList?.size ?: 0}"
                        ),
                        positiveButtonTitle = getString(R.string.confirm),
                        negativeButtonTitle = getString(R.string.cancel),
                    ) {
                        viewModel.printComprehensiveReport(requireContext())
                    }
                } else {
                    viewModel.printComprehensiveReport(requireContext())
                }
            }
        }

        binding?.tvReportFields?.setOnClickListener {
            // 打开报表字段选择弹窗
            ReportFieldsDialog.showDialog(
                parentFragmentManager,
            ) { selectedFields ->
                val diningStyle = if (selectedOrderType != null) {
                    selectedOrderType!!.id
                } else {
                    null
                }
//                val startDate = binding?.timeSelectionView?.getStartDate()
//                val endDate = binding?.timeSelectionView?.getEndDate()
                val selectedQuickTime = binding?.timeSelectionView?.getSelectedQuickTime()

                val startTime = if (binding?.timeSelectionView?.getStartDate() != null) {
                    binding?.timeSelectionView?.getStartDate()?.formatDateStr(FORMAT_DATE_REALIZED)
                } else {
                    null
                }
                val endTime = if (binding?.timeSelectionView?.getEndDate() != null) {
                    binding?.timeSelectionView?.getEndDate()?.formatDateStr(FORMAT_DATE_REALIZED)
                } else {
                    null
                }


                val request = ComprehensiveReportRequest(
                    // 传入选中的订单类型列表，如果未选择则传所有类型
                    diningStyle = diningStyle,
                    // 传入合并类型的 code 值
                    mergeType = selectedMergeType.code,
                    // 传入快捷时间的 code 值
                    timeType = selectedQuickTime?.type,
                    // 传入开始日期
                    startTime = startTime,
                    // 传入结束日期
                    endTime = endTime
                )
                // 调用 ViewModel 的方法发起请求
                viewModel.getReportFormPage(request)
            }
        }

        // 导出按钮点击事件
        binding?.tvExport?.setOnClickListener {
            // 处理导出逻辑
            SingleClickUtils.isFastDoubleClick {
                if (PermissionHelper.checkPermissions(requireActivity())) {
                    val request = viewModel.getLastSearchRequest()
                    viewModel.getReportFormExportUrl(request, requireContext())
                } else {
                    PermissionHelper.requestPermissions(requireActivity())
                }
            }
        }
    }

    /**
     * 将日期格式化为 API 请求需要的格式
     * @param date 要格式化的日期
     * @param isStart 是否为开始日期，如果是开始日期，时间设置为 00:00:00，否则设置为 23:59:59
     * @return 格式化后的日期字符串，如果日期为 null 则返回 null
     */
    private fun formatDateForApi(date: Date?, isStart: Boolean): String? {
        return date?.let {
            val calendar = Calendar.getInstance()
            calendar.time = it
            if (isStart) {
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
            } else {
                calendar.set(Calendar.HOUR_OF_DAY, 23)
                calendar.set(Calendar.MINUTE, 59)
                calendar.set(Calendar.SECOND, 59)
                calendar.set(Calendar.MILLISECOND, 999)
            }
            SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(calendar.time)
        }
    }

    private fun resetFilters() {
        // 重置订单类型
        selectedOrderType = null
        binding?.tvOrderType?.text = getString(R.string.order_type)
        binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black20, null))
        binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
        binding?.arrowOrderType?.setOnClickListener(null)

        // 重置合并类型
        selectedMergeType = MergeType.MERGED_TIME
        binding?.tvMergeType?.text = getString(R.string.merged_time)

        // 重置时间选择
        timeSelectionView?.resetTimeSelection()
    }


    private fun showOrderTypePopupWindow(anchorView: View) {
        val popupViewTable = DialogFilterTimeSlotBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupViewTable.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        binding?.arrowOrderType?.animate()?.rotation(180f)?.setDuration(200)
        // 显示弹窗时将图标设置为下拉箭头
        binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)

        popupWindow.elevation = 20f
        popupWindow.animationStyle = R.style.PopupAnimation
        // 显示弹窗时设置背景
        anchorView.setBackgroundResource(R.drawable.background_spinner_top)
        popupWindow.showAsDropDown(anchorView)

        popupWindow.setOnDismissListener {
            binding?.arrowOrderType?.animate()?.rotation(0f)?.setDuration(200)
            // 弹窗关闭时恢复原背景
            anchorView.setBackgroundResource(R.drawable.background_white_border_black12_radius_100)
//            // 若有选择，将图标设置为删除图标
            if (selectedOrderType != null) {
                binding?.arrowOrderType?.setImageResource(R.drawable.icon_input_delete)
                // 为删除图标添加点击事件
                binding?.arrowOrderType?.setOnClickListener {
                    clearOrderTypeSelection()
                }
            } else {
                // 若无选择，恢复下拉箭头图标
                binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
                // 移除点击事件
                binding?.arrowOrderType?.setOnClickListener(null)
            }
        }

        val adapter = OrderTypeAdapter(
            getOrderTypeItems(),
            selectedOrderType,  // 修改为单选
            requireContext()
        )
        adapter.onItemClickCallback = { selected ->
            selectedOrderType = selected
            // 更新适配器选中状态
//            adapter.setSelectedItem(selectedOrderType)
            // 更新订单类型显示文本
            updateOrderTypeDisplayText()
            popupWindow.dismiss()
        }
        popupViewTable.recyclerViewTable.adapter = adapter
        orderTypePopupWindow = popupWindow
    }

    /**
     * 更新订单类型显示文本
     */
    private fun updateOrderTypeDisplayText() {
//        selectedOrderType = null
//        // 恢复默认文本
//        binding?.tvOrderType?.text = getString(R.string.order_type)
//        binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black20, null))
//        // 恢复下拉箭头图标
//        binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
//        // 移除点击事件
//        binding?.arrowOrderType?.setOnClickListener(null)
//        if (selectedOrderTypeList.isEmpty()) {
//            binding?.tvOrderType?.text = getString(R.string.order_type)
//            binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black20, null))
//        } else {
//            val displayText = selectedOrderTypeList.joinToString(", ") { getDisplayName(it) }
//            binding?.tvOrderType?.text = displayText
//            binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black, null))
//        }
        if (selectedOrderType == null) {
            binding?.tvOrderType?.text = getString(R.string.order_type)
            binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black20, null))
            binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
            binding?.arrowOrderType?.setOnClickListener(null)
        } else {
            val displayText = getDisplayName(selectedOrderType!!)
            binding?.tvOrderType?.text = displayText
            binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black, null))
            binding?.arrowOrderType?.setImageResource(R.drawable.icon_input_delete)
            binding?.arrowOrderType?.setOnClickListener {
                clearOrderTypeSelection()
            }
        }
    }


    /**
     * 清空订单类型选择
     */
    private fun clearOrderTypeSelection() {
        selectedOrderType = null
        // 恢复默认文本
        binding?.tvOrderType?.text = getString(R.string.order_type)
        binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black20, null))
        // 恢复下拉箭头图标
        binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
        // 移除点击事件
        binding?.arrowOrderType?.setOnClickListener(null)
    }


    private fun showMergeTypePopupWindow(anchorView: View) {
        val popupViewTable = DialogFilterTimeSlotBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupViewTable.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        binding?.arrowMergeType?.animate()?.rotation(180f)?.setDuration(200)
        popupWindow.elevation = 20f
        popupWindow.animationStyle = R.style.PopupAnimation
        // 显示弹窗时设置背景
        anchorView.setBackgroundResource(R.drawable.background_spinner_top)
        popupWindow.showAsDropDown(anchorView)

        popupWindow.setOnDismissListener {
            binding?.arrowMergeType?.animate()?.rotation(0f)?.setDuration(200)
            // 弹窗关闭时恢复原背景
            anchorView.setBackgroundResource(R.drawable.background_white_border_black12_radius_100)
        }

        val adapter = MergeTypeAdapter(
            getMergeTypeItems(),
            selectedMergeType,
            requireContext()
        )

        adapter.onItemClickCallback = { selected ->
            selectedMergeType = selected
            // 根据枚举值设置显示文本
            binding?.tvMergeType?.text = when (selected) {
                MergeType.MERGED_TIME -> getString(R.string.merged_time)
                MergeType.INDEPENDENT_TIME -> getString(R.string.independent_time)
            }
            popupWindow.dismiss()
        }
        popupViewTable.recyclerViewTable.adapter = adapter
        mergeTypePopupWindow = popupWindow
    }


    private fun getOrderTypeItems(): List<DiningStyleEnum> {
        if (MainDashboardFragment.STORE_INFO?.isDeliveryWhiteList != true) {
            return DiningStyleEnum.entries.filter { it != DiningStyleEnum.TAKE_OUT }
        }
        return DiningStyleEnum.entries
    }

    private fun getDisplayName(style: DiningStyleEnum): String {
        return when (style) {
            DiningStyleEnum.DINE_IN -> getString(R.string.dine_in)
            DiningStyleEnum.TAKE_AWAY -> getString(R.string.take_away)
            DiningStyleEnum.PRE_ORDER -> getString(R.string.pre_order)
            DiningStyleEnum.TAKE_OUT -> getString(R.string.take_out)
        }
    }


    private fun getMergeTypeItems(): List<MergeType> {
        return MergeType.entries
    }


    override fun onDestroyView() {
        super.onDestroyView()
        tableScrollController.clear()
        binding = null
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.95).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun download(url: String) {
        try {
            val fileName =
                FileUtil.generateReportFileName(url, getString(R.string.comprehensive_report))

            val dir = FolderHelper.getDownloadFolderPath()
            DownloadManager.getInstance()
                .download(
                    TAG,
                    url,
                    fileName,
                    dir ?: "",
                    object : DownloadListener {
                        override fun onProgress(progress: Long, max: Long) {
                            requireActivity().apply {
                                Timber.e("progress $progress")
                            }
                        }

                        override fun onSuccess(localPath: String) {
                            Timber.e("localPath:${localPath}")
                            val file = File(dir, fileName)
                            val renameToRes = File(localPath).renameTo(file)
                            // 新增：删除原临时文件
                            val originalTempFile = File(localPath)
                            if (originalTempFile.exists()) {
                                originalTempFile.delete()
                            }
                            FileUtil.scanFile(requireActivity(), file)
                            requireActivity().runOnUiThread {
                                binding?.apply {
                                    tvExport.setEnableWithAlpha(true)
                                }
                                Toast.makeText(
                                    requireActivity(),
                                    requireActivity().getString(
                                        R.string.save_at_location,
                                        file.absolutePath
                                    ),
                                    Toast.LENGTH_LONG
                                ).show()
                            }

                        }

                        override fun onFail(errorInfo: String) {
                            requireActivity().apply {
                                runOnUiThread {
                                    binding?.apply {
                                        tvExport.setEnableWithAlpha(true)
                                    }
                                    if (errorInfo.isNotEmpty())
                                        Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                                }
                            }
                        }
                    })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {
        private const val TAG = "ComprehensiveReportDialog"

        //用餐人数
        const val TOTAL_PAX = "totalPax"

        //订单数量
        const val ORDER_COUNT = "orderCount"


        /**
         * 显示综合报表弹窗
         * @param fragmentManager FragmentManager 实例，用于显示弹窗
         */
        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        /**
         * 关闭综合报表弹窗
         * @param fragmentManager FragmentManager 实例，用于查找并关闭弹窗
         */
        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? ComprehensiveReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        /**
         * 创建综合报表弹窗实例
         * @return ComprehensiveReportDialog 实例
         */
        private fun newInstance(): ComprehensiveReportDialog {
            val args = Bundle()
            val fragment = ComprehensiveReportDialog()
            fragment.arguments = args
            return fragment
        }
    }
}