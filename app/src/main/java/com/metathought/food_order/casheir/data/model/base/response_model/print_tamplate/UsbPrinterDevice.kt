package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate

import android.hardware.usb.UsbDevice
import net.posprinter.IDeviceConnection
import net.posprinter.POSPrinter
import net.posprinter.TSPLPrinter


/**
 *<AUTHOR>
 *@time  2024/11/14
 *@desc  usb打印配置
 **/

class UsbPrinterDevice {
    /**
     *USB Device
     */
    var usbDevice: UsbDevice? = null

    /**
     *USB Connection
     */
    var iDeviceConnection: IDeviceConnection? = null

    /**
     *USB 小票打印
     */
    var pOSPrinter: POSPrinter? = null

    /**
     *USB 标签打印 Map
     */
    var tSPLPrinter: TSPLPrinter? = null


}