package com.metathought.food_order.casheir.data.model.base.request_model.ordered

import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed


/**
 *<AUTHOR>
 *@time  2024/8/27
 *@desc
 **/

data class RequsetCouponGoodModel(
    val id: String?,
    val num: Int?,
    val cartsId: String?,
    val feeds: List<Feed>?,
    val tagItemId: String?,
    val weight: Double?,
    val pricingMethod: Int
)