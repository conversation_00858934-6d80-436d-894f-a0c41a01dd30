package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.MealsetGoodRepeatItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber

/**
 * 套餐内商品重复选
 *
 * @property isShowImage
 * @property mealSetGroup
 * @property mealSetGood
 * @property context
 * @property onClickCallback
 * @property onUpdateCheck
 * @constructor Create empty Meal set goods no repeat adapter
 */

class MealSetGoodsRepeatAdapter(
    private var isShowImage: Boolean? = false,
    private var mealSetGroup: MealSetGroup,
    private val mealSetGood: Goods,
    val context: Context,
    val onAddCallback: (Int) -> Unit,
    val onSubCallback: (Int) -> Unit,
    val onUpdateCheck: (Int) -> Unit,
    val onShowSelectedTag: (Int, View) -> Unit,
    val isSecondView: Boolean? = false,
    val selectIndex: Int? = -1
) : RecyclerView.Adapter<MealSetGoodsRepeatAdapter.MealSetGoodsRepeatViewHolder>() {


    inner class MealSetGoodsRepeatViewHolder(val binding: MealsetGoodRepeatItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            binding.apply {
                val good = mealSetGroup.mealSetGoodsList?.get(position)
                good?.apply {
                    if (isSecondView == true) {
                        imgPlus.isVisible = false
                        imgMinus.isVisible = false
                        tvDishedName.textSize = 12f
                        tvTag.textSize = 12f
                        tvQTY.textSize = 14f

                    }
                    if (isShowImage == true) {
                        cardImage.isVisible = true
                        Glide.with(context).load(good.picUrl)
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .placeholder(R.drawable.icon_menu_default2)
                            .transition(DrawableTransitionOptions.withCrossFade())
                            .centerCrop()
                            .error(R.drawable.icon_menu_default2)
                            .into(ivGood)
                    } else {
                        cardImage.isVisible = false
                    }
                    var finalName = name ?: ""
                    var endStr = name ?: ""
//                    tvDishedName.text = name
                    tvTag.isVisible = false
                    if (optionalSpec == false) {
                        tvTag.text = getGoodsTagStr()
                        tvTag.isVisible = tvTag.text.isNotEmpty()
                    }

                    val number = num ?: 0
                    if (number >= 2) {
                        finalName = "${finalName} *${number} "
                        endStr = " *${number} "
                    }
                    val price = priceMarkup ?: 0
                    if (price > 0) {
                        finalName =
                            "${finalName} +${
                                FoundationHelper.getPriceStrByUnit(
                                    FoundationHelper.useConversionRatio,
                                    priceMarkup ?: 0L,
                                    FoundationHelper.isKrh
                                )
                            }"
                        endStr = "${endStr} +${
                            FoundationHelper.getPriceStrByUnit(
                                FoundationHelper.useConversionRatio,
                                priceMarkup ?: 0L,
                                FoundationHelper.isKrh
                            )
                        }"
                    }
                    tvDishedName.text = finalName
                    tvDishedName.postDelayed({
                        tvDishedName.initWidth(tvDishedName.measuredWidth)
                        tvDishedName.applyMiddleEllipsis(finalName, endStr)
                    }, 200)


                    //这边减号不能用就隐藏
                    val isSelect = isSelectItem()
                    tvQTY.isVisible = isSelect
                    tvQTY.text = "${getSelectNum()}"
                    if (isSecondView != true) {
                        imgMinus.isVisible = isSelect
                    }

                    setAddBtnEnable(imgPlus, true)

                    vDisable.isVisible = false
                    tvDishedName.setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        null,
                        null,
                        null
                    )
                    tvDishedName.setOnClickListener {
                        SingleClickUtils.isFastDoubleClick {
                            if (!good.tags.isNullOrEmpty() && !good.selectItems.isNullOrEmpty() && good.optionalSpec == true) {
//                                selectIndex = position
                                tvDishedName.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    ContextCompat.getDrawable(
                                        context,
                                        R.drawable.icon_dropdown_primary_up
                                    ),
                                    null
                                )
                                onShowSelectedTag.invoke(position, layoutMain)
                            }
                        }
                    }
                    if (isSelect) {
                        //没到最大值且 未售罄
                        setAddBtnEnable(
                            imgPlus,
                            !mealSetGroup.isMaxNum() || good.isSoldOutOrTakeDown(mealSetGood.mealSetInfo?.removalDoesNotAffectSale)
                        )
                        tvDishedName.setTextColor(context.getColor(R.color.primaryColor))
                        tvDishedName.isSelected = true
                        layoutMain.isSelected = true
                        if (!good.tags.isNullOrEmpty() && good.optionalSpec == true) {
                            tvDishedName.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                ContextCompat.getDrawable(
                                    context,
                                    if (selectIndex == position) R.drawable.icon_dropdown_primary_up else R.drawable.ic_dropdown_primary
                                ),
                                null
                            )
                        }
                    } else {
                        tvDishedName.isSelected = false
                        //如果没选中情况下
                        Timber.e("mealSetGroup.isMaxNum()  ${mealSetGroup.isMaxNum()}  ${mealSetGroup.name}")
                        //没到最大值 或 未售罄
                        layoutMain.isSelected = false
                        if (mealSetGroup.isMaxNum() || good.isSoldOutOrTakeDown(mealSetGood.mealSetInfo?.removalDoesNotAffectSale)) {
                            //该分类商品已经选到最大值
                            tvDishedName.setTextColor(context.getColor(R.color.black40))
                            vDisable.isVisible = true
                            setAddBtnEnable(imgPlus, false)
                        } else {
                            tvDishedName.setTextColor(context.getColor(R.color.black))
                        }
                    }

                    imgMinus.setOnClickListener {
                        SingleClickUtils.isFastDoubleClick(500) {
                            if (good.tags.isNullOrEmpty()) {
                                selectCustomTag("reduce", mutableListOf(), goodsNum = 1)
                                onUpdateCheck.invoke(position)
                            } else {
                                if (good.optionalSpec == false) {
                                    selectFixedTag("reduce", goodsNum = 1)
                                    onUpdateCheck.invoke(position)
                                } else {
                                    onSubCallback.invoke(position)
                                }
                            }
                        }
                    }

                    imgPlus.setOnClickListener {
                        SingleClickUtils.isFastDoubleClick(500) {
                            if (good.tags.isNullOrEmpty()) {
                                //没规格小料直接加
                                selectCustomTag("add", mutableListOf(), goodsNum = 1)
                                onUpdateCheck.invoke(position)
                            } else {
                                if (good.optionalSpec == false) {
                                    selectFixedTag("add", goodsNum = 1)
                                    onUpdateCheck.invoke(position)
                                } else {
                                    onAddCallback.invoke(position)
                                }
                            }
                        }

                    }
                }
            }
        }
    }


    private fun setAddBtnEnable(imageView: ImageView, enable: Boolean) {
        imageView.isEnabled = enable
        imageView.setImageResource(if (enable) R.drawable.ic_add else R.drawable.ic_add_disable)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): MealSetGoodsRepeatViewHolder {
        return MealSetGoodsRepeatViewHolder(
            MealsetGoodRepeatItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return mealSetGroup.mealSetGoodsList?.size ?: 0
    }

    override fun onBindViewHolder(holder: MealSetGoodsRepeatViewHolder, position: Int) {
        holder.bind(position)
    }
}