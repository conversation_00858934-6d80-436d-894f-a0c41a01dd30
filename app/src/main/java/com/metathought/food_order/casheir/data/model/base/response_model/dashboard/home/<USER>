package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home


import com.google.gson.annotations.SerializedName

data class GrabStoreMoneyOrder(
    @SerializedName("turnover") val turnover: Int?,
    @SerializedName("basketPromo") val basketPromo: Int?,
    @SerializedName("cancelledAmount") val cancelledAmount: Int?,
    @SerializedName("failedAmount") val failedAmount: Int?,
    @SerializedName("orderNum") val orderNum: Int?,
    @SerializedName("promoOrderNum") val promoOrderNum: Int?,
    @SerializedName("cancelledOrderNum") val cancelledOrderNum: Int?,
    @SerializedName("failedOrderNum") val failedOrderNum: Int?,
    @SerializedName("merchantPromo") val merchantPromo: Int?,
    @SerializedName("grabPromo") val grabPromo: Int?,
    @SerializedName("storeMoneyOrderDetailVos") val storeMoneyOrderDetailVos: List<Any>?,
    @SerializedName("promoDetails") val promoDetails: List<StorePromoDetail>?
)