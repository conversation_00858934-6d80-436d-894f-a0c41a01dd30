package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home


import com.google.gson.annotations.SerializedName

data class StoreGrabMoneyOrderDetail(
    @SerializedName("orderDate") val orderDate: String?,
    @SerializedName("turnover") val turnover: Long?,
    @SerializedName("basketPromo") val basketPromo: Long?,
    @SerializedName("cancelledAmount") val cancelledAmount: Long?,
    @SerializedName("failedAmount") val failedAmount: Long?,
    @SerializedName("orderNum") val orderNum: Long?
)