package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home


import com.google.gson.annotations.SerializedName

data class StoreMoneyOrderDetailVo(
    @SerializedName("orderDate")
    val orderDate: String?,
    @SerializedName("orderNum")
    val orderNum: Int?,
    @SerializedName("payMoney")
    val payMoney: String?,
    @SerializedName("payOrderNum")
    val payOrderNum: Int?,
    @SerializedName("refundMoney")
    val refundMoney: String?,
    @SerializedName("refundOrderNum")
    val refundOrderNum: Int?,
    @SerializedName("turnover")
    val turnover: String?
)