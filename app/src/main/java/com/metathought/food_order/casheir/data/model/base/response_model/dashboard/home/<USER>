package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home


import com.google.gson.annotations.SerializedName

data class CurStoreMoneyOrder(
    @SerializedName("balance")
    val balance: Double?,
    @SerializedName("credit")
    val credit: Double?,
    @SerializedName("cash")
    val cash: Double?,
    @SerializedName("onlinePay")
    val onlinePay: Double?,
    @SerializedName("orderNum")
    val orderNum: Int?,
    @SerializedName("payMoney")
    val payMoney: Double?,
    @SerializedName("payOrderNum")
    val payOrderNum: Int?,
    @SerializedName("refundMoney")
    val refundMoney: Double?,
    @SerializedName("refundOrderNum")
    val refundOrderNum: Int?,
    @SerializedName("settledMoney")
    val settledMoney: Double?,
    @SerializedName("turnover")
    val turnover: Double?,
    @SerializedName("waitPayMoney")
    val waitPayMoney: Double?,
    @SerializedName("waitPayOrderNum")
    val waitPayOrderNum: Int?,
    @SerializedName("waitSettledMoney")
    val waitSettledMoney: Double?
)