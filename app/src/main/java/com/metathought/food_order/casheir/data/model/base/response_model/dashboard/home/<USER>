package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home


import com.google.gson.annotations.SerializedName

data class StoreStatisticResponse(
    @SerializedName("curStoreMoneyOrder")
    val curStoreMoneyOrder: CurStoreMoneyOrder?,
    @SerializedName("lastStoreMoneyOrder")
    val lastStoreMoneyOrder: LastStoreMoneyOrder?,
    @SerializedName("salesRankingList")
    val salesRankingList: List<SalesRanking?>?,
    @SerializedName("storeMoneyOrderDetailList")
    val storeMoneyOrderDetailList: StoreMoneyOrderDetailList?
)