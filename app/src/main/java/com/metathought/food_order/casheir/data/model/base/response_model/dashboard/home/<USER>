package com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home


import com.google.gson.annotations.SerializedName

data class StoreGrabStatisticResponse(
    @SerializedName("curStoreMoneyOrder")
    val curStoreMoneyOrder: GrabStoreMoneyOrder?,
    @SerializedName("lastStoreMoneyOrder")
    val lastStoreMoneyOrder: GrabStoreMoneyOrder?,
    @SerializedName("salesRankingList")
    val salesRankingList: List<SalesRanking?>?,
    @SerializedName("storeMoneyOrderDetailVos")
    val storeMoneyOrderDetailVos: List<StoreGrabMoneyOrderDetail?>?,
    val promoDetails: List<StorePromoDetail>?
)