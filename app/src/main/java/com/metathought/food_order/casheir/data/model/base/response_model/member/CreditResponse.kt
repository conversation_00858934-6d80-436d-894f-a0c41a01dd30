package com.metathought.food_order.casheir.data.model.base.response_model.member

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.PayInfo
import com.metathought.food_order.casheir.utils.PaymentUtils
import java.math.BigDecimal

data class CreditListResponse(
    @SerializedName("pages")
    val pages: Int?,
    @SerializedName("records")
    val records: ArrayList<CreditRecord>?,
    @SerializedName("size")
    val size: Int?,
    @SerializedName("total")
    val total: Int?,
    @SerializedName("current")
    val current: Int?
)

data class CreditRecord(
    @SerializedName("consumerId")
    var consumerId: Long?, //店铺会员id
    @SerializedName("accountId")
    var accountId: Long?, //账号id
    @SerializedName("nickName")
    var nickName: String?, //昵称
    @SerializedName("telephone")
    var telephone: String?, //账户(电话)
    @SerializedName("amount")
    var amount: BigDecimal?,    //挂账金额
    @SerializedName("createTime")
    var createTime: String?, //账号创建时间
    @SerializedName("memberNumber")
    var memberNumber: String?, //会员卡号
)


data class CreditInfo(
    @SerializedName("consumerId")
    var consumerId: Long?, //店铺会员id
    @SerializedName("accountId")
    var accountId: Long?, //账号id
    @SerializedName("nickName")
    var nickName: String?, //昵称
    @SerializedName("telephone")
    var telephone: String?, //账户(电话)
    @SerializedName("amount")
    var amount: BigDecimal?,    //挂账金额
    @SerializedName("createTime")
    var createTime: String?, //账号创建时间
    @SerializedName("rechargeMembersNum")
    var rechargeMembersNum: Int?, //充值次数
    @SerializedName("consumerMembersNum")
    var consumerMembersNum: Int?, //消费次数


    var balanceAmount: BigDecimal? = null, //账户余额
    var creditRecordList: List<CreditRecordVo>? = null, //挂账记录
    var repaymentRecordList: List<RepaymentRecordVo>? = null, //还款记录

    var memberNumber: String? = null, //会员卡号
)


data class CreditRecordVo(
    @SerializedName("orderId")
    val orderId: String? = null,    //订单id
    @SerializedName("amount")
    var amount: BigDecimal?,    //挂账金额
    @SerializedName("creditDate")
    var creditDate: String?, //挂账时间,这里取订单更新时间
    @SerializedName("payStatus")
    var payStatus: Int? = null, //订单状态:10-挂账-未支付:11-挂账-已支付
)

data class RepaymentRecordVo(
    @SerializedName("amount")
    var amount: BigDecimal?,    //还款金额
    @SerializedName("payType")
    var payType: Int? = null, //支付类型:1-线上支付;2-现金支付;3-用户余额支付;4-其他支付(外卖使用);5-组合支付
    @SerializedName("offlinePaymentChannelsId")
    var offlinePaymentChannelsId: Long? = null, //线下支付渠道id
    @SerializedName("offlinePaymentChannelsName")
    var offlinePaymentChannelsName: String? = null,    // 线下支付渠道名称
    @SerializedName("repaymentDate")
    var repaymentDate: String? = null,    // 还款时间

    @SerializedName("combinedPayInfoList")
    var combinedPayInfoList: List<PayInfo>? = null,
) {


    fun getPaymentMethod(context: Context): String {
        return when (payType) {

            PayTypeEnum.CREDIT.id -> {
                context.getString(R.string.credit_payment)
            }

            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getString(R.string.online_payment)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                getOfflinePayMethod(offlinePaymentChannelsId, offlinePaymentChannelsName, context)
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getString(R.string.pay_by_balance)
            }

            PayTypeEnum.PAY_OTHER.id -> {
                context.getString(R.string.pay_by_other)
            }

            PayTypeEnum.MIXED_PAYMENT.id -> {
                if (combinedPayInfoList.isNullOrEmpty()) {
                    "${context.getString(R.string.pay_by_balance)}\n${
                        getOfflinePayMethod(
                            offlinePaymentChannelsId,
                            offlinePaymentChannelsName,
                            context
                        )
                    }"
                } else {
                    val result = PaymentUtils.getMixedPaymentText(context, combinedPayInfoList)
                    result
                }
            }

            else -> ""
        }
    }

    fun getOfflinePayMethod(
        offlinePaymentChannelsId: Long? = null,
        offlinePaymentChannelsName: String?,
        context: Context
    ): String {
        if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.cash)}"
        } else if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        } else {
            return "${context.getString(R.string.offline_payments)} - $offlinePaymentChannelsName"
        }
    }

}


data class CreditRecordResponse(
    @SerializedName("pages")
    val pages: Int?,
    @SerializedName("records")
    val records: ArrayList<CreditRecordVo>?,
    @SerializedName("size")
    val size: Int?,
    @SerializedName("total")
    val total: Int?,
    @SerializedName("current")
    val current: Int?
)

data class RepaymentRecordResponse(
    @SerializedName("pages")
    val pages: Int?,
    @SerializedName("records")
    val records: ArrayList<RepaymentRecordVo>?,
    @SerializedName("size")
    val size: Int?,
    @SerializedName("total")
    val total: Int?,
    @SerializedName("current")
    val current: Int?
)
