package com.metathought.food_order.casheir.utils

import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioAttributes
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.media.SoundPool
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.wecloud.im.common.context.AppContextWrapper


@SuppressLint("StaticFieldLeak")
object SoundPlayUtils {
    // SoundPool对象
    private var mSoundPlayer: SoundPool? = null
        get() {
            if (field == null) {
                field = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    SoundPool.Builder().setMaxStreams(10)
                        .setAudioAttributes(
                            AudioAttributes.Builder()
                                /**
                                 * 当用途是通知“即时”通信（例如聊天或短信）时使用的使用值。
                                 */
                                .setUsage(AudioAttributes.USAGE_NOTIFICATION_COMMUNICATION_INSTANT)
                                /**
                                 * 当内容类型是用于伴随用户动作的声音（例如表示按键的哔声或音效）或事件（例如游戏中收到的奖励的声音类型）时使用的内容类型值。这些声音大多是合成的
                                 * 或短 Foley 声音。
                                 */
                                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                                .build()
                        )
                        .build()
                } else {
                    SoundPool(10, AudioManager.STREAM_MUSIC, 1)
                }
            }
            return field
        }

    // SoundPool对象
    private var mRingSoundPlayer: SoundPool? = null
        get() {
            if (field == null) {
                field = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    SoundPool.Builder().setMaxStreams(10)
                        .setAudioAttributes(
                            AudioAttributes.Builder()
//                                /**
//                                 * 当用途为电话铃声时使用的用法值。
//                                 */
//                                .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                                /**
                                 * 当内容类型为音乐时使用的内容类型值。
                                 */
                                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                                .build()
                        )
                        .build()
                } else {
                    SoundPool(10, AudioManager.STREAM_MUSIC, 1)
                }
            }
            return field
        }

    private val orderSubmitMap = mapOf(
        "zh" to R.raw.order_submit_zh,
        "en" to R.raw.order_submit_en,
        "km" to R.raw.order_submit_km
    )


    fun play(looper: Boolean? = false) {
        release()
        val resId =
            orderSubmitMap[LocaleHelper.getLang(AppContextWrapper.getApplicationContext())]
                ?: return

        val soundId = mSoundPlayer?.load(AppContextWrapper.getApplicationContext(), resId, 1)
        mSoundPlayer?.setOnLoadCompleteListener { soundPool, _, _ ->

            soundPool?.play(
                soundId ?: 0,
                1F,
                1F,
                1,
                if (looper == true) {
                    -1
                } else {
                    0
                },
                1F
            )
        }
    }


    fun stopRing() {
        mRingSoundPlayer?.release()
        mRingSoundPlayer = null
    }

    /**
     * 播放消息声音
     */
    fun messagePlay() {
        play()
    }


    fun streamType() {

    }


//    fun outgoingCall() {
//        play(R.raw.outgoing_call_ring, true)
//    }


    private const val TAG = "SoundPlayUtils"

//
//    private fun switchSpeakerState(on: Boolean) {
//        val audioManager = App.getInstants().getSystemService(Context.AUDIO_SERVICE) as AudioManager
//
//        if (on) {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//                audioManager.clearCommunicationDevice()
//                audioManager.availableCommunicationDevices.firstOrNull { it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER }
//                    ?.let {
//                        audioManager.setCommunicationDevice(it)
//                    }
//            } else {
//                audioManager.isSpeakerphoneOn = true
//            }
//        } else {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//                audioManager.clearCommunicationDevice()
//                audioManager.availableCommunicationDevices.firstOrNull { it.type == AudioDeviceInfo.TYPE_BUILTIN_EARPIECE }
//                    ?.let {
//                        audioManager.setCommunicationDevice(it)
//                    }
//            } else {
//                audioManager.isSpeakerphoneOn = false
//            }
//        }
//    }
//
//    @RequiresApi(api = Build.VERSION_CODES.S)
//    fun setCommunicationDevice(context: Context, targetDeviceType: Int) {
//        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
//        val devices = audioManager.availableCommunicationDevices
//        for (device in devices) {
//            if (device.type == targetDeviceType) {
//                audioManager.clearCommunicationDevice()
//                val result = audioManager.setCommunicationDevice(device)
//            }
//        }
//    }
//

    /**
     * 释放资源
     */
    fun release() {
        mSoundPlayer?.release()
        mSoundPlayer = null
    }


}
