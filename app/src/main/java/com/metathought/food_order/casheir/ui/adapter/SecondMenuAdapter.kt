package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.bingoogolapple.badgeview.BGABadgeViewHelper
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderGoods
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.MenuItemBinding
import com.metathought.food_order.casheir.databinding.NewMenuItemBinding
import com.metathought.food_order.casheir.databinding.SecondMenuItemBinding
import com.metathought.food_order.casheir.databinding.SecondStickerHeaderItemBinding
import com.metathought.food_order.casheir.databinding.StickerHeaderItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.view.text.addTextTag
import timber.log.Timber
import androidx.core.graphics.toColorInt


class SecondMenuAdapter(
    val list: ArrayList<BaseGoods>,
    val context: Context,
    var nameLine: Int = 2,
) : RecyclerView.Adapter<SecondMenuAdapter.Companion.BaseViewHolder<*>>() {

    private lateinit var headerBinding: StickerHeaderItemBinding
    private lateinit var contentBinding: NewMenuItemBinding
    var localDiningStyle: Int = 0

    inner class MenuViewHolder(val binding: NewMenuItemBinding) :
        BaseViewHolder<NewMenuItemBinding>(binding.root) {

        fun bind(resource: Goods?) {
            resource?.let { _ ->
                val record = GoodsHelper.get(resource.id, localDiningStyle)
                if (record != null) {
                    resource.sellCount = record.num
                } else {
                    resource.sellCount = 0
                }
//                resource.cartsId = record?.cardsId

                binding.apply {

                    tvCouponActivity.isVisible = false
                    llCouponActivityWithNoImage.isVisible = false
                    val activityLabel = resource.activityLabels?.firstOrNull()
                    if (activityLabel != null) {
                        val gradientDrawable = GradientDrawable()
                        gradientDrawable.setColor(activityLabel.color.toColorInt())
                        val radius = DisplayUtils.dp2px(context, 12f).toFloat()
                        if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == true) {
                            gradientDrawable.cornerRadii = floatArrayOf(
                                0f, 0f,  // 左上角
                                radius, radius,  // 右上角
                                radius, radius,  // 右下角
                                0f, 0f,// 左下角
                            )
                            tvCouponActivity.background = gradientDrawable
                            tvCouponActivity.text = activityLabel.name
                            tvCouponActivity.isVisible = true
                        } else {
                            gradientDrawable.cornerRadii = floatArrayOf(
                                radius, radius,  // 左上角
                                0f, 0f,  // 右上角
                                0f, 0f,  // 右下角
                                radius, radius,// 左下角
                            )
                            tvCouponActivityWithNoImage.background = gradientDrawable
                            tvCouponActivityWithNoImage.text = activityLabel.name
                            llCouponActivityWithNoImage.isVisible = true
                        }
                    }

                    tvGoodName.setLines(nameLine)
                    tvGoodName.text = resource.name
                    if (resource.isMealSet()) {
                        tvGoodName.addMealSetTag(context)
//                        tvGoodName.addTextTag {
//                            text = context.getString(R.string.set_menu)
//                            position = 0
//                            strokeWidth = 1
//                            strokeColor = context.getColor(R.color.color_ff7f00)
//                            textSize = DisplayUtils.dpf2(context, 12f)
//                            backgroundColor = Color.TRANSPARENT
//                            textColor = context.getColor(R.color.color_ff7f00)
//                            marginRight = 4
//                        }
                    }

                    tvGoodName.textSize = 12f
                    tvCouponActivity.textSize = 10f
                    tvCouponActivityWithNoImage.textSize = 10f
                    tvPrice.textSize = 12f

                    if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == true) {
                        imgImage.isVisible = true
                        Glide.with(context).load(resource.picUrl)
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .placeholder(R.drawable.icon_menu_default2)
                            .transition(DrawableTransitionOptions.withCrossFade())
                            .centerCrop()
                            .error(R.drawable.icon_menu_default2)
                            .into(imgImage)
                    } else {
                        imgImage.isVisible = false
                    }

                    /**
                     * 售罄状态
                     */
                    tvSoldOut.isVisible = false
                    if (resource.soldOut == true) {
                        tvSoldOut.isVisible = true
                        tvSoldOut.text = context.getString(R.string.sold_out)
                        tvPrice.setTextColor(context.getColor(R.color.black40))
                    } else {
                        tvPrice.setTextColor(context.getColor(R.color.primaryColor))
                    }


//                    val isHasDiscountPrice = resource.isHasDiscountPrice()
//                    val sellPrice =
//                        if (isHasDiscountPrice) resource.discountPrice else resource.sellPrice
//                    tvPrice.text =
//                        "${sellPrice?.priceFormatTwoDigitZero2()}"
//                    if (resource.isToBeWeighed()) {
//                        val unit = resource.getWeightUnit()
//                        tvPrice.text =
//                            "${sellPrice?.priceFormatTwoDigitZero2()}/${unit}"
//                    }
//                    if (resource.isTimePriceGood()) {
//                        //如果是时价菜
//                        tvPrice.text = context.getString(R.string.time_price)
//                    }
                    val isHasDiscountPrice = resource.isHasDiscountPrice()
                    val sellPrice =
                        if (isHasDiscountPrice) resource.discountPrice else resource.sellPrice
                    val sellPriceStr = FoundationHelper.getPriceStrByUnit(
                        FoundationHelper.useConversionRatio,
                        sellPrice ?: 0L,
                        FoundationHelper.isKrh
                    )

                    if (resource.isTimePriceGood()) {
                        //只要是时价菜 在菜单就显示时价
                        tvPrice.text = context.getString(R.string.time_price)
                    } else if (resource.isToBeWeighed()) {
                        //如果非时价菜
                        val unit = resource.getWeightUnit()
                        tvPrice.text =
                            "${sellPriceStr}/${unit}"
                    } else {
                        tvPrice.text = sellPriceStr
                    }

                    rootView.badgeViewHelper.setBadgeGravity(BGABadgeViewHelper.BadgeGravity.RightTop)
                    rootView.badgeViewHelper.setBadgeTextSizeSp(14)
                    rootView.badgeViewHelper.setBadgePaddingDp(4)
                    if ((resource.sellCount ?: 0) > 0) {
                        rootView.showTextBadge(resource.sellCount.toString())
                    } else {
                        rootView.hiddenBadge()
                    }

                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<*> {
        return when (viewType) {
            ViewType.Header.ordinal -> {
                headerBinding = StickerHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HeaderViewHolder(headerBinding)
            }

            ViewType.Content.ordinal -> {
                contentBinding =
                    NewMenuItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                MenuViewHolder(contentBinding)
            }

            else -> throw IllegalArgumentException("Not a layout")
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                holder.binding.tvValue.text = (list[position] as HeaderGoods).name
                holder.binding.tvValue.setPadding(
                    0,
                    if (position == 0) 0 else DisplayUtils.dp2px(context, 12f),
                    0,
                    DisplayUtils.dp2px(context, 12f)
                )
            }

            is MenuViewHolder -> {
                (list[position] is Goods).let {
                    holder.bind(list[position] as Goods)
                }

            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (list[position].header) {
            true -> ViewType.Header.ordinal
            false -> ViewType.Content.ordinal
            else -> {
                ViewType.Content.ordinal
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    //是否有优惠活动
    private var isHasCouponActivity = false
    fun updateItems(newItems: ArrayList<BaseGoods>) {
        isHasCouponActivity =
            newItems.firstOrNull { (it is Goods) && !it.activityLabels.isNullOrEmpty() } != null
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateItem(goods: Goods) {
        val index = list.indexOf(goods)
        if (index != -1) {
            notifyItemChanged(index)
        }
    }

    fun getHeaderPosition(groupId: String): Int {
        for (i in 0..<list.size) {
            if (list[i] is HeaderGoods) {
                if ((list[i] as HeaderGoods).id == groupId)
                    return i
            }
        }
        return 0
    }

    inner class HeaderViewHolder(val binding: StickerHeaderItemBinding) :
        BaseViewHolder<StickerHeaderItemBinding>(binding.root) {

    }

    companion object {
        abstract class BaseViewHolder<T>(view: View) : RecyclerView.ViewHolder(view)
    }

    enum class ViewType {
        Header,
        Content
    }
}