package com.metathought.food_order.casheir.data.model.base.response_model.cart

import com.google.gson.annotations.SerializedName

data class GoodsJsonList(
    @SerializedName("goodsList")
    val goodsList: List<Goods>?,
    @SerializedName("goodsTotalNum")
    val goodsTotalNum: Int?,
    @SerializedName("isReservation")
    val isReservation: Boolean?,
    @SerializedName("peopleDate")
    val peopleDate: Any?,
    @SerializedName("peopleNum")
    val peopleNum: Any?,
    @SerializedName("totalPrice")
    val totalPrice: Long?,
    @SerializedName("totalVatPrice")
    val totalVatPrice: Long?,
    @SerializedName("totalVipPrice")
    val totalVipPrice: Long?,
    @SerializedName("totalVipVatPrice")
    val totalVipVatPrice: Long?,
    @SerializedName("totalDiscountPrice")
    val totalDiscountPrice: Long?,
    @SerializedName("totalDiscountVatPrice")
    val totalDiscountVatPrice: Long?,
    @SerializedName("totalPackingFee")
    val totalPackingFee: Long?,

    //堂食服务费
    @SerializedName("totalServiceCharge")
    val totalServiceCharge: Long?,

    //折扣堂食服务费
    @SerializedName("totalDiscountServiceCharge")
    val totalDiscountServiceCharge: Long?,

    //Vip堂食服务费
    @SerializedName("totalVipServiceCharge")
    val totalVipServiceCharge: Long?,

    @SerializedName("totalGoodsReduceVipVatPrice")
    val totalGoodsReduceVipVatPrice: Long?,
    //现价 单品减免后的vat
    @SerializedName("totalGoodsReduceVatPrice")
    val totalGoodsReduceVatPrice: Long?,

    //现金单品减免后的总价
    @SerializedName("totalGoodsReducePrice")
    val totalGoodsReducePrice: Long?,
    //vip单品减免后总价
    @SerializedName("totalGoodsReduceVipPrice")
    val totalGoodsReduceVipPrice: Long?,

    //vip 单品减免后的堂食服务费
    @SerializedName("totalGoodsReduceVipServiceCharge")
    val totalGoodsReduceVipServiceCharge: Long?,
    //现价 单品减免后的堂食服务费
    @SerializedName("totalGoodsReduceServiceCharge")
    val totalGoodsReduceServiceCharge: Long?,


    ) {

    /**
     * 是否有待定价商品
     *
     * @return
     */
    fun isHasNeedProcess(): Boolean {
        var hasUnProcess = false
        //判断是否含有 需要称重的物品 且未称重
        goodsList?.forEach {
            if (!it.isHasProcessed()) {
                hasUnProcess = true
            }
        }
        return hasUnProcess
    }


    fun isHasDiscountPrice(): Boolean {
        return totalDiscountPrice != null && totalDiscountPrice != totalPrice
    }

    fun getOrderPrice(): Long {
        var price = totalPrice ?: 0
        if (isHasDiscountPrice()) {
            price = totalDiscountPrice ?: 0
        }
        if (totalGoodsReducePrice != null && totalGoodsReducePrice != totalDiscountPrice) {
            return totalGoodsReducePrice
        }
        return price
    }

    fun getOrderOriginalPrice(): Long {
        var price = totalPrice ?: 0
        return price
    }

    fun getTotalVipPrice(): Long {
        var price = totalVipPrice ?: 0L
        if (totalGoodsReduceVipPrice != null && totalGoodsReduceVipPrice != totalVipPrice) {
            price = totalGoodsReduceVipPrice
        }
        return price
    }

    //获取实际的增值税
    fun getRealVatPrice(): Long {
        var price = totalVatPrice ?: 0L
        if (isHasDiscountPrice()) {
            price = totalDiscountVatPrice ?: 0L
        }
        if (totalGoodsReduceVatPrice != null && totalGoodsReduceVatPrice != totalDiscountVatPrice) {
            return totalGoodsReduceVatPrice
        }
        return price
    }

    fun getVipTotalVatPrice(): Long {
        if (totalGoodsReduceVipVatPrice != null && totalGoodsReduceVipVatPrice != totalVipVatPrice) {
            return totalGoodsReduceVipVatPrice
        }
        return totalVipVatPrice ?: 0L
    }

    //获取实际的服务费
    fun getRealServiceFeePrice(): Long {
        //已支付 且 优惠了
        var price = totalServiceCharge ?: 0L
        if (isHasDiscountPrice()) {
            price = totalDiscountServiceCharge ?: 0L
        }
        if (totalGoodsReduceServiceCharge != null && totalGoodsReduceServiceCharge != totalDiscountServiceCharge) {
            return totalGoodsReduceServiceCharge
        }
        return price
    }

    //获取会员对应的服务费
    fun getVipServiceFeePrice(): Long {
        if (totalGoodsReduceVipServiceCharge != null && totalGoodsReduceVipServiceCharge != totalVipServiceCharge) {
            return totalGoodsReduceVipServiceCharge
        }
        return totalVipServiceCharge ?: 0L
    }
}