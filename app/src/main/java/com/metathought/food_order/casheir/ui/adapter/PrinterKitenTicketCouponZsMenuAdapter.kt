package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.OrderCouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemKitchenTicketOrderMenuBinding
import com.metathought.food_order.casheir.databinding.ItemTicketOrderMenuBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber
import java.util.Locale

/**
 * 厨打优惠券赠品
 * Dine-in/Take-away/Booking to print receipt information
 * <AUTHOR>
 * @date 2024/09/1014:15
 * @description
 */
class PrinterKitenTicketCouponZsMenuAdapter(
    val list: ArrayList<UsageGoods>,
    val currentOrderedInfo: OrderedInfoResponse?,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机
    val lang: Locale? = null
) :
    RecyclerView.Adapter<PrinterKitenTicketCouponZsMenuAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemKitchenTicketOrderMenuBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: UsageGoods, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {

                        vLine.isVisible = position != list.size - 1

                        val localeList = mutableListOf<Locale>()
                        val langList = templateItem.getLangList()
                        langList.forEach {
                            localeList.add(Locale(it.uppercase()))
                        }
                        if (localeList.isEmpty()) {
                            localeList.add(Locale("EN"))
                        }

                        localeList.forEachIndexed { index, lang ->
//                            val foodName = it.getNameByLocal(lang)
                            localeList.forEachIndexed { index, lang ->
                                val foodName = it.getNameByLocal(lang)
                                if (index == 0) {
                                    tvFoodNameEn.text = foodName
                                    tvFoodNameEn.isVisible = !foodName.isNullOrEmpty()
                                } else if (index == 1) {
                                    tvFoodNameKh.text = foodName
                                    tvFoodNameKh.isGone =
                                        tvFoodNameKh.text == tvFoodNameEn.text || tvFoodNameKh.text.isNullOrEmpty()
                                }
                            }
                        }
//                        Timber.e("position ${position} tvFoodNameEn.text:${tvFoodNameEn.text}   tvFoodNameKh.text:${tvFoodNameKh.text}")
                        //如果菜名一样就隐藏一个
                        tvFoodNameKh.isGone =
                            tvFoodNameKh.text == tvFoodNameEn.text || tvFoodNameKh.text.isNullOrEmpty()
//

                        tvFoodNameEn.text =
                            "${tvFoodNameEn.text}(${
                                tvFoodNameEn.context.getStringByLocale(
                                    R.string.free,
                                    localeList.first()
                                )
                            })"

                        specRecyclerView.isGone = true

                        feedRecyclerView.isGone = true

                        tvFoodCount.text = "x1"
                        if (templateItem.informationShow != null) {
                            tvFoodNameEn.textSize =
                                templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                            tvFoodNameKh.textSize =
                                templateItem.informationShow.getProductNameFontRealSize(itemView.context, isEightyWidth = true)
                            tvFoodCount.textSize =
                                templateItem.informationShow.getProductNumFontRealSize(itemView.context, isEightyWidth = true)
                        }

                        tvRemarkTitle.isVisible = false
                        tvRemark.isVisible = false
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = PrinterTicketMenuViewHolder(
        ItemKitchenTicketOrderMenuBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: ArrayList<UsageGoods>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}