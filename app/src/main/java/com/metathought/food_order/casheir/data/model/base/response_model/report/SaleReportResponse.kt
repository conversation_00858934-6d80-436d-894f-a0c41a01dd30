package com.metathought.food_order.casheir.data.model.base.response_model.report

data class SaleReportResponse(
    val startTime: String?,
    val endTime: String?,
    val exportTime: String?,
    val salesOrdersVo: SalesOrdersVo?
)

data class SalesOrdersVo(
    val totalAmount: String?,
    val totalOrderNum: Int?,
    val onlinePay: String?,
    val offlinePay: String?,
    val balancePay: String?,
    val creditPay: String?,
    val otherPay: String?,
    val totalVat: String?,
    val totalDiscountAmount: String?,
    val totalReceiveAmount: String?,
    val salesOrdersDetailVoList: List<SalesOrdersDetailVo>?
)

data class SalesOrdersDetailVo(
    val orderNo: String?,
    val tableName: String?,
    val productBasicVoList: List<ProductBasicVo>?,
    val totalPrice: String?,
    val totalVat: String?,
    val discountAmount: String?,
    val receiveAmount: String?,
    val payTime: String?,
    val commission: String?,
    val tableCharge: String?
)

data class ProductBasicVo(
    val itemName: String?,
    val quantity: String?,
    val unitPrice: String?,
    val packingFee: String?,
    val vat: String?,
    val serviceCharge: String?
)