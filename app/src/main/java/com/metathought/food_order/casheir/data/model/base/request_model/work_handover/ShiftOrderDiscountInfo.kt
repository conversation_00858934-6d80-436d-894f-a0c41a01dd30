package com.metathought.food_order.casheir.data.model.base.request_model.work_handover

data class ShiftOrderDiscountInfo(
    val ordersId: String?,  //订单id/编号
    val shiftId: String?,    //交接班记录id
    val totalDiscountPrice: Double?, //订单优惠总金额
    val orderDiscountInfo: List<OrderDiscountInfo>?,
    val saasStoreId: Long?,
    val id: Long?,
    val createTime: String?,
    val updateTime: String?,
)


data class OrderDiscountInfo(
    val promotionName: String?,  //活动名称，无论是什么直接展示，服务端会处理好
    val price: Double?,
    val discountGoodInfo: List<DiscountGoodInfo>?
)

data class DiscountGoodInfo(
    val goodName: String?,   //商品名称
    val price: Double?
)
