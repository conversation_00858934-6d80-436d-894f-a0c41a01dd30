package com.metathought.food_order.casheir.ui.second_display

import androidx.annotation.IntDef

/**
 * <AUTHOR>
 * @date 2024/5/1616:03
 * @description
 */

@IntDef(
    SecondaryRoute.DEFAULT,
    SecondaryRoute.MENU_ORDER,
    SecondaryRoute.ORDERED_INFO,
    SecondaryRoute.PAYMENT_QR,
    SecondaryRoute.PAYMENT_RESULT,
)
@Retention(
    AnnotationRetention.SOURCE
)
annotation class SecondaryRoute {
    companion object {
        const val DEFAULT = 0

        const val MENU_ORDER = 1

        const val ORDERED_INFO = 2

        const val PAYMENT_QR = 3

        const val PAYMENT_RESULT = 4
    }
}

