package com.metathought.food_order.casheir.data.model.base.response_model.member


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class RechargePaymentResponse(
    @SerializedName("amount")
    val amount: Int?,
    @SerializedName("orderId")
    val orderId: String?,
    @SerializedName("qrCode")
    val qrCode: String?,
    @SerializedName("upayDeeplink")
    val upayDeeplink: String?,
    @Transient
    var payType:Int,
) : Parcelable {

    @IgnoredOnParcel
    private var expiredTimestamp: Long? = null
    fun getExpiredTimestamp(): Long? {
        if (expiredTimestamp == null) {
            expiredTimestamp = System.currentTimeMillis() + (300 * 1000)
        }
        return expiredTimestamp
    }
    fun isOnline(): <PERSON><PERSON>an{
        return payType == 1
    }
}