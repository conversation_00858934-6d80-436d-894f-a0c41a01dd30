package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.SpecificationTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTag
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.databinding.SpecificationItemBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.helper.FoundationHelper
import timber.log.Timber


class SpecificationSubItemAdapter(
    private var tagType: Int,
    private var goodsTags: List<GoodsTagItem>,
    val context: Context,
    val onClickCallback: (Int) -> Unit
) : RecyclerView.Adapter<SpecificationSubItemAdapter.SpecificationViewHolder>() {

    inner class SpecificationViewHolder(val binding: SpecificationItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            binding.apply {
                goodsTags[position].apply {
                    Timber.d("tagType:$tagType price:$price")
                    when (tagType) {

                        SpecificationTypeEnum.FEATURE.id -> {
                            tvDishedNameAndPrice.text = name
                        }

                        SpecificationTypeEnum.SPECIFICATION.id,
                        SpecificationTypeEnum.INGREDIENT.id -> {
                            if ((price ?: 0.0) > 0) {
                                tvDishedNameAndPrice.text = "$name +${
                                    FoundationHelper.getPriceStrByUnit(
                                        FoundationHelper.useConversionRatio,
                                        (price ?: 0.0).toLong(),
                                        FoundationHelper.isKrh
                                    )
                                }"
//                                    "$name +$${price?.priceDecimalFormatTwoDigitZero()}"
                            } else {
                                tvDishedNameAndPrice.text = name
                            }
                        }
                    }
                }
                tvDishedNameAndPrice.setTextColor(
                    ContextCompat.getColor(
                        context,
                        if (goodsTags[position].isCheck == true) R.color.primaryColor else R.color.black
                    )
                )
                layoutMain.strokeColor = ContextCompat.getColor(
                    context,
                    if (goodsTags[position].isCheck == true) R.color.primaryColor else R.color.black20
                )
                layoutMain.setOnClickListener {
                    when (tagType) {
                        SpecificationTypeEnum.SPECIFICATION.id,
                        SpecificationTypeEnum.FEATURE.id -> {
                            for (i in goodsTags.indices) {
                                if (goodsTags[i].isCheck == true) {
                                    goodsTags[i].isCheck = false
                                    notifyItemChanged(i)
                                    break
                                }
                            }
                            goodsTags[position].isCheck = true
                            notifyItemChanged(position)
                        }

                        SpecificationTypeEnum.INGREDIENT.id -> {
                            goodsTags[position].isCheck = goodsTags[position].isCheck != true
                            notifyItemChanged(position)
                        }
                    }
                    onClickCallback.invoke(position)
                }
            }
        }
    }

    fun getSelectGoodsTags(): ArrayList<GoodsTagItem> {
        return goodsTags.filter { it.isCheck == true }.toList() as ArrayList<GoodsTagItem>
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SpecificationViewHolder {
        return SpecificationViewHolder(
            SpecificationItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return goodsTags.size
    }

    override fun onBindViewHolder(holder: SpecificationViewHolder, position: Int) {
        holder.bind(position)
    }

}