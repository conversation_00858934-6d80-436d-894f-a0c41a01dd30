package com.metathought.food_order.casheir.data.model.base.request_model.menu


import com.google.gson.annotations.SerializedName

data class AddTmpGoodsToCartRequest(
    /**
     * 餐桌Uuid
     */
    @SerializedName("tableUuid")
    var tableUuid: String? = null,

    /**
     * 就餐方式
     */
    @SerializedName("diningStyle")
    var diningStyle: Int? = null,
    /**
     * 加购临时菜品list
     */
    @SerializedName("goodsTemporaryInfoDTOList")
    var goodsTemporaryInfoDTOList: List<GoodsTemporaryInfo>? = null,
 )

data class GoodsTemporaryInfo(
    @SerializedName("goodsId")
    var goodsId: Long,
    @SerializedName("num")
    var num: Int,
)