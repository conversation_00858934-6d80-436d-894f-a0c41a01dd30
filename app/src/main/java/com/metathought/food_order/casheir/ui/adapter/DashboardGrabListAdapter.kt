package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreGrabMoneyOrderDetail
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreMoneyOrderDetailVo
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.DashboardRevenueGrabItemBinding
import com.metathought.food_order.casheir.databinding.DashboardRevenueItemBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class DashboardGrabListAdapter(
    val list: ArrayList<StoreGrabMoneyOrderDetail?>,
    val onItemClickListener: (StoreGrabMoneyOrderDetail) -> Unit,
) : RecyclerView.Adapter<DashboardGrabListAdapter.DashboardListViewHolder>() {


    inner class DashboardListViewHolder(val binding: DashboardRevenueGrabItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: StoreGrabMoneyOrderDetail?, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
                        tvDate.text = resource?.orderDate
                        tvTurnOver.text = resource?.turnover?.priceFormatTwoDigitZero2()
                        tvPromoAmout.text = resource?.basketPromo?.priceFormatTwoDigitZero2()
                        tvCancleAmoute.text = resource?.cancelledAmount?.priceFormatTwoDigitZero2()
                        tvFailAmout.text = resource?.failedAmount?.priceFormatTwoDigitZero2()
                        tvOrderNum.text = "${resource?.orderNum}"
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DashboardListViewHolder {
        val itemView =
            DashboardRevenueGrabItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return DashboardListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: DashboardListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }

    fun replaceData(list: ArrayList<StoreGrabMoneyOrderDetail?>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<StoreGrabMoneyOrderDetail?>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }

}