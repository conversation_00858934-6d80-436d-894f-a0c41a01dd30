package com.metathought.food_order.casheir.ui.dialog.store_report

import android.annotation.SuppressLint
import android.content.res.Resources
import android.graphics.PointF
import android.os.Bundle
import android.text.TextPaint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogBusinessReportBinding
import com.metathought.food_order.casheir.adapter.BusinessReportRowAdapter
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.constant.QuickTimeType
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.PermissionHelper
import com.metathought.food_order.casheir.model.BusinessReportItem
import com.metathought.food_order.casheir.model.QuickTimeItem
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.decoration.DividerItemDecoration
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.PopupUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.math.BigDecimal


@AndroidEntryPoint
class BusinessReportDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "BusinessReportDialog"
        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? BusinessReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): BusinessReportDialog {
            val args = Bundle()
            val fragment = BusinessReportDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private lateinit var binding: DialogBusinessReportBinding
    private lateinit var reportAdapter: BusinessReportRowAdapter
    private val viewModel: BusinessReportViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogBusinessReportBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding.root)

        initView()
        initAdapter()
        setupObservers()
        loadReportData()
    }

    private fun initView() {
        binding.topBar.getCloseBtn()?.setOnClickListener {
            dismissCurrentDialog()
        }

        // 初始化 TimeSelectionView
        initTimeSelectionView()

        // 设置重置按钮点击事件
        binding.btnReset.setOnClickListener {
            resetData()
        }

        // 设置导出按钮点击事件
        binding.btnExport.setOnClickListener {
            if (PermissionHelper.checkPermissions(requireActivity())) {
                val startTime = if (binding.timeSelectionView.getStartDate() != null) {
                    binding.timeSelectionView.getStartDate()?.formatDateStr(FORMAT_DATE_REALIZED)
                } else {
                    null
                }
                val endTime = if (binding.timeSelectionView.getEndDate() != null) {
                    binding.timeSelectionView.getEndDate()?.formatDateStr(FORMAT_DATE_REALIZED)
                } else {
                    null
                }
                val selectedQuickTime = binding.timeSelectionView.getSelectedQuickTime()?.type
                viewModel.getReportFormExportUrl(startTime, endTime, selectedQuickTime)
            } else {
                PermissionHelper.requestPermissions(requireActivity())
            }
        }
    }

    private fun initTimeSelectionView() {
        // 设置 FragmentManager
        binding.timeSelectionView.setFragmentManager(childFragmentManager)

        // 设置快捷时间数据源
        val quickTimeList = listOf(
            QuickTimeItem(getString(R.string.today), QuickTimeType.TODAY.code),
            QuickTimeItem(getString(R.string.yesterday), QuickTimeType.YESTERDAY.code),
            QuickTimeItem(getString(R.string.last_7_days), QuickTimeType.SEVEN_DAYS.code),
            QuickTimeItem(getString(R.string.last_30_days), QuickTimeType.THIRTY_DAYS.code),
        )
        binding.timeSelectionView.setQuickTimeList(quickTimeList, QuickTimeType.SEVEN_DAYS.code)

        // 设置快捷时间选择监听器
        binding.timeSelectionView.setOnQuickTimeSelectedListener { quickTimeType ->
            loadReportData()
        }

        // 设置日期选择监听器
        binding.timeSelectionView.setOnDateSelectedListener { startDate, endDate ->
            loadReportData()
        }

        // 默认选择今天
        binding.timeSelectionView.resetTimeSelection()
    }

    private fun initAdapter() {
        reportAdapter = BusinessReportRowAdapter()

        binding.rvBusinessReport.layoutManager =
            androidx.recyclerview.widget.LinearLayoutManager(context)
        binding.rvBusinessReport.adapter = reportAdapter

        // 添加分割线装饰器
        binding.rvBusinessReport.addItemDecoration(DividerItemDecoration(requireContext()))

        reportAdapter.setOnItemClickListener { position ->
            showBusinessReportDetail(position)
        }

        initInfoClickListeners()
    }

    private fun initInfoClickListeners() {
        binding.llBalanceHeader.setOnClickListener {
            PopupUtils.showSmartTipPopup(
                requireContext(),
                binding.ivBalanceInfo,
                getString(R.string.profit_balance_description)
            )
        }

        binding.llIncomeHeader.setOnClickListener {
            PopupUtils.showSmartTipPopup(
                requireContext(),
                binding.ivIncomeInfo,
                getString(R.string.actual_amount_received_description)
            )
        }

        binding.llReserveHeader.setOnClickListener {
            PopupUtils.showSmartTipPopup(
                requireContext(),
                binding.ivReserveInfo,
                getString(R.string.reserve_fund_description)
            )
        }

        binding.llExpenseHeader.setOnClickListener {
            PopupUtils.showSmartTipPopup(
                requireContext(),
                binding.ivExpenseInfo,
                getString(R.string.expenditure_amount_description)
            )
        }

        binding.llCreditHeader.setOnClickListener {
            PopupUtils.showSmartTipPopup(
                requireContext(),
                binding.ivCreditInfo,
                getString(R.string.credit_amount_description)
            )
        }
    }


    private fun setupObservers() {
        viewModel.businessReportData.observe(viewLifecycleOwner, Observer { response ->
            when (response) {
                is ApiResponse.Success -> {
                    val reportData = response.data ?: emptyList()
                    reportAdapter.setNewInstance(reportData.toMutableList())
                    updateTotalData(reportData)
                }

                is ApiResponse.Error -> {
                    // 处理错误情况
                    resetData()
//                    context?.let {
//                        Toast.makeText(it, "加载数据失败：${response.message}", Toast.LENGTH_SHORT)
//                            .show()
//                    }
                }

                else -> {
                    // 处理其他情况
//                    resetData()
                }
            }
        })

        viewModel.isLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            // 可以在这里显示/隐藏加载指示器
            // binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        })

        viewModel.exportUrlResponse.observe(viewLifecycleOwner, Observer { response ->
            when (response) {
                is ApiResponse.Loading -> {
                    // 显示加载状态
                    binding.btnExport?.isEnabled = false
                }

                is ApiResponse.Success -> {
                    download(response.data)
//                    updateTableData(response.data.headerList, response.data.masterReportDataList)
                }

                is ApiResponse.Error -> {
                    binding.btnExport?.isEnabled = true
                    // 显示错误信息
                }
            }
        })
    }

    private fun loadReportData() {
        lifecycleScope.launch {
            val selectedQuickTime = binding.timeSelectionView.getSelectedQuickTime()
//            var startDate = binding.timeSelectionView.getStartDate()
//            var endDate = binding.timeSelectionView.getEndDate()

//            val (finalStartDate, finalEndDate) = if (selectedQuickTime != null) {
//                // 使用快捷时间
//                when (selectedQuickTime.type) {
//                    QuickTimeType.TODAY -> TimeUtils.getTodayTimeRange()
//                    QuickTimeType.YESTERDAY -> TimeUtils.getYesterdayTimeRange()
//                    QuickTimeType.THIS_WEEK -> TimeUtils.getThisWeekTimeRange()
//                    QuickTimeType.LAST_WEEK -> TimeUtils.getLastWeekTimeRange()
//                    QuickTimeType.THIS_MONTH -> TimeUtils.getThisMonthTimeRange()
//                    QuickTimeType.LAST_MONTH -> TimeUtils.getLastMonthTimeRange()
//                }
//            } else if (startDate != null && endDate != null) {
//                // 使用自定义日期范围
//                Pair(startDate, endDate)
//            } else {
//                // 默认使用今天
//                TimeUtils.getTodayTimeRange()
//            }

//            Timber.e("time range: ${startDate} to ${endDate}")
//            if (startDate == null || endDate == null) {
//                startDate = Date()
//                endDate = Date()
//                val startTime = startDate.formatDateStr(FORMAT_DATE_TIME_REALIZED)
//                val endTime = endDate.formatDateStr(FORMAT_DATE_TIME_REALIZED)
//                viewModel.getSalesStoreIncome(startTime, endTime)
//            } else {
//                val startTime = startDate.formatDateStr(FORMAT_DATE_TIME_REALIZED)
//                val endTime = endDate.formatDateStr(FORMAT_DATE_TIME_REALIZED)
//                viewModel.getSalesStoreIncome(startTime, endTime)
//            }
            val startTime = if (binding.timeSelectionView.getStartDate() != null) {
                binding.timeSelectionView.getStartDate()?.formatDateStr(FORMAT_DATE_REALIZED)
            } else {
                null
            }
            val endTime = if (binding.timeSelectionView.getEndDate() != null) {
                binding.timeSelectionView.getEndDate()?.formatDateStr(FORMAT_DATE_REALIZED)
            } else {
                null
            }
//            val startTime = startDate.formatDateStr(FORMAT_DATE_TIME_REALIZED)
//            val endTime = endDate.formatDateStr(FORMAT_DATE_TIME_REALIZED)
            viewModel.getSalesStoreIncome(startTime, endTime, selectedQuickTime?.type)
        }
    }

    private fun resetData() {
        // 重置时间选择
        binding.timeSelectionView.resetTimeSelection()

        // 重置数据，清空列表
        reportAdapter.setNewInstance(emptyList())

        // 清空总计数据
        updateTotalData(emptyList())

        loadReportData()
    }

    @SuppressLint("SetTextI18n")
    private fun updateTotalData(reportData: List<BusinessReportItem>) {
        var totalOperatingIncome = BigDecimal.ZERO
        var totalRevenueAmount = BigDecimal.ZERO
        var totalOpeningCashUSD = BigDecimal.ZERO
        var totalOpeningCashKHR = BigDecimal.ZERO
        var totalAmountPaidUSD = BigDecimal.ZERO
        var totalAmountPaidKHR = BigDecimal.ZERO
        var totalCreditAmount = BigDecimal.ZERO

        reportData.forEach { item ->
            totalOperatingIncome = totalOperatingIncome.add(item.operatingIncome)
            totalRevenueAmount = totalRevenueAmount.add(item.revenueAmount)
            totalOpeningCashUSD = totalOpeningCashUSD.add(item.openingCashUSD)
            totalOpeningCashKHR = totalOpeningCashKHR.add(item.openingCashKHR)
            totalAmountPaidUSD = totalAmountPaidUSD.add(item.amountPaidUSD)
            totalAmountPaidKHR = totalAmountPaidKHR.add(item.amountPaidKHR)
            totalCreditAmount = totalCreditAmount.add(item.creditAmount)
        }

        // 更新总计显示
        binding.tvTotalBalance.text =
            totalOperatingIncome.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2("$")
        binding.tvTotalIncome.text =
            totalRevenueAmount.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2()
        binding.tvTotalReserve.text = if (totalOpeningCashKHR > BigDecimal.ZERO) {
            "${
                totalOpeningCashUSD.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2()
            } + ៛${totalOpeningCashKHR.decimalFormatZeroDigit()}"
        } else {
            totalOpeningCashUSD.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2()
        }
        binding.tvTotalExpense.text = if (totalAmountPaidKHR > BigDecimal.ZERO) {
            "${
                totalAmountPaidUSD.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2()
            } + ៛${totalAmountPaidKHR.decimalFormatZeroDigit()}"
        } else {
            totalAmountPaidUSD.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2()
        }
        binding.tvTotalCreditCustomers.text =
            totalCreditAmount.setScale(2, BigDecimal.ROUND_HALF_UP).priceFormatTwoDigitZero2()
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.95).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.clearData()
    }


    private fun showBusinessReportDetail(position: Int) {
        val item = reportAdapter.getItem(position)
        if (item != null) {
            // 打开门店收支详情 DialogFragment
            val detailDialog = StoreIncomeDetailDialog.newInstance(item.businessDate ?: "")
            detailDialog.show(parentFragmentManager, "StoreIncomeDetailDialog")
        }
    }


    private fun download(url: String) {
        try {
            val fileName =
                FileUtil.generateReportFileName(url, getString(R.string.business_report))

            val dir = FolderHelper.getDownloadFolderPath()
            DownloadManager.getInstance()
                .download(
                    TAG,
                    url,
                    fileName,
                    dir ?: "",
                    object : DownloadListener {
                        override fun onProgress(progress: Long, max: Long) {
                            requireActivity().apply {
                                Timber.e("progress $progress")
                            }
                        }

                        override fun onSuccess(localPath: String) {
                            Timber.e("localPath:${localPath}")
                            val file = File(dir, fileName)
                            val renameToRes = File(localPath).renameTo(file)
                            // 新增：删除原临时文件
                            val originalTempFile = File(localPath)
                            if (originalTempFile.exists()) {
                                originalTempFile.delete()
                            }
                            FileUtil.scanFile(requireActivity(), file)
                            requireActivity().runOnUiThread {
                                binding?.apply {
                                    btnExport.setEnableWithAlpha(true)
                                }
                                Toast.makeText(
                                    requireActivity(),
                                    requireActivity().getString(
                                        R.string.save_at_location,
                                        file.absolutePath
                                    ),
                                    Toast.LENGTH_LONG
                                ).show()
                            }

                        }

                        override fun onFail(errorInfo: String) {
                            requireActivity().apply {
                                runOnUiThread {
                                    binding?.apply {
                                        btnExport.setEnableWithAlpha(true)
                                    }
                                    if (errorInfo.isNotEmpty())
                                        Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                                }
                            }
                        }
                    })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * 显示智能提示弹窗，根据可用空间自动选择显示位置
     * - 左右空间都足够时，显示在中间
     * - 左侧空间不足时，显示在右侧
     * - 右侧空间不足时，显示在左侧
     *
     * @param anchorView 锚点视图，弹窗将显示在此视图附近
     * @param message 要显示的提示信息
     */
    private fun showSmartTipPopup(anchorView: View, message: String) {
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val anchorX = location[0]
        val anchorY = location[1]
        val anchorWidth = anchorView.width

        // 获取屏幕宽度
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenWidth = displayMetrics.widthPixels

        // 估算消息宽度 (使用TextPaint计算实际文本宽度)
        val textPaint = TextPaint()
        textPaint.textSize =
            resources.getDimension(R.dimen._12ssp) // 使用与CustomBubbleAttachPopup相同的文本大小
        val estimatedMessageWidth = textPaint.measureText(message).toInt() +
                resources.getDimensionPixelSize(R.dimen.fragment_horizontal_margin) * 2 // 添加左右内边距
        val halfMessageWidth = estimatedMessageWidth / 2

        // 计算锚点中心位置
        val anchorCenterX = anchorX + (anchorWidth / 2)

        // 判断左右空间是否足够
        val hasEnoughSpaceOnLeft = anchorCenterX > halfMessageWidth
        val hasEnoughSpaceOnRight = (screenWidth - anchorCenterX) > halfMessageWidth

        // 创建XPopup构建器
        val popupBuilder = XPopup.Builder(requireContext())
            .hasShadowBg(false)
            .isTouchThrough(false)
            .isDestroyOnDismiss(true)

        // 根据空间决定显示位置
        if (hasEnoughSpaceOnLeft && hasEnoughSpaceOnRight) {
            // 左右空间都足够，显示在中间
            popupBuilder.popupPosition(PopupPosition.Top) // 使用Top位置，但实际上是居中的
            // 设置居中显示
            popupBuilder.isCenterHorizontal(true)
        } else if (hasEnoughSpaceOnRight) {
            // 右侧空间足够，显示在右侧
            popupBuilder.popupPosition(PopupPosition.Right)
        } else {
            // 默认显示在左侧
            popupBuilder.popupPosition(PopupPosition.Left)
        }

        // 显示弹窗
        popupBuilder
            .atView(anchorView)
            .atPoint(PointF(anchorCenterX.toFloat(), anchorY.toFloat()))
            .asCustom(CustomBubbleAttachPopup(requireContext(), message, 5000))
            .show()
    }


}