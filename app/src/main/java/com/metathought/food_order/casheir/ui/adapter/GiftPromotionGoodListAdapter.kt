package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.GiftGood
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import timber.log.Timber


/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class GiftPromotionGoodListAdapter(
    val list: ArrayList<GiftGood>,
) : RecyclerView.Adapter<GiftPromotionGoodListAdapter.GiftPromotionGoodListItemViewHolder>() {
    private var mContext: Context? = null

    inner class GiftPromotionGoodListItemViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.run {

            }
        }

        fun bind(resource: GiftGood?, index: Int?) {
            resource?.let { _ ->
                binding?.apply {

                    tvName.text = "${resource.num}"
                    tvQTY.text = "x${resource.num}"
                    Timber.e(" mContext?.getString(R.string.free) ${mContext?.getString(R.string.free)} ${mContext}")
                    layoutPrice.tvFoodPrice.text = mContext?.getString(R.string.free)
                }
            }
        }
    }


    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): GiftPromotionGoodListItemViewHolder {
        val itemView = SelectedMenuItemBinding.inflate(
            LayoutInflater.from(
                parent.context
            ), parent, false
        )

        return GiftPromotionGoodListItemViewHolder(
            itemView
        )
    }

    override fun onBindViewHolder(holder: GiftPromotionGoodListItemViewHolder, position: Int) {

        holder.bind(list[position], position)
    }


    override fun getItemCount(): Int {
        return list.size
    }

    fun updateItems(mContext: Context?, newItems: List<GiftGood>?) {
        if (this.mContext == null) {
            this.mContext = mContext
        }
        list.clear()
        list.addAll(newItems ?: listOf())
        notifyDataSetChanged()
    }

}