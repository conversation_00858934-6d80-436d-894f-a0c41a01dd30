package com.metathought.food_order.casheir.data.model.base.response_model.ordered


import com.google.gson.annotations.SerializedName

data class GoodClassificationModel(
    @SerializedName("storeName")
    val storeName: String? = null,
    @SerializedName("groupId")
    val groupId: String? = null,
    @SerializedName("groupName")
    val groupName: String? = null,

    @Transient
    var ischeck: Boolean = false
)