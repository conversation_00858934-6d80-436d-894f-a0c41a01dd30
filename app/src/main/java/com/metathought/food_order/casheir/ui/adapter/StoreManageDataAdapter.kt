package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.ItemStoreManagerDataBinding


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class StoreManageDataAdapter(
    val list: ArrayList<StoreData>,
) : RecyclerView.Adapter<StoreManageDataAdapter.StoreManageDataViewHolder>() {


    inner class StoreManageDataViewHolder(val binding: ItemStoreManagerDataBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: StoreData, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding?.apply {
                        content.setTitle(resource.title)
                        content.setContent(resource.content)
                        if (resource.startDraw != null)
                            content.setContentStartDrawable(resource.startDraw)
                    }

                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StoreManageDataViewHolder {
        val itemView =
            ItemStoreManagerDataBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return StoreManageDataViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: StoreManageDataViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    fun replaceData(list: ArrayList<StoreData>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }
}

data class StoreData(
    val title: String? = null,
    val content: String? = null,
    val startDraw: Int? = null
)