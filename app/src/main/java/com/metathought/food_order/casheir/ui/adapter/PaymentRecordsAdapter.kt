package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.member.RepaymentRecordVo
import com.metathought.food_order.casheir.databinding.ItemCreditRecordsBinding
import com.metathought.food_order.casheir.databinding.ItemPaymentRecordsBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4
import com.metathought.food_order.casheir.ui.member.dialog.RechargeDetailDialog
import kotlin.math.min


class PaymentRecordsAdapter :
    RecyclerView.Adapter<PaymentRecordsAdapter.CreditRecordsViewHolder>() {

    var maxItemCount = -1
    private val list = mutableListOf<RepaymentRecordVo>()

    inner class CreditRecordsViewHolder(val binding: ItemPaymentRecordsBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: RepaymentRecordVo) {
            binding.apply {
                tvAmount.text = resource.amount?.priceFormatTwoDigitZero4()
                tvTime.text = resource.repaymentDate?.formatDate()
                tvPaymentMethod.text = resource.getPaymentMethod(tvAmount.context)
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = CreditRecordsViewHolder(
        ItemPaymentRecordsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() =
        if (maxItemCount == -1) list.size else min(maxItemCount, list.size)

    fun getItemSize() = list.size

    override fun onBindViewHolder(
        holder: PaymentRecordsAdapter.CreditRecordsViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<RepaymentRecordVo>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addData(list: List<RepaymentRecordVo>) {
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}