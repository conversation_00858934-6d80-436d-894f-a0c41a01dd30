package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemKitchenTicketOrderMenuBinding
import com.metathought.food_order.casheir.extension.getStringByLocale
import timber.log.Timber
import java.util.Locale

/**
 * 厨打小票
 * Dine-in/Take-away/Booking to print receipt information
 * <AUTHOR>
 * @date 2024/5/1014:15
 * @description
 */
class PrinterKitchenTicketMenuAdapter(
    val list: ArrayList<OrderedGoods>,
    val currentOrderedInfo: OrderedInfoResponse?,
    val templateItem: PrintTamplateResponseItem
) :
    RecyclerView.Adapter<PrinterKitchenTicketMenuAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemKitchenTicketOrderMenuBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: OrderedGoods, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        vLine.isVisible = position != list.size - 1

                        val localeList = mutableListOf<Locale>()
                        val langList = templateItem.getLangList()
                        langList.forEach {
                            localeList.add(Locale(it.uppercase()))
                        }
                        if (localeList.isEmpty()) {
                            localeList.add(Locale("EN"))
                        }
                        var remarkTitle = ""
                        localeList.forEachIndexed { index, lang ->
                            val foodName = it.getNameByLocal(lang)
                            if (index == 0) {
                                tvFoodNameEn.text = foodName
                                tvFoodNameEn.isVisible = !foodName.isNullOrEmpty()
                                remarkTitle =
                                    itemView.context.getStringByLocale(R.string.remark, lang)
                            } else if (index == 1) {
                                tvFoodNameKh.text = foodName
                                tvFoodNameKh.isGone =
                                    tvFoodNameKh.text == tvFoodNameEn.text || tvFoodNameKh.text.isNullOrEmpty()
                                remarkTitle = "${remarkTitle} ${
                                    itemView.context.getStringByLocale(
                                        R.string.remark,
                                        lang
                                    )
                                }"
                            }
                        }
                        remarkTitle = "${remarkTitle}:"

                        Timber.e("tvFoodNameEn.text ${tvFoodNameEn.text}   ${tvFoodNameKh.text}")
                        //如果菜名一样就隐藏一个
                        tvFoodNameKh.isGone =
                            tvFoodNameKh.text == tvFoodNameEn.text || tvFoodNameKh.text.isNullOrEmpty()
                        if (it.isToBeWeighed() && it.isHasProcessed() && !it.isMealSet()) {
                            tvFoodNameEn.text =
                                "${tvFoodNameEn.text}(${it.getWeightStr()})"
                        }

                        setMealRecyclerView.isGone = it.orderMealSetGoodsDTOList.isNullOrEmpty()
                        setMealRecyclerView.adapter = PrinterKitchenSetMealGoodAdapter(
                            it.orderMealSetGoodsDTOList ?: listOf(),
                            templateItem
                        )
                        specRecyclerView.isGone = it.tagItems.isNullOrEmpty()
                        specRecyclerView.adapter =
                            PrinterKitchenSpecificationsAdapter(
                                it.tagItems ?: arrayListOf(),
                                resource,
                                templateItem
                            )

                        feedRecyclerView.isGone = it.feeds.isNullOrEmpty()
                        feedRecyclerView.adapter =
                            PrinterKitchenFeedAdapter(
                                it.feeds ?: arrayListOf(),
                                templateItem
                            )

                        tvFoodCount.text = "x${it.num.toString()}"

                        if (it.getFinalNote().isNullOrEmpty()) {
                            tvRemark.isVisible = false
                            tvRemarkTitle.isVisible = false
                        } else {
                            tvRemark.isVisible = true
                            tvRemarkTitle.isVisible = true
                            tvRemark.text = it.getFinalNote()
                            tvRemarkTitle.text = remarkTitle
                        }

                        if (templateItem.informationShow != null) {
                            tvFoodNameEn.textSize =
                                templateItem.informationShow.getProductNameFontRealSize(
                                    itemView.context,
                                    isEightyWidth = true
                                )
                            tvFoodNameKh.textSize =
                                templateItem.informationShow.getProductNameFontRealSize(
                                    itemView.context,
                                    isEightyWidth = true
                                )
                            tvFoodCount.textSize =
                                templateItem.informationShow.getProductNumFontRealSize(
                                    itemView.context,
                                    isEightyWidth = true
                                )
                            tvRemark.textSize =
                                templateItem.informationShow.getCookTicketMemoFontRealSize(
                                    itemView.context,
                                    isEightyWidth = true
                                )
                        }
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = PrinterTicketMenuViewHolder(
        ItemKitchenTicketOrderMenuBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: ArrayList<OrderedGoods>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}