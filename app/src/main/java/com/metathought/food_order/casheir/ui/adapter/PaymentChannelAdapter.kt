package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.PaymentChannelModel
import com.metathought.food_order.casheir.databinding.PaymentChannelItemsBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2


/**
 * <AUTHOR> @date
 * @description
 */
class PaymentChannelAdapter(
    val list: MutableList<PaymentChannelModel>,
//    val onItemClickListener: (Record) -> Unit,
) : RecyclerView.Adapter<PaymentChannelAdapter.PaymentChannelViewHolder>() {


    inner class PaymentChannelViewHolder(val binding: PaymentChannelItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: PaymentChannelModel, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
//                        val data = list[position]
                        when (resource.paymentMethodId) {
                            "1" -> {
                                tvPayment.text = context.getString(R.string.online_payment)
                            }

                            "2" -> {
                                tvPayment.text = context.getString(R.string.offline_payments)
                            }

                            "3" -> {
                                tvPayment.text = context.getString(R.string.pay_by_balance)
                            }

                            else -> {
                                tvPayment.text = resource.paymentMethodName
                            }
                        }
                        this.root.setOnClickListener {
                            mOnPaymentChannelAdapterListener?.onClick(resource)
                        }
                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaymentChannelViewHolder {
        val itemView =
            PaymentChannelItemsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PaymentChannelViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: PaymentChannelViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    fun replaceData(list: List<PaymentChannelModel>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }


    private var mOnPaymentChannelAdapterListener: OnPaymentChannelAdapterListener? = null

    fun setOnPaymentChannelAdapterListener(mOnPaymentChannelAdapterListener: OnPaymentChannelAdapterListener) {
        this.mOnPaymentChannelAdapterListener = mOnPaymentChannelAdapterListener
    }

    interface OnPaymentChannelAdapterListener {
        fun onClick(paymentChannelModel: PaymentChannelModel)
    }

}