<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <!-- 快捷时间选择 -->
    <LinearLayout
        android:id="@+id/dropdownQuickTime"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="10dp"
        android:layout_weight="0.7"
        android:background="@drawable/background_white_border_black12_radius_100"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp">

        <TextView
            android:id="@+id/tvQuickTime"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/today"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp" />

        <ImageView
            android:id="@+id/arrowQuickTime"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_dropdown"
            android:visibility="visible"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <!-- 时间选择 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingHorizontal="4dp">

        <!-- 日期时间显示区域 -->
        <com.metathought.food_order.casheir.ui.widget.CalendarTextView
            android:id="@+id/tvCalendar"
            style="@style/commonCalendarTextViewStyle"
            android:layout_width="match_parent"
            android:background="@drawable/background_white_border_black12_radius_100"
            tools:text="01 03, 2024 - 01 03, 2024" />

    </LinearLayout>

</LinearLayout>